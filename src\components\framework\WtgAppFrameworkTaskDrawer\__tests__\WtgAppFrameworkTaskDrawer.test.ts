import { WtgFrameworkTask } from '@components/framework/types';
import WtgAppFrameworkTaskDrawer from '@components/framework/WtgAppFrameworkTaskDrawer';
import WtgApp from '@components/WtgApp';
import WtgDrawer from '@components/WtgDrawer';
import WtgIconButton from '@components/WtgIconButton';
import WtgStatus from '@components/WtgStatus';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { h, nextTick } from 'vue';
import WtgUi from '../../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgAppFrameworkTaskDrawer', () => {
    let el: HTMLElement;
    let task: WtgFrameworkTask;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        task = new WtgFrameworkTask();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is WtgAppFrameworkTaskDrawer', () => {
        const wrapper = mountComponent({
            props: {
                task,
            },
        });
        expect(wrapper.vm.$options.name).toBe('WtgAppFrameworkTaskDrawer');
    });

    test('it has a value property that controls the visibility of the drawer', async () => {
        const wrapper = mountComponent({
            props: {
                task,
                value: true,
            },
        });
        const drawer = wrapper.findComponent(WtgDrawer);
        expect(drawer.props('modelValue')).toBe(true);
    });

    describe('when given a title', () => {
        beforeEach(() => {
            task.title = 'Drawer Title';
        });

        test('it shows it as the modal drawer title', () => {
            const wrapper = mountComponent({
                props: {
                    task,
                    value: true,
                },
            });
            const drawer = wrapper.findComponent(WtgDrawer);
            expect(drawer.props('title')).toBe('Drawer Title');
        });
    });

    describe('when rendering in a viewport', () => {
        test('it renders the desktop footer on a desktop', () => {
            const wrapper = mountComponent({
                props: {
                    task,
                    value: true,
                },
            });
            const footer = wrapper.findComponent({ name: 'DesktopBar' });
            expect(footer.exists()).toBe(true);
        });

        // test('it renders the mobile footer on a mobile', () => {
        //     window.innerWidth = 412;
        //     const wrapper = mountComponent({
        //         props: {
        //             task,
        //             value: true,
        //         },
        //     });
        //     const footer = wrapper.findComponent({ name: 'MobileBar' });
        //     expect(footer.exists()).toBe(true);
        // });
    });

    describe('when given a task', () => {
        beforeEach(() => {
            task = {
                showFooter: true,
                cancelAction: {
                    visible: false,
                    caption: 'Cancel',
                    onInvoke: jest.fn(),
                },
                saveAction: {
                    visible: false,
                    caption: 'Save',
                    label: 'Save',
                    onInvoke: jest.fn(),
                },
                saveCloseAction: {
                    caption: 'Save and close',
                    label: 'Close',
                    onInvoke: jest.fn(),
                },
                genericActions: [],
                showEDocsAction: {
                    visible: true,
                    caption: 'eDocs',
                    label: 'eDocs',
                    onInvoke: jest.fn(),
                },
                documents: {
                    visible: true,
                    caption: 'Documents',
                },
                showLogsAction: {
                    visible: true,
                    caption: 'eDocs',
                    label: 'eDocs',
                    onInvoke: jest.fn(),
                },
                showMessagesAction: {
                    visible: true,
                    caption: 'eDocs',
                    label: 'eDocs',
                    onInvoke: jest.fn(),
                },
                showNotesAction: {
                    visible: true,
                    caption: 'eDocs',
                    label: 'eDocs',
                    onInvoke: jest.fn(),
                },
                showWorkflowActions: {
                    visible: true,
                    caption: 'eDocs',
                    label: 'eDocs',
                    menuItems: [],
                },
            } as any;
        });

        test('it passes it to the actions', () => {
            window.innerWidth = 1920;
            const wrapper = mountComponent({
                props: {
                    task,
                    value: true,
                },
            });
            const actionsBar = wrapper.findComponent({ name: 'DesktopBar' });
            expect(actionsBar.exists()).toBe(true);
        });

        test('it renders a task-action component', async () => {
            const wrapper = mountComponent({
                props: {
                    task,
                    value: true,
                },
            });

            const overflowActions = wrapper.findComponent({ name: 'TaskActionsOverflow' });
            expect(overflowActions.exists()).toBe(true);
        });

        test('it should render a status if provided', () => {
            task.currentStatus = { code: 'TST', label: 'Test', sentiment: 'primary', variant: undefined };
            const wrapper = mountComponent({
                props: {
                    task,
                },
            });

            const status = wrapper.findComponent(WtgStatus);
            expect(status.exists()).toBe(true);

            const statusProps = status.props();
            expect(statusProps.label).toBe(task.currentStatus.label);
            expect(statusProps.sentiment).toBe(task.currentStatus.sentiment);
            expect(statusProps.variant).toBe(task.currentStatus.variant);
        });

        test('it should not render a status if not provided', () => {
            const wrapper = mountComponent({
                props: {
                    task,
                },
            });

            const status = wrapper.findComponent(WtgStatus);
            expect(status.exists()).toBe(false);
        });

        test('it should not render a status if empty', () => {
            task.currentStatus = { code: '', label: '', sentiment: undefined, variant: undefined };
            const wrapper = mountComponent({
                props: {
                    task,
                },
            });

            const status = wrapper.findComponent(WtgStatus);
            expect(status.exists()).toBe(false);
        });
    });

    describe('toolbar buttons', () => {
        describe('favorite button', () => {
            test('it should display favorite button with star-empty icon', () => {
                task.entityUrl = '#/formFlow/definitionPk1/entityPk1';
                const wrapper = mountComponent({
                    props: {
                        task,
                    },
                });

                const favoriteButton = wrapper.findComponent(WtgIconButton);
                expect(favoriteButton.props('tooltip')).toBe('Toggle favorite');
                expect(favoriteButton.props('icon')).toBe('s-icon-star-empty');
                expect(favoriteButton.props('color')).toBe('');
            });

            test('it should display favorite button with star-filled icon', () => {
                task.entityUrl = '#/formFlow/definitionPk1/entityPk1';
                task.isFavorite = true;
                const wrapper = mountComponent({
                    props: {
                        task,
                    },
                });

                const favoriteButton = wrapper.findComponent(WtgIconButton);
                expect(favoriteButton.props('tooltip')).toBe('Toggle favorite');
                expect(favoriteButton.props('icon')).toBe('s-icon-star-filled');
                expect(favoriteButton.props('color')).toBe('gold');
            });

            test('it should call toggleFavorite when button is clicked', () => {
                task.entityUrl = '#/formFlow/definitionPk1/entityPk1';
                task.toggleFavorite = jest.fn();
                const wrapper = mountComponent({
                    props: {
                        task,
                    },
                });

                const favoriteButton = wrapper.findComponent(WtgIconButton);
                favoriteButton.trigger('click');
                expect(task.toggleFavorite).toHaveBeenCalledTimes(1);
            });
        });

        describe('copy link button', () => {
            test('it should display copy link button if entityUrl is provided', () => {
                task.entityUrl = '#/formFlow/definitionPk1/entityPk1';
                const wrapper = mountComponent({
                    props: {
                        task,
                    },
                });

                const copyLinkButton = wrapper.findAllComponents(WtgIconButton).at(1);
                expect(copyLinkButton!.exists()).toBe(true);
                expect(copyLinkButton!.props('icon')).toBe('s-icon-link');
                expect(copyLinkButton!.props('tooltip')).toBe('Copy link');
            });

            test('it should copy the link to current entity to clipboard', async () => {
                task.entityUrl = '#/formFlow/definitionPk1/entityPk1';
                const wrapper = mountComponent({
                    props: {
                        task,
                    },
                });

                const copyLinkButton = wrapper.findAllComponents(WtgIconButton).at(1);
                const writeText = jest.fn();

                Object.assign(navigator, { clipboard: { writeText } });

                copyLinkButton!.trigger('click');
                expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
                    'http://localhost/#/formFlow/definitionPk1/entityPk1'
                );
            });

            test('it should set button icon to "check" and reset after 3 seconds', async () => {
                task.entityUrl = '#/formFlow/definitionPk1/entityPk1';
                const wrapper = mountComponent({
                    props: {
                        task,
                    },
                });

                const copyLinkButton = wrapper.findAllComponents(WtgIconButton).at(1);
                const writeText = jest.fn();

                Object.assign(navigator, { clipboard: { writeText } });
                expect(copyLinkButton!.props('icon')).toBe('s-icon-link');

                copyLinkButton!.trigger('click');
                await nextTick();

                expect(copyLinkButton!.props('icon')).toBe('s-icon-check');

                await new Promise((resolve) => setTimeout(resolve, 3500));
                expect(copyLinkButton!.props('icon')).toBe('s-icon-link');
            });
        });

        describe('open in new tab button', () => {
            test('it should display open in new tab button if entityUrl is provided', () => {
                task.entityUrl = '#/formFlow/definitionPk1/entityPk1';
                const wrapper = mountComponent({
                    props: {
                        task,
                    },
                });

                const openLinkButton = wrapper.findAllComponents(WtgIconButton).at(2);
                expect(openLinkButton!.exists()).toBe(true);
                expect(openLinkButton!.props('href')).toBe('#/formFlow/definitionPk1/entityPk1');
                expect(openLinkButton!.props('target')).toBe('_blank');
                expect(openLinkButton!.props('tooltip')).toBe('Open in new tab');
                expect(openLinkButton!.props('icon')).toBe('s-icon-new-window');
            });
        });
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgApp, {
            slots: {
                default: () => h(WtgAppFrameworkTaskDrawer, { ...props, slots }),
            },
            global: {
                plugins: [wtgUi],
            },
        }).findComponent(WtgAppFrameworkTaskDrawer);
    }
});
