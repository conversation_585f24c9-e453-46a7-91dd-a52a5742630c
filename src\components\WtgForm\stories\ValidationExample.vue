<template>
    <WtgPanel caption="Create Account" layout="grid" max-width="400px">
        <WtgTextField
            v-model="email"
            :validation-state="emailValidationState"
            :loading="emailLoadingState"
            label="Email (Async validation)"
        />
        <WtgPasswordField v-model="password" label="Password" :validation-state="passwordValidationState" />
        <WtgPasswordField
            v-model="confirmPassword"
            label="Confirm Password"
            :validation-state="confirmPasswordValidationState"
        />
    </WtgPanel>
</template>

<script setup lang="ts">
import { WtgPanel, WtgPasswordField, WtgTextField } from '@components';
import { AlertLevel, ValidationState } from '@composables/notifications';
import { ref, watch } from 'vue';

const emailValidationState = ref<ValidationState | undefined>(undefined);
const emailLoadingState = ref(false);
const email = ref('');

const passwordValidationState = ref<ValidationState | undefined>(undefined);
const password = ref('');

const confirmPasswordValidationState = ref<ValidationState | undefined>(undefined);
const confirmPassword = ref('');

watch(email, validateEmailAsync);
watch(password, validatePassword);
watch([password, confirmPassword], validateConfirmPassword);

const validateEmail = (email: string) => {
    return email
        .toLowerCase()
        .match(
            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        );
};

function validatePassword() {
    if (password.value.length < 6) {
        passwordValidationState.value = {
            alertLevel: AlertLevel.Error,
            targetKey: undefined,
            targetProperty: 'password',
            messages: ['Password must be at least 6 characters long'],
            error: true,
            warning: false,
        };
    } else if (password.value.length < 10) {
        passwordValidationState.value = {
            alertLevel: AlertLevel.Warning,
            targetKey: undefined,
            targetProperty: 'password',
            messages: ['Passwords under 10 characters long are unsafe'],
            error: true,
            warning: false,
        };
    } else {
        passwordValidationState.value = undefined;
    }
}

function validateConfirmPassword() {
    if (confirmPassword.value.length === 0) {
        return;
    }
    if (confirmPassword.value !== password.value) {
        confirmPasswordValidationState.value = {
            alertLevel: AlertLevel.Error,
            targetKey: undefined,
            targetProperty: 'password',
            messages: ['Passwords do not match'],
            error: true,
            warning: false,
        };
    } else {
        confirmPasswordValidationState.value = undefined;
    }
}

function validateEmailAsync(newVal: string) {
    emailLoadingState.value = true;
    setTimeout(() => {
        if (!validateEmail(newVal)) {
            emailValidationState.value = {
                alertLevel: AlertLevel.Error,
                targetKey: undefined,
                targetProperty: 'email',
                messages: ['Please enter a valid email'],
                error: true,
                warning: false,
            };
        } else if (newVal === '<EMAIL>') {
            emailValidationState.value = {
                alertLevel: AlertLevel.Error,
                targetKey: undefined,
                targetProperty: 'email',
                messages: ['That email is already in use'],
                error: true,
                warning: false,
            };
        } else {
            emailValidationState.value = undefined;
        }
        emailLoadingState.value = false;
    }, 1000);
}
</script>
