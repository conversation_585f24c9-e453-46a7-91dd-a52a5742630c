<template>
    <VDialog
        :aria-label="formatCaption('dialog.overlay')"
        :absolute="absolute"
        :eager="eager"
        :height="computedHeight"
        :fullscreen="computedFullscreen"
        :location="location"
        :max-height="computedMaxHeight"
        :max-width="maxWidth"
        :min-height="computedMinHeight"
        :min-width="minWidth"
        :model-value="internalValue"
        :persistent="persistent"
        :retain-focus="computedRetainFocus"
        :scrollable="scrollable"
        :size="size"
        :title="title"
        :transition="transition"
        :width="computedWidth"
        @update:model-value="onUpdateModelValue"
    >
        <template #activator="slotData">
            <slot
                name="activator"
                v-bind="{
                    ...slotData,
                    on: toLegacyOn(slotData?.props),
                }"
            />
        </template>
        <template #default="{ isActive }: { isActive: Ref<Boolean> }">
            <slot :is-active="isActive" />
        </template>
    </VDialog>
</template>

<script setup lang="ts">
import { useDisplay } from '@composables/display';
import { useFocusTrap } from '@composables/focusTrap';
import { useLocale } from '@composables/locale';
import { toLegacyOn } from '@utils/compat-utils';
import { computed, Ref, ref, watchEffect } from 'vue';
import { VDialog } from 'vuetify/components/VDialog';
import { modalProps } from './types';

//
// Properties
//
const props = defineProps(modalProps());

//
// Emits
//
const emit = defineEmits<{
    input: [value: boolean];
    'model-compat:input': [value: any];
    'update:modelValue': [value: boolean];
}>();

//
// State
//
const internalValue = ref(false);

//
// Composables
//
const { onDesktop, xs } = useDisplay();
const { trapFocus } = useFocusTrap();
const { formatCaption } = useLocale();
const computedRetainFocus = computed(() => (props.retainFocus !== undefined ? props.retainFocus : trapFocus.value));

//
// Computed
//
const computedFullscreen = computed(() => {
    if (props.fullscreen !== undefined) return props.fullscreen;

    switch (props.size) {
        case 'xl':
        case 'l':
            return !onDesktop.value;
        case 'm':
            return xs.value;
        default:
            return undefined;
    }
});

const computedHeight = computed(() => {
    if (props.height !== undefined) return props.height;

    switch (props.size) {
        case 'l':
            return '90vh';
        case 'm':
            return '60vh';
        default:
            return undefined;
    }
});

const computedWidth = computed(() => {
    if (props.width !== undefined) return props.width;

    switch (props.size) {
        case 'xl':
            return '90vw';
        case 'l':
            return '1200';
        case 'm':
            return '780';
        default:
            return undefined;
    }
});

const computedMinHeight = computed(() => {
    if (props.minHeight !== undefined) return props.minHeight;

    switch (props.size) {
        case 'xl':
            return '90vh';
        default:
            return undefined;
    }
});

const computedMaxHeight = computed(() => {
    if (props.maxHeight !== undefined) return props.maxHeight;

    switch (props.size) {
        case 'l':
            return '1600';
        case 'm':
            return '1200';
        default:
            return undefined;
    }
});

//
// Watchers
//
watchEffect(() => {
    internalValue.value = props.modelValue ?? props.value ?? false;
});

//
// Event Handlers
//
function onUpdateModelValue(state: boolean) {
    internalValue.value = state;
    emit('input', state);
    emit('model-compat:input', state);
    emit('update:modelValue', state);
}
</script>

<style lang="scss">
.wtg-modal__container {
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0;
    flex-grow: 1;
}

.wtg-modal__title {
    align-items: center;
    background: var(--s-neutral-bg-default);
    border: 1px solid var(--s-neutral-border-weak-default);
    border-bottom: none;
    border-radius: var(--s-radius-m) var(--s-radius-m) 0px 0px;
    color: var(--s-neutral-txt-default);
    padding: var(--s-padding-l) var(--s-padding-xl) var(--s-padding-l) var(--s-padding-xl);
    display: flex;
}

.wtg-modal__tabs {
    background: var(--s-neutral-bg-default);
    border-left: 1px solid var(--s-neutral-border-weak-default);
    border-right: 1px solid var(--s-neutral-border-weak-default);
    color: var(--s-neutral-txt-default);
    padding-inline-start: var(--s-padding-xl);
    padding-inline-end: var(--s-padding-xl);
}

.wtg-modal__content {
    background: var(--s-neutral-bg-default);
    border: 1px solid var(--s-neutral-border-weak-default);
    color: var(--s-neutral-txt-default);
    padding: var(--s-padding-xl);
    flex-grow: 1;
    overflow-y: auto;
}

.wtg-modal__content-canvas {
    background: var(--s-neutral-canvas-default);
    border: 1px solid var(--s-neutral-border-weak-default);
    color: var(--s-neutral-txt-default);
    padding: var(--s-padding-xl);
    flex-grow: 1;
    overflow-y: auto;
}

.wtg-modal__actions {
    background: var(--s-neutral-bg-default);
    border: 1px solid var(--s-neutral-border-weak-default);
    border-top: none;
    border-radius: 0px 0px var(--s-radius-m) var(--s-radius-m);
    color: var(--s-neutral-txt-default);
    display: flex;
    padding: var(--s-padding-l) var(--s-padding-xl) var(--s-padding-l) var(--s-padding-xl);
    gap: var(--s-spacing-m);
}

.v-dialog--fullscreen {
    .wtg-modal__title,
    .wtg-modal__actions {
        border-radius: 0px;
    }
}
</style>
