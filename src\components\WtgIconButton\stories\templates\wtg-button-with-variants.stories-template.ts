export const ButtonWithVariantsTemplate = `
<div style="flex: 1 1 100%; display: flex; flex-wrap: wrap; gap: 8px">
    <WtgIconButton 
        v-bind="args"
        aria-label="Aria Name"
        :icon="args.icon"
        @click="action">
    </WtgIconButton>
    <WtgIconButton 
        v-bind="args"
        aria-label="Aria Name"
        variant="fill"
        sentiment="primary"
        :icon="args.icon"
        @click="action">
    </WtgIconButton>
    <WtgIconButton 
        v-bind="args"
        aria-label="Aria Name"
        variant="ghost"
        :icon="args.icon"
        @click="action">
    </WtgIconButton>
</div>`;
