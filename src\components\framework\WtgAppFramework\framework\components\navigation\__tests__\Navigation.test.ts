import { WtgFramework } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount, VueWrapper } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../WtgUi';
import Navigation from '../Navigation.vue';

enableAutoUnmount(afterEach);

let wtgUi: WtgUi;

window.URL.createObjectURL = jest.fn().mockReturnValue('dummyBlob');

describe('navigation', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        wtgUi = new WtgUi();
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.navDrawer = {
            isPinned: false,
            visible: false,
            isRailActive: false,
            open: jest.fn(),
            close: jest.fn(),
        } as any;
        setApplication(application);
        wtgUi.breakpoint.mdAndDown = false;
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is Navigation', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('Navigation');
    });

    test('it renders a wtg-navigation based on hideAppBar', async () => {
        application.hideAppBar = false;
        const wrapper = await mountComponentAsync();
        let Navigation = wrapper.findComponent({ name: 'WtgNavigation' });
        expect(Navigation.props().modelValue).toBe(true);

        application.hideAppBar = false;
        await nextTick();
        Navigation = wrapper.findComponent({ name: 'WtgNavigation' });
        expect(Navigation.props().modelValue).toBe(true);
    });

    test('it sets the correct props for the navigation drawer to be part of the app framework, the navigation drawer (and the entity drawer) take precedence over masthead and footer', async () => {
        const wrapper = await mountComponentAsync();
        const masthead = wrapper.findComponent({ name: 'WtgNavigation' });
        expect(masthead.props('order')).toBe(1);
    });

    test('its renders a navigation menu', async () => {
        const wrapper = await mountComponentAsync();
        const navigationMenu = wrapper.findComponent({ name: 'NavigationMenu' });
        expect(navigationMenu.exists()).toBe(true);
    });

    test('it renders a navigation footer', async () => {
        const wrapper = await mountComponentAsync();
        const navigationFooter = wrapper.findComponent({ name: 'NavigationFooter' });
        expect(navigationFooter.exists()).toBe(true);
    });

    describe('on a mobile device', () => {
        let wrapper: any;

        beforeEach(() => {
            wtgUi.breakpoint.mdAndDown = true;
        });

        test('it toggles the nav drawer when model value updated', async () => {
            application.navDrawer.visible = true;
            wrapper = await mountComponentAsync();
            const Navigation = wrapper.findComponent({ name: 'WtgNavigation' });
            Navigation.vm.$emit('update:modelValue', false);

            expect(application.navDrawer.visible).toBe(false);
        });

        test('it closes if a menu item is clicked', async () => {
            application.navDrawer.visible = true;
            const wrapper = await mountComponentAsync();
            const menu = wrapper.findComponent({ name: 'NavigationMenu' });
            menu.vm.$emit('item-click');

            expect(application.navDrawer.close).toHaveBeenCalledTimes(1);
        });

        test('it closes if a footer item is clicked', async () => {
            application.navDrawer.visible = true;
            wrapper = await mountComponentAsync();
            const footer = wrapper.findComponent({ name: 'NavigationFooter' });
            footer.vm.$emit('item-click');

            expect(application.navDrawer.close).toHaveBeenCalledTimes(1);
        });

        test('it renders the navigation menu, spacer and footer', async () => {
            application.navDrawer.visible = true;
            wrapper = await mountComponentAsync();
            const drawer = wrapper.findComponent({ name: 'WtgNavigation' });
            expect(drawer.findComponent({ name: 'NavigationMenu' }).exists()).toBe(true);
            expect(drawer.findAllComponents({ name: 'WtgSpacer' }).length).toBe(2);
            expect(drawer.findComponent({ name: 'NavigationFooter' }).exists()).toBe(true);
        });
    });

    describe('on a desktop device', () => {
        let wrapper: any;

        beforeEach(() => {
            wtgUi.breakpoint.lgAndUp = true;
        });

        test('visibility is based on model value', async () => {
            application.navDrawer.visible = true;
            wrapper = await mountComponentAsync();
            const Navigation = wrapper.findComponent({ name: 'WtgNavigation' });
            expect(Navigation.props().modelValue).toBe(true);
            application.navDrawer.visible = false;
            await nextTick();

            expect(Navigation.props().modelValue).toBe(false);
        });

        test('it toggles collapsed state based on collapsed model value', async () => {
            application.navDrawer.isRailActive = true;
            wrapper = await mountComponentAsync();
            const Navigation = wrapper.findComponent({ name: 'WtgNavigation' });
            expect(Navigation.vm.collapsed).toBe(true);
            Navigation.vm.$emit('update:collapsed', false);
            await nextTick();

            expect(Navigation.vm.collapsed).toBe(false);
        });

        test('it renders the navigation menu and footer, with out the spacer', async () => {
            wrapper = await mountComponentAsync();
            const drawer = wrapper.findComponent({ name: 'WtgNavigation' });
            expect(drawer.findComponent({ name: 'NavigationMenu' }).exists()).toBe(true);
            expect(drawer.findAllComponents({ name: 'WtgSpacer' }).length).toBe(1);
            expect(drawer.findComponent({ name: 'NavigationFooter' }).exists()).toBe(true);
        });

        test('it does not expand the navigation drawer when a menu item is clicked', async () => {
            application.navDrawer.isRailActive = true;
            wrapper = await mountComponentAsync();
            const menu = wrapper.findComponent({ name: 'NavigationMenu' });
            menu.vm.$emit('item-click');

            expect(application.navDrawer.isRailActive).toBe(true);
        });

        test('it does not collapse the navigation drawer when a menu item is clicked', async () => {
            application.navDrawer.isRailActive = false;
            wrapper = await mountComponentAsync();
            const menu = wrapper.findComponent({ name: 'NavigationMenu' });
            menu.vm.$emit('item-click');

            expect(application.navDrawer.isRailActive).toBe(false);
        });
    });

    test('it will open the navigation drawer if group click is heard', async () => {
        application.navDrawer.isRailActive = true;
        const wrapper: VueWrapper<any> = await mountComponentAsync();
        wrapper.vm.onGroupClick();
        await nextTick();

        const NavigationDrawer = wrapper.findComponent({ name: 'WtgNavigation' });
        expect(NavigationDrawer.vm.collapsed).toBe(false);
    });

    describe('Accessibility', () => {
        beforeEach(() => {
            application.ariaLabels = {
                applicationTitle: 'Portal Title',
                logoImageAlt: 'Logo',
                navigationDrawer: 'Drawer',
                navigationDrawerIcon: 'Toggle Drawer',
            } as any;
            application.navDrawer.visible = true;
            application.navDrawer.isRailActive = false;
        });

        test('it passes the navigation the aria labels', async () => {
            const wrapper = await mountComponentAsync();
            const Navigation = wrapper.findComponent({ name: 'WtgNavigation' });
            expect(Navigation.props().drawerAriaLabel).toBe('Drawer');
            expect(Navigation.props().titleAriaLabel).toBe('Portal Title ');
        });
    });

    test('it does not render a navigation component if hideAppBar is true, (hideAppBar really means hideFrame)', async () => {
        application.hideAppBar = true;
        const wrapper = await mountComponentAsync();
        let WtgAppBar = wrapper.findComponent({ name: 'WtgNavigation' });
        expect(WtgAppBar.exists()).toBe(false);

        application.hideAppBar = false;
        await nextTick();
        WtgAppBar = wrapper.findComponent({ name: 'WtgNavigation' });
        expect(WtgAppBar.exists()).toBe(true);
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(Navigation) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(Navigation);
    }
});
