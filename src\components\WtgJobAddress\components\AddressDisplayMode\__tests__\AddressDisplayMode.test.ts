import WtgLabel from '@components/WtgLabel/WtgLabel.vue';
import { useWtgUi } from '@composables/global';
import { mount } from '@vue/test-utils';
import AddressDisplayMode from '../AddressDisplayMode.vue';

const wtgUi = useWtgUi();

describe('AddressDisplayMode', () => {
    test('it renders the component', () => {
        const wrapper = mountComponent();
        expect(wrapper.isVisible()).toBe(true);
    });

    test('it displays the provided display address when captions are provided', () => {
        const wrapper = mountComponent({
            propsData: {
                showAdditionalInformation: true,
                showContact: true,
                showOrganizationNAme: true,
                addressLines: ['Address Line 1', 'Address Line 2'],
                companyHeader: 'My Company Header',
                contact: 'My Contact Header',
                additionalInformation: 'My Additional Information',
            },
        });

        const labels = wrapper.findAllComponents(WtgLabel);
        expect(labels.length).toBe(5);
        expect(labels.at(0)!.text()).toBe('My Company Header');
        expect(labels.at(1)!.text()).toBe('Address Line 1');
        expect(labels.at(2)!.text()).toBe('Address Line 2');
        expect(labels.at(3)!.text()).toBe('My Additional Information');
        expect(labels.at(4)!.text()).toBe('My Contact Header');
    });

    it('shows contact in WtgLabel when showContact is true and contact is provided', () => {
        const wrapper = mountComponent({
            propsData: {
                showContact: true,
                contact: 'John Doe',
            },
        });
        const labels = wrapper.findAllComponents({ name: 'WtgLabel' });
        expect(labels.at(labels.length - 1)?.text()).toContain('John Doe');
    });

    it('shows noAddressSelected in WtgLabel when showContact is true and contact is empty', () => {
        const wrapper = mountComponent({
            propsData: {
                showAdditionalInformation: true,
                showContact: true,
                showOrganizationNAme: true,
                addressLines: ['Address Line 1', 'Address Line 2'],
                companyHeader: 'My Company Header',
                additionalInformation: 'My Additional Information',
                contact: '',
            },
        });
        const labels = wrapper.findAllComponents({ name: 'WtgLabel' });
        expect(labels.at(labels.length - 1)?.text()).toContain('No contact details supplied.');
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount<any>(AddressDisplayMode, {
            propsData: {
                ...propsData,
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
