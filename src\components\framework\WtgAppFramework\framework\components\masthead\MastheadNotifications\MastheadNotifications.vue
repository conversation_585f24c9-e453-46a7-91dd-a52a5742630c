<template>
    <div :class="!isMobile ? 'ml-2' : undefined">
        <WtgNotifications v-model="open" :notifications="notifications" />
    </div>
</template>

<script setup lang="ts">
import WtgNotifications from '@components/WtgNotifications';
import { WtgFrameworkNotification } from '@components/framework/types';
import { useFramework } from '@composables/framework';
import { useApplication } from '@composables/application';
import { computed } from 'vue';

const application = useApplication();
const { isMobile } = useFramework();

const notifications = computed((): WtgFrameworkNotification[] => {
    return application.currentTask?.notifications ?? [];
});

const open = computed({
    get(): boolean {
        return application.notificationsDrawer.visible;
    },
    set(value: boolean): void {
        application.notificationsDrawer.visible = value;
    },
});
</script>
