# Use Vue Composition API and TypeScript

## Status
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

WiseTech has chosen [Vue](https://vuejs.org/guide/introduction.html) as the framework of choice for all web front-end development.

## Decision

## Status
- [ ] Proposed
- [x] Accepted
- [ ] Rejected
- [ ] Deprecated
- [ ] Superseded

With the choice of Vue already taken Composition API and Typescript are the natural paradigms to choose when working in the environment.

## Consequences

Pros:
1. [Why Composition API?](https://vuejs.org/guide/extras/composition-api-faq.html#why-composition-api).
2. [Typescript](https://www.typescriptlang.org/).

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by <PERSON>](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.
