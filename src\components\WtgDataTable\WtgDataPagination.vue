<template>
    <div class="wtg-data__pagination">
        <WtgSpacer />
        <span id="items-per-page"> {{ itemsPerPageCaption }} </span>
        <WtgSelectField
            :model-value="internalItemsPerPage"
            aria-labelledby="items-per-page"
            item-text="title"
            :items="itemsPerPageOptions"
            style="max-width: 80px"
            @update:model-value="onUpdateItemsPerPage"
        >
        </WtgSelectField>
        <span data-testid="current-items-caption">{{ currentItemsCaption }}</span>
        <VPagination
            :length="pageCount"
            :total-visible="0"
            show-first-last-page
            density="compact"
            @update:model-value="onUpdatePagination"
        >
            <template #first="first">
                <WtgIconButton
                    icon="s-icon-first"
                    :tooltip="firstPageTooltip"
                    :aria-label="firstPageTooltip"
                    :disabled="first.disabled"
                    variant="ghost"
                    @click="first.onClick"
                />
            </template>
            <template #prev="prev">
                <WtgIconButton
                    icon="s-icon-move-left"
                    :tooltip="prevPageTooltip"
                    :aria-label="prevPageTooltip"
                    :disabled="prev.disabled"
                    variant="ghost"
                    @click="prev.onClick"
                />
            </template>
            <template #next="next">
                <WtgIconButton
                    icon="s-icon-move-right"
                    :tooltip="nextPageTooltip"
                    :aria-label="nextPageTooltip"
                    :disabled="next.disabled"
                    variant="ghost"
                    @click="next.onClick"
                />
            </template>
            <template #last="last">
                <WtgIconButton
                    icon="s-icon-last"
                    :tooltip="lastPageTooltip"
                    :aria-label="lastPageTooltip"
                    :disabled="last.disabled"
                    variant="ghost"
                    @click="last.onClick"
                />
            </template>
        </VPagination>
    </div>
</template>

<script setup lang="ts">
import WtgIconButton from '@components/WtgIconButton';
import WtgSelectField from '@components/WtgSelectField';
import WtgSpacer from '@components/WtgSpacer';
import { useLocale } from '@composables/locale';
import { computed, PropType, ref, watch } from 'vue';
import { VPagination } from 'vuetify/components/VPagination';

const props = defineProps({
    itemsLength: {
        type: Number,
        default: 0,
    },
    itemsPerPage: {
        type: [Number, String],
        default: '10',
    },
    itemsPerPageOptions: {
        type: Array as PropType<(number | { title: string; value: number })[]>,
        default: () => [5, 10, 25, 50, 100],
    },
    page: {
        type: [Number, String],
        default: '1',
    },
});

const { formatCaption } = useLocale();

const emit = defineEmits<{
    'update:items-per-page': [value: number];
    'update:options': [value: any];
    'update:page': [value: number];
}>();

const firstPageTooltip = computed(() => formatCaption('dataTable.firstPageTooltip'));
const prevPageTooltip = computed(() => formatCaption('dataTable.prevPageTooltip'));
const nextPageTooltip = computed(() => formatCaption('dataTable.nextPageTooltip'));
const lastPageTooltip = computed(() => formatCaption('dataTable.lastPageTooltip'));
const internalPage = ref(Number(props.page));
const internalItemsPerPage = ref(Number(props.itemsPerPage));
const internalOptions = computed(() => ({
    page: internalPage.value,
    itemsPerPage: internalItemsPerPage.value,
}));
const itemsPerPageCaption = computed(() => formatCaption('dataTable.itemsPerPage'));
const itemsLength = computed(() => Math.max(0, props.itemsLength));
const currentItemsCaption = computed(() => {
    if (itemsLength.value === 0) {
        return `0-0 of 0`;
    }
    const itemsPerPage = Math.min(
        internalItemsPerPage.value === -1 ? itemsLength.value : internalItemsPerPage.value,
        itemsLength.value
    );
    return `${itemsPerPage * (internalPage.value - 1) + 1}-${internalPage.value * itemsPerPage} of ${
        itemsLength.value
    }`;
});
const pageCount = computed(() => {
    if (internalItemsPerPage.value === -1 || itemsLength.value === 0) return 1;
    return Math.ceil(itemsLength.value / internalItemsPerPage.value);
});

watch(
    () => props.page,
    (value) => {
        internalPage.value = Number(value);
    }
);

watch(
    () => props.itemsPerPage,
    (value) => {
        internalItemsPerPage.value = Number(value);
    }
);

const updatePage = (newPage: number): void => {
    if (internalPage.value !== newPage) {
        internalPage.value = newPage;
        emit('update:page', internalPage.value);
        updateOptions();
    }
};

function updateOptions(): void {
    emit('update:options', internalOptions.value);
}

const onUpdateItemsPerPage = (newValue: string) => {
    let newItemsPerPage = parseInt(newValue, 10);
    newItemsPerPage = isFinite(newItemsPerPage) ? newItemsPerPage : -1;
    if (internalItemsPerPage.value !== newItemsPerPage) {
        internalItemsPerPage.value = newItemsPerPage;
        emit('update:items-per-page', newItemsPerPage);
        updateOptions();
    }
};
function onUpdatePagination(newPage: number): void {
    updatePage(newPage);
}
</script>

<style lang="scss">
.wtg-data__pagination {
    display: flex;
    align-items: center !important;
    gap: var(--s-spacing-m);
    padding-top: var(--s-spacing-m);
    padding-bottom: var(--s-spacing-m);
    overflow: hidden;
    flex-shrink: 0;

    .v-pagination__item,
    .v-pagination__first,
    .v-pagination__prev,
    .v-pagination__next,
    .v-pagination__last {
        margin: 0px;
        margin-inline-end: var(--s-spacing-s);
    }
}
</style>
