import DocumentViewerImage from '@components/WtgDocumentViewer/components/imageViewer/DocumentViewerImage.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../../../WtgUi';

const wtgUi = new WtgUi();
enableAutoUnmount(afterEach);
window.URL.createObjectURL = jest.fn().mockReturnValue('dummyBlob');

jest.mock('utif', () => ({
    decode: jest.fn().mockReturnValue([{ width: 800, height: 600 }]),
    decodeImage: jest.fn(),
    toRGBA8: jest.fn().mockReturnValue(new Uint8Array(800 * 600 * 4)),
}));

describe('WtgDocumentViewerImage.vue', () => {
    it('renders properly', () => {
        const wrapper = mountComponent();
        expect(wrapper.exists()).toBe(true);
    });

    it('accepts an ArrayBuffer as imageSource and renders an img tag', async () => {
        const wrapper = mountComponent();
        await nextTick();
        expect(wrapper.html()).toContain('img');
    });

    it('supports zooming functionality', async () => {
        const wrapper = mountComponent();
        await nextTick();
        await wrapper.setProps({ zoomPercentage: '20%' });
        expect(wrapper.html()).toContain('scale(0.2)');
    });

    it('handles rotation correctly', async () => {
        const wrapper = mountComponent();
        await nextTick();
        await wrapper.setProps({ imageRotation: 90 });
        expect(wrapper.html()).toContain('rotate(90deg)');
    });

    it('handles the tiff file type images correctly', async () => {
        const wrapper = mountComponent({ props: { fileType: 'tiff' } });
        await nextTick();
        expect(wrapper.html()).toContain('img');
        expect(wrapper.html()).toContain('data:image/png;base64,00');
    });

    it('handles image scaling correctly', async () => {
        const wrapper = mountComponent();

        await nextTick();

        (wrapper.vm.$.exposed as any).initialImageHeight.value = 300;

        await wrapper.setProps({
            scaleConfig: {
                scaleMode: 'ver',
            },
        });

        await nextTick();
        expect(wrapper.html()).toContain('scale(3.5833333333333335)');

        await wrapper.setProps({
            scaleConfig: {
                scaleMode: 'hor',
            },
        });

        expect(wrapper.html()).toContain('scale(1)');
    });

    it('has a default slot with imageData', async () => {
        const wrapper = mountComponent({
            slots: {
                default: `<div class="highlight-slot">
                            Image Width: {{ imageData.imageWidth }},
                            Image Height: {{ imageData.imageHeight }},
                        </div>`,
            },
        });
        await nextTick();

        const slotContent = wrapper.find('.highlight-slot');
        await nextTick();

        expect(slotContent.text()).toContain('Image Width: 0, Image Height: 0');

        (wrapper.vm.$.exposed as any).imageWidth.value = 180;
        await nextTick();

        expect(slotContent.text()).toContain('Image Width: 180, Image Height: 0');

        (wrapper.vm.$.exposed as any).imageHeight.value = 90;
        await nextTick();

        expect(slotContent.text()).toContain('Image Width: 180, Image Height: 90');
    });
});

function mountComponent({ props = {}, slots = {} } = {}) {
    return mount(DocumentViewerImage as any, {
        props: { imageSource: new ArrayBuffer(8), imageViewerWrapWidth: 1200, imageViewerWrapHeight: 1200, ...props },
        slots,
        global: {
            plugins: [wtgUi],
        },
    });
}
