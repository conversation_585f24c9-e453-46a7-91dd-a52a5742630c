<template>
    <component
        v-bind="{ ...ariaProps }"
        :is="computedTag"
        v-floating-vue-tooltip="tooltipDirective"
        :class="computedClasses"
        :disabled="disabled"
        :href="useLinkHref"
        :to="isLink ? to : undefined"
        :exact="isLink ? exact : undefined"
        :replace="isLink ? replace : undefined"
        :style="{ ...computedStyle, ...measurableStyles }"
        :type="computedType"
        @click="onClick"
    >
        <div class="wtg-button__shadow" />
        <WtgIcon v-if="leadingIcon" class="wtg-button__icon">{{ leadingIcon }}</WtgIcon>
        <div class="wtg-button__content">
            <slot />
        </div>
        <WtgLoader v-if="loading" class="wtg-button__loader" />
        <WtgIcon v-if="trailingIcon" class="wtg-button__icon">{{ trailingIcon }}</WtgIcon>
    </component>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { WtgLoader } from '@components/WtgLoader';
import { useColor } from '@composables/color';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { makeMeasureProps, useMeasure } from '@composables/measure';
import { makeRouterProps, useLink } from '@composables/router';
import { useSegmentedControl } from '@composables/segmentedControl';
import { makeSizeProps, useSize } from '@composables/size';
import { makeTooltipProps, useTooltip } from '@composables/tooltip';
import { PropType, computed } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * Specifies whether the button is in an "active" state (e.g., when the button is pressed).
     */
    active: {
        type: Boolean,
        default: false,
    },

    /**
     * The name of the color to apply from the design system or a custom color.
     * This only applies when the `sentiment` property is not set.
     * If `sentiment` is insufficient, you can hardcode a color (e.g., hex or rgba values), but use this sparingly.
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * Determines if the button should be disabled.
     * When true, the button will be unclickable and visually appear disabled.
     */
    disabled: {
        type: Boolean,
        default: false,
    },

    /**
     * If set to true, the button will expand to fill the available width of its container.
     */
    fill: {
        type: Boolean,
        default: false,
    },

    /**
     * The leading icon to display before the button's text content.
     */
    leadingIcon: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the button will show a loading spinner or some kind of loading indicator.
     */
    loading: {
        type: Boolean,
        default: false,
    },

    /**
     * Defines the sentiment or visual style of the button.
     * Options include 'critical', 'primary', or 'success'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'primary' | 'success'>,
        default: undefined,
    },

    /**
     * The trailing icon to display after the button's text content.
     */
    trailingIcon: {
        type: String,
        default: undefined,
    },

    /**
     * The type of the button element.
     * Options include 'submit', 'reset', or 'button'.
     */
    type: {
        type: String as PropType<'submit' | 'reset' | 'button'>,
        default: undefined,
    },

    /**
     * Specifies the visual variant of the button.
     * Options include 'fill' for a solid background button or 'ghost' for a transparent button with a border.
     */
    variant: {
        type: String as PropType<'fill' | 'ghost'>,
        default: undefined,
    },

    /**
     * A value associated with the button, often used for buttons in segmented controls.
     * It can be of any primitive type, such as a boolean, number, or string.
     */
    value: {
        type: [Boolean, Number, String],
        default: undefined,
    },

    ...makeLayoutGridColumnProps(),
    ...makeMeasureProps(),
    ...makeRouterProps(),
    ...makeSizeProps(),
    ...makeTooltipProps(),
});

//
// Emits
//
const emit = defineEmits<{
    /**
     * Emitted when the button is clicked.
     * @event
     * @param {MouseEvent} e - The click event object.
     */
    click: [e: MouseEvent];
}>();

//
// Composables
//
const { isLink, href: useLinkHref } = useLink(props);
const { sizeClass } = useSize(props, 'button');
const { tooltipDirective } = useTooltip(props);
const { measurableStyles } = useMeasure(props);
const { isInSegmentedControl, isSelected, toggleSelected } = useSegmentedControl(props);

const { colorClasses, colorStyles } = useColor(
    props,
    computed(() => {
        return { background: props.variant === 'fill' };
    })
);

useLayoutGridColumn(props);

//
// Computed
//
const ariaProps = computed(() => {
    if (isInSegmentedControl.value) {
        return {
            'aria-pressed': isSelected.value,
        };
    }
    return {};
});

const hasCssStyleColor = computed(() => {
    return !props.disabled && !props.sentiment && (colorStyles.value.backgroundColor || colorStyles.value.color);
});

const hasCssClassColor = computed(() => {
    return !props.disabled && !props.sentiment && colorClasses.value.length > 0;
});

const computedClasses = computed(() => {
    let classes = [
        {
            'wtg-button--fill': props.variant === 'fill',
            'wtg-button--ghost': props.variant === 'ghost',
            'wtg-button--default': props.variant !== 'fill' && props.variant !== 'ghost',
            'wtg-button--fill-available': props.fill,
            'wtg-button--active': props.active,
            'wtg-button--color': (hasCssClassColor.value || hasCssStyleColor.value) && !props.disabled,
            'wtg-button--loading': props.loading,
            'wtg-button--success': props.sentiment === 'success' && !props.disabled,
            'wtg-button--critical': props.sentiment === 'critical' && !props.disabled,
            'wtg-button--primary': props.sentiment === 'primary' && !props.disabled,
            'wtg-button--selected': isSelected.value,
            'wtg-button--disabled': props.disabled,
        },
        sizeClass.value,
        'wtg-button',
    ];
    if (hasCssClassColor.value && props.color) {
        classes = [...classes, ...colorClasses.value];
    }
    return classes;
});

const computedStyle = computed(() => {
    if (hasCssStyleColor.value) {
        if (props.variant !== 'ghost' && props.variant !== 'fill') {
            return { ...colorStyles.value, borderColor: colorStyles.value.color };
        }

        return { ...colorStyles.value };
    }

    if (hasCssClassColor.value) {
        if (props.variant !== 'ghost' && props.variant !== 'fill') {
            return { borderColor: 'currentColor' };
        }
    }

    return {};
});

const computedTag = computed(() => {
    if (!props.disabled && isLink.value) {
        if (props.to) {
            return 'router-link';
        }
        if (props.href) {
            return 'a';
        }
    }
    return 'button';
});

const computedType = computed(() => (!props.disabled && props.href ? undefined : props.type ? props.type : 'button'));

//
// Event Handlers
//
function onClick(e: MouseEvent) {
    toggleSelected();
    emit('click', e);
}
</script>

<style lang="scss">
.wtg-button {
    align-items: center;
    border-radius: var(--s-radius-s);
    border: 1px solid;
    cursor: pointer;
    display: inline-flex;
    font: var(--s-text-md-strong);
    gap: var(--s-spacing-null);
    justify-content: center;
    padding: var(--s-padding-s);
    position: relative;
    text-decoration: none;
    transition-duration: 0.28s;
    transition-property: transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;

    &:link,
    &:visited,
    &:hover,
    &:active {
        text-decoration: none;
    }

    &.wtg-button:disabled {
        background: var(--s-neutral-bg-disabled);
        border-color: var(--s-neutral-bg-disabled);
        color: var(--s-neutral-txt-disabled);
        cursor: default;
    }

    &:focus-visible {
        outline-offset: 1px;
        outline: 2px solid var(--s-primary-border-default);
        z-index: 1;
    }

    &.wtg-button--fill-available {
        flex: 1 0 auto;
        min-width: 100% !important;
        max-width: none;
    }

    & > .wtg-button__shadow {
        border-radius: var(--s-radius-s);
        bottom: 0;
        box-shadow: var(--s-elevation-button-default);
        content: '';
        left: 0;
        mix-blend-mode: multiply;
        position: absolute;
        right: 0;
        top: 0;
        transition-duration: 0.28s;
        transition-property: box-shadow;
        margin: -1px;
    }

    & > .wtg-button__loader {
        position: absolute;
    }

    &.wtg-button:disabled > .wtg-button__shadow {
        box-shadow: none;
        margin: unset;
    }

    & > .wtg-button__content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        z-index: 1;
        padding: var(--s-padding-null) var(--s-padding-s);
    }

    &.wtg-button--default {
        background: var(--s-neutral-bg-default);
        border-color: var(--s-neutral-border-weak-default);
        color: var(--s-neutral-txt-default);

        &.wtg-button--selected {
            background: var(--s-neutral-bg-weak-active);
        }

        &.wtg-button--primary {
            border-color: var(--s-primary-border-weak-default);
            color: var(--s-primary-txt-default);

            &:not(.wtg-button--selected):not(.wtg-button--disabled):hover:not(:active) {
                background: var(--s-primary-bg-weak-hover);
                border-color: var(--s-primary-border-weak-hover);
                color: var(--s-primary-txt-hover);
            }
            &:not(.wtg-button--selected):not(.wtg-button--disabled) {
                &:active,
                &.wtg-button--active {
                    background: var(--s-neutral-bg-default);
                    border-color: var(--s-primary-border-weak-active);
                    color: var(--s-primary-txt-active);
                }
            }

            &:focus-visible {
                border-color: var(--s-primary-border-weak-active);
                color: var(--s-primary-txt-active);
            }

            &.wtg-button--selected {
                background: var(--s-primary-bg-weak-active);
            }
        }

        &.wtg-button--critical {
            border-color: var(--s-error-border-weak-default);
            color: var(--s-error-txt-default);

            &:not(.wtg-button--selected):not(.wtg-button--disabled):hover:not(:active) {
                background: var(--s-error-bg-weak-hover);
                border-color: var(--s-error-border-weak-hover);
                color: var(--s-error-txt-hover);
            }

            &:not(.wtg-button--selected):not(.wtg-button--disabled) {
                &:active,
                &.wtg-button--active {
                    background: var(--s-neutral-bg-default);
                    border-color: var(--s-error-border-weak-active);
                    color: var(--s-error-txt-active);
                }
            }

            &:focus-visible {
                border-color: var(--s-error-border-weak-active);
                color: var(--s-error-txt-active);
            }

            &.wtg-button--selected {
                background: var(--s-error-bg-weak-active);
            }
        }

        &.wtg-button--success {
            border-color: var(--s-success-border-weak-default);
            color: var(--s-success-txt-default);

            &:not(.wtg-button--selected):not(.wtg-button--disabled):hover:not(:active) {
                background: var(--s-success-bg-weak-hover);
                border-color: var(--s-success-border-weak-hover);
                color: var(--s-success-txt-hover);
            }

            &:not(.wtg-button--selected):not(.wtg-button--disabled) {
                &:active,
                &.wtg-button--active {
                    background: var(--s-neutral-bg-default);
                    border-color: var(--s-success-border-weak-active);
                    color: var(--s-success-txt-active);
                }
            }

            &:focus-visible {
                border-color: var(--s-success-border-weak-active);
                color: var(--s-success-txt-active);
            }

            &.wtg-button--selected {
                background: var(--s-success-bg-weak-active);
            }
        }

        &:not(.wtg-button--selected):not(.wtg-button--disabled):hover:not(:active) {
            background: var(--s-neutral-bg-weak-hover);
            border-color: var(--s-neutral-border-weak-hover);
            color: var(--s-neutral-txt-hover);
        }

        &:not(.wtg-button--selected):not(.wtg-button--disabled) {
            &:active,
            &.wtg-button--active {
                background: var(--s-neutral-bg-default);
                border-color: var(--s-neutral-border-weak-active);
                color: var(--s-neutral-txt-active);
            }
        }

        &.wtg-button--color:not(:active):hover::before {
            background-color: currentColor;
            bottom: 0;
            content: '';
            left: 0;
            opacity: 0.12;
            pointer-events: none;
            position: absolute;
            right: 0;
            top: 0;
        }

        &:not(.wtg-button--disabled).wtg-button:hover > .wtg-button__shadow {
            box-shadow: var(--s-elevation-button-hover);
        }

        &:not(.wtg-button--disabled) {
            &.wtg-button:active,
            &.wtg-button--active:not(:hover) {
                > .wtg-button__shadow {
                    box-shadow: var(--s-elevation-button-pressed);
                }
            }
        }
    }

    &.wtg-button--fill {
        background: var(--s-neutral-bg-weak-default);
        border-color: var(--s-neutral-bg-weak-default);
        color: var(--s-neutral-txt-default);

        &:not(.wtg-button--disabled):hover {
            background: var(--s-neutral-bg-weak-hover);
            border-color: var(--s-neutral-bg-weak-hover);
        }

        &:not(.wtg-button--disabled):active {
            background: var(--s-neutral-bg-weak-active);
            border-color: var(--s-neutral-bg-weak-active);
        }

        &.wtg-button--primary {
            background: var(--s-primary-bg-default);
            border-color: var(--s-primary-border-default);
            color: var(--s-primary-txt-inv-default);

            &:hover {
                background: var(--s-primary-bg-hover);
                border-color: var(--s-primary-border-hover);
                color: var(--s-primary-txt-inv-hover);
            }

            &:active,
            &.wtg-button--active:not(:hover) {
                background: var(--s-primary-bg-active);
                border-color: var(--s-primary-bg-active);
                color: var(--s-primary-txt-inv-hover);
            }
        }

        &.wtg-button--critical {
            background: var(--s-error-bg-default);
            border-color: var(--s-error-border-default);
            color: var(--s-error-txt-inv-default);

            &:hover {
                background: var(--s-error-bg-hover);
                border-color: var(--s-error-border-hover);
                color: var(--s-error-txt-inv-hover);
            }

            &:active,
            &.wtg-button--active:not(:hover) {
                background: var(--s-error-bg-active);
                border-color: var(--s-error-border-active);
                color: var(--s-error-txt-inv-active);
            }
        }

        &.wtg-button--success {
            background: var(--s-success-bg-default);
            border-color: var(--s-success-border-default);
            color: var(--s-success-txt-inv-default);

            &:hover {
                background: var(--s-success-bg-hover);
                border-color: var(--s-success-border-hover);
                color: var(--s-success-txt-inv-hover);
            }

            &:active,
            &.wtg-button--active:not(:hover) {
                background: var(--s-success-bg-active);
                border-color: var(--s-success-bg-active);
                color: var(--s-success-txt-inv-active);
            }
        }

        &.wtg-button--color {
            &:hover::before {
                background-color: var(--s-neutral-txt-default);
                bottom: 0;
                content: '';
                left: 0;
                opacity: 0.12;
                position: absolute;
                right: 0;
                top: 0;
            }

            &:active::before,
            &.wtg-button--active:not(:hover)::before {
                background-color: var(--s-neutral-txt-default);
                bottom: 0;
                content: '';
                left: 0;
                opacity: 0.24;
                position: absolute;
                right: 0;
                top: 0;
            }
        }

        &:not(.wtg-button--disabled).wtg-button:hover > .wtg-button__shadow {
            box-shadow: var(--s-elevation-button-hover);
        }

        &:not(.wtg-button--disabled).wtg-button {
            &:active,
            &.wtg-button--active:not(:hover) {
                > .wtg-button__shadow {
                    box-shadow: var(--s-elevation-button-pressed);
                }
            }
        }
    }

    &.wtg-button--ghost {
        background: transparent;
        border-color: transparent;
        color: inherit;

        &.wtg-button--selected {
            background: var(--s-neutral-bg-weak-active);
        }

        &.wtg-button--primary {
            color: var(--s-primary-txt-default);

            &:not(.wtg-button--selected):hover {
                background: var(--s-primary-bg-weak-hover);
                color: var(--s-primary-txt-hover);
            }

            &:active,
            &.wtg-button--active:not(:hover) {
                background: var(--s-primary-bg-weak-active);
                color: var(--s-primary-txt-active);
            }

            &:focus-visible {
                color: var(--s-primary-txt-active);
            }

            &.wtg-button--selected {
                background: var(--s-primary-bg-weak-active);
            }
        }

        &.wtg-button--critical {
            color: var(--s-error-txt-default);

            &:not(.wtg-button--selected):hover {
                background: var(--s-error-bg-weak-hover);
                color: var(--s-error-txt-hover);
            }

            &:active,
            &.wtg-button--active:not(:hover) {
                background: var(--s-error-bg-weak-active);
                color: var(--s-error-txt-active);
            }

            &:focus-visible {
                color: var(--s-error-txt-active);
            }

            &.wtg-button--selected {
                background: var(--s-error-bg-weak-active);
            }
        }

        &.wtg-button--success {
            color: var(--s-success-txt-default);

            &:not(.wtg-button--selected):hover {
                background: var(--s-success-bg-weak-hover);
                color: var(--s-success-txt-hover);
            }

            &:active,
            &.wtg-button--active:not(:hover) {
                background: var(--s-success-bg-weak-active);
                color: var(--s-success-txt-active);
            }

            &:focus-visible {
                color: var(--s-success-txt-active);
            }

            &.wtg-button--selected {
                background: var(--s-success-bg-weak-active);
            }
        }

        & > .wtg-button__shadow {
            box-shadow: none;
            margin: unset;
        }

        &:not(.wtg-button--selected):not(.wtg-button--disabled):hover:before {
            border-radius: inherit;
            background-color: currentColor;
            bottom: 0;
            content: '';
            left: 0;
            opacity: 0.12;
            pointer-events: none;
            position: absolute;
            right: 0;
            top: 0;
        }

        &:not(.wtg-button--selected):not(.wtg-button--disabled) {
            &:active::before,
            &.wtg-button--active:not(:hover)::before {
                border-radius: inherit;
                background-color: currentColor;
                bottom: 0;
                content: '';
                left: 0;
                opacity: 0.24;
                pointer-events: none;
                position: absolute;
                right: 0;
                top: 0;
            }
        }
    }

    &.wtg-button--xs {
        padding: var(--s-padding-xs) var(--s-padding-xs);
    }

    &.wtg-button--s {
        padding: var(--s-padding-s) var(--s-padding-s);
    }

    &.wtg-button--m {
        padding: var(--s-padding-m) var(--s-padding-m);
    }

    &.wtg-button--l {
        padding: var(--s-padding-l) var(--s-padding-l);
    }

    &.wtg-button--xl {
        padding: var(--s-padding-xl) var(--s-padding-xl);
    }

    &.wtg-button--xxl {
        padding: var(--s-padding-xxl) var(--s-padding-xxl);
    }

    &.wtg-button--loading {
        pointer-events: none;

        .wtg-button__content {
            opacity: 0;
        }

        .wtg-button__icon {
            opacity: 0;
        }
    }
}
</style>
