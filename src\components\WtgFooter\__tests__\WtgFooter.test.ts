import { WtgApp } from '@components/WtgApp';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { h } from 'vue';
import { VFooter } from 'vuetify/components/VFooter';
import { WtgFooter } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgFooter', () => {
    test('its name is WtgFooter', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgFooter');
    });

    test('it renders a VFooter component', () => {
        const wrapper = mountComponent();
        const spacer = wrapper.findComponent(VFooter);
        expect(spacer.exists()).toBe(true);
    });

    test('it passes all its properties to the VFooter component', () => {
        const wrapper = mountComponent({
            props: {
                app: true,
                order: 1,
            },
        });
        const props = wrapper.findComponent(VFooter).props();
        expect(props.app).toBe(true);
        expect(props.order).toBe(1);
    });

    test('it passes the default slot content to the VFooter component', () => {
        const wrapper = mountComponent();
        expect(wrapper.text()).toBe('Some Text');
    });

    function mountComponent({ props = {} } = {}) {
        return mount(WtgApp, {
            slots: {
                default: () => h(WtgFooter, { ...props }, () => 'Some Text'),
            },
            global: {
                plugins: [wtgUi],
            },
        }).findComponent(WtgFooter);
    }
});
