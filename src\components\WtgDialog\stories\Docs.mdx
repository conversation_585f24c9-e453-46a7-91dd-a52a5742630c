import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import image from '../../../storybook/assets/component-button-decisiontree.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgDialog from './WtgDialog.stories.ts';

<Meta of={WtgDialog} />

<div className="component-header">
    <h1>Dialog</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable} />[
                Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=224-25247&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

<div className="banner-warning">

    <div>
        <h4>⚠️ Please note</h4>
        <p>Dialog is the base component that temporarily halts other interactions to display important messages or prompts in Supply applications. However, this role is typically handled by the [Modal](../?path=/docs/components-modal--docs) component, so it is recommended to use Modal instead.</p>
    </div>

</div>

## Overview

In this basic example we use the activator slot to render a button that is used to open the dialog.

<Canvas className="canvas-preview" of={WtgDialog.Default} />
<Controls of={WtgDialog.Default} sort={'alpha'} />

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
