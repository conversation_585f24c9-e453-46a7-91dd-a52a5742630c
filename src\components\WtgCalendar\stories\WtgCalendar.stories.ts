import { Meta, StoryObj } from '@storybook/vue3';
import WtgCalendar from '@components/WtgCalendar/WtgCalendar.vue';

type Story = StoryObj<typeof WtgCalendar>;
export default {
    title: 'Components/Calendar',
    component: WtgCalendar,
    parameters: {
        docs: {
            description: {
                component: 'The calendar component is used to display information in a daily, weekly or monthly view.',
            },
        },
    },
    decorators: [
        () => ({
            template: `
                <div style="max-width:400px">
                    <story/>
                </div>`,
        }),
    ],
} as Meta;

export const Default: Story = {
    render: (args: any) => ({
        components: { WtgCalendar },
        setup() {
            return { args };
        },
        template: '<WtgCalendar v-bind="args" />',
    }),
};
