# Prefer String Literal Types over String Enums

## Status
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

In TypeScript, both string literal types and string enums can be used to enforce type safety for strings. However, the team needs to standardize the approach to reduce confusion, promote better practices, and streamline development.

## Decision

## Status
- [ ] Proposed
- [x] Accepted
- [ ] Rejected
- [ ] Deprecated
- [ ] Superseded

Supply will prefer string literal types over string enums for the following reasons:

1. Simplicity.

String literal types are simpler to use and understand, as they are just plain strings with no extra syntax or abstraction. This simplicity reduces cognitive load for developers.

2. No overhead in code generation

String literal types are erased during TypeScript's compilation process, resulting in no additional JavaScript code. In contrast, enums are compiled into objects in JavaScript, which increases bundle size and runtime overhead.

3. Avoiding redundancy

String enums require the developer to duplicate information (e.g., the name of the enum key and its value) which can introduce inconsistencies or mistakes

4. Interoperability

String literal types integrate more seamlessly with third-party libraries and APIs, which often work directly with plain strings. Using string enums may require additional conversions (e.g., mapping enum values to string values).

5. Consistency with Industry Standards

Many TypeScript best practices recommend using string literal types over enums, aligning with modern patterns in TypeScript development.

## Consequences

By preferring string literal types:

1. Developers will produce leaner and more efficient code.
2. The team will avoid unnecessary complexity and verbosity in the codebase.

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.
