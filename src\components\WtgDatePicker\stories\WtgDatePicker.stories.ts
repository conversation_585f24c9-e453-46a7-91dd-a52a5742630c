import WtgPanel from '@components/WtgPanel';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import WtgDatePicker from '..';

type Story = StoryObj<typeof WtgDatePicker>;
const meta: Meta<typeof WtgDatePicker> = {
    title: 'Components/Date Picker',
    component: WtgDatePicker,
    parameters: {
        docs: {
            description: {
                component: 'Date picker allows users to select a date.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=383-43226',
        },
        controls: {
            exclude: [
                'disabled',
                'displayOnly',
                'label',
                'loading',
                'readonly',
                'required',
                'restricted',
                'sentiment',
                'messages',
            ],
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgDatePicker, WtgPanel },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `<WtgPanel class="d-inline-flex">
                    <WtgDatePicker
                        v-bind="args"
                        @update:model-value="updateModel"
                        @change="changeAction"
                        @input="inputAction"
                        @focus="focusAction"
                        @blur="blurAction">
                      </WtgDatePicker>
                    </WtgPanel>`,
    }),
    decorators: [
        () => ({
            template: `
                <div>
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        modelValue: '08-Jan-24',
    },
};
