<template>
    <div>
        <WtgProgressCircular v-if="loading" class="wtg-chart-loading" indeterminate />
        <canvas ref="canvas" :style="computedChartStyle" />
    </div>
</template>

<script setup lang="ts">
import WtgProgressCircular from '@components/WtgProgressCircular';
import { useWtgUi } from '@composables/global';
import { useTheme } from '@composables/theme';
import {
    BarController,
    BarElement,
    CategoryScale,
    Chart,
    ChartData,
    ChartDataset,
    ChartOptions,
    ChartType,
    Filler,
    Legend,
    LinearScale,
    Title,
    Tooltip,
} from 'chart.js';
import { PropType, computed, onMounted, onUnmounted, ref, watch } from 'vue';

const props = defineProps({
    type: {
        type: String as PropType<ChartType>,
        default: 'bar',
    },
    data: {
        type: Object as PropType<ChartData>,
        default: (): ChartData => {
            return {
                datasets: [],
            };
        },
    },
    options: {
        type: Object as PropType<ChartOptions>,
        default: (): ChartOptions => {
            return {};
        },
    },
    loading: {
        type: Boolean,
        default: false,
    },
});

Chart.register(Filler, <PERSON>, Title, Tooltip, CategoryScale, LinearScale, BarController, BarElement);

const { theme } = useTheme();
const wtgUi = useWtgUi();

const nonReactive = {} as { chart: Chart };
const canvas = ref<HTMLCanvasElement | null>(null);

const font = { family: 'Inter' };
const applyDefaults = (options: Record<string, any>, defaults: Record<string, any> = {}): Record<string, any> => {
    const result = { ...defaults };
    for (const key in options) {
        let option = options[key];
        if (option instanceof Object && !Array.isArray(option) && !(option instanceof Function)) {
            option = applyDefaults(option, defaults[key]);
        }
        result[key] = option;
    }
    return result;
};

const computedColors = computed(() => {
    return wtgUi.colors.controls.chart;
});

const computedDatasets = computed(() => {
    return props.data?.datasets ?? [];
});

const computedDefaultDatasetOptions = computed(() => {
    return {
        backgroundColor: computedColors.value.background,
        borderColor: computedColors.value.border,
    };
});

const computedChartDatasets = computed(() => {
    const result: ChartDataset[] = [];
    for (const dataset of computedDatasets.value) {
        result.push(applyDefaults(dataset, computedDefaultDatasetOptions.value) as ChartDataset);
    }
    return result;
});

const computedChartData = computed(() => {
    const data = props.data ?? {};
    return { ...data, datasets: computedChartDatasets.value };
});

const computedDefaultOptions = computed(() => {
    return {
        plugins: {
            title: {
                color: computedColors.value.text,
                font,
            },
            legend: {
                labels: {
                    color: computedColors.value.text,
                    font,
                },
            },
        },
    };
});

const computedDefaultScales = computed(() => {
    switch (props.type) {
        case 'bar':
        case 'bubble':
        case 'line':
        case 'scatter': {
            return ['x', 'y'];
        }
        case 'polarArea': {
            return ['r'];
        }
    }
    return [];
});

const computedDefaultScaleOptions = computed(() => {
    return {
        grid: {
            color: computedColors.value.grid,
        },
        ticks: {
            backdropColor: computedColors.value.backdrop,
            color: computedColors.value.ticks,
            font,
        },
    };
});

const computedChartOptions = computed(() => {
    const result = applyDefaults(props.options, computedDefaultOptions.value);

    for (const scale of computedDefaultScales.value) {
        if (result.scales === undefined) {
            result.scales = {};
        }
        if (result.scales[scale] === undefined) {
            result.scales[scale] = {};
        }
    }

    for (const scale in result.scales) {
        result.scales[scale] = applyDefaults(result.scales[scale], computedDefaultScaleOptions.value);
    }
    return result;
});

const computedChartStyle = computed(() => {
    return {
        visibility: props.loading ? 'hidden' : '',
        maxWidth: '100%',
    } as Record<string, any>;
});

watch(
    () => props.options,
    () => {
        nonReactive.chart.options = computedChartOptions.value;
        nonReactive.chart.update();
    },
    { deep: true }
);

watch(
    () => props.data,
    () => {
        nonReactive.chart.data = computedChartData.value;
        nonReactive.chart.update();
    },
    { deep: true }
);

watch(
    () => theme,
    () => {
        nonReactive.chart.data = computedChartData.value;
        nonReactive.chart.options = computedChartOptions.value;
        nonReactive.chart.update();
    },
    { deep: true }
);

watch(
    () => wtgUi.appearance,
    () => {
        nonReactive.chart.data = computedChartData.value;
        nonReactive.chart.options = computedChartOptions.value;
        nonReactive.chart.update();
    },
    { deep: true }
);

onMounted(() => {
    if (canvas.value) {
        const context = canvas.value.getContext('2d');
        if (context) {
            nonReactive.chart = new Chart(context, {
                type: props.type as ChartType,
                data: computedChartData.value,
                options: computedChartOptions.value,
            });
        }
    }
});

onUnmounted(() => {
    nonReactive.chart?.destroy();
});
</script>
<style lang="scss">
.wtg-chart-loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
</style>
