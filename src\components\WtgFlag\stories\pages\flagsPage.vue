<template>
    <VTextField
        v-model="filter"
        aria-label="Search flags"
        variant="solo"
        placeholder="Search flags..."
        density="comfortable"
        autofocus
        color="success"
    ></VTextField>

    <div class="flags-layout">
        <template v-for="flag in computedMatchingFlags" :key="flag">
            <div class="flags-item" @click="onClickFlag(flag)">
                <p class="flags-item-code text-center">{{ flag.code }}</p>
                <WtgFlag size="big" :elevation="4" :country="flag.code"></WtgFlag>
                <p class="flags-item-text text-center">{{ flag.name }}</p>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import WtgFlag from '@components/WtgFlag/WtgFlag.vue';
import { computed, ref, watch } from 'vue';
import { VTextField } from 'vuetify/components/VTextField';
import { SupportedFlags } from './SupportedFlags';

const filter = ref('');
const delayedFilter = ref('');
const delayFilterTimeout = ref(0);
const flags = ref(SupportedFlags);

watch(filter, (newValue) => {
    if (delayFilterTimeout.value) {
        window.clearTimeout(delayFilterTimeout.value);
    }

    delayFilterTimeout.value = window.setTimeout(() => {
        delayedFilter.value = newValue ? newValue.toUpperCase() : '';
        delayFilterTimeout.value = 0;
    }, 300);
});

const computedMatchingFlags = computed(() => {
    if (!delayedFilter.value) {
        return flags.value;
    }
    const matchedFlags = flags.value.filter((flag) => {
        return (
            flag.name.toUpperCase().includes(delayedFilter.value) ||
            flag.code.toUpperCase().includes(delayedFilter.value)
        );
    });
    return matchedFlags;
});

const onClickFlag = (flag: any) => {
    navigator.clipboard.writeText(`${flag.code}`);
};
</script>

<style lang="scss">
.v-field__input:focus {
    border: 1px solid var(--s-primary-border-default);
}
.flags-layout {
    display: grid;
    grid-gap: 5px;
    grid-template-columns: repeat(auto-fill, 150px);
    justify-content: space-around;
    vertical-align: middle;

    .flags-item {
        display: flex;
        align-items: center;
        flex-direction: column;
        height: 150px;

        .flags-item-text {
            font-size: 12px;
            font-weight: bold;
            padding-top: 8px;
        }

        .flags-item-code {
            font-size: 12px;
            font-weight: lighter;
            padding-top: 8px;
        }
    }

    .flags-item:hover {
        background: var(--s-neutral-bg-weak-hover);
        border-color: var(--s-neutral-border-weak-hover);
        color: var(--s-neutral-txt-hover);
        cursor: pointer;
    }

    .flags-item:active {
        background: var(--s-neutral-bg-default);
        border-color: var(--s-neutral-border-weak-active);
        color: var(--s-neutral-txt-active);
    }
}
</style>
