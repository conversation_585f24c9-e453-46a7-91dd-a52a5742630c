<template>
    <RecentInlineMenu
        v-if="isTabletOrMobile"
        :items="items"
        :recents-caption="recentsCaption"
        :favorites-caption="favoritesCaption"
        :caption-close="captionClose"
        :on-dialog-open="onDialogOpen"
        :on-click="onClick"
        @item-click="emit('item-click')"
    />
    <WtgPopover
        v-else
        v-model="menu"
        :close-on-content-click="false"
        location="right bottom"
        nudge-right="var(--s-padding-xl)"
        min-width="240px"
    >
        <template #activator="args">
            <WtgListItem
                role="menuitem"
                :aria-label="favoritesCaption"
                aria-haspopup="menu"
                :class="!collapsed ? '' : 'd-flex justify-center'"
                v-bind="args.props"
                leading-icon="s-icon-star-empty"
                trailing-icon="s-icon-caret-right"
                :collapsed="collapsed"
                @click="() => onClick?.()"
            >
                {{ favoritesCaption }}
            </WtgListItem>
        </template>
        <RecentList :items="items" @item-click="onItemClick" />
    </WtgPopover>
</template>

<script setup lang="ts">
import { WtgListItem } from '@components/WtgList';
import WtgPopover from '@components/WtgPopover';
import { useFramework } from '@composables/framework';
import { PropType, ref } from 'vue';
import RecentInlineMenu from './RecentInlineMenu.vue';
import RecentList from './RecentList.vue';
import { Items, WtgRecentItem } from './types';

const { isTabletOrMobile } = useFramework();

const emit = defineEmits<{
    'item-click': [item?: WtgRecentItem];
}>();

const menu = ref(false);

defineProps({
    items: {
        type: Object as PropType<Items>,
        default: () => ({
            favorites: [],
            recents: [],
        }),
    },
    favoritesCaption: {
        type: String,
        default: 'Favorites',
    },
    recentsCaption: {
        type: String,
        default: 'Recent',
    },
    collapsed: {
        type: Boolean,
        default: false,
    },
    captionClose: {
        type: String,
        default: 'Close',
    },
    onClick: {
        type: Function,
        default: undefined,
    },
    onDialogOpen: {
        type: Function,
        default: undefined,
    },
});

function onItemClick(item: WtgRecentItem) {
    menu.value = false;
    emit('item-click', item);
}
</script>
