import { WtgHyperlink } from '../';
import WtgUi from '../../../WtgUi';
import { mount, enableAutoUnmount } from '@vue/test-utils';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgHyperlink', () => {
    test('it renders an <a> tag', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('A');
    });

    test('it passes its properties to the <a> tag>', () => {
        const wrapper = mountComponent({
            propsData: {
                href: 'myhref',
                target: '_self',
            },
        });
        const anchor = wrapper.element as HTMLAnchorElement;
        expect(anchor.href).toContain('myhref');
        expect(anchor.target).toBe('_self');
    });

    test('it defaults to opening the href is a new window', () => {
        const wrapper = mountComponent();
        const anchor = wrapper.element as HTMLAnchorElement;
        expect(anchor.target).toBe('_blank');
    });

    test('it emits a click event when the <a> is clicked', async () => {
        const wrapper = mountComponent();
        const link = wrapper.find('a');
        expect(wrapper.emitted('click')).toBeUndefined();
        await link.trigger('click');
        expect(wrapper.emitted('click')!.length).toBe(1);
        expect(wrapper.emitted('click')![0][0]).toBeInstanceOf(MouseEvent);
    });

    test('it passes the default slot to the <a>', () => {
        const wrapper = mountComponent();
        expect(wrapper.text()).toBe('My Link');
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgHyperlink, {
            wtgUi,
            propsData,
            slots: {
                default: 'My Link',
            },
        });
    }
});
