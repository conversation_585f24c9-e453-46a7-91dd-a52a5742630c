<template>
    <VApp class="wtg-app">
        <slot />
    </VApp>
</template>

<script setup lang="ts">
import { VApp } from 'vuetify/components/VApp';
</script>

<style lang="scss">
.wtg-app {
    background-color: var(--s-neutral-canvas-default);
    color: var(--s-neutral-txt-default);
}
.v-application,
.v-overlay-container {
    .theme--light.v-label,
    .theme--dark.v-label,
    .theme--light.v-data-table {
        color: var(--s-neutral-txt-default);
    }

    /* When embedded in non-material GLOW we need to fit in the bounds of the framework */
    &.glow-vue-embedded {
        height: 100%;
        .v-application--wrap {
            min-height: 100%;
        }
    }
    /* When displaying GLOW 1 content ensure we display the correct font */
    .g-anchored,
    .g-modals-container {
        font-family: 'Noto Sans', Microsoft YaHei, PingFang SC, 'Noto Sans KR', Arial, Sans-Serif;
        font-size: 13px;
        line-height: 1.42857143;
    }
    /* Reset to match Vuetify defaults. Only necessary while embedded in a GLOW runtime that contains bootstrap.css */
    ul,
    ol {
        &.list-unstyled {
            padding-left: 0px;
        }
    }
    hr {
        &.v-divider {
            width: unset;
            margin: 0;
        }
    }
    button[disabled] {
        color: unset;
        background-color: unset;
        border-color: unset;
        cursor: unset;
    }
    fieldset {
        margin: 0;
    }
    /* Necessary as long as GLOW runtime is adding pointer-events: none through g-modal-open */
    .v-menu__content {
        pointer-events: auto;
    }
    .mx-1px {
        margin-right: 1px;
        margin-left: 1px;
    }
    .wtg-border-top-1 {
        border-top-width: 1px;
        border-top-style: solid;
    }
    .wtg-border-bottom-1 {
        border-bottom-width: 1px;
        border-bottom-style: solid;
    }
    .wtg-border-radius-0 {
        border-radius: 0px !important;
    }
    .wtg-br-1 {
        border-radius: 4px !important;
    }
    .wtg-cursor-pointer {
        cursor: pointer;
    }
    .wtg-h-100 {
        height: 100%;
    }
    .wtg-w-100 {
        width: 100%;
    }
    .wtg-fit-to-height {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        min-height: 0px;
        min-width: 0px;
    }

    .wtg-fit-to-height-table {
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        width: 100%;
    }
    .wtg-fit-to-height-table .v-data-table__wrapper {
        flex-grow: 1;
    }
    .wtg-notification-highlight:not(.wtg-search-data-table__cell--error):not(.wtg-search-data-table__cell--warning):not(
            .wtg-search-data-table__cell--info
        ) {
        position: relative;
        &::before {
            content: '';
            background-color: currentColor;
            bottom: 0px;
            left: 0;
            opacity: 0.12;
            pointer-events: none;
            position: absolute;
            right: 0px;
            top: 0px;
        }
    }
    /* Needed to ensure that certain GLOW elements appear under the app bar & footer */
    .kgTopPanel {
        z-index: 3;
    }
    .input-group-addon {
        z-index: 3;
    }
    .g-anchored {
        z-index: 3;
    }
    // Required as this appears to be a bug in vuetify's code as normal is fine but opposite does not have the style
    .v-timeline--align-top .v-timeline-item__opposite {
        align-self: auto;
    }

    // Necessary until we globally disable ripple
    .v-ripple__container {
        display: none;
    }
    // Necessary until we stop using GLOW's _input.less
    .wtg-input input {
        color: var(--s-neutral-txt-default);
        padding-bottom: inherit;
    }
    // Necessary until we stop using GLOW's _input.less
    .wtg-checkbox label,
    .wtg-switch label,
    .wtg-radio label,
    .wtg-input label {
        overflow: unset;
        text-overflow: unset;
        white-space: unset;
        margin-bottom: unset;
        font-weight: unset;
        color: unset;
    }
}
</style>
