import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { Canvas, ColorItem, ColorPalette, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgChip from './WtgChip.stories.ts';

<Meta of={WtgChip} />

<div className="component-header">
    <h1>Chip</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=84-16462&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img> With pending updates
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style={{ width: '33%' }}>
                [Project
                PRJ00052167](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/06b101cb-68de-43eb-932a-569ebac5fc49?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>
                Chips will soon be replacing the current filter chip pattern on data table. Content below reflects this
                change.
            </td>
        </tr>
    </tbody>
</table>

## Overview

Chips are compact, interactive elements typically used to display applied or default filters on a data table.

## API

<Canvas className="canvas-preview" of={WtgChip.Default} />
<Controls of={WtgChip.Default} sort={'alpha'} />

## How to use

### Summary table

The chip has similar functionality with other components. Use the information below to help decide which to use.

<table width="100%" className="component-summary-table">
    <thead>
        <tr>
            <th></th>
            <th>[Tag](/docs/components-tag--docs)</th>
            <th>[Status](/docs/components-status--docs)</th>
            <th>[Chip](/docs/components-chip--docs)</th>
           
        </tr>
    </thead>
    <tbody>
        <tr>
        <td>
                <p>**Description**</p> 
            </td>
            <td>
                <p>Compact labels used to show selected items in multiselect fields, or to highlight attributes like categories, references, or types.</p>
            </td>
            <td>
                <p>A clear visual indicator showing the current state of an item, process, or entity.</p>
            </td>
            <td>
                <p>Compact, interactive elements typically used to display applied or default filters on a data table.</p>
            </td>
           
        </tr>

         <tr>
        <td>
                <p>**Primary purpose**</p>
            </td>
            <td>
                <p>Show selected items or content attributes.</p>
            </td>
            <td>
                <p>
                Quickly communicate the state of an item or process.
                </p>
            </td>
            <td>
                <p>Show and manage applied filters.</p>
            </td>

        </tr>

         <tr>
        <td>
                <p>**Interactive**</p>
            </td>
            <td>
                <p>Limited (see dismissible prop)</p>
            </td>
            <td>
                <p>Non editable = No</p>
                <p>Editable = Yes</p>
            </td>
            <td>
                <p>Yes</p>
            </td>


        </tr>
         <tr>
        <td>
                <p>**Example**</p>
            </td>
            <td>
                <p> Selected items in multiselect field or identifying workflows in PAVE.</p>
            </td>
            <td>
                <p>Indicating the status of a delivery like "Rejected" or "Delivered"</p>
            </td>
            <td>
                <p> Applied filters on a data table.</p>
            </td>
        </tr>


    </tbody>

</table>

## Do's and dont's

### ✅ Do

-   Use chips for filtering roles in the UI.

### ❌ Don't

-   Use chips to highlight attributes (see [tag](/docs/components-tag--docs) instead) or show the state of an item, process, or entity (see [status](/docs/components-status--docs) instead)

## Variants

<Canvas className="canvas-preview" of={WtgChip.Default} />
<Canvas className="canvas-preview" of={WtgChip.Refinement} />
<Canvas className="canvas-preview" of={WtgChip.Dropdown} />

## Related components

-   [Tag](/docs/components-tag--docs)
-   [Status](/docs/components-status--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
