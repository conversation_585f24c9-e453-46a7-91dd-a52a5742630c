import WtgChart from '@components/WtgChart/WtgChart.vue';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { ArcElement, Chart, ChartData, ChartOptions, PieController } from 'chart.js';
import { defineComponent, h, PropType, VNode } from 'vue';

Chart.register(ArcElement, PieController);

export default defineComponent({
    name: 'WtgPieChart',
    props: {
        data: {
            type: Object as PropType<ChartData>,
            default: (): ChartData => {
                return {
                    datasets: [],
                };
            },
        },
        options: {
            type: Object as PropType<ChartOptions>,
            default: (): ChartOptions => {
                return {};
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    setup(props) {
        useLayoutGridColumn(props);
    },
    render(): VNode {
        return h(Wtg<PERSON>hart, {
            type: 'pie',
            data: this.data,
            options: this.options,
            loading: this.loading,
        });
    },
});
