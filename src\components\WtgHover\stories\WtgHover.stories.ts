import WtgApp from '@components/WtgApp';
import WtgHover from '@components/WtgHover/WtgHover.vue';
import WtgPanel from '@components/WtgPanel';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgHover>;
const meta: Meta<typeof WtgHover> = {
    title: 'Utilities/Hover',
    component: WtgHover,
    parameters: {
        docs: {
            description: {
                component:
                    'Hover is a renderless component that uses the default slot to provide scoped access to its internal model, as well as mouse event listeners to modify it. To explicitly control the internal state, use the model-value property.',
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: { WtgApp, WtgHover, WtgPanel },
        setup: () => ({ args }),
        methods: {
            changeAction: action('change'),
        },
        template: `<WtgHover v-bind="args"><template v-slot:default="{ isHovering, props }"><WtgPanel v-bind="props" :class="isHovering ? 'bg-red' : undefined" caption="Hover over me"><span>Panel background color changes when mouse is over the panel</span></WtgPanel></template></WtgHover>`,
    }),
    decorators: [
        () => ({
            components: { WtgApp },
            template:
                '<WtgApp class="content-embedded-app"><div class="wtg-h-100 d-flex align-center justify-center"><story /></div></WtgApp>',
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {},
};
