describe('Dummy Test', () => {
    test('its runs a test', () => {
        expect(true).toBe(true);
    });
});

// import { createLocalVue, mount } from '@vue/test-utils';
// import Vue, { ref } from 'vue';
// import WtgMaterialUi from '../../../../../../WtgMaterialUi';
// import { currentTaskInjectionKey } from '../../../../composable/currentTask';
// import { WtgFrameworkNotification, WtgFrameworkTask } from '../../../../types';
// import NotificationsButton from '../NotificationsButton.vue';

// Vue.use(WtgMaterialUi);

// const localVue = createLocalVue();
// const wtgMaterialUi = new WtgMaterialUi();

// describe('notifications-button', () => {
//     const task = ref<WtgFrameworkTask | undefined>();

//     beforeEach(() => {
//         task.value = new WtgFrameworkTask();
//     });

//     test('it is called NotificationsButton', () => {
//         const wrapper = mountComponent();
//         expect(wrapper.vm.$options.name).toBe('NotificationsButton');
//     });

//     test("when the task doesn't have notifications it does NOT show a notification button", () => {
//         const wrapper = mountComponent({ propsData: { task, showNotifications: true } });
//         const btn = wrapper.findComponent({ name: 'WtgBtn' });
//         expect(btn.exists()).toBe(false);
//     });

//     test('when the task has notifications it shows a notification button', () => {
//         task.value!.notifications.push(new WtgFrameworkNotification('ID-1', 'property'));
//         const wrapper = mountComponent({ propsData: { task, showNotifications: true } });
//         const btn = wrapper.findComponent({ name: 'WtgBtn' });
//         expect(btn.isVisible()).toBe(true);
//     });

//     test('when the task is undefined it does NOT show a notification button', () => {
//         task.value = undefined;
//         const wrapper = mountComponent({ propsData: { showNotifications: true } });
//         const btn = wrapper.findComponent({ name: 'WtgBtn' });
//         expect(btn.exists()).toBe(false);
//     });

//     function mountComponent({ propsData = {} } = {}) {
//         return mount<any>(NotificationsButton, {
//             localVue,
//             wtgMaterialUi,
//             propsData,
//             provide: {
//                 [currentTaskInjectionKey]: task,
//             },
//         });
//     }
// });
