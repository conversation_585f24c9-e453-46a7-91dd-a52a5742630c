<template>
    <WtgApp>
        <Framework><slot /></Framework>
    </WtgApp>
</template>

<script setup lang="ts">
import WtgApp from '@components/WtgApp';
import { setApplication } from '@composables/application';
import { onMounted, PropType, reactive, watch } from 'vue';
import { $wtgUi } from '../../../WtgUi';
import { ThemeOptions } from '../../../theme';
import {
    WtgFramework,
    WtgFrameworkAbout,
    WtgFrameworkAriaLabels,
    WtgFrameworkCaptions,
    WtgFrameworkHelpItem,
    WtgFrameworkMenuItem,
    WtgFrameworkPageHelp,
    WtgFrameworkPortalSwitcherMenuItem,
    WtgFrameworkRecentItem,
    WtgFrameworkSearchHandler,
    WtgFrameworkSearchProvider,
    WtgFrameworkTheme,
    WtgFrameworkUser,
} from '../types';
import { Framework } from './framework';

const application = reactive(new WtgFramework());
setApplication(application);

const props = defineProps({
    about: { type: Object as PropType<Partial<WtgFrameworkAbout>>, default: (): {} => ({}) },
    ariaLabels: { type: Object as PropType<Partial<WtgFrameworkAriaLabels>>, default: (): {} => ({}) },
    captions: { type: Object as PropType<Partial<WtgFrameworkCaptions>>, default: (): {} => ({}) },
    favorites: { type: Array as PropType<WtgFrameworkRecentItem[]>, default: (): [] => [] },
    helpItems: { type: Array as PropType<WtgFrameworkHelpItem[]>, default: (): [] => [] },
    hideAppBar: { type: Boolean, default: false },
    hideBackButton: { type: Boolean, default: false },
    hideFooter: { type: Boolean, default: false },
    hrefHome: { type: String, default: '' },
    loading: { type: Boolean, default: false },
    logoDark: { type: String, default: '' },
    logoLight: { type: String, default: '' },
    navMenu: { type: Array as PropType<WtgFrameworkMenuItem[]>, default: (): [] => [] },
    newMenu: { type: Array as PropType<WtgFrameworkMenuItem[]>, default: (): [] => [] },
    pageHelp: { type: Object as PropType<WtgFrameworkPageHelp>, default: undefined },
    portalSwitcherItems: { type: Array as PropType<WtgFrameworkPortalSwitcherMenuItem[]>, default: (): [] => [] },
    recentItems: { type: Array as PropType<WtgFrameworkRecentItem[]>, default: (): [] => [] },
    searchProvider: { type: Object as PropType<WtgFrameworkSearchProvider> | undefined, default: undefined },
    themes: { type: Array as PropType<WtgFrameworkTheme[]>, default: (): [] => [] },
    themeConfigurations: { type: Array as PropType<WtgFrameworkTheme[]>, default: (): [] => [] },
    title: { type: String, default: '' },
    user: { type: Object as PropType<Partial<WtgFrameworkUser>>, default: (): {} => ({}) },
    clickFavoritesHandler: { type: Function, default: () => null },
});

watch(
    () => props.about,
    (newValue) => application.applyAbout(newValue),
    { immediate: true }
);

watch(
    () => props.ariaLabels,
    (newValue) => application.applyAriaLabels(newValue),
    { immediate: true }
);

watch(
    () => props.captions,
    (newValue) => application.applyCaptions(newValue),
    { immediate: true }
);

watch(
    () => props.favorites,
    (newValue) => (application.favorites = newValue),
    { immediate: true }
);

watch(
    () => props.helpItems,
    (newValue) => (application.helpItems = newValue),
    { immediate: true }
);

watch(
    () => props.hideAppBar,
    (newValue) => (application.hideAppBar = newValue),
    { immediate: true }
);

watch(
    () => props.hideBackButton,
    (newValue) => (application.hideBackButton = newValue),
    { immediate: true }
);

watch(
    () => props.hideFooter,
    (newValue) => (application.hideFooter = newValue),
    { immediate: true }
);

watch(
    () => props.hrefHome,
    (newValue) => (application.href = newValue),
    { immediate: true }
);

watch(
    () => props.logoDark,
    (newValue) => (application.logoDark = newValue),
    { immediate: true }
);

watch(
    () => props.logoLight,
    (newValue) => (application.logoLight = newValue),
    { immediate: true }
);

watch(
    () => props.loading,
    (newValue) => (application.loading = newValue),
    { immediate: true }
);

watch(
    () => props.navMenu,
    (newValue) => (application.menu = newValue),
    { immediate: true }
);

watch(
    () => props.newMenu,
    (newValue) => (application.entityCreationMenu = newValue),
    { immediate: true }
);

watch(
    () => props.pageHelp,
    (newValue) => (application.pageHelp = newValue),
    { immediate: true }
);

watch(
    () => props.portalSwitcherItems,
    (newValue) => (application.portalSwitcherItems = newValue),
    { immediate: true }
);

watch(
    () => props.recentItems,
    (newValue) => (application.recentItems = newValue),
    { immediate: true }
);

watch(
    () => props.searchProvider,
    (newValue) => {
        application.searchProvider = newValue;
        application.searchHandler = new WtgFrameworkSearchHandler(newValue);
    },
    { immediate: true }
);

watch(
    () => props.themes,
    (newValue) => (application.themes = newValue),
    { immediate: true }
);

watch(
    () => props.themeConfigurations,
    (newValue) => (application.themeConfigurations = newValue),
    { immediate: true }
);

watch(
    () => application.themeOptions,
    (newValue) => {
        $wtgUi?.installTheme(newValue as ThemeOptions);
    },
    { immediate: true }
);

watch(
    () => props.title,
    (newValue) => (application.title = newValue),
    { immediate: true }
);

watch(
    () => props.user,
    (newValue) => application.applyUser(newValue),
    { immediate: true, deep: true }
);

onMounted(() => (application.clickFavoritesHandler = props.clickFavoritesHandler));
</script>
