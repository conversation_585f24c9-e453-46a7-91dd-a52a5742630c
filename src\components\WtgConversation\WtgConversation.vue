<template>
    <div class="d-flex flex-column" :style="measurableStyles">
        <div class="d-flex flex-column justify-space-between">
            <WtgLabel v-if="conversation.name" typography="h6">{{ conversation.name }}</WtgLabel>
            <div class="d-flex justify-space-between mb-1">
                <WtgSwitch
                    v-model="following"
                    class="ml-1"
                    text
                    small
                    :label="followCaption"
                    @update:model-value="onFollowClick"
                >
                </WtgSwitch>
                <WtgPopover v-if="followers.length" mod :close-on-content-click="false" offset-y>
                    <template #activator="{ props: activatorProps }: { props: Record<string, any> }">
                        <WtgButton
                            variant="ghost"
                            size="m"
                            type="button"
                            :loading="loadingMessages"
                            v-bind="activatorProps"
                        >
                            {{ followersCaption }}
                        </WtgButton>
                    </template>
                    <template #default="{ isActive }: { isActive: Ref<Boolean> }">
                        <div>
                            <WtgList>
                                <WtgListItem @click="() => onPopoverAddFollowersClick(isActive)">
                                    <template #leading>
                                        <WtgIcon class="mr-1" size="m" icon="s-icon-plus" />
                                    </template>
                                    {{ addFollowersCaption }}
                                </WtgListItem>
                                <WtgListItem v-for="follower in followers" :key="follower.id">
                                    <template #leading>
                                        <WtgAvatar
                                            :icon="follower.icon"
                                            :image="follower.photo?.image"
                                            :alt="follower.name"
                                            :fallback-image="follower.photo?.fallbackImage"
                                            class="mr-2"
                                            size="m"
                                        />
                                    </template>
                                    {{ follower.name }}
                                    <template #trailing>
                                        <WtgIconButton
                                            icon="s-icon-close"
                                            size="xs"
                                            class="ml-2"
                                            variant="ghost"
                                            @click.prevent="onRemoveFollowerClick(follower, isActive)"
                                        />
                                    </template>
                                </WtgListItem>
                            </WtgList>
                        </div>
                    </template>
                </WtgPopover>
                <WtgButton v-else class="mb-1" variant="ghost" size="m" type="button" @click="onAddFollowersClick">
                    {{ addFollowersCaption }}
                </WtgButton>
            </div>
            <div>
                <WtgTextarea
                    v-model="inputValue"
                    counter="10000"
                    :placeholder="messageCaption"
                    :readonly="readonly"
                    max-length="10000"
                />
            </div>
            <div class="d-flex justify-end mt-2">
                <WtgButton :loading="sendingMessage" :disabled="sendDisabled" @click="onSendClick">
                    {{ sendCaption }}
                </WtgButton>
            </div>
        </div>
        <div class="d-flex flex-column overflow-auto">
            <WtgProgressLinear v-if="loadingMessages" indeterminate />
            <div v-for="(value, key, index) in messages" :key="index">
                <WtgLabel align="center" display="block" font-weight="bold" class="py-2">
                    {{ key }}
                </WtgLabel>
                <WtgConversationMessage
                    v-for="(message, idx) in value"
                    :key="idx"
                    :message="message"
                    class="mb-4"
                    @like-click="onLikeClick"
                    @dislike-click="onDislikeClick"
                />
            </div>
        </div>
        <RemoveFollowerDialog
            :value="showRemoveFollowerDialog"
            :conversation="conversation"
            :follower="followerToRemove"
            @close="showRemoveFollowerDialog = false"
        />
        <AddFollowersDialog
            :value="showAddFollowersDialog"
            :conversation="conversation"
            @close="showAddFollowersDialog = false"
        />
    </div>
</template>

<script lang="ts">
import WtgAvatar from '@components/WtgAvatar';
import WtgButton from '@components/WtgButton';
import WtgIcon from '@components/WtgIcon';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import WtgList from '@components/WtgList/WtgList.vue';
import WtgListItem from '@components/WtgList/WtgListItem.vue';
import WtgPopover from '@components/WtgPopover';
import WtgProgressLinear from '@components/WtgProgressLinear';
import WtgSwitch from '@components/WtgSwitch';
import WtgTextarea from '@components/WtgTextArea';
import { useLocale } from '@composables/locale';
import { makeMeasureProps, useMeasure } from '@composables/measure';
import { defineComponent, PropType, Ref, ref } from 'vue';
import AddFollowersDialog from './components/AddFollowersDialog.vue';
import RemoveFollowerDialog from './components/RemoveFollowerDialog.vue';
import WtgConversationMessage from './components/WtgConversationMessage.vue';
import {
    Conversation,
    ConversationFollower,
    ConversationMessage,
    ConversationProvider,
    defaultConversation,
} from './types';

export default defineComponent({
    name: 'WtgConversation',
    components: {
        RemoveFollowerDialog,
        AddFollowersDialog,
        WtgAvatar,
        WtgButton,
        WtgConversationMessage,
        WtgIcon,
        WtgIconButton,
        WtgLabel,
        WtgPopover,
        WtgProgressLinear,
        WtgTextarea,
        WtgSwitch,
        WtgList,
        WtgListItem,
    },
    props: {
        /**
         * The conversation object containing details such as messages, followers, and metadata.
         */
        conversation: {
            type: Object as PropType<Conversation>,
            default: (): Conversation => defaultConversation(),
        },

        /**
         * If true, the conversation will be displayed in a read-only mode, disabling user interactions.
         */
        readonly: {
            type: Boolean,
            default: false,
        },

        ...makeMeasureProps(),
    },
    setup(props) {
        const { formatCaption } = useLocale();
        const { measurableStyles } = useMeasure(props);
        const following = ref(false);
        following.value = props.conversation.following;

        return { formatCaption, measurableStyles, following };
    },
    data() {
        return {
            inputValue: '',
            sendingMessage: false,
            followerToRemove: undefined as ConversationFollower | undefined,
            showRemoveFollowerDialog: false,
            showAddFollowersDialog: false,
        };
    },
    computed: {
        followers(): ConversationFollower[] {
            return this.conversation.followers;
        },
        followersCaption(): string {
            return this.formatCaption('conversation.followers');
        },
        addFollowersCaption(): string {
            return this.formatCaption('conversation.addFollowers');
        },
        loadingMessages(): boolean {
            return this.conversation.loadingMessages;
        },
        messageCaption(): string {
            return this.formatCaption('conversation.placeholder');
        },
        messages(): Record<string, ConversationMessage[]> {
            return this.conversation.messages;
        },
        provider(): ConversationProvider {
            return this.conversation.provider;
        },
        sendDisabled(): boolean {
            return this.readonly || this.inputValue.trim().length === 0;
        },
        followCaption(): string {
            return this.following
                ? this.formatCaption(`conversation.following`)
                : this.formatCaption(`conversation.follow`);
        },
        sendCaption(): string {
            return this.formatCaption('conversation.send');
        },
    },
    methods: {
        async onDislikeClick(message: ConversationMessage): Promise<void> {
            message['loadingDislikeScore'] = true;

            try {
                const score = await this.provider.dislikeMessageAsync(message);
                message['score'] = score;
            } finally {
                message['loadingDislikeScore'] = false;
            }
        },
        async onFollowClick(newVal: boolean): Promise<void> {
            if (newVal) {
                await this.provider.followConversationAsync(this.conversation);
            } else {
                await this.provider.unfollowConversationAsync(this.conversation);
            }
        },
        async onLikeClick(message: ConversationMessage): Promise<void> {
            message['loadingLikeScore'] = true;
            try {
                const score = await this.provider.likeMessageAsync(message);
                message['score'] = score;
            } finally {
                message['loadingLikeScore'] = false;
            }
        },
        onRemoveFollowerClick(follower: ConversationFollower, isActive: Ref<Boolean>): void {
            isActive.value = false;
            this.followerToRemove = follower;
            this.showRemoveFollowerDialog = true;
        },
        onPopoverAddFollowersClick(isActive: Ref<Boolean>): void {
            isActive.value = false;
            this.showAddFollowersDialog = true;
        },
        onAddFollowersClick(): void {
            this.showAddFollowersDialog = true;
        },
        async onSendClick(): Promise<void> {
            this.sendingMessage = true;
            try {
                await this.provider.sendMessageAsync(this.conversation, this.inputValue);
                this.inputValue = '';
            } finally {
                this.sendingMessage = false;
            }
        },
    },
});
</script>

<style lang="scss" scoped>
.v-text-field :deep(.v-text-field__details) {
    flex-direction: row-reverse;
    margin-bottom: 0px;
    padding: 0px;
}

.v-text-field :deep(.v-counter) {
    margin-left: 0px !important;
}
</style>
