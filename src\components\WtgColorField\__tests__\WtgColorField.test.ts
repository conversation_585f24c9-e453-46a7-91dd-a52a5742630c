import WtgInput from '@components/WtgInput/WtgInput.vue';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import { WtgColorField } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgColorField', () => {
    test('its name is WtgColorField', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgColorField');
    });

    test('it passes its props to the base WtgInput', () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
                displayOnly: true,
                flat: true,
                id: '111',
                label: 'My Label',
                leadingIcon: 'icon1',
                loading: true,
                messages: 'message',
                placeholder: 'placeholder',
                readonly: true,
                required: true,
                restricted: true,
                sentiment: 'primary',
            },
        });
        const props = wrapper.findComponent({ name: 'WtgInput' }).props();
        expect(props.disabled).toBe(true);
        expect(props.displayOnly).toBe(true);
        expect(props.flat).toBe(true);
        expect(props.filled).toBe(false);
        expect(props.hideMessages).toBe(false);
        expect(props.id).toBe('111');
        expect(props.label).toBe('My Label');
        expect(props.leadingIcon).toBe('icon1');
        expect(props.loading).toBe(true);
        expect(props.messages).toBe('message');
        expect(props.placeholder).toBe('placeholder');
        expect(props.readonly).toBe(true);
        expect(props.required).toBe(true);
        expect(props.restricted).toBe(true);
        expect(props.sentiment).toBe('primary');
    });

    test("it has the autocomplete attribute set to 'off'", () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        expect(input.attributes('autocomplete')).toBe('off');
    });

    test('it ensures the embedded prompter button has the correct role and is focusable', () => {
        const wrapper = mountComponent();

        const prompter = wrapper.findComponent({ name: 'WtgIcon' });
        expect(prompter.attributes('role')).toBe('button');
        expect(prompter.attributes('tabindex')).toBe('-1');
    });

    test('is responding to loading prop changes correctly', async () => {
        const wrapper = mountComponent({
            props: {
                loading: true,
            },
        });

        const inputField = wrapper.findComponent(WtgInput);
        expect(inputField.props().loading).toBe(true);

        await wrapper.setProps({ loading: false });

        expect(inputField.props().loading).toBe(false);
    });

    test('Is responding to props changes correctly ', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: '#FFFFFFFF',
            },
        });

        await wrapper.setProps({ restricted: false });

        await wrapper.setProps({
            modelValue: '#00000000',
            label: 'new test label',
            messages: 'new test messages',
            readonly: false,
        });

        const textField = wrapper.findComponent({ name: 'WtgInput' });
        const props = textField.props();

        expect(props.modelValue).toBe('#00000000');
        expect(props.label).toBe('new test label');
        expect(props.messages).toBe('new test messages');
        expect(props.readonly).toBe(false);

        const prompter = wrapper.findComponent({ name: 'WtgIcon' });
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await nextTick();

        const pickerProps = wrapper.findComponent({ name: 'WtgColorPicker' }).props();

        expect(pickerProps.modelValue).toBe('#00000000');
    });

    test('When given a valid color hex code, it is forwarding it to the picker', async () => {
        const color = '#00FF00FF';
        const wrapper = mountComponent({
            props: {
                modelValue: color,
            },
        });

        const prompter = wrapper.findComponent({ name: 'WtgIcon' });
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await nextTick();

        const picker = wrapper.findComponent({ name: 'WtgColorPicker' });
        expect(picker.props().modelValue).toBe(color);
    });

    test('Is emitting a change event after picker color change', async () => {
        const color = '#00FF00FF';
        const colorNew = '#000000FF';
        const wrapper = mountComponent({
            props: {
                modelValue: color,
            },
        });

        const prompter = wrapper.findComponent({ name: 'WtgIcon' });
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await nextTick();

        const picker = wrapper.findComponent({ name: 'WtgColorPicker' });
        picker.vm.model = colorNew;
        await nextTick();

        const pickerEmit: string[] = picker.emitted()['update:modelValue'][0] as string[];
        expect(pickerEmit.length).toBe(1);
        expect(pickerEmit).toEqual([colorNew]);

        const wrapperEmit = wrapper.emitted()['update:modelValue'];
        expect(wrapperEmit.length).toBe(1);
        expect(wrapperEmit).toEqual([[colorNew]]);
    });

    test('Is emitting a change event on text field change', async () => {
        const color = '#00FF00FF';
        const colorNew = '#FF0000FF';
        const wrapper = mountComponent({
            props: {
                modelValue: color,
            },
        });

        const prompter = wrapper.findComponent({ name: 'WtgIcon' });
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await nextTick();

        const input = wrapper.find('input');
        await input.setValue(colorNew);

        const wrapperEmit = wrapper.emitted()['update:modelValue'];
        expect(wrapperEmit.length).toBe(1);
        expect(wrapperEmit).toEqual([[colorNew]]);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('when disabled, it sets the disabled attribute on the input field', async () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });
        expect(wrapper.find('input').attributes('disabled')).toBeDefined();

        await wrapper.setProps({ disabled: false });
        expect(wrapper.find('input').attributes('disabled')).toBeUndefined();
    });

    test('it passes the aria* properties to the input to ensure fields with a hidden label can still meet accessibility requirements', async () => {
        const wrapper = mountComponent({
            props: {
                ariaLabel: 'Aria label',
                ariaLabelledby: 'Aria labelledby',
            },
        });
        expect(wrapper.find('input').attributes('aria-label')).toBe('Aria label');
        expect(wrapper.find('input').attributes('aria-labelledby')).toBe('Aria labelledby');
    });

    test('when readonly, it sets the readonly attribute on the input field', async () => {
        const wrapper = mountComponent({
            props: {
                readonly: true,
            },
        });
        expect(wrapper.find('input').attributes('readonly')).toBeDefined();

        await wrapper.setProps({ readonly: false });
        expect(wrapper.find('input').attributes('readonly')).toBeUndefined();
    });

    test('it has a (deprecated) inputId property that gets applied if no id is specified to aid the GLOW VUE 3 migration', async () => {
        const wrapper = mountComponent();
        await wrapper.setProps({ inputId: 'id1' });
        expect(wrapper.find('input').attributes('id')).toBe('id1');

        await wrapper.setProps({ id: 'id2' });
        expect(wrapper.find('input').attributes('id')).toBe('id2');
    });

    test('it has (deprecated) VALUE property and INPUT event that allows it to be backwards compatible with the V-MODEL handling of the VUE 2 implementation', async () => {
        const onInput = jest.fn();
        const wrapper = mountComponent({
            props: {
                value: '#000000FF',
            },
            attrs: {
                onInput,
            },
        });

        const input = wrapper.find<HTMLInputElement>('input');
        expect(input.element.value).toBe('#000000FF');

        await input.setValue('#FFFFFFFF');
        expect(onInput).toHaveBeenCalledTimes(1);
        expect(onInput).toHaveBeenCalledWith('#FFFFFFFF');

        await wrapper.setProps({ modelValue: '#00FF00FF' });
        expect(input.element.value).toBe('#00FF00FF');
    });

    test('it has (deprecated) VALUE property and model-compat:input event that allows it to be backwards compatible with the V-MODEL handling of the VUE COMPAT build implementation', async () => {
        const onModelCompatInput = jest.fn();
        const wrapper = mountComponent({
            props: {
                value: '#000000FF',
            },
            attrs: {
                'onModelCompat:input': onModelCompatInput,
            },
        });

        const input = wrapper.find<HTMLInputElement>('input');
        expect(input.element.value).toBe('#000000FF');

        await input.setValue('#FFFFFFFF');
        expect(onModelCompatInput).toHaveBeenCalledTimes(1);
        expect(onModelCompatInput).toHaveBeenCalledWith('#FFFFFFFF');

        await wrapper.setProps({ modelValue: '#00FF00FF' });
        expect(input.element.value).toBe('#00FF00FF');
    });

    test('it sets hideMessages to true when menu opens', async () => {
        const wrapper = mountComponent();
        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        expect(wrapper.findComponent({ name: 'WtgInput' }).props().hideMessages).toBe(true);
    });

    function mountComponent({ props = {}, attrs = {}, provide = {} } = {}) {
        return mount(WtgColorField, {
            props,
            attrs,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
