import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgJobAddress from './WtgJobAddress.stories.ts';

<Meta of={WtgJobAddress} />

<div className="component-header">
    <h1>Job address</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td scope="row">
                <img className="status-chip" src={statusAvailable}></img>[Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=215-12676&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>With pending updates
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style={{ width: '33%' }}>
                [PRJ00053037](https://ediprod.cw.wisetechglobal.com/link/ShowEditForm/Project/38bc979e-6255-47dc-a0e3-4bb1718f9ed2?LicenceCode=EDIAUSSYD&lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>
                Supply JobAddress - Address validation, additional info searching, search filter
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    The job address field allows users to select, display, edit, and create an organization's address while linking
    additional information and contacts.
</p>

## API

<Canvas className="canvas-preview" of={WtgJobAddress.RealAddressAndFreeText} />
<Controls of={WtgJobAddress.Info} sort={'alpha'} />

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
