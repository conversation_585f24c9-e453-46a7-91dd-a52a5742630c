﻿using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Dat.Integration;
using Dat.Integration.Deployment;

namespace WTG.Design.Deployment
{
	public sealed class BuildDeployer : IBuildDeployer
	{
		public BuildDeployer(ITaskLogger taskLogger)
			: this(taskLogger, new StreamingProcessInvoker(taskLogger), new SecureStorage().Decrypt)
		{
		}

		public BuildDeployer(ITaskLogger taskLogger, IStreamingProcessInvoker processInvoker, PasswordDecrypter passwordDecrypter)
		{
			this.taskLogger = taskLogger ?? throw new ArgumentNullException(nameof(taskLogger));
			this.processInvoker = processInvoker ?? throw new ArgumentNullException(nameof(processInvoker));
			this.passwordDecrypter = passwordDecrypter ?? throw new ArgumentNullException(nameof(passwordDecrypter));
		}

		public delegate string PasswordDecrypter(string encryptedPassword);

		readonly ITaskLogger taskLogger;
		readonly IStreamingProcessInvoker processInvoker;
		readonly PasswordDecrypter passwordDecrypter;

		public void AutoDeployLatestBuild(string buildConfiguration, string deploymentConfiguration, string sourcePath, string binPath)
			=> DeployWebSiteAsync(binPath).GetAwaiter().GetResult();

		async Task DeployWebSiteAsync(string binPath, string testRigPath = "")
		{
			var distPath = Path.Combine(binPath, Configuration.SourceFolder);
			if (!Directory.Exists(distPath))
			{
				throw new InvalidOperationException($@"Error when deploying Design site. The source path ""{distPath}"" does not exist.");
			}

			var msDeploy = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "IIS", "Microsoft Web Deploy V3", "msdeploy.exe");
			var password = passwordDecrypter(Configuration.PasswordEncrypted);
			var authenticationOptions = $"userName={Configuration.Username},password={password}";

			using (taskLogger.RecordTask($"Deploying Design site..."))
			{
				await processInvoker.ExecuteProcessAsync(
					msDeploy,
					new[]
					{
						"-verb:sync",
						$"-source:contentPath=\"{distPath}\"",
						$"-dest:contentPath=\"{Configuration.SiteName}{testRigPath}\",wmsvc={Configuration.ServerName},{authenticationOptions}",
						"-allowUntrusted",
						"-skip:objectName=dirPath,absolutePath=testrig$,skipAction=delete",
					})
					.ConfigureAwait(false);
			}
		}

		public void AutoDeployTestedShelf(string buildConfiguration, string deploymentConfiguration, string sourcePath, string binPath, TaskInfo taskInfo)
			=> DeployWebSiteAsync(binPath, GetTestRigName(taskInfo)).GetAwaiter().GetResult();

		static string GetTestRigName(TaskInfo taskInfo)
		{
			string testRigName = null;

			if (taskInfo != null)
			{
				var testRigNames = (taskInfo.TaskComments ?? string.Empty)
					.Replace("\r\n", "\r")
					.Split(new[] { "\r" }, StringSplitOptions.RemoveEmptyEntries)
					.Select(u => u.Trim())
					.Where(u => !string.IsNullOrWhiteSpace(u))
					.Select(u => u.Split(new[] { ':' }, StringSplitOptions.RemoveEmptyEntries).Select(v => v.Trim()).Where(w => !string.IsNullOrEmpty(w)).ToArray())
					.Where(u => u.Length == 2 && u[0] == "WebComponentsTestRigName");
				if (testRigNames.Count() > 1)
				{
					throw new DeploymentCancelledException("More than one WebComponentsTestRigName found in task notes.");
				}
				if (!testRigNames.Any())
				{
					throw new DeploymentCancelledException("WebComponentsTestRigName not found in task notes.");
				}
				testRigName = testRigNames.ElementAt(0)[1];
			}

			if (string.IsNullOrWhiteSpace(testRigName))
			{
				throw new DeploymentCancelledException("WebComponentsTestRigName not found in task notes.");
			}

			var rg = new Regex(@"^[a-zA-Z0-9]*$");
			if (!rg.IsMatch(testRigName))
			{
				throw new DeploymentCancelledException("Invalid WebComponentsTestRigName, test rig name must only contain letters and numbers.");
			}

			return $"\\testrig\\{testRigName}";
		}

		public void TeardownTestedShelf(string buildConfiguration, string deploymentConfiguration, TaskInfo taskInfo)
			=> TeardownWebSiteAsync(GetTestRigName(taskInfo)).GetAwaiter().GetResult();

		async Task TeardownWebSiteAsync(string testRigPath = "")
		{
			var msDeploy = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "IIS", "Microsoft Web Deploy V3", "msdeploy.exe");
			var password = passwordDecrypter(Configuration.PasswordEncrypted);
			var authenticationOptions = $"userName={Configuration.Username},password={password}";

			using (taskLogger.RecordTask($"Shutting down test rig..."))
			{
				await processInvoker.ExecuteProcessAsync(
					msDeploy,
					new[]
					{
						"-verb:delete",
						$"-dest:contentPath=\"{Configuration.SiteName}{testRigPath}\",wmsvc={Configuration.ServerName},{authenticationOptions}",
						"-allowUntrusted",
					})
					.ConfigureAwait(false);
			}
		}

		public void DeployOnDemand(string buildConfiguration, string deploymentConfiguration, string sourcePath, string binPath)
			=> throw new NotSupportedException();
	}
}
