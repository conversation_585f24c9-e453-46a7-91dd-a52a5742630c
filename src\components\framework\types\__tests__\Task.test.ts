import { WtgFrameworkNotification } from '../framework';

describe('WtgFrameworkTask', () => {
    describe('WtgFrameworkNotification', () => {
        let notification: WtgFrameworkNotification;

        beforeEach(() => {
            notification = new WtgFrameworkNotification(
                'ID',
                'some caption',
                'some text',
                'property1',
                'property1 key'
            );
        });

        test('it initializes notification properties', () => {
            expect(notification.id).toBe('ID');
            expect(notification.caption).toBe('some caption');
            expect(notification.text).toBe('some text');
            expect(notification.propertyName).toBe('property1');
            expect(notification.targetKey).toBe('property1 key');
        });
    });
});
