import { promises as fs } from 'fs';
import path from 'path';
import fg from 'fast-glob';
import { execSync } from 'child_process';

async function generateMarkdownFromExamples() {
    const repoRoot = execSync('git rev-parse --show-toplevel', { encoding: 'utf-8' }).trim();

    const exampleFiles = await fg('**/*.example.vue', {
        cwd: repoRoot,
        absolute: true,
        ignore: ['node_modules'],
    });

    const sections: string[] = [];

    for (const filePath of exampleFiles) {
        const fileName = path.basename(filePath);
        const componentName = fileName.replace('.example.vue', '');
        const relativePath = path.relative(repoRoot, filePath);
        const content = await fs.readFile(filePath, 'utf-8');

        sections.push(
            `## ${componentName}\n\n` + `**Source:** \`${relativePath}\`\n\n` + '```vue\n' + content.trim() + '\n```\n'
        );
    }

    const output = sections.join('\n');

    const outputDir = path.join(repoRoot, 'dist/metadata');
    await fs.mkdir(outputDir, { recursive: true });

    const outputPath = path.join(outputDir, 'examples.md');
    await fs.writeFile(outputPath, output, 'utf-8');

    console.log(`✅ Markdown generated at: ${outputPath}`);
}

generateMarkdownFromExamples().catch((err) => {
    console.error('❌ Failed to generate markdown:', err);
    process.exit(1);
});
