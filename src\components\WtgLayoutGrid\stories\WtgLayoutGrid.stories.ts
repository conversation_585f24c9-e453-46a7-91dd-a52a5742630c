import WtgCard from '@components/WtgCard';
import WtgLayoutGrid from '@components/WtgLayoutGrid/WtgLayoutGrid';
import WtgPanel from '@components/WtgPanel';
import { layoutGridColumnArgTypes } from '@composables/layoutGridColumn';
import { measureArgTypes } from '@composables/measure';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { StoryObj } from '@storybook/vue3';

export default {
    title: 'Utilities/Layout Grid',
    component: WtgLayoutGrid,
    parameters: {
        docs: {
            description: {
                component:
                    'LayoutGrid is a helper component whose primary purpose is to simplify responsive design code by negating the need for individual row and col components in layout construction in favor of a columns property on individual components. When wrapped in a layout grid component, you can control the layout behaviour of components through the components columns property.',
            },
        },
    },
    render: (args) => ({
        components: { WtgPanel, WtgLayoutGrid },
        setup: () => ({ args }),
        template:
            '<WtgLayoutGrid v-bind="args" some-attr="hello"><WtgPanel caption="Panel 1" /><WtgPanel caption="Panel 2" /><WtgPanel caption="Panel 3" /></WtgLayoutGrid>',
    }),
    argTypes: {
        noGutters: {
            control: 'boolean',
        },
        ...layoutGridColumnArgTypes,
        ...measureArgTypes,
    },
} as StoryObj;

export const Default = { args: {} };

type Story = StoryObj<typeof WtgLayoutGrid>;

function snippetContent(args1: string): string {
    return `<WtgLayoutGrid ${args1}class="text-center mt-2">
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6">col-sm-6</WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6">col-sm-6</WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-3">col-sm-6 col-md-3</WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-3">col-sm-6 col-md-3</WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-3">col-sm-6 col-md-3</WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-3">col-sm-6 col-md-3</WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-2 col-lg-2">
            col-sm-6 col-md-2 col-lg-2
        </WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-2 col-lg-2">
            col-sm-6 col-md-2 col-lg-2
        </WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-2 col-lg-2">
            col-sm-6 col-md-2 col-lg-2
        </WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-2 col-lg-2">
            col-sm-6 col-md-2 col-lg-2
        </WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-2 col-lg-2">
            col-sm-6 col-md-2 col-lg-2
        </WtgCard>
        <WtgCard color="brand" class="pa-m" fill columns="col-sm-6 col-md-2 col-lg-2">
            col-sm-6 col-md-2 col-lg-2
        </WtgCard>
    </WtgLayoutGrid>`;
}

export const Columns: Story = {
    parameters: {
        docs: {
            controls: {
                exclude: /.*/g,
            },
            source: {
                code: `<template>
    ${snippetContent('')}
</template>

<script setup lang="ts">
import {WtgLayoutGrid, WtgCard} from '@wtg/wtg-components';
</script>`,
            },
        },
    },
    render: () => ({
        components: { WtgLayoutGrid, WtgCard },
        template: snippetContent(''),
    }),
};

export const NoGutters: Story = {
    parameters: {
        docs: {
            controls: {
                exclude: /.*/g,
            },
            source: {
                code: `<template>
    ${snippetContent('no-gutters ')}
</template>

<script setup lang="ts">
import {WtgLayoutGrid, WtgCard} from '@wtg/wtg-components';
</script>`,
            },
        },
    },
    render: () => ({
        components: { WtgLayoutGrid, WtgCard },
        template: snippetContent('no-gutters '),
    }),
};

export const Sandbox: StoryObj = {
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgLayoutGrid, WtgCard },
        setup: () => ({ args }),
        template: templateWithRtl(`<div>${snippetContent('')}</div><div>${snippetContent('no-gutters ')}</div>`),
    }),
};
