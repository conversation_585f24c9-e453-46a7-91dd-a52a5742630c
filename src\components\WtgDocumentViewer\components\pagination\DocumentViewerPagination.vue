<template>
    <div class="document-pagination">
        <span>
            <WtgTextField
                :alignment="InputAlignment.Center"
                class="page-input"
                :model-value="props.pageCount > 0 ? pageNumber.toString() : '0'"
                @update:model-value="onHandlePageInput"
            />
            <WtgLabel class="page-count">{{ pageCountString }}</WtgLabel>
        </span>
    </div>
</template>

<script setup lang="ts">
import WtgLabel from '@components/WtgLabel';
import WtgTextField from '@components/WtgTextField';
import { InputAlignment } from '@composables/input';
import { computed, ref, watch } from 'vue';

const props = defineProps({
    activePageNumber: { type: Number, default: 0 },
    pageCount: { type: Number, default: 1 },
});

const emit = defineEmits<{
    'active-page-updated': [number];
}>();

const pageNumber = ref(props.activePageNumber);

const pageCountString = computed((): string => {
    return '/ ' + props.pageCount.toString();
});

const onHandlePageInput = (value: string) => {
    const newPageNumber = parseInt(value);
    if (newPageNumber <= 0 || newPageNumber > props.pageCount) return;
    if (isNaN(newPageNumber)) return;
    pageNumber.value = newPageNumber;
    emit('active-page-updated', pageNumber.value);
};

watch(
    () => props.activePageNumber,
    (newActivePageNumber: number) => {
        pageNumber.value = newActivePageNumber;
    }
);
</script>

<style scoped lang="scss">
.document-pagination {
    text-align: center;
    margin-right: 5px;

    > span {
        display: flex;
    }

    .page-input {
        width: 30px;
        height: 30px;
    }
    .page-count {
        color: grey;
        padding: 5px;
    }
}
</style>
