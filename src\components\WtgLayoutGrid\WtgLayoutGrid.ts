import WtgRow from '@components/WtgRow';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { Comment, defineComponent, Fragment, h, PropType, Text, useSlots, VNode } from 'vue';
import WtgLayoutGridColumn from './WtgLayoutGridColumn';

export default defineComponent({
    props: {
        fillAvailable: {
            type: [Boolean, String, undefined] as PropType<Boolean | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'>,
            default: undefined,
        },
        noGutters: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    setup(props: any) {
        const slots = useSlots();
        useLayoutGridColumn(props);
        return () => {
            const data = {
                ...props,
            };
            const cols = [] as VNode[];
            if (slots.default) {
                const defaultSlot = slots.default() || [];
                cols.push(...getCols(defaultSlot));
            }
            return h(WtgRow, data, () => cols);
        };
    },
});

function getCols(children: VNode[]) {
    const cols = [] as VNode[];
    for (const node of children) {
        if (node.type === Fragment) {
            cols.push(...getCols(node.children as VNode[]));
        } else if (node.type !== Comment && node.type !== Text) {
            const dataAttrs = node.props;
            if (dataAttrs && dataAttrs['data-wtg-layout-grid-ignore'] !== undefined) {
                cols.push(node);
            } else {
                const col = h(WtgLayoutGridColumn, {}, () => [node]);
                cols.push(col);
            }
        }
    }
    return cols;
}
