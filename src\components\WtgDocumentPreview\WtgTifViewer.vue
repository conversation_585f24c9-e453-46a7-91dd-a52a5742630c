<template>
    <div style="height: 100%">
        <WtgTifPage
            v-if="hasData && !isMultiPage"
            :buffer="buffer"
            :ifd="ifds[0]"
            @error="onError"
            @tif-decode-error="onTifDecodeError"
        />
        <VCarousel v-if="hasData && isMultiPage" v-model="currentPage" height="100%" :show-arrows="true">
            <VCarouselItem v-for="(_, index) in ifds" :key="index" class="wtg-carousel-item">
                <WtgTifPage
                    v-if="index === currentPage"
                    :buffer="buffer"
                    :ifd="ifds[index]"
                    @error="onError"
                    @tif-decode-error="onTifDecodeError"
                />
            </VCarouselItem>
        </VCarousel>
    </div>
</template>

<script setup lang="ts">
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import UTIF from 'utif';
import { computed, ref, Ref, watch } from 'vue';
import { VCarousel, VCarouselItem } from 'vuetify/components/VCarousel';
import WtgTifPage from './WtgTifPage.vue';

//
// Properties
//
const props = defineProps({
    src: {
        type: String,
        default: '',
    },
    ...makeLayoutGridColumnProps(),
});

//
// Emits
//
const emit = defineEmits<{
    error: [];
    load: [];
    'tif-decode-error': [];
}>();

//
// State
//
const buffer: Ref<ArrayBuffer | undefined> = ref(undefined);
const currentPage = ref(0);
const ifds: Ref<UTIF.IFD[]> = ref([]);

//
// Composables
//
useLayoutGridColumn(props);

//
// Computed
//
const hasData = computed(() => ifds.value.length > 0);
const isMultiPage = computed(() => ifds.value.length > 1);

//
// Watchers
//
watch(
    () => props.src,
    async () => {
        buffer.value = undefined;
        ifds.value = [];

        if (!props.src) {
            return;
        }

        const response = await fetch(props.src);
        if (response) {
            emit('load');
        } else {
            emit('error');
            return;
        }

        buffer.value = await response.arrayBuffer();
        ifds.value = UTIF.decode(buffer.value);
    },
    { immediate: true }
);

//
// Event Handlers
//
function onError(): void {
    emit('error');
}

function onTifDecodeError(): void {
    emit('tif-decode-error');
}
</script>

<style lang="scss" scoped>
.wtg-carousel-item :deep(.v-carousel__item) {
    height: 100% !important;
    overflow-y: auto;
}
</style>
