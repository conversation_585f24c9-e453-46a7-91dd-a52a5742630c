<template>
    <VContainer ref="root" :fluid="fluid" :style="computedStyles" :class="computedClasses">
        <WtgLayoutGrid
            v-if="isGridLayout || isGridFillLayout"
            :fill-available="isGridFillLayout"
            :no-gutters="noGutters"
        >
            <slot />
        </WtgLayoutGrid>
        <slot v-else />
    </VContainer>
</template>

<script setup lang="ts">
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import { useColor } from '@composables/color';
import { useDisplay } from '@composables/display';
import {
    makeDialogHeightStyle,
    makeLayoutProps,
    makeFitToDialogHeightStyles,
    makeFitToViewportHeightStyles,
    makeViewportHeightStyle,
    useLayout,
} from '@composables/layoutGrid';
import { makeMeasureProps, useMeasure } from '@composables/measure/measure';
import { computed, onMounted, onUnmounted, ref, type ComponentPublicInstance } from 'vue';
import { VContainer } from 'vuetify/components/VGrid';

//
// Properties
//
const props = defineProps({
    /**
     * The name of the color to apply from the design system or a custom color.
     * This color will be applied to the background of the container.
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * * @deprecated Use `layout` instead.
     * When set to true, the container will expand to fill the full height of its parent dialog or viewport.
     * This is particularly useful for dialogs or full-screen layouts where you want the content to occupy the entire height.
     * If the parent is a dialog, it will adjust to the dialog's height; otherwise, it will fit to the viewport height.
     */
    fitToHeight: {
        type: Boolean,
        default: false,
    },

    /**
     * When set, the container will expand to fill the full width of its parent, rather
     * than having a fixed maximum width based on breakpoints. Useful for layouts that require
     * edge-to-edge content or more flexible horizontal spacing.
     */
    fluid: {
        type: Boolean,
        default: true,
    },

    /**
     * Controls the horizontal alignment of the container's content.
     * Can be set to 'left', 'center', or 'right' to align content accordingly.
     * The default is 'center'.
     */
    align: {
        type: String,
        default: 'center',
    },
    ...makeLayoutProps(),
    ...makeMeasureProps(),
});

//
// State
//
const root = ref<ComponentPublicInstance>();
const parentDialogFullScreen = ref(false);
const parentDialog = ref<HTMLElement | null>(null);
let parentDialogObserver = undefined as MutationObserver | undefined;

//
// Composables
//
const { mobile } = useDisplay('', { mobileBreakpoint: 'sm' });
const { layoutClasses, isGridLayout, isGridFillLayout, isFillLayout } = useLayout(props);
const { measurableStyles } = useMeasure(props);
const { colorClasses, colorStyles } = useColor(props, { background: true });

//
// Computed
//
const computedClasses = computed(() => {
    const classes = [
        ...layoutClasses.value,
        ...colorClasses.value,
        'wtg-container',
        {
            'wtg-container--mobile': mobile.value,
            'ml-0': props.align === 'left',
            'mr-0': props.align === 'right',
        },
    ];

    if (isFillLayout.value || isGridFillLayout.value) {
        classes.push('overflow-y-auto', 'overflow-x-hidden', 'wtg-container-fill-height');
    }

    return classes;
});

const computedStyles = computed(() => {
    let styles: any = { ...measurableStyles.value };
    if (props.fitToHeight) {
        styles =
            parentDialog.value && !parentDialogFullScreen.value
                ? { ...makeFitToDialogHeightStyles() }
                : { ...makeFitToViewportHeightStyles() };
    } else if (isFillLayout.value || isGridFillLayout.value) {
        styles =
            parentDialog.value && !parentDialogFullScreen.value
                ? { ...makeDialogHeightStyle() }
                : { ...makeViewportHeightStyle(0, 600) };
    }
    return { ...styles, ...colorStyles.value };
});

//
// Lifecycle
//
onMounted(() => {
    parentDialog.value = root.value && root.value.$el.closest('.v-dialog');
    if (parentDialog.value) {
        parentDialogObserver = new MutationObserver(() => {
            if (parentDialog.value) {
                parentDialogFullScreen.value = parentDialog.value.classList.contains('v-dialog--fullscreen');
            }
        });
        parentDialogObserver.observe(parentDialog.value, {
            attributes: true,
            attributeFilter: ['class'],
        });
    }
});

onUnmounted(() => {
    if (parentDialogObserver) {
        parentDialogObserver.disconnect();
    }
});
</script>

<style lang="scss">
.wtg-container {
    padding: var(--s-padding-l) var(--s-padding-xl);

    &.wtg-container--mobile {
        padding: var(--s-padding-null);

        > .wtg-panel,
        > .v-row > [class*='v-col-'] > .wtg-panel {
            border-radius: var(--s-radius-null);
            border-inline-start: var(--s-padding-null);
            border-inline-end: var(--s-padding-null);
        }
    }
}
</style>
