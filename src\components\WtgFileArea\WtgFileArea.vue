<template>
    <div ref="area">
        <input
            ref="input"
            :aria-label="ariaLabel ?? formatCaption('fileArea.selectFiles')"
            :aria-labelledby="ariaLabelledby"
            type="file"
            class="d-none"
            :multiple="multiple"
            :accept="accept"
        />
        <slot :on="on" :drag-over-area="dragOverArea">
            <WtgButton @click="handleDragAreaClicked()">{{ defaultText }}</WtgButton>
        </slot>
    </div>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import { useLocale } from '@composables/locale';
import { computed, onMounted, PropType, ref, Ref, watch } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * Specifies the accepted file types for the file input.
     * Example: '.jpg,.png,.pdf' or 'image/*'.
     */
    accept: {
        type: String,
        default: '',
    },

    /**
     * The ARIA label for the file input element.
     */
    ariaLabel: {
        type: String,
        default: undefined,
    },

    /**
     * The ID of the element that labels the file input element.
     */
    ariaLabelledby: {
        type: String,
        default: undefined,
    },

    /**
     * If true, drag-and-drop functionality will be disabled.
     */
    disableDrag: {
        type: Boolean,
        default: false,
    },

    /**
     * The current value of the file area, used for two-way binding.
     * Contains an array of selected files.
     */
    modelValue: {
        type: Array as PropType<File[]>,
        default: undefined,
    },

    /**
     * If true, multiple files can be selected.
     */
    multiple: {
        type: Boolean,
        default: false,
    },

    /**
     * The value of the file area.
     * @deprecated Use `modelValue` instead.
     */
    value: {
        type: Boolean,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    input: [value: File[]];
    'model-compat:input': [value: File[]];
    'update:modelValue': [value: File[]];
}>();

//
// State
//
const area: Ref<HTMLElement | null> = ref(null);
const input: Ref<HTMLElement | null> = ref(null);

const dragCount = ref(0);
const dragInitialized = ref(false);
const firstFileName = ref('');
const filesCount = ref(0);

//
// Composables
//
const { formatCaption } = useLocale();

//
// Computed
//
const defaultText = computed(() =>
    filesCount.value > 0 ? (filesCount.value > 1 ? filesCount.value + ' files' : firstFileName.value) : 'Browse'
);

const dragOverArea = computed(() => dragCount.value > 0);

const on = computed(() => ({
    click: (): void => {
        handleDragAreaClicked();
    },
}));

//
// Watchers
//
watch(
    () => props.disableDrag,
    () => initializeDrag()
);

//
// Event Handlers
//
function onDragEnter(): void {
    dragCount.value++;
}

function onDragLeave(): void {
    if (dragCount.value > 0) {
        dragCount.value--;
    }
}

function onDragOver(e: DragEvent): void {
    e.preventDefault();
}

function onDragDrop(e: DragEvent): void {
    dragCount.value = 0;
    e.preventDefault();

    const files = e.dataTransfer?.files;
    if (files && files.length > 0) {
        emitFilesSelected(files);
    }
}

function onDragEnd(): void {
    dragCount.value = 0;
}

function handleInputChanged(event: Event): void {
    const target = event.target as HTMLInputElement;

    if (target.files && target.files.length > 0) {
        emitFilesSelected(target.files);
    }
}

//
// Helpers
//
function initializeDrag(): void {
    const dropElement = area.value as HTMLElement;

    if (dropElement) {
        if (!props.disableDrag) {
            if (!dragInitialized.value) {
                dropElement.addEventListener('dragend', onDragEnd);
                dropElement.addEventListener('dragenter', onDragEnter);
                dropElement.addEventListener('dragleave', onDragLeave);
                dropElement.addEventListener('dragover', onDragOver);
                dropElement.addEventListener('drop', onDragDrop);
                dragInitialized.value = true;
            }
        } else if (dragInitialized.value) {
            dropElement.removeEventListener('dragend', onDragEnd);
            dropElement.removeEventListener('dragenter', onDragEnter);
            dropElement.removeEventListener('dragleave', onDragLeave);
            dropElement.removeEventListener('dragover', onDragOver);
            dropElement.removeEventListener('drop', onDragDrop);
            dragInitialized.value = false;
        }
    }
}

function initializeFileInput(): void {
    const fileInput = input.value as HTMLElement;

    if (fileInput) {
        fileInput.addEventListener('change', (e) => {
            handleInputChanged(e);
        });
    }
}

function handleDragAreaClicked(): void {
    const fileInputElement = input.value as HTMLInputElement;
    if (fileInputElement) {
        fileInputElement.value = '';
        fileInputElement.click();
    }
}

function emitFilesSelected(fileList: FileList): void {
    let returnFiles: Array<File> = [];
    returnFiles = Array.from(fileList);

    if (!props.multiple) {
        returnFiles = returnFiles.slice(0, 1);
    }
    emit('input', returnFiles);
    emit('model-compat:input', returnFiles);
    emit('update:modelValue', returnFiles);
    firstFileName.value = returnFiles[0].name;
    filesCount.value = returnFiles.length;
}

//
// Lifecycle
//
onMounted(() => {
    initializeDrag();
    initializeFileInput();
});

//
// Expose
//
defineExpose({
    area,
    handleDragAreaClicked,
});
</script>
