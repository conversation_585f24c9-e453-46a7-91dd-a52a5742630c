<template>
    <WtgAlertModal v-model="model" :title="title" :sentiment="computedSentiment" persistent width="auto">
        <span id="message" style="white-space: pre-wrap">{{ message }}</span>
        <div v-if="isCustomTextConfirmationType" class="mt-2">
            <span id="custom" style="white-space: pre-wrap">{{ customMessage }}</span>
            <WtgTextField v-model="inputValue" class="mt-1" @input="inputValue = inputValue.toUpperCase()" />
        </div>
        <template #actions>
            <WtgButton
                v-for="(button, i) in actionButtons"
                :key="i"
                :min-width="88"
                :variant="button.isDefault ? 'fill' : undefined"
                :sentiment="button.isDefault ? 'primary' : undefined"
                :disabled="isButtonDisabled(button)"
                :loading="button.isLoading"
                @click="button.onClick"
            >
                {{ button.caption }}
            </WtgButton>
        </template>
    </WtgAlertModal>
</template>

<script setup lang="ts">
import { WtgAlertModal } from '@components/WtgAlertModal';
import { WtgButton } from '@components/WtgButton';
import { WtgTextField } from '@components/WtgTextField';
import { computed, PropType, ref } from 'vue';
import { ButtonType, MessageType } from './types';

const model = defineModel<boolean>({ default: false });
const inputValue = ref('');

const props = defineProps({
    buttons: {
        type: Array as PropType<ButtonType[]>,
        default: (): Array<ButtonType> => [],
    },
    customMessage: {
        type: String,
        default: '',
    },
    message: {
        type: String,
        default: '',
    },
    messageResultType: {
        type: String,
        default: 'Acknowledgement',
    },
    messageType: {
        type: Number as () => MessageType,
        default: MessageType.Success,
    },
    title: {
        type: String,
        default: '',
    },
});

const computedSentiment = computed(() => {
    switch (props.messageType) {
        case MessageType.Success:
            return 'success';
        case MessageType.Warning:
            return 'warning';
        case MessageType.Error:
            return 'error';
        case MessageType.Question:
            return 'question';
        case MessageType.MessageError:
            return 'message-error';
        default:
            return 'info';
    }
});

const isCustomTextConfirmationType = computed(() => {
    return props.messageResultType === 'CustomTextConfirmation';
});

const actionButtons = computed(() => {
    const cancelButton = props.buttons.filter((b) => !b.isDefault && b.caption === 'Cancel');
    const secondaryButtons = props.buttons.filter((b) => !b.isDefault && b.caption !== 'Cancel');
    const primaryButtons = props.buttons.filter((b) => b.isDefault);
    return [...cancelButton, ...secondaryButtons, ...primaryButtons];
});

function isButtonDisabled(button: ButtonType): boolean {
    return isCustomTextConfirmationType.value && button.isDefault && inputValue.value !== props.customMessage;
}
</script>
