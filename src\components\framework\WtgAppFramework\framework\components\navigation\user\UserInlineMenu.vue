<template>
    <WtgListGroup
        color=""
        role="menuitem"
        aria-haspopup="menu"
        :aria-label="application.ariaLabels.userAccountPrefix + ' ' + name"
    >
        <template #activator="{ isOpen }">
            <WtgListItem>
                <template #leading>
                    <WtgAvatar
                        v-if="currentUser.image.image"
                        :alt="currentUser.name"
                        :image="currentUser.image.image"
                        size="l"
                    />
                    <WtgIcon v-else>{{ currentUser.image.fallbackImage }}</WtgIcon>
                </template>
                {{ name }}
                <template #trailing>
                    <WtgIcon>
                        {{ isOpen ? 's-icon-caret-up' : 's-icon-caret-down' }}
                    </WtgIcon>
                </template>
            </WtgListItem>
        </template>
        <WtgListItem
            v-if="currentUser.onProfile"
            role="menuitem"
            :aria-label="application.captions.manageAccount"
            @click="onProfileClick()"
        >
            {{ application.captions.manageAccount }}
        </WtgListItem>
        <WtgListItem
            v-if="currentUser.onChangePassword"
            role="menuitem"
            :aria-label="application.captions.changePassword"
            @click="onPasswordClick"
        >
            {{ application.captions.changePassword }}
        </WtgListItem>
        <WtgListItem
            v-if="currentUser.onChangeBranchDepartment"
            role="menuitem"
            :aria-label="application.captions.changeBranchDepartment"
            @click="onChangeBranchClick()"
        >
            {{ application.captions.changeBranchDepartment }}
        </WtgListItem>
        <WtgListItem
            v-if="currentUser.onImpersonateContactUser"
            role="menuitem"
            :aria-label="application.captions.impersonateContactUser"
            @click="onImpersonateContactUserClick()"
        >
            {{ application.captions.impersonateContactUser }}
        </WtgListItem>
        <WtgListItem role="menuitem" :aria-label="appearanceText" @click="toggleAppearance()">
            {{ appearanceText }}
        </WtgListItem>
        <WtgDivider class="mt-2 mb-1" />
        <WtgListItem role="menuitem" :aria-label="application.captions.logOff" @click="onLogOffClick">
            {{ application.captions.logOff }}
        </WtgListItem>
    </WtgListGroup>
</template>

<script setup lang="ts">
import WtgAvatar from '@components/WtgAvatar';
import WtgDivider from '@components/WtgDivider';
import { WtgIcon } from '@components/WtgIcon';
import { WtgListGroup, WtgListItem } from '@components/WtgList';
import { WtgFrameworkUser } from '@components/framework/types';
import { useApplication } from '@composables/application';
import { useTheme } from '@composables/theme';
import { computed } from 'vue';

const application = useApplication();
const { darkOption, appearance } = useTheme();

const emit = defineEmits<{
    'item-click': [];
}>();

const appearanceText = computed((): string => {
    switch (appearance.value) {
        case 'dark':
            return application.captions.mutedMode;
        case 'muted':
            return application.captions.lightMode;
        default:
            return application.captions.darkMode;
    }
});

const currentUser = computed((): WtgFrameworkUser => {
    return (
        application.user ?? {
            name: '',
            emailAddress: '',
            image: { image: '', fallbackImage: '' },
        }
    );
});

const name = computed((): string => {
    return currentUser.value.name ?? '';
});

function onProfileClick(): void {
    emit('item-click');
    if (currentUser.value.onProfile) {
        currentUser.value.onProfile('_self');
    }
}

function onPasswordClick(): void {
    emit('item-click');
    if (currentUser.value.onChangePassword) {
        currentUser.value.onChangePassword('_self');
    }
}

function onChangeBranchClick(): void {
    emit('item-click');
    if (currentUser.value.onChangeBranchDepartment) {
        currentUser.value.onChangeBranchDepartment('_self');
    }
}

function onImpersonateContactUserClick(): void {
    emit('item-click');
    if (currentUser.value.onImpersonateContactUser) {
        currentUser.value.onImpersonateContactUser();
    }
}

function onLogOffClick(): void {
    emit('item-click');
    if (currentUser.value.onLogOff) {
        currentUser.value.onLogOff();
    }
}

function toggleAppearance(): void {
    emit('item-click');
    switch (appearance.value) {
        case 'light':
            appearance.value = 'dark';
            break;
        case 'dark':
            appearance.value = 'muted';
            break;
        case 'muted':
            appearance.value = 'light';
            break;
        default:
            appearance.value = darkOption ? 'dark' : 'light';
            break;
    }
}
</script>
