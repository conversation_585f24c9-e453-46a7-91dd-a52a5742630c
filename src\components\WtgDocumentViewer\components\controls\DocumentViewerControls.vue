<template>
    <div class="document-controls">
        <div class="controls-group-1">
            <WtgIconButton
                v-if="enableThumbnails"
                :icon="'s-icon-thumbnails'"
                variant="ghost"
                :tooltip="formatCaption('documentViewer.thumbnails')"
                @click="onThumbnailsClick"
            />
        </div>
        <div class="controls-group-2">
            <DocumentViewerPagination
                v-if="!isLoading && enablePagination"
                class="document-viewer-pagination"
                :page-count="pageCount"
                :active-page-number="activePageNumber"
                @active-page-updated="handleActivePageUpdate"
            />
            <div v-if="enableZoom" class="d-flex column">
                <WtgIconButton
                    icon="s-icon-zoom-out"
                    variant="ghost"
                    :tooltip="formatCaption('documentViewer.zoomOut')"
                    @click="onZoomOut"
                />
                <WtgSelectField
                    :model-value="internalZoomScaleOption"
                    :filterable="false"
                    :items="zoomScaleOptions"
                    item-text="label"
                    item-text-short="zoomPercentage"
                    item-value="scaleMode"
                    :style="{ 'max-width': '80px' }"
                    return-object
                    @update:model-value="handleZoomScaleOptionUpdate"
                />
                <WtgIconButton
                    icon="s-icon-zoom-in"
                    variant="ghost"
                    :tooltip="formatCaption('documentViewer.zoomIn')"
                    @click="onZoomIn"
                />
            </div>
            <WtgIconButton
                v-if="enableRotate"
                icon="s-icon-file-rotate"
                variant="ghost"
                :tooltip="formatCaption('documentViewer.rotate')"
                @click="onRotate"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { getScalingModeLabel, ScalingMode } from '@components/WtgDocumentViewer/types';
import WtgIconButton from '@components/WtgIconButton';
import WtgSelectField from '@components/WtgSelectField';
import { useLocale } from '@composables/locale';
import { PropType, ref, watch } from 'vue';
import DocumentViewerPagination from '../pagination/DocumentViewerPagination.vue';

interface ZoomScaleOption {
    label: string;
    zoomPercentage: string;
    scaleMode: ScalingMode;
}

const { formatCaption } = useLocale();

const props = defineProps({
    activePageNumber: { type: Number, default: 0 },
    enablePagination: { type: Boolean, default: true },
    enableRotate: { type: Boolean, default: true },
    enableThumbnails: { type: Boolean, default: true },
    enableZoom: { type: Boolean, default: true },
    isLoading: { type: Boolean, default: false },
    pageCount: { type: Number, default: 1 },
    rotationDegree: { type: Number, default: 0 },
    scalingMode: { type: String as PropType<ScalingMode>, default: ScalingMode.FitToScreen },
    zoomPercentage: { type: String, default: '' },
});

const emit = defineEmits<{
    'update-zoom': [string];
    'update-rotate': [number];
    thumbnails: [];
    'update-scale': [ScalingMode];
    'active-page-updated': [number];
}>();

const internalZoomPercentage = ref(props.zoomPercentage);
const internalRotationDegree = ref(props.rotationDegree);
const showPagination = ref(false);

const defaultScaleMode = props.scalingMode || ScalingMode.FitToScreen;
const internalScaleMode = ref(defaultScaleMode);
const internalZoomScaleOption = ref<ZoomScaleOption>({
    label: getScalingModeLabel(defaultScaleMode),
    zoomPercentage: '',
    scaleMode: defaultScaleMode,
});

const zoomScaleOptions = ref<ZoomScaleOption[]>([
    { label: getScalingModeLabel(ScalingMode.FitToScreen), zoomPercentage: '', scaleMode: ScalingMode.FitToScreen },
    {
        label: getScalingModeLabel(ScalingMode.FitHorizontally),
        zoomPercentage: '',
        scaleMode: ScalingMode.FitHorizontally,
    },
    { label: getScalingModeLabel(ScalingMode.FitVertically), zoomPercentage: '', scaleMode: ScalingMode.FitVertically },
    { label: getScalingModeLabel(ScalingMode.ActualSize), zoomPercentage: '', scaleMode: ScalingMode.ActualSize },
]);

const onZoomIn = () => {
    const currentZoom =
        internalZoomPercentage.value === '' ? 0 : parseInt(internalZoomPercentage.value.replace('%', ''), 10);
    const newZoom = currentZoom + 25;
    internalZoomPercentage.value = `${newZoom}%`;
    emit('update-zoom', internalZoomPercentage.value);
};

const onZoomOut = () => {
    const currentZoom =
        internalZoomPercentage.value === '' ? 0 : parseInt(internalZoomPercentage.value.replace('%', ''), 10);
    if (currentZoom > 25) {
        const newZoom = currentZoom - 25;
        internalZoomPercentage.value = `${newZoom}%`;
    }
    emit('update-zoom', internalZoomPercentage.value);
};

const onRotate = () => {
    internalRotationDegree.value = (internalRotationDegree.value + 90) % 360;
    emit('update-rotate', internalRotationDegree.value);
};

const onThumbnailsClick = () => {
    emit('thumbnails');
};

const onSetScale = (value: ScalingMode) => {
    emit('update-scale', value);
};

const handleActivePageUpdate = (newActivePageNumber: number) => {
    emit('active-page-updated', newActivePageNumber);
};

const handleZoomScaleOptionUpdate = (item: ZoomScaleOption) => {
    internalScaleMode.value = item.scaleMode;
    onSetScale(item.scaleMode);
};

watch(
    () => props.isLoading,
    (newIsLoading: boolean) => {
        showPagination.value = newIsLoading;
    }
);

watch(
    () => props.zoomPercentage,
    (newZoomPercentage: string) => {
        internalZoomPercentage.value = newZoomPercentage;
        const updatedInternalZoomScaleOption = {
            label: getScalingModeLabel(internalScaleMode.value),
            zoomPercentage: newZoomPercentage,
            scaleMode: internalScaleMode.value,
        };
        internalZoomScaleOption.value = updatedInternalZoomScaleOption;
        zoomScaleOptions.value = zoomScaleOptions.value.map((option) =>
            option.scaleMode === internalScaleMode.value
                ? { ...option, zoomPercentage: newZoomPercentage }
                : { ...option, zoomPercentage: '' }
        );
    }
);
</script>

<style scoped lang="scss">
.document-controls {
    display: flex;
    padding: 10px 15px;
    background: var(--s-neutral-bg-weak-default);
    border-bottom: 1px solid var(--s-neutral-border-weak-default);
    margin-top: 50px;

    .wtg-button {
        svg {
            margin-bottom: -4px;
            margin-left: -2px;
        }
    }

    > div {
        display: flex;
        flex: 1;

        &.controls-group-1 {
            margin-left: -8px;
            max-width: fit-content;
        }

        &.controls-group-2 {
            justify-content: center;
            align-self: center;
        }
    }
}
</style>
