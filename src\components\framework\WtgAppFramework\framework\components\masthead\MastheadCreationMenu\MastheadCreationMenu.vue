<template>
    <WtgDropdownButton v-if="show" variant="fill" sentiment="primary" leading-icon="s-icon-plus" @click="onClick">
        {{ caption }}
        <template #popover>
            <PopupMenuList :actions="actions" density="compact" />
        </template>
    </WtgDropdownButton>
</template>

<script setup lang="ts">
import WtgDropdownButton from '@components/WtgDropdownButton';
import { WtgActionItemData } from '@components/WtgMenu';
import { PopupMenuList } from '@components/WtgMenuBar/popup';
import { WtgFrameworkMenuItem } from '@components/framework/types';
import { useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { computed } from 'vue';

const application = useApplication();
const { isTabletOrMobile } = useFramework();

const caption = computed((): string => {
    return application.captions.addNew;
});

const menuItems = computed((): WtgFrameworkMenuItem[] => {
    return application.entityCreationMenu;
});

const actions = computed((): WtgActionItemData[] => {
    return menuItems.value.map((item) => actionFromItem(item));
});

const show = computed((): boolean => {
    return !isTabletOrMobile.value && actions.value.length > 0;
});

const singleItem = computed((): WtgActionItemData | undefined => {
    return actions.value[0] ?? undefined;
});

const onClick = (): void => {
    const action = singleItem.value;
    if (action?.click) {
        action.click();
    }
};

function actionFromItem(item: WtgFrameworkMenuItem): WtgActionItemData {
    return {
        caption: item.caption ?? '',
        icon: item.icon ?? '',
        href: item.href,
        to: item.to,
        actions: !item.items
            ? []
            : item.items.map((childItem: any) => {
                  return actionFromItem(childItem);
              }),
        click: item.onClick,
        heading: item.heading,
    };
}
</script>
