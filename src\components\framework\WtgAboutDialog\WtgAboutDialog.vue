<template>
    <WtgDialog v-model="model" width="600" persistent>
        <WtgForm>
            <div class="wtg-modal__title">
                <WtgLabel typography="title-md-default">{{ dialogTitle + ' ' + title }}</WtgLabel>
                <WtgSpacer />
                <WtgIconButton tooltip="close" icon="s-icon-close" variant="ghost" @click="onCloseClick" />
            </div>
            <div class="wtg-modal__content-canvas">
                <WtgPanel layout="grid">
                    <slot />
                </WtgPanel>
            </div>
            <div class="wtg-modal__actions">
                <WtgSpacer />
                <WtgButton :min-width="88" text outlined sentiment="primary" variant="fill" @click="onCloseClick">
                    {{ doneCaption }}
                </WtgButton>
            </div>
        </WtgForm>
    </WtgDialog>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgDialog from '@components/WtgDialog';
import WtgForm from '@components/WtgForm';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import WtgPanel from '@components/WtgPanel';
import WtgSpacer from '@components/WtgSpacer';
import { useLocale } from '@composables/locale';
import { computed } from 'vue';

defineProps({
    dialogTitle: {
        type: String,
        default: '',
    },
    title: {
        type: String,
        default: '',
    },
});
const { formatCaption } = useLocale();

const doneCaption = computed(() => formatCaption('dialog.done'));

const model = defineModel<boolean>({ default: false });

const onCloseClick = (): void => {
    model.value = false;
};
</script>
