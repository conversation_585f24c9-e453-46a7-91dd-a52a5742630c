import WtgFileInput from '@components/WtgFileInput/WtgFileInput.vue';
import WtgInput from '@components/WtgInput/WtgInput.vue';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import '@testing-library/jest-dom';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const testTextFile = new File(['content'], 'test.txt', { type: 'text/plain' });
const testImageFile = new File(['content'], 'test.png', { type: 'image/png' });

describe('WtgFileInput', () => {
    test('is able to mount and render', () => {
        const wrapper = mountComponent();
        expect(wrapper.exists()).toBe(true);
    });

    test('it passes the multiple property to the file input', async () => {
        const wrapper = mountComponent({
            propsData: {
                multiple: true,
            },
        });

        const fileInput = wrapper.get('input[type="file"]');

        expect(fileInput.attributes()).toHaveProperty('multiple');

        await wrapper.setProps({ multiple: false });
        expect(fileInput.attributes()).not.toHaveProperty('multiple');
    });

    test('it passes the acceptedFiles property to the file input', async () => {
        const wrapper = mountComponent({
            propsData: {
                acceptedFiles: 'image/*',
            },
        });

        const fileInput = wrapper.get('input[type="file"]');

        expect(fileInput.attributes('accept')).toEqual('image/*');

        await wrapper.setProps({ acceptedFiles: ['image/*', '.pdf'] });
        expect(fileInput.attributes('accept')).toEqual('image/*,.pdf');
    });

    test('it passes the required to the wtg-input', async () => {
        const wrapper = mountComponent({
            propsData: {
                label: 'some label',
            },
        });

        let input = wrapper.findComponent(WtgInput);
        expect(input.exists()).toBe(true);
        expect(input.props('required')).toBe(false);
        expect(input.text()).toContain('some label');

        await wrapper.setProps({
            required: true,
        });

        input = wrapper.findComponent(WtgInput);
        expect(input.exists()).toBe(true);
        expect(input.props('required')).toBe(true);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('it displays name of the selected file(s)', async () => {
        const wrapper = mountComponent({
            propsData: {
                modelValue: testTextFile,
            },
        });

        const label = wrapper.getComponent({ name: 'WtgLabel' });

        expect(label.text()).toBe('test.txt');

        await wrapper.setProps({
            modelValue: [testTextFile, testImageFile],
        });
        expect(label.text()).toBe('test.txt, test.png');
    });

    test('it emits single file as modelValue when multiple is set to false', async () => {
        const wrapper: VueWrapper<any> = mountComponent();

        const fileList = {
            0: testTextFile,
            length: 1,
            item: () => testTextFile,
        };

        wrapper.vm.onChange({ target: { files: fileList } });
        expect(wrapper.emitted()['update:modelValue'][0]).toEqual([testTextFile]);
    });

    test('it emits file array as modelValue when multiple is set to true', async () => {
        const wrapper: VueWrapper<any> = mountComponent({ propsData: { multiple: true } });

        const fileList = {
            0: testTextFile,
            1: testImageFile,
            length: 2,
            item: () => testTextFile,
        };

        wrapper.vm.onChange({ target: { files: fileList } });
        expect(wrapper.emitted()['update:modelValue'][0]).toEqual([[testTextFile, testImageFile]]);
    });

    test('it clears the internal input value when falsy value or empty array is passed to modelValue', async () => {
        const wrapper: VueWrapper<any> = mountComponent();
        const inputRef = wrapper.vm.inputRef;
        const spy = jest.spyOn(inputRef, 'value', 'set');

        await wrapper.setProps({
            modelValue: testTextFile,
        });
        expect(spy).toHaveBeenCalledTimes(0);

        await wrapper.setProps({
            modelValue: undefined,
        });
        expect(spy).toHaveBeenCalledTimes(1);

        await wrapper.setProps({
            modelValue: null,
        });
        expect(spy).toHaveBeenCalledTimes(2);

        await wrapper.setProps({
            modelValue: '',
        });
        expect(spy).toHaveBeenCalledTimes(3);

        await wrapper.setProps({
            modelValue: [],
        });
        expect(spy).toHaveBeenCalledTimes(4);
    });

    function mountComponent({ propsData = {}, slots = {}, provide = {} } = {}) {
        return mount(WtgFileInput, {
            propsData,
            global: {
                plugins: [wtgUi],
                provide,
            },
            slots,
        });
    }
});
