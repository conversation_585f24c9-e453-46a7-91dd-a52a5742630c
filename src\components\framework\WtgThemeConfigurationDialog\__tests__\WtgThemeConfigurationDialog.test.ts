import WtgThemeConfigurationDialog from '@components/framework/WtgThemeConfigurationDialog/WtgThemeConfigurationDialog.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { Base64 } from 'js-base64';
import { TextEncoder } from 'util';
import { nextTick, ref } from 'vue';
import { VDialog } from 'vuetify/components/VDialog';
import { ThemeOptions } from '../../../../theme';
import supplyTheme from '../../../../theme/presets/supply';
import WtgUi from '../../../../WtgUi';
global.TextEncoder = TextEncoder;

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

window.URL.createObjectURL = jest.fn().mockReturnValue('dummyBlob');

const dhlOptions = {
    themeVersion: 1,
    brandColor: '#F7CE46',
    brandColorDark: false,
    logoLightImage: `<svg width="216" height="51" viewBox="0 0 216 31" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 27.9H26.0697L24.5714 30H0V27.9Z" fill="white"/><path d="M215 30H183.087L184.735 27.9H215.15V30H215Z" fill="white"/><path d="M0 24H29.0662L27.4181 26.1H0V24Z" fill="white"/><path d="M0 20.1H32.0627L30.4146 22.2H0V20.1Z" fill="white"/><path d="M215 26.1H186.084L187.732 24H215.15V26.1H215Z" fill="white"/><path d="M190.578 20.1H215V22.2H188.93L190.578 20.1Z" fill="white"/><path d="M28.1672 30L41.9512 11.55C41.9512 11.55 57.2334 11.55 59.0314 11.55C60.9791 11.55 60.9791 12.3 59.9303 13.5C59.0313 14.7 57.3833 16.95 56.4843 18.15C56.0348 18.9 55.1359 19.95 57.9826 19.95C61.1289 19.95 80.9059 19.95 80.9059 19.95C79.108 22.65 72.8153 30.15 61.7282 30.15C52.7387 30 28.1672 30 28.1672 30Z" fill="white"/><path d="M107.125 20.1L99.6341 30.15H80.007L87.4982 20.1H107.125Z" fill="white"/><path d="M135.742 20.1L128.251 30.15H108.474L115.965 20.1H135.742Z" fill="white"/><path d="M142.185 20.1C142.185 20.1 140.686 22.05 140.087 22.95C137.54 26.25 139.787 30.15 147.878 30.15C157.167 30.15 179.641 30.15 179.641 30.15L187.132 20.1H142.185Z" fill="white"/><path d="M37.9059 0.15L31.0139 9.3C31.0139 9.3 66.5226 9.3 68.4704 9.3C70.4181 9.3 70.4181 10.05 69.3693 11.25C68.4704 12.45 66.8223 14.7 65.9233 15.9C65.4738 16.5 64.5749 17.7 67.4216 17.7C70.5679 17.7 82.7038 17.7 82.7038 17.7C82.7038 17.7 85.2509 14.4 87.1986 11.55C90.0453 7.8 87.4982 0 77.4599 0C68.4704 0.15 37.9059 0.15 37.9059 0.15Z" fill="white"/><path d="M137.39 17.7H89.1464L102.331 0.150024H122.108L114.617 10.2H123.456L130.948 0.150024H150.725L137.39 17.7Z" fill="white"/><path d="M177.993 0.150024L164.808 17.7H143.833L157.017 0.150024H177.993Z" fill="white"/></svg>`,
    logoDarkImage: `<svg width="215" height="50" viewBox="0 0 215 30" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_67_4445)"><path d="M0 27.75H26.0697L24.5714 29.85H0V27.75Z" fill="#D40511"/><path d="M215 29.85H183.087L184.735 27.75H215.15V29.85H215Z" fill="#D40511"/><path d="M0 23.85H29.0662L27.4181 25.95H0V23.85Z" fill="#D40511"/><path d="M0 19.95H32.0627L30.4146 22.05H0V19.95Z" fill="#D40511"/><path d="M215 25.95H186.084L187.732 23.85H215.15V25.95H215Z" fill="#D40511"/><path d="M190.578 19.95H215V22.05H188.93L190.578 19.95Z" fill="#D40511"/><path d="M28.1672 29.85L41.9512 11.4C41.9512 11.4 57.2334 11.4 59.0314 11.4C60.9791 11.4 60.9791 12.15 59.9303 13.35C59.0313 14.55 57.3833 16.8 56.4843 18C56.0348 18.75 55.1359 19.8 57.9826 19.8C61.1289 19.8 80.9059 19.8 80.9059 19.8C79.108 22.5 72.8153 30 61.7282 30C52.7387 29.85 28.1672 29.85 28.1672 29.85Z" fill="#D40511"/><path d="M107.125 19.95L99.6341 30H80.007L87.4982 19.95H107.125Z" fill="#D40511"/><path d="M135.742 19.95L128.251 30H108.474L115.965 19.95H135.742Z" fill="#D40511"/><path d="M142.185 19.95C142.185 19.95 140.686 21.9 140.087 22.8C137.54 26.1 139.787 30 147.878 30C157.167 30 179.641 30 179.641 30L187.132 19.95H142.185Z" fill="#D40511"/><path d="M37.9059 -2.44118e-05L31.0139 9.14998C31.0139 9.14998 66.5226 9.14998 68.4704 9.14998C70.4181 9.14998 70.4181 9.89998 69.3693 11.1C68.4704 12.3 66.8223 14.55 65.9233 15.75C65.4738 16.35 64.5749 17.55 67.4216 17.55C70.5679 17.55 82.7038 17.55 82.7038 17.55C82.7038 17.55 85.2509 14.25 87.1986 11.4C90.0453 7.64998 87.4982 -0.150024 77.4599 -0.150024C68.4704 -2.44118e-05 37.9059 -2.44118e-05 37.9059 -2.44118e-05Z" fill="#D40511"/><path d="M137.39 17.55H89.1464L102.331 0H122.108L114.617 10.05H123.456L130.948 0H150.725L137.39 17.55Z" fill="#D40511"/><path d="M177.993 0L164.808 17.55H143.833L157.017 0H177.993Z" fill="#D40511"/></g><defs><clipPath id="clip0_67_4445"><rect width="215" height="30" fill="white"/></clipPath></defs></svg>`,
    logoLightImageFileType: 'image/svg+xml',
    logoDarkImageFileType: 'image/svg+xml',
};

const fedExOptions = {
    themeVersion: 1,
    brandColor: '#471887',
    brandColorDark: true,
    logoLightImage: `<svg xmlns="http://www.w3.org/2000/svg" width="2500" height="993" viewBox="10 45.67 160.003 44.33"><path d="M169.018 84.244c0-2.465-1.748-4.27-4.156-4.27-2.404 0-4.154 1.805-4.154 4.27 0 2.461 1.75 4.263 4.154 4.263 2.408 0 4.156-1.805 4.156-4.263zm-5.248.219v2.789h-.901v-6.15h2.239c1.312 0 1.914.573 1.914 1.69 0 .688-.465 1.233-1.064 1.312v.026c.52.083.711.547.818 1.396.082.55.191 1.504.387 1.728h-1.066c-.248-.578-.223-1.396-.414-2.081-.158-.521-.436-.711-1.033-.711h-.875v.003l-.005-.002zm1.117-.795c.875 0 1.125-.466 1.125-.877 0-.486-.25-.87-1.125-.87h-1.117v1.749h1.117v-.002zm-5.17.576c0-3.037 2.411-5.09 5.141-5.09 2.738 0 5.146 2.053 5.146 5.09 0 3.031-2.407 5.086-5.146 5.086-2.73 0-5.141-2.055-5.141-5.086z" fill="#ff5a00"/><g fill="#ff5a00"><path d="M141.9 88.443l-5.927-6.647-5.875 6.647h-12.362l12.082-13.574-12.082-13.578h12.748l5.987 6.596 5.761-6.596h12.302l-12.022 13.521 12.189 13.631zM93.998 88.443V45.67h23.738v9.534h-13.683v6.087h13.683v9.174h-13.683v8.42h13.683v9.558z"/></g><path d="M83.98 45.67v17.505h-.111c-2.217-2.548-4.988-3.436-8.201-3.436-6.584 0-11.544 4.479-13.285 10.396-1.986-6.521-7.107-10.518-14.699-10.518-6.167 0-11.035 2.767-13.578 7.277V61.29H21.361v-6.085h13.91v-9.533H10v42.771h11.361V70.465h11.324a17.082 17.082 0 0 0-.519 4.229c0 8.918 6.815 15.185 15.516 15.185 7.314 0 12.138-3.437 14.687-9.694h-9.737c-1.316 1.883-2.316 2.439-4.949 2.439-3.052 0-5.686-2.664-5.686-5.818h19.826C62.683 83.891 68.203 90 75.779 90c3.268 0 6.26-1.607 8.089-4.322h.11v2.771h10.017V45.672H83.98v-.002zM42.313 70.593c.633-2.718 2.74-4.494 5.37-4.494 2.896 0 4.896 1.721 5.421 4.494H42.313zm35.588 11.341c-3.691 0-5.985-3.439-5.985-7.031 0-3.84 1.996-7.529 5.985-7.529 4.139 0 5.788 3.691 5.788 7.529 0 3.638-1.746 7.031-5.788 7.031z" fill="#ffffff"/></svg>`,
    logoDarkImage: `<svg xmlns="http://www.w3.org/2000/svg" width="2500" height="993" viewBox="10 45.67 160.003 44.33"><path d="M169.018 84.244c0-2.465-1.748-4.27-4.156-4.27-2.404 0-4.154 1.805-4.154 4.27 0 2.461 1.75 4.263 4.154 4.263 2.408 0 4.156-1.805 4.156-4.263zm-5.248.219v2.789h-.901v-6.15h2.239c1.312 0 1.914.573 1.914 1.69 0 .688-.465 1.233-1.064 1.312v.026c.52.083.711.547.818 1.396.082.55.191 1.504.387 1.728h-1.066c-.248-.578-.223-1.396-.414-2.081-.158-.521-.436-.711-1.033-.711h-.875v.003l-.005-.002zm1.117-.795c.875 0 1.125-.466 1.125-.877 0-.486-.25-.87-1.125-.87h-1.117v1.749h1.117v-.002zm-5.17.576c0-3.037 2.411-5.09 5.141-5.09 2.738 0 5.146 2.053 5.146 5.09 0 3.031-2.407 5.086-5.146 5.086-2.73 0-5.141-2.055-5.141-5.086z" fill="#ff5a00"/><g fill="#ff5a00"><path d="M141.9 88.443l-5.927-6.647-5.875 6.647h-12.362l12.082-13.574-12.082-13.578h12.748l5.987 6.596 5.761-6.596h12.302l-12.022 13.521 12.189 13.631zM93.998 88.443V45.67h23.738v9.534h-13.683v6.087h13.683v9.174h-13.683v8.42h13.683v9.558z"/></g><path d="M83.98 45.67v17.505h-.111c-2.217-2.548-4.988-3.436-8.201-3.436-6.584 0-11.544 4.479-13.285 10.396-1.986-6.521-7.107-10.518-14.699-10.518-6.167 0-11.035 2.767-13.578 7.277V61.29H21.361v-6.085h13.91v-9.533H10v42.771h11.361V70.465h11.324a17.082 17.082 0 0 0-.519 4.229c0 8.918 6.815 15.185 15.516 15.185 7.314 0 12.138-3.437 14.687-9.694h-9.737c-1.316 1.883-2.316 2.439-4.949 2.439-3.052 0-5.686-2.664-5.686-5.818h19.826C62.683 83.891 68.203 90 75.779 90c3.268 0 6.26-1.607 8.089-4.322h.11v2.771h10.017V45.672H83.98v-.002zM42.313 70.593c.633-2.718 2.74-4.494 5.37-4.494 2.896 0 4.896 1.721 5.421 4.494H42.313zm35.588 11.341c-3.691 0-5.985-3.439-5.985-7.031 0-3.84 1.996-7.529 5.985-7.529 4.139 0 5.788 3.691 5.788 7.529 0 3.638-1.746 7.031-5.788 7.031z" fill="#29007c"/></svg>`,
    logoLightImageFileType: 'image/svg+xml',
    logoDarkImageFileType: 'image/svg+xml',
};

const portals = [
    { text: '123 - GLOW 1.0 Hello World', value: '453ba235ef9d4525884e02a65c4ebfbe' },
    { text: '321 - GLOW Interactive Library', value: 'a552af60517f4e9b8659320b3db83bb0' },
    {
        text: 'DHL - Global Logistics and International Shipping',
        value: '5ec2675920444222bbc3968fba022102',
        themeOptions: JSON.parse(JSON.stringify(dhlOptions)),
    },
    {
        text: 'FedEx - FedEx Australia',
        value: 'e11e157004a242c2baa22f4dee7e55be',
        themeOptions: JSON.parse(JSON.stringify(fedExOptions)),
    },
];

const currentThemeOptions: ThemeOptions = {
    brandColor: '#333333',
    brandColorDark: true,
};

const emptyThemeOptions: any = {
    colors: {
        light: {
            app: {},
            controls: {},
        },
        dark: {
            app: {},
            controls: {},
        },
    },
    themeVersion: 1,
};

describe('WtgThemeConfigurationDialog', () => {
    let el: HTMLElement, wrapper: any;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        wrapper = mountComponent();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is WtgThemeConfigurationDialog', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('WtgThemeConfigurationDialog');
    });

    test('it passes its properties to the WtgDialog component', async () => {
        const props = wrapper.findComponent({ name: 'WtgDialog' }).props();
        expect(props.persistent).toBe(true);
        expect(props.maxWidth).toBe('500');
    });

    test('it brings up a dialog', async () => {
        await wrapper.setProps({ modelValue: true });
        const dialog = wrapper.findComponent(VDialog);
        expect(dialog.props('modelValue')).toBe(true);
    });

    describe('when the value property is set to true', () => {
        let manager: any, wrapper: any, portalsResolve: any, settingsResolve: any;

        beforeEach(async () => {
            manager = ref({
                currentContext: {
                    portal: 'portal-1',
                },
                loadPortalsAsync: jest.fn(),
                loadSettingsAsync: jest.fn(),
                loadThemeConfigurationAsync: jest.fn(),
                onSaveAsync: jest.fn(),
                updateThemeOptions: jest.fn(),
                portals: [],
                themeOptions: undefined,
                activePortal: 'portal-1',
            });

            manager.value.loadPortalsAsync.mockReturnValue(
                new Promise((resolve) => {
                    portalsResolve = resolve;
                })
            );

            manager.value.loadSettingsAsync.mockReturnValue(
                new Promise((resolve) => {
                    settingsResolve = resolve;
                })
            );

            wrapper = mountComponent({ propsData: { modelValue: true, manager: manager.value } });
            await wrapper.setData({ openTab: 1 });
        });

        test('it brings up a dialog', () => {
            const dialog = wrapper.findComponent({ name: 'WtgDialog' });
            expect(dialog.props('modelValue')).toBe(true);
        });

        test('it initializes the dialog fields on start up', () => {
            const colorFields = wrapper.findAllComponents({ name: 'wtg-color-field' });
            expect(colorFields.at(0)!.props('modelValue')).toBe('#0022CC');
            expect(colorFields.at(1)!.props('modelValue')).toBe('#371ee1');
            expect(colorFields.at(2)!.props('modelValue')).toBe('#371ee1');
            expect(colorFields.at(3)!.props('modelValue')).toBe('#7486DC');
            expect(colorFields.at(4)!.props('modelValue')).toBe('#161615');
            expect(colorFields.at(5)!.props('modelValue')).toBe('#9baed4');
        });

        describe('when loading portals', () => {
            let portalSelection: any, saveButton: any;

            beforeEach(async () => {
                portalSelection = wrapper.findAllComponents({ name: 'WtgSelectField' }).at(0);
                saveButton = wrapper.findComponent('[data-testid="save-button"]');
                expect(saveButton.text()).toBe('Save');

                await settingsResolve();
            });

            test('portals list should be loading', async () => {
                expect(portalSelection.props('loading')).toBe(true);
            });

            test('send button should be disabled', async () => {
                expect(saveButton.props('disabled')).toBe(true);
            });

            describe('is completed', () => {
                beforeEach(async () => {
                    await portalsResolve();
                });

                test('portals list should stop loading', async () => {
                    expect(portalSelection.props('loading')).toBe(false);
                });

                test('send button should be enabled', async () => {
                    expect(saveButton.props('disabled')).toBe(false);
                });
            });
        });

        describe('when loading settings', () => {
            let saveButton: any, colorFields: any;

            beforeEach(async () => {
                saveButton = wrapper.findComponent('[data-testid="save-button"]');
                expect(saveButton.text()).toBe('Save');
                colorFields = wrapper.findAllComponents({ name: 'wtg-color-field' });

                await portalsResolve();
            });

            test('color fields should be readonly', async () => {
                expect(colorFields.at(0).props('readonly')).toBe(true);
                expect(colorFields.at(1).props('readonly')).toBe(true);
            });

            test('send button should be disabled', async () => {
                expect(saveButton.props('disabled')).toBe(true);
            });

            describe('is completed', () => {
                beforeEach(async () => {
                    await settingsResolve();
                });

                test('color fields should not be readonly', async () => {
                    expect(colorFields.at(0).props('readonly')).toBe(false);
                    expect(colorFields.at(1).props('readonly')).toBe(false);
                });

                test('send button should be enabled', async () => {
                    expect(saveButton.props('disabled')).toBe(false);
                });
            });
        });

        describe('once portals/settings resolved', () => {
            beforeEach(async () => {
                await portalsResolve();
                await settingsResolve();

                manager.value.portals = [
                    { text: 'Portal 1', value: 'portal-1' },
                    { text: 'Portal 2', value: 'portal-2' },
                    { text: 'Portal 3', value: 'portal-3' },
                ];
                manager.value.themeOptions = {
                    colors: {
                        light: {
                            primary: '#333333',
                        },
                        dark: {
                            primary: '#666666',
                        },
                    },
                };
                await wrapper.setProps({ manager: manager.value });
            });

            test('it should render the list portals and set the default selection', () => {
                const portalSelection = wrapper.findAllComponents({ name: 'WtgSelectField' }).at(0);
                expect(portalSelection!.props('items').length).toBe(3);
                expect(portalSelection!.props('items')[0].text).toBe('Portal 1');
                expect(portalSelection!.props('items')[1].text).toBe('Portal 2');
                expect(portalSelection!.props('items')[2].text).toBe('Portal 3');
                expect(portalSelection!.props('modelValue')).toBe('portal-1');
            });

            describe('primary color field', () => {
                let colorField: any;

                beforeEach(() => {
                    colorField = wrapper.findAllComponents({ name: 'wtg-color-field' }).at(0);
                });

                test('sets the value', () => {
                    expect(colorField.props('modelValue')).toBe('#333333');
                });

                test('sets the default theme value to empty', async () => {
                    const savedThemeOptions = manager.value.themeOptions;
                    manager.value.themeOptions = {};
                    await nextTick();
                    expect(colorField.props('modelValue')).toBe('#0022CC');
                    manager.value.themeOptions = savedThemeOptions;
                });
            });

            test('it should call loadSettingsAsync when portal changed', async () => {
                const portalSelection = wrapper.findComponent('[data-testid="active-portal-field"]');
                expect(portalSelection.props('modelValue')).toBe('portal-1');

                await portalSelection.vm.selectItem({ text: 'Portal 2', value: 'portal-2' });

                expect(portalSelection.props('modelValue')).toBe('portal-2');
                expect(manager.value.loadSettingsAsync).toHaveBeenCalled();
                expect(manager.value.currentContext.portal).toBe('portal-2');
            });

            describe('when save is clicked', () => {
                let saveResolve: any, saveButton: any;

                beforeEach(async () => {
                    manager.value.onSaveAsync.mockReturnValue(
                        new Promise((resolve) => {
                            saveResolve = resolve;
                        })
                    );
                    saveButton = wrapper.findComponent('[data-testid="save-button"]');
                    expect(saveButton.text()).toBe('Save');
                    await wrapper.setData({
                        logo: 'light logo',
                        logoDark: 'dark logo',
                        brandColor: 'green',
                        brandColorDark: 'red',
                        darkBrand: false,
                        darkBrandDark: false,
                        brandLoginColor: 'blue',
                        brandLoginColorDark: 'yellow',
                        darkBrandLogin: false,
                        darkBrandLoginDark: false,
                    });
                    await saveButton.trigger('click');
                });

                test('it calls manager updateThemeOptions', () => {
                    expect(manager.value.updateThemeOptions).toHaveBeenCalledWith({
                        colors: {
                            dark: {
                                app: {
                                    brandColor: 'red',
                                    brandColorDark: false,
                                    brandLoginColor: 'yellow',
                                    brandLoginColorDark: false,
                                },
                                controls: {},
                                primary: '#666666',
                            },
                            light: {
                                app: {
                                    brandColor: 'green',
                                    brandColorDark: false,
                                    brandLoginColor: 'blue',
                                    brandLoginColorDark: false,
                                },
                                controls: {},
                                primary: '#333333',
                            },
                        },
                        logoDarkImage: 'dark logo',
                        logoDarkImageFileType: 'image/svg+xml',
                        logoLightImage: 'light logo',
                        logoLightImageFileType: 'image/svg+xml',
                        themeVersion: 1,
                    });
                });

                test('it calls manager onSaveAsync', () => {
                    expect(manager.value.onSaveAsync).toHaveBeenCalled();
                });

                test('button is loading', () => {
                    expect(saveButton.props('loading')).toBe(true);
                });

                describe('once onSaveAsync resolved', () => {
                    beforeEach(async () => {
                        await saveResolve();
                    });

                    test('stops loading save', () => {
                        expect(saveButton.props('loading')).toBe(false);
                    });
                });
            });

            describe('when save is clicked and only the dak switches have changed', () => {
                let saveButton: any;

                beforeEach(async () => {
                    manager.value.onSaveAsync.mockReturnValue(
                        new Promise((resolve) => {
                            resolve;
                        })
                    );
                    saveButton = wrapper.findComponent('[data-testid="save-button"]');
                    expect(saveButton.text()).toBe('Save');
                    await wrapper.setData({
                        logo: 'light logo',
                        logoDark: 'dark logo',
                        darkBrand: false,
                        darkBrandLogin: false,
                        darkBrandDark: false,
                        darkBrandLoginDark: false,
                    });
                    await saveButton.trigger('click');
                });

                test('it calls manager updateThemeOptions', () => {
                    expect(manager.value.updateThemeOptions).toHaveBeenCalledWith({
                        colors: {
                            dark: {
                                app: {
                                    brandColor: '#161615',
                                    brandColorDark: false,
                                    brandLoginColor: '#9baed4',
                                    brandLoginColorDark: false,
                                },
                                controls: {},
                                primary: '#666666',
                            },
                            light: {
                                app: {
                                    brandColor: '#371ee1',
                                    brandColorDark: false,
                                    brandLoginColor: '#371ee1',
                                    brandLoginColorDark: false,
                                },
                                controls: {},
                                primary: '#333333',
                            },
                        },
                        logoDarkImage: 'dark logo',
                        logoDarkImageFileType: 'image/svg+xml',
                        logoLightImage: 'light logo',
                        logoLightImageFileType: 'image/svg+xml',
                        themeVersion: 1,
                    });
                });
            });
        });

        test('when resetting the theme then should undo user changes and restore startup theme', async () => {
            await wrapper.setData({
                logo: new TextEncoder().encode('light logo'),
                logoDark: new TextEncoder().encode('dark logo'),
            });
            expect(wrapper.props('manager').themeOptions).not.toBe(supplyTheme);

            const reset = wrapper.findComponent('[data-testid="reset-button"]');
            expect(reset.text()).toBe('Reset');
            await reset.trigger('click');
            expect(manager.value.updateThemeOptions).toHaveBeenCalledWith(undefined);
            expect(wrapper.props('manager').themeOptions).toBe(undefined);
            expect(wrapper.vm.$data.logo).toEqual(supplyTheme.logoLightImage);
            expect(wrapper.vm.$data.logoDark).toEqual(supplyTheme.logoDarkImage);
            expect(wrapper.vm.themeOptionsFromDialogOptions()).toStrictEqual(emptyThemeOptions);
        });

        test('when canceling then should close dialog', async () => {
            const dialog = wrapper.findComponent({ name: 'WtgDialog' });
            const cancel = wrapper.findComponent('[data-testid="cancel-button"]');
            expect(cancel.text()).toBe('Cancel');
            await cancel.trigger('click');
            expect(dialog.props('modelValue')).toBe(false);
        });

        describe('when update light theme logos', () => {
            beforeEach(async () => {
                await wrapper.setData({ openTab: 0 });
            });

            test('onLogoSelectClick should be called when select button is clicked', async () => {
                const onLogoSelectClick = jest.spyOn(wrapper.vm, 'onLogoSelectClick');
                const selectButton = wrapper.findComponent('[data-testid="select-light-logo-button"]');
                selectButton.trigger('click');
                expect(onLogoSelectClick).toBeCalledWith('light');
            });

            test('onLogoSelectClick should open file explorer', () => {
                const fileInput = wrapper.vm.$refs[`light-logo-input`] as HTMLInputElement;
                expect(fileInput).toBeTruthy();
                jest.spyOn(fileInput, 'click');
                wrapper.vm.onLogoSelectClick('light');
                expect(fileInput.value).toBe('');
                expect(fileInput.click).toBeCalled();
            });

            test('light logo should be updated via svg file', () => {
                const testFileString = 'This is test file';
                const testFile = new TextEncoder().encode(testFileString);
                const blob = new Blob([testFile]);
                const file = new File([blob], 'values.svg', {
                    type: 'image/svg+xml',
                });
                const event = {
                    target: {
                        files: [file],
                        result: testFile,
                    },
                };
                const updateLogo = jest.spyOn(wrapper.vm, 'updateLogo');
                const readAsArrayBuffer = jest.spyOn(FileReader.prototype, 'readAsArrayBuffer');
                jest.spyOn(FileReader.prototype, 'addEventListener').mockImplementation(() => {
                    wrapper.vm.updateLogo(event.target, 'light');
                });
                wrapper.vm.onLogoChange(event, 'light');
                expect(readAsArrayBuffer).toHaveBeenCalled();
                expect(updateLogo).toHaveBeenCalled();

                const decodedLogo = Base64.decode(wrapper.vm.logo);
                expect(decodedLogo).toBe(testFileString);
            });

            test('light logo should be updated via png file', () => {
                const testFileString = 'This is test file';
                const testFile = new TextEncoder().encode(testFileString);
                const blob = new Blob([testFile]);
                const file = new File([blob], 'values.png', {
                    type: 'image/png',
                });
                const event = {
                    target: {
                        files: [file],
                        result: testFile,
                    },
                };
                const updateLogo = jest.spyOn(wrapper.vm, 'updateLogo');
                const readAsArrayBuffer = jest.spyOn(FileReader.prototype, 'readAsArrayBuffer');
                jest.spyOn(FileReader.prototype, 'addEventListener').mockImplementation(() => {
                    wrapper.vm.updateLogo(event.target, 'light');
                });
                wrapper.vm.onLogoChange(event, 'light');
                expect(readAsArrayBuffer).toHaveBeenCalled();
                expect(updateLogo).toHaveBeenCalled();

                const decodedLogo = Base64.decode(wrapper.vm.logo);
                expect(decodedLogo).toBe(testFileString);
            });
        });

        describe('when update dark theme logos', () => {
            beforeEach(async () => {
                await wrapper.setData({ openTab: 0 });
            });

            test('onLogoSelectClick should be called when select button is clicked', () => {
                const onLogoSelectClick = jest.spyOn(wrapper.vm, 'onLogoSelectClick');
                const selectButton = wrapper.findComponent('[data-testid="select-dark-logo-button"]');
                selectButton.trigger('click');
                expect(onLogoSelectClick).toBeCalledWith('dark');
            });

            test('onLogoSelectClick should open file explorer', () => {
                const fileInput = wrapper.vm.$refs[`dark-logo-input`] as HTMLInputElement;
                expect(fileInput).toBeTruthy();
                jest.spyOn(fileInput, 'click');
                wrapper.vm.onLogoSelectClick('dark');
                expect(fileInput.value).toBe('');
                expect(fileInput.click).toBeCalled();
            });

            test('dark logo should be updated via svg', () => {
                const testFileString = 'This is test file';
                const testFile = new TextEncoder().encode(testFileString);
                const blob = new Blob([testFile]);
                const file = new File([blob], 'values.svg', {
                    type: 'image/svg+xml',
                });
                const event = {
                    target: {
                        files: [file],
                        result: testFile,
                    },
                };
                const updateLogo = jest.spyOn(wrapper.vm, 'updateLogo');
                const readAsArrayBuffer = jest.spyOn(FileReader.prototype, 'readAsArrayBuffer');
                jest.spyOn(FileReader.prototype, 'addEventListener').mockImplementation(() => {
                    wrapper.vm.updateLogo(event.target, 'dark');
                });
                wrapper.vm.onLogoChange(event, 'dark');
                expect(readAsArrayBuffer).toHaveBeenCalled();
                expect(updateLogo).toHaveBeenCalled();

                const decodedLogoDark = Base64.decode(wrapper.vm.logoDark);
                expect(decodedLogoDark).toEqual(testFileString);
            });

            test('dark logo should be updated via png', () => {
                const testFileString = 'This is test file';
                const testFile = new TextEncoder().encode(testFileString);
                const blob = new Blob([testFile]);
                const file = new File([blob], 'values.png', {
                    type: 'image/png',
                });
                const event = {
                    target: {
                        files: [file],
                        result: testFile,
                    },
                };
                const updateLogo = jest.spyOn(wrapper.vm, 'updateLogo');
                const readAsArrayBuffer = jest.spyOn(FileReader.prototype, 'readAsArrayBuffer');
                jest.spyOn(FileReader.prototype, 'addEventListener').mockImplementation(() => {
                    wrapper.vm.updateLogo(event.target, 'dark');
                });
                wrapper.vm.onLogoChange(event, 'dark');
                expect(readAsArrayBuffer).toHaveBeenCalled();
                expect(updateLogo).toHaveBeenCalled();

                const decodedLogoDark = Base64.decode(wrapper.vm.logoDark);
                expect(decodedLogoDark).toEqual(testFileString);
            });
        });

        describe('when a portal is selected in the copy theme from dropdown', () => {
            let colorFields: any;

            beforeEach(async () => {
                colorFields = wrapper.findAllComponents({ name: 'wtg-color-field' });

                manager.value.portals = [
                    { text: 'Portal 1', value: 'portal-1' },
                    { text: 'Portal 2', value: 'portal-2' },
                    { text: 'Portal 3', value: 'portal-3' },
                ];
                manager.value.loadThemeConfigurationAsync.mockResolvedValue({
                    colors: {
                        light: {
                            primary: '#333333',
                            app: {
                                brandColor: '#444444',
                                brandLoginColor: '#555555',
                            },
                        },
                        dark: {
                            primary: '#666666',
                            app: {
                                brandColor: '#777777',
                                brandLoginColor: '#888888',
                            },
                        },
                    },
                });
            });

            test('updates dialog options correctly', async () => {
                const portalSelection = wrapper.findComponent('[data-testid="copy-from-field"]') as any;

                await portalSelection.vm.selectItem({ text: 'Portal 2', value: 'portal-2' });

                expect(portalSelection.props('modelValue')).toBe('portal-2');
                expect(manager.value.loadThemeConfigurationAsync).toHaveBeenCalled();

                await nextTick();
                await nextTick();

                expect(colorFields.at(0).props('modelValue')).toBe('#333333');
                expect(colorFields.at(1).props('modelValue')).toBe('#444444');
                expect(colorFields.at(2).props('modelValue')).toBe('#555555');
                expect(colorFields.at(3).props('modelValue')).toBe('#666666');
                expect(colorFields.at(4).props('modelValue')).toBe('#777777');
                expect(colorFields.at(5).props('modelValue')).toBe('#888888');
            });
        });
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgThemeConfigurationDialog, {
            propsData: {
                manager: {
                    currentContext: {
                        portal: 'portal-1',
                    },
                    loadPortalsAsync: jest.fn(),
                    loadSettingsAsync: jest.fn(),
                    loadThemeConfigurationAsync: jest.fn(),
                    onSaveAsync: jest.fn(),
                    updateThemeOptions: jest.fn(),
                    portals: [],
                    themeOptions: undefined,
                    activePortal: 'portal-1',
                } as any,
                ...propsData,
                eager: true,
                transition: 'none',
                portals,
                themeOptions: currentThemeOptions,
                activePortal: '453ba235ef9d4525884e02a65c4ebfbe',
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
