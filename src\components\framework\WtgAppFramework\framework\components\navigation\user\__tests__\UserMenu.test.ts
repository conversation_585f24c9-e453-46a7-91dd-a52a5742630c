import { WtgFramework } from '@components/framework/types';
import WtgCallout from '@components/WtgCallout';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import UserMenu from '../UserMenu.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi({
    language: {
        captionProvider: (languageCode: string, key: string, params?: (string | number)[]) => `C:${key}${params}`,
    },
});

describe('user-menu', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.user = {
            name: 'Test User',
            orgCode: 'TESTORG',
            emailAddress: '<EMAIL>',
            image: {
                image: "https://localhost/Glow/Global/GlbStaffPhotos(guid'99dd9437-ae1c-41f3-ba78-3a1998bfacb2')/TU_ProfilePhoto",
            },
            onLogOff: jest.fn(),
        } as any;
        application.captions = {
            manageAccount: 'Manage my account',
            changePassword: 'Change Password',
            changeBranchDepartment: 'Change branch/department',
            impersonateContactUser: 'Impersonate Contact User',
            configure: 'Configure',
            theme: 'Theme',
            darkMode: 'Dark Mode',
            lightMode: 'Light Mode',
            mutedMode: 'Muted Mode',
            logOff: 'Logoff',
        } as any;
        setApplication(application);

        wtgUi.breakpoint.mdAndDown = false;
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is UserMenu', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('UserMenu');
    });

    test('it renders a list item with the users name & profile picture to active the menu', async () => {
        const wrapper = await mountComponentAsync();
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        const avatar = listItem.findComponent({ name: 'WtgAvatar' });
        const title = listItem.find('.wtg-list-item__container');
        expect(avatar.exists()).toBe(true);
        expect(title.exists()).toBe(true);
        expect(avatar.vm.$props.alt).toBe(application.user.name);
        expect(title.text()).toBe(application.user.name);
        expect(avatar.vm.$props.image).toBe(application.user.image.image);
    });

    test('it renders a list item with aria properties for accessibility', async () => {
        const wrapper = await mountComponentAsync();
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        expect(listItem.attributes('role')).toBe('menuitem');
        expect(listItem.attributes('aria-label')).toBe('User Account for Test User');
        expect(listItem.attributes('aria-haspopup')).toBe('menu');
    });

    describe('when no user profile image available', () => {
        beforeEach(() => {
            application.user = {
                name: 'Test User',
                image: {
                    image: '',
                    fallbackImage: 's-icon-user',
                },
                onLogOff: jest.fn(),
            } as any;
        });

        test('renders fallbackImage as an icon', async () => {
            const wrapper = await mountComponentAsync();
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            expect(listItem.exists()).toBe(true);
            const avatar = listItem.findComponent({ name: 'WtgAvatar' });
            expect(avatar.exists()).toBe(false);
            const image = listItem.findComponent({ name: 'WtgIcon' });
            expect(image.exists()).toBe(true);
        });
    });

    describe('when open and no user handlers exist', () => {
        let wrapper: any;

        beforeEach(async () => {
            wrapper = await mountComponentAsync();
            application.navDrawer.visible = true;
            const listTitle = wrapper.find('.wtg-list-item__container');
            await listTitle.trigger('click');
        });

        test('it will not display user items without a handler', () => {
            expect(wrapper.html()).not.toContain(application.captions.manageAccount);
            expect(wrapper.html()).not.toContain(application.captions.changePassword);
            expect(wrapper.html()).not.toContain(application.captions.impersonateContactUser);
        });

        test('it will display the user info, appearance toggle & log off user item and call user onLogOff', async () => {
            const list = wrapper.findComponent({ name: 'WtgList' });
            const userMenuItems = list.findAllComponents({ name: 'WtgListItem' });
            const orgCodeChip = userMenuItems.at(0).findComponent({ name: 'WtgTag' });
            expect(userMenuItems.length).toBe(3);
            expect(userMenuItems.at(0).text()).toContain(application.user.name);
            expect(userMenuItems.at(0).text()).toContain(application.user.emailAddress);
            expect(orgCodeChip.text()).toBe(application.user.orgCode);
            expect(userMenuItems.at(1).find('.wtg-list-item__container').text()).toBe(application.captions.darkMode);
            expect(userMenuItems.at(2).find('.wtg-list-item__container').text()).toBe(application.captions.logOff);
        });

        test('it will toggle the appearance mode when the option is clicked', async () => {
            expect(wtgUi.dark).toBe(false);
            expect(wtgUi.appearance).toBe('light');
            const list = wrapper.findComponent({ name: 'WtgList' });
            const userMenuItems = list.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(1).trigger('click');

            expect(wtgUi.dark).toBe(true);
            expect(wtgUi.appearance).toBe('dark');
            await nextTick();
            await userMenuItems.at(1).trigger('click');

            expect(wtgUi.dark).toBe(false);
            expect(wtgUi.appearance).toBe('muted');
            await nextTick();
            await userMenuItems.at(1).trigger('click');

            expect(wtgUi.dark).toBe(false);
            expect(wtgUi.appearance).toBe('light');
        });

        test('it will display a log off user item and call user onLogOff', async () => {
            const list = wrapper.findComponent({ name: 'WtgList' });
            const userMenuItems = list.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(2).trigger('click');

            expect(application.user.onLogOff).toHaveBeenCalledTimes(1);
        });
    });

    describe('when open and user handlers exist', () => {
        let wrapper: any;

        beforeEach(async () => {
            application.user.onChangePassword = jest.fn();
            application.user.onProfile = jest.fn();
            application.user.onChangeBranchDepartment = jest.fn();
            application.user.onImpersonateContactUser = jest.fn();
            wrapper = await mountComponentAsync();
            const listTitle = wrapper.find('.wtg-list-item__container');
            await listTitle.trigger('click');

            const list = wrapper.findComponent({ name: 'WtgList' });
            const userMenuItems = list.findAll('.wtg-list-item__container');
            expect(userMenuItems.length).toBe(7);
            await userMenuItems.at(5).trigger('click');
        });

        test('it will display user items with a handler', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(userMenuItems.at(2).find('.wtg-list-item__container').text()).toBe(
                application.captions.manageAccount
            );
            expect(userMenuItems.at(3).find('.wtg-list-item__container').text()).toBe(
                application.captions.changePassword
            );
            expect(userMenuItems.at(4).find('.wtg-list-item__container').text()).toBe(
                application.captions.changeBranchDepartment
            );
            expect(userMenuItems.at(5).find('.wtg-list-item__container').text()).toBe(
                application.captions.impersonateContactUser
            );
        });

        test('it will call user onProfile when my profile is clicked', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(2).trigger('click');

            expect(application.user.onProfile).toHaveBeenCalledTimes(1);
        });

        test('it will call user changePassword when change password is clicked', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(3).trigger('click');

            expect(application.user.onChangePassword).toHaveBeenCalledTimes(1);
        });

        test('it will call user changeBranchDepartment when change branch department is clicked', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(4).trigger('click');

            expect(application.user.onChangeBranchDepartment).toHaveBeenCalledTimes(1);
        });

        test('it will call user impersonateContactUser when impersonate contact user is clicked', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(5).trigger('click');

            expect(application.user.onImpersonateContactUser).toHaveBeenCalledTimes(1);
        });

        describe('Accessibility', () => {
            test('it renders the menu with aria properties', () => {
                const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                expect(userMenuItems.at(2).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(2).attributes('aria-label')).toBe(application.captions.manageAccount);
                expect(userMenuItems.at(3).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(3).attributes('aria-label')).toBe(application.captions.changePassword);
                expect(userMenuItems.at(4).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(4).attributes('aria-label')).toBe(application.captions.changeBranchDepartment);
                expect(userMenuItems.at(5).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(5).attributes('aria-label')).toBe(application.captions.impersonateContactUser);
                expect(userMenuItems.at(6).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(6).attributes('aria-label')).toBe(application.captions.darkMode);
                expect(userMenuItems.at(7).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(7).attributes('aria-label')).toBe(application.captions.logOff);
            });
        });
    });

    describe('when user has no organization code', () => {
        let wrapper: any;
        beforeEach(async () => {
            application.user = {
                name: 'Test User',
                image: {
                    image: '',
                    fallbackImage: 's-icon-user',
                },
                onLogOff: jest.fn(),
            } as any;

            wrapper = await mountComponentAsync();
            application.navDrawer.visible = true;
            const listTitle = wrapper.find('.wtg-list-item__container');
            await listTitle.trigger('click');
        });

        test('renders no org code', async () => {
            const list = wrapper.findComponent({ name: 'WtgList' });
            const userMenuItems = list.findAllComponents({ name: 'WtgListItem' });
            const orgCodeChip = userMenuItems.at(0)?.findComponent({ name: 'WtgChip' });
            expect(userMenuItems.length).toBe(3);
            expect(orgCodeChip?.exists()).toBeFalsy();
        });
    });

    describe('when is impersonated', () => {
        let wrapper: any;
        beforeEach(async () => {
            application.user.isImpersonated = true;
            wrapper = await mountComponentAsync();
            const listTitle = wrapper.find('.wtg-list-item__container');
            await listTitle.trigger('click');
        });

        test('it will display impersonating warning', () => {
            const userMenuItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(2);
            expect(userMenuItem.findComponent(WtgCallout).exists()).toBe(false);
            const alertBox = wrapper.findComponent(WtgCallout);
            expect(alertBox.exists()).toBe(true);
            expect(alertBox.props('description')).toBe('C:userImpersonation.impersonatingWarning');
        });
    });

    describe('on mobile', () => {
        beforeEach(() => {
            wtgUi.breakpoint.mdAndDown = true;
        });

        test('it will render user menu item component', async () => {
            const wrapper = await mountComponentAsync();
            const menuItem = wrapper.findComponent({ name: 'UserInlineMenu' });
            const menu = wrapper.findComponent({ name: 'WtgPopover' });
            expect(menuItem.exists()).toBe(true);
            expect(menu.exists()).toBe(false);
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(UserMenu) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(UserMenu);
    }
});
