<template>
    <canvas ref="canvas" style="width: 100%" />
</template>

<script setup lang="ts">
import UTIF from 'utif';
import { onMounted, PropType, ref, Ref } from 'vue';

//
// Properties
//
const props = defineProps({
    buffer: {
        type: undefined,
        required: true,
    },
    ifd: {
        type: Object as PropType<UTIF.IFD>,
        required: true,
    },
});

//
// Emits
//
const emit = defineEmits<{
    error: [];
    'tif-decode-error': [];
}>();

//
// State
//
const canvas: Ref<HTMLCanvasElement | null> = ref(null);

//
// Lifecycle
//
onMounted(() => {
    const page = props.ifd;
    try {
        UTIF.decodeImage(props.buffer as ArrayBuffer, page);
    } catch (err) {
        emit('error');
        return;
    }
    if (!page.width) {
        emit('tif-decode-error');
        return;
    }

    if (!canvas.value) {
        emit('error');
        return;
    }
    const rgba = UTIF.toRGBA8(page);
    canvas.value.width = page.width;
    canvas.value.height = page.height;

    const ctx = canvas.value.getContext('2d');
    if (!ctx) {
        emit('error');
        return;
    }

    const imageData = ctx.createImageData(page.width, page.height);
    for (let i = 0; i < rgba.length; i++) {
        imageData.data[i] = rgba[i];
    }
    ctx.putImageData(imageData, 0, 0);
});
</script>
