<template>
    <WtgDrawer
        :width="width"
        order="2"
        :model-value="value"
        dismissible
        :title="title"
        :status="currentStatus"
        @input="onInput"
        @close="onClose"
        @update:width="onUpdateWidth"
    >
        <template #toolbar>
            <WtgIconButton
                v-if="!!task.entityUrl"
                :icon="task.isFavorite ? 's-icon-star-filled' : 's-icon-star-empty'"
                :color="task.isFavorite ? 'gold' : ''"
                :tooltip="formatCaption('drawer.toggleFavorite')"
                variant="ghost"
                @click="task.toggleFavorite()"
            />
            <WtgIconButton
                v-if="!!task.entityUrl"
                :icon="linkCopied ? 's-icon-check' : 's-icon-link'"
                :tooltip="formatCaption('drawer.copyLink')"
                @click="onCopyClick"
            />
            <WtgIconButton
                v-if="!!task.entityUrl"
                :href="task.entityUrl"
                icon="s-icon-new-window"
                target="_blank"
                :tooltip="formatCaption('drawer.openInNewTab')"
            />
            <TaskActionsOverflow v-if="showTaskActions" :task="currentTask" class="order-last order-sm-0" />
        </template>
        <template v-if="tabInfo" #tabs>
            <div class="flex-shrink-1" style="min-width: 0">
                <WtgTabs
                    v-model="tabInfo.current"
                    background-color="transparent"
                    show-arrows
                    height="28px"
                    class="mx-0"
                >
                    <WtgTab v-for="(tab, index) in tabInfo.tabs" :key="index">
                        {{ tab.caption }}
                    </WtgTab>
                </WtgTabs>
            </div>
        </template>
        <slot />
        <template #actions>
            <DesktopBar v-if="$wtgMaterialUi.breakpoint.smAndUp" :task="currentTask" />
            <MobileBar v-else :task="currentTask" />
        </template>
    </WtgDrawer>
</template>

<script lang="ts">
import { WtgFrameworkTask, WtgFrameworkTaskStandardAction, WtgFrameworkTaskStatus } from '@components/framework/types';
import DesktopBar from '@components/framework/WtgAppFramework/framework/components/actionBar/DesktopBar.vue';
import MobileBar from '@components/framework/WtgAppFramework/framework/components/actionBar/MobileBar.vue';
import TaskActionsOverflow from '@components/framework/WtgAppFramework/framework/components/entityActions/TaskActionsOverflow.vue';
import WtgDrawer from '@components/WtgDrawer';
import WtgIconButton from '@components/WtgIconButton';
import { WtgTab, WtgTabs } from '@components/WtgTabs';
import { createAppLevelTabs, hasTaskActions, setCurrentTask, useApplication } from '@composables/application';
import { useLocale } from '@composables/locale';
import { computed, defineComponent, PropType } from 'vue';

export default defineComponent({
    name: 'WtgAppFrameworkTaskDrawer',
    components: {
        WtgDrawer,
        TaskActionsOverflow,
        WtgIconButton,
        WtgTab,
        WtgTabs,
        DesktopBar,
        MobileBar,
    },
    props: {
        task: {
            type: Object as PropType<WtgFrameworkTask>,
            default: (): WtgFrameworkTask => new WtgFrameworkTask(),
        },
        value: {
            type: Boolean,
            default: false,
        },
        width: {
            type: [String, Number],
            default: '',
        },
    },
    emits: { 'update:width': null, input: null },
    setup(props, { emit }) {
        const { formatCaption } = useLocale();
        const currentTask = computed(() => props.task);
        const application = useApplication();
        setCurrentTask(currentTask);
        const showTaskActions = hasTaskActions(currentTask);

        const title = computed((): string => {
            return props.task.entityName ? props.task.entityName + ' -  ' + props.task.title : props.task.title;
        });

        const onInput = (value: string): void => {
            emit('input', value);
        };

        const currentStatus = computed((): WtgFrameworkTaskStatus | undefined => props.task.currentStatus);

        const tabInfo = createAppLevelTabs();

        return {
            application,
            currentStatus,
            currentTask,
            formatCaption,
            onInput,
            showTaskActions,
            tabInfo,
            title,
        };
    },
    data() {
        return {
            linkCopied: false,
        };
    },
    computed: {
        cancelAction(): WtgFrameworkTaskStandardAction {
            return this.task.cancelAction;
        },
    },
    methods: {
        onCopyClick() {
            const { origin, pathname } = window.location;
            const url = origin + pathname + this.task.entityUrl;
            navigator.clipboard.writeText(url);
            this.linkCopied = true;
            setTimeout(() => {
                this.linkCopied = false;
            }, 3000);
        },
        onClose(): void {
            this.cancelAction.onInvoke();
        },
        onUpdateWidth(width: string | number): void {
            this.$emit('update:width', width);
        },
    },
});
</script>
