import { TaskActionsOverflow } from '@components/framework/WtgAppFramework/framework/components/entityActions';
import WtgButton from '@components/WtgButton/WtgButton.vue';
import WtgContainer from '@components/WtgContainer/WtgContainer.vue';
import WtgDateField from '@components/WtgDateField/WtgDateField.vue';
import WtgDrawer from '@components/WtgDrawer/WtgDrawer.vue';
import WtgIconButton from '@components/WtgIconButton/WtgIconButton.vue';
import WtgPanel from '@components/WtgPanel/WtgPanel.vue';
import WtgTabs from '@components/WtgTabs';
import WtgTab from '@components/WtgTabs/WtgTab.vue';
import WtgTabsWindow from '@components/WtgTabs/WtgTabsWindow.vue';
import WtgTabsWindowItem from '@components/WtgTabs/WtgTabsWindowItem.vue';
import WtgTextArea from '@components/WtgTextArea/WtgTextArea.vue';
import WtgTextField from '@components/WtgTextField/WtgTextField.vue';
import { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import { WtgApp, WtgFrameworkTask } from '../../..';

type Story = StoryObj<typeof WtgDrawer>;
const meta: Meta<typeof WtgDrawer> = {
    title: 'Components/Drawer',
    component: WtgDrawer,
    parameters: {
        layout: 'fullscreen',
    },
};

export default meta;

export const Default: Story = {
    args: {
        title: 'Title',
        status: {
            code: 'TEST',
            label: 'Status',
            sentiment: undefined,
            variant: undefined,
        },
        dismissible: true,
    },
    render: (args) => ({
        components: {
            WtgContainer,
            WtgDateField,
            WtgDrawer,
            WtgPanel,
            WtgTabs,
            WtgTab,
            WtgTabsWindow,
            WtgTabsWindowItem,
            WtgTextArea,
            WtgTextField,
            WtgButton,
            WtgIconButton,
            TaskActionsOverflow,
        },
        setup: () => {
            const isFavorite = ref(false);
            const task = ref(new WtgFrameworkTask());
            task.value.showEDocsAction.visible = true;
            task.value.documents.visible = true;
            task.value.showLogsAction.visible = true;
            task.value.showMessagesAction.visible = true;
            task.value.showNotesAction.visible = true;
            task.value.showWorkflowActions.visible = true;
            task.value.showWorkflowActions.menuItems.push({ caption: 'Milestones', onInvoke: () => {} });
            task.value.documents.loadDocumentsAsync = () => {
                return Promise.resolve([
                    {
                        caption: 'Documents 1',
                    },
                ]);
            };
            return { args, isFavorite, task };
        },
        data: () => {
            return {
                tab: undefined,
                isOpen: false,
            };
        },
        template: `
        <div>
            <WtgButton variant="fill" sentiment="primary" @click="isOpen = !isOpen">{{ isOpen ? 'Close' : 'Open'}} drawer</WtgButton>
        </div>
        <WtgDrawer v-bind="args" v-model="isOpen">
            <template #toolbar>
                <WtgIconButton aria-label="Favorite" variant="ghost" v-if="isFavorite" icon="s-icon-star-empty" @click="isFavorite = false"/>
                <WtgIconButton aria-label="Favorite" variant="ghost" v-if="!isFavorite" icon="s-icon-star-empty" @click="isFavorite = true"/>
                <div style="display: flex; gap: var(--s-padding-s); ">
                    <WtgIconButton aria-label="Move up" icon="s-icon-move-up" />
                    <WtgIconButton aria-label="Move down" icon="s-icon-move-down"/>
                </div>
                <WtgIconButton aria-label="Copy link" icon="s-icon-link"/>
                <WtgIconButton aria-label="Open in new" icon="s-icon-new-window"/>
                <TaskActionsOverflow :task="task" />
            </template>
            <template #tabs>
                <WtgTabs v-model="tab">
                    <WtgTab>Label 1</WtgTab>
                    <WtgTab>Label 2</WtgTab>
                    <WtgTab>Label 3</WtgTab>
                </WtgTabs>
            </template>
            <template #default>
                <WtgTabsWindow v-model="tab">
                    <WtgTabsWindowItem>
                        <WtgContainer layout="grid">
                            <WtgPanel layout="grid" caption="Client">
                                <WtgTextField label="Name" />
                                <WtgTextField label="Company" />
                                <WtgTextField label="Title" />
                            </WtgPanel>
                            <WtgPanel layout="grid" caption="Complaint">
                                <WtgDateField label="Date lodged" />
                                <WtgTextField label="Summary" />
                                <WtgTextArea label="Details" />
                            </WtgPanel>
                        </WtgContainer>
                    </WtgTabsWindowItem>
                    <WtgTabsWindowItem>
                        <WtgContainer>Content Two</WtgContainer>
                    </WtgTabsWindowItem>
                    <WtgTabsWindowItem>
                        <WtgContainer>Content Three</WtgContainer>
                    </WtgTabsWindowItem>
                </WtgTabsWindow>
            </template>
            <template #actions>
                    <WtgButton>Cancel</WtgButton>
                    <WtgButton>Save and close</WtgButton>
                    <WtgButton variant="fill" sentiment="primary">Save changes</WtgButton>
            </template>
        </WtgDrawer>`,
    }),
    decorators: [
        () => ({
            components: { WtgApp },
            template: '<WtgApp class="content-embedded-app"><story /></WtgApp>',
        }),
    ],
};

export const NonEntity: Story = {
    args: {
        title: 'Title',
        dismissible: true,
    },
    render: (args) => ({
        components: {
            WtgContainer,
            WtgDrawer,
            WtgTabs,
            WtgTab,
            WtgTabsWindow,
            WtgTabsWindowItem,
            WtgButton,
            WtgIconButton,
        },
        setup: () => ({ args }),
        data: () => {
            return {
                tab: 'one',
                isOpen: false,
            };
        },
        template: `
        <div>
            <WtgButton variant="fill" sentiment="primary" @click="isOpen = !isOpen">{{ isOpen ? 'Close' : 'Open'}} drawer</WtgButton>
        </div>
        <WtgDrawer v-bind="args" v-model="isOpen">
            <template #tabs>
                <WtgTabs v-model="tab">
                    <WtgTab values="one">Label 1</WtgTab>
                    <div style="padding-right: var(--s-padding-xl);"/>
                    <WtgTab values="two">Label 2</WtgTab>
                    <div style="padding-right: var(--s-padding-xl);"/>
                    <WtgTab values="three">Label 3</WtgTab>
                </WtgTabs>
            </template>
            <template #default>
                <WtgTabsWindow v-model="tab">
                    <WtgTabsWindowItem>
                        <WtgContainer>Content One</WtgContainer>
                    </WtgTabsWindowItem>
                    <WtgTabsWindowItem>
                        <WtgContainer>Content Two</WtgContainer>
                    </WtgTabsWindowItem>
                    <WtgTabsWindowItem>
                        <WtgContainer>Content Three</WtgContainer>
                    </WtgTabsWindowItem>
                </WtgTabsWindow>
            </template>
            <template #actions>
                    <WtgButton>Cancel</WtgButton>
                    <WtgButton>Save and close</WtgButton>
                    <WtgButton variant="fill" sentiment="primary">Save changes</WtgButton>
            </template>
        </WtgDrawer>`,
    }),
    decorators: [
        () => ({
            components: { WtgApp },
            template: '<WtgApp class="content-embedded-app"><story /></WtgApp>',
        }),
    ],
};

export const ReadOnly: Story = {
    args: {
        title: 'Title',
        dismissible: true,
    },
    render: (args) => ({
        components: {
            WtgContainer,
            WtgDrawer,
            WtgTabs,
            WtgTab,
            WtgTabsWindow,
            WtgTabsWindowItem,
            WtgButton,
            WtgIconButton,
            TaskActionsOverflow,
        },
        setup: () => ({ args }),
        data: () => {
            return {
                tab: 'one',
                isOpen: false,
            };
        },
        template: `
        <div>
            <WtgButton variant="fill" sentiment="primary" @click="isOpen = !isOpen">{{ isOpen ? 'Close' : 'Open'}} drawer</WtgButton>
        </div>
        <WtgDrawer v-bind="args" v-model="isOpen">
            <template #default>
                <WtgContainer>Content</WtgContainer>
            </template>
            <template #actions>
                    <WtgButton>Close</WtgButton>
            </template>
        </WtgDrawer>`,
    }),
    decorators: [
        () => ({
            components: { WtgApp },
            template: '<WtgApp class="content-embedded-app"><story /></WtgApp>',
        }),
    ],
};
