import { WtgFrameworkTask } from '@components/framework/types';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import EntityNavigation from '../EntityNavigation.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('EntityNavigation', () => {
    let task: WtgFrameworkTask;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        task = new WtgFrameworkTask();
        task.others = {
            visible: true,
            hasNext: true,
            hasPrevious: true,
            indexCountText: '2 of 3',
            onNext: jest.fn(),
            onPrevious: jest.fn(),
        };
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    it('renders navigation when visible', () => {
        const wrapper = mountComponent({ propsData: { task } });
        expect(wrapper.find('.wtg-entity-navigation').exists()).toBe(true);
        expect(wrapper.text()).toContain('2 of 3');
    });

    it('disables prev button when hasPrevious is false', () => {
        task!.others.hasPrevious = false;
        const wrapper = mountComponent({ propsData: { task } });
        const prevBtn = wrapper.findAllComponents({ name: 'WtgIconButton' }).at(0);
        expect(prevBtn?.props('disabled')).toBe(true);
    });

    it('disables next button when hasNext is false', () => {
        task!.others.hasNext = false;
        const wrapper = mountComponent({ propsData: { task } });
        const nextBtn = wrapper.findAllComponents({ name: 'WtgIconButton' }).at(1);
        expect(nextBtn?.props('disabled')).toBe(true);
    });

    it('calls onPrevious when prev button is clicked', async () => {
        const onPrevious = jest.fn();
        task!.others.onPrevious = onPrevious;
        const wrapper = mountComponent({ propsData: { task } });
        const prevBtn = wrapper.findAllComponents({ name: 'WtgIconButton' }).at(0);
        await prevBtn?.trigger('click');
        expect(onPrevious).toHaveBeenCalled();
    });

    it('calls onNext when next button is clicked', async () => {
        const onNext = jest.fn();
        task!.others.onNext = onNext;
        const wrapper = mountComponent({ propsData: { task } });
        const nextBtn = wrapper.findAllComponents({ name: 'WtgIconButton' }).at(1);
        await nextBtn?.trigger('click');
        expect(onNext).toHaveBeenCalled();
    });

    it('does not render navigation when visible is false', () => {
        task!.others.visible = false;
        const wrapper = mountComponent({ propsData: { task } });
        expect(wrapper.find('.wtg-entity-navigation').exists()).toBe(false);
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(EntityNavigation, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
