* {
    box-sizing: border-box;
}

html {
    overflow-y: auto;
}

body,
p {
    font: var(--s-text-md-default);
}

h1 {
    margin-bottom: var(--s-spacing-xl) !important;
    color: var(--s-primary-txt-default) !important;
    font: var(--s-display-md-default) !important;
}

h2 {
    margin-bottom: var(--s-spacing-xl) !important;
    margin-top: var(--s-spacing-xxl) !important;
    color: var(--s-neutral-txt-default) !important;
    font: var(--s-title-lg-default) !important;
}

h3 {
    color: var(--s-neutral-txt-default) !important;
    font: var(--s-title-md-default) !important;
}

h4 {
    color: var(--s-neutral-txt-default) !important;
    font: var(--s-title-sm-default) !important;
}

/* unvisited link */
a:link {
    color: var(--s-primary-txt-default);
    font: var(--s-text-md-default-link);
    & > p {
        color: var(--s-primary-txt-default);
        font: var(--s-text-md-default-link) !important;
    }
}

/* visited link */
a:visited {
    color: var(--s-primary-txt-default);
    font: var(--s-text-md-default-link);
    text-decoration: underline;
    & > p {
        color: var(--s-primary-txt-default);
        text-decoration: underline;
        font: var(--s-text-md-default-link) !important;
    }
}

/* mouse over link */
a:hover {
    color: var(--s-primary-txt-hover);
    text-decoration: underline;
}

/* selected link */
a:active {
    color: var(--s-primary-txt-default);
    text-decoration: underline;
}

.docs-story {
    background-color: var(--s-neutral-bg-default);
}

.image-background {
    img {
        background-color: var(--s-neutral-bg-active);
    }
}

.status-chip {
    height: 24px;
}

.status-text {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: var(--s-radius-xl);
    border: 1px solid var(--s-neutral-border-weak-default);
    background: var(--s-neutral-bg-weak-default);
    padding: var(--s-padding-xs) var(--s-padding-m);
    font: var(--s-text-sm-default) !important;

    --status-color: var(--s-success-txt-inv-default);
    --status-background: var(--s-success-bg-default);

    &.complete {
        border-color: var(--s-success-border-weak-default);
        color: var(--s-success-txt-default);
        --status-color: var(--s-success-txt-default);
    }

    &.in-progress {
        border-color: var(--s-warning-border-weak-default);
        color: var(--s-warning-txt-default);
        --status-color: var(--s-warning-txt-default);
    }
}

.component-legend {
    display: flex;
    gap: var(--s-spacing-xl);
    margin-bottom: var(--s-spacing-xl) !important;
}

.component-legend p {
    margin: 0px !important;
}

.component-header {
    background: url(../src/storybook/assets/component-header.svg) no-repeat center;
    color: var(--s-primary-txt-default) !important;
    background-size: cover;
    margin-top: 0px;
    margin-bottom: var(--s-spacing-xxl) !important;
    padding: 16px 24px !important;
    border: 1px solid var(--s-primary-border-weak-default);
    border-radius: var(--s-radius-m);
}

.component-header h1 {
    color: var(--s-primary-txt-default) !important;
    font: var(--s-display-md-default) !important;
    margin-top: var(--s-padding-l) !important;
    margin-bottom: var(--s-padding-l) !important;
}

.component-status-badge {
    background: var(--s-warning-bg-weak-default);
    border-radius: var(--s-radius-s);
    border: 1px solid var(--s-warning-border-weak-default);
    padding: var(--s-padding-m);
    font: var(--s-text-sm-default) !important;
    color: var(--s-warning-txt-default);
}

.component-status {
    margin-bottom: var(--s-spacing-xxl) !important;
    th {
        text-align: left;
    }

    td {
        width: 33%;
    }

    td p {
        display: inline-flex;
        flex-direction: row;
        gap: 8px;
        font-size: 12px;
    }
}

.td-20 {
    td {
        width: 20%;
    }
}

.td-25 {
    td {
        width: 25%;
    }
}

.component-summary-table {
    margin-bottom: var(--s-spacing-xxl) !important;
    text-wrap: wrap;
    text-align: left;
    border-collapse: collapse;
    border-radius: var(--s-radius-m);
    border-style: hidden;
    box-shadow: 0 0 0 1px var(--s-neutral-border-weak-default);

    th,
    td {
        vertical-align: top;
        width: 16.6%;
        border: 1px solid var(--s-neutral-border-weak-default) !important;
    }

    tr {
        background-color: unset !important;
    }
}

.component-description p {
    font: var(--s-text-md-default);
    margin-bottom: var(--s-spacing-xxl);
}

.canvas-preview {
    position: relative !important;
    justify-content: center !important;
    border: 1px var(--s-neutral-border-weak-default) solid !important;
    border-radius: var(--s-radius-m) !important;
}

.component-preview {
    position: relative;
    justify-content: center;
    padding: var(--s-padding-xxl);
    border: 1px var(--s-neutral-border-weak-default) solid;
    border-radius: var(--s-radius-m);
    background: var(--s-neutral-bg-default);
    margin-bottom: var(--s-spacing-xxl) !important;
}

.component-preview > span {
    position: absolute;
    bottom: 0;
    left: 0;
    font: var(--s-text-xs-strong);
    padding: var(--s-padding-m);
    background: var(--s-neutral-bg-weak-default);
    border-bottom-left-radius: var(--s-radius-m);
    border-top-right-radius: var(--s-radius-m);
    color: var(--s-neutral-txt-weak-default);
}

.component-quicktip {
    padding: var(--s-padding-l);
    background: var(--s-info-bg-weak-default);
    border-radius: var(--s-radius-m);
    border: 1px var(--s-info-border-weak-default) solid;
    margin: var(--s-spacing-xxl) 0 !important;

    ul {
        margin: 0px;
        &::before {
            margin-left: 0px !important;
        }
    }
}

.component-quicktip p {
    font: var(--s-text-md-default) !important;
    color: var(--s-neutral-txt-default) !important;
    margin: 0px !important;
    padding: 0px !important;
}

.component-img {
    border: 1px solid var(--s-primary-border-default);
    border-radius: var(--s-radius-m);
    margin-top: var(--s-spacing-xl);
    margin-bottom: var(--s-spacing-xxl);
    width: 100%;
}

.content-header {
    background: url(../src/storybook/assets/content-header.svg) no-repeat;
    background-size: cover;
    margin-top: 0px;
    margin-bottom: var(--s-spacing-xxl) !important;
    padding: 40px 24px !important;
    font: var(--s-display-md-default);
    border-radius: var(--s-radius-m);
    color: var(--s-primary-txt-inv-default) !important;
}

.component-version-badge {
    background: var(--s-info-bg-weak-default);
    border-radius: var(--s-radius-s);
    border: 1px solid var(--s-info-border-weak-default);
    padding: var(--s-padding-m);
    max-width: fit-content;
    font: var(--s-text-xs-strong);
    color: var(--s-info-txt-default);
}

.component-version-badge-next {
    background: var(--s-warning-bg-weak-default);
    border-radius: var(--s-radius-s);
    border: 1px solid var(--s-warning-border-weak-default);
    padding: 0px var(--s-padding-s);
    max-width: fit-content;
    font-size: 14px !important;
    color: var(--s-warning-txt-default);
}

.content-embedded-app {
    height: 100vh !important;
    > .v-application__wrap {
        min-height: 100%;
    }
}

.docs-page .content-embedded-app,
.docs-page .v-container.wtg-fill,
.docs-page .v-container.wtg-grid-fill {
    height: 600px !important;
}

.banner-warning {
    padding: var(--s-padding-xxl);
    background: var(--s-warning-bg-weak-default);
    border-radius: var(--s-radius-m);
    border: 1px var(--s-warning-border-weak-default) solid;
    margin: var(--s-spacing-xxl) 0 !important;
}

.banner-warning p {
    font-size: 14px;
    line-height: var(--s-line-heights-400);
    border: none;
    color: var(--s-warning-txt-default);
    margin: 0;
}

.grid {
    display: flex;
    gap: var(--s-spacing-xxl);
}

.grid-2 {
    display: grid;
    gap: var(--s-spacing-xxl);
    grid-template-columns: 1fr 1fr;
}

.grid-3 {
    display: grid;
    gap: var(--s-spacing-xxl);
    grid-template-columns: 1fr 1fr 1fr;
}

.content-description p {
    font: var(--s-text-md-default);
    margin-bottom: var(--s-spacing-xxl);
    color: var(--s-neutral-txt-default);
}

.intro-card-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--s-spacing-xxl);
    margin-bottom: var(--s-spacing-xxl) !important;
}

.content-card {
    background: var(--s-primary-bg-weak-default);
    border-radius: var(--s-radius-m);
    padding: var(--s-padding-xxl);
}

.content-card img {
    margin-bottom: var(--s-spacing-xl);
}

.content-card h3 {
    margin-bottom: -16px !important;
}

.team-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: var(--s-spacing-xxl);
}

.key-contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--s-spacing-xxl);
}

.card-canvas {
    box-shadow: var(--s-elevation-button-default);
    padding: var(--s-padding-xl);
    text-align: center;
    background-color: var(--s-canvas-default);
    border-radius: var(--s-radius-m);
    border: 1px solid var(--s-neutral-border-weak-default);
}

.intro-header {
    background-color: var(--s-neutral-bg-weak-default);
    border-radius: var(--s-radius-m);
    padding: var(--s-padding-xxl);
    margin-bottom: var(--s-spacing-xxl) !important;
    display: flex;
    align-items: center;
}

.intro-header img {
    margin-bottom: var(--s-spacing-s);
}

.intro-header h1 {
    color: var(--s-neutral-txt-default) !important;
    font: var(--s-title-md-default) !important;
    margin-bottom: var(--s-spacing-xxl) !important;
    padding-bottom: var(--s-padding-xxl) !important;
    border-bottom: 1px solid var(--s-neutral-border-weak-default);
}

.intro-header-content {
    float: left;
}

.intro-header-gfx {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.intro-version-badge {
    background: var(--s-neutral-bg-default);
    border-radius: var(--s-radius-s);
    border: 1px solid var(--s-neutral-border-weak-default);
    padding: var(--s-padding-m);
    font: var(--s-text-xs-strong);
}

.version-latest {
    &::before {
        content: '2.0.2';
    }
}

.intro-content {
    margin-top: var(--s-spacing-xl) !important;
}

.intro-content img {
    width: 100%;
    margin-bottom: var(--s-spacing-l);
    border-radius: var(--s-radius-m);
}

.intro-content h3 {
    margin-bottom: -16px !important;
}

.component-related h3 {
    font-size: var(--s-font-size-400) !important;
    font-weight: var(--s-font-weights-600);
    margin-bottom: -16px !important;
}

.component-related-canvas {
    background: var(--s-canvas-default);
    height: 160px;
    border-radius: var(--s-radius-m);
    border: 1px solid var(--s-neutral-border-weak-default);
    margin-bottom: var(--s-spacing-s) !important;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}

.docs__unordered-list {
    padding-left: var(--s-padding-xl) !important;
}

.padding-b-xl {
    padding-bottom: var(--s-padding-xl);
}

.padding-b-xxl {
    padding-bottom: var(--s-padding-xxl);
}

.status-coming-soon {
    background: var(--s-info-bg-weak-default);
    border-radius: var(--s-radius-s);
    // border: 1px solid var(--s-warning-border-weak-default);
    padding: var(--s-padding-s);
    font: var(--s-text-xs-strong) !important;
    color: var(--s-info-txt-default);
}

.sb-search-text-field-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--s-neutral-bg-weak-default);
    border-bottom: 1px solid var(--s-neutral-border-weak-default);
    padding: 0 20px;
    gap: 3rem;
    z-index: 2;

    & > .sb-search-text-field-storybook-extra {
        width: 10rem;

        @media (max-width: 768px) {
            display: none;
        }
    }

    & > .sb-search-text-field {
        flex-grow: 1;
        max-width: 1000px;
        position: static;

        & > .sb-search-dropdown {
            width: calc(100% - 40px);
            position: absolute;
            max-width: 1000px;

            & > .sb-search-dropdown-content {
                background-color: #f9f9f9;
                box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
                padding: 12px 16px;

                & > a > .sb-search-result {
                    padding: var(--s-padding-m);
                    color: var(--s-neutral-txt-default);
                    &:hover {
                        background-color: var(--s-neutral-bg-weak-hover);
                    }
                }
            }
        }

        & > .sb-search-input-container {
            display: flex;
            padding: var(--s-padding-s) var(--s-padding-m);
            justify-content: flex-end;
            align-items: center;
            gap: 4px;
            align-self: stretch;
            border-radius: var(--s-radius-s);
            border: 1px solid var(--s-neutral-border-weak-default);
            background: var(--s-neutral-bg-default);

            box-shadow: var(--s-elevation-100);
            & > .sb-search-content {
                display: flex;
                height: var(--s-sizing-l);
                padding: var(--s-padding-null);
                justify-content: flex-end;
                align-items: center;
                gap: 10px;
                flex-grow: 1;
                border-radius: var(--s-radius-null);

                & > input {
                    display: flex;
                    height: var(--s-sizing-l);
                    padding: var(--s-padding-null);
                    justify-content: flex-end;
                    align-items: center;
                    gap: 10px;
                    flex-grow: 1;
                    border-radius: var(--s-radius-null);
                    &:focus {
                        outline: none;
                    }
                }
            }
        }
    }
}

.sb-story {
    font: var(--s-text-md-default);
}

footer {
    font: var(--s-text-md-strong);
    margin-top: 112px !important;
    border-top: 1px solid hsla(203, 50%, 30%, 0.15);
}

footer hr {
    padding-top: var(--s-spacing-xl) !important;
}

.do-dont-pair {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.do-dont-panel {
}

.do-dont-example {
    background-color: #f6f6f6;
    display: flex;
    justify-items: center;
    align-items: center;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    height: 220px;
}

.do-dont-example-do {
    border-bottom: 4px solid var(--s-success-border-default);
}

.do-dont-example-dont {
    border-bottom: 4px solid var(--s-error-border-default);
}

.do-dont-content p {
    margin: var(--s-padding-m) 0;
}

.anatomy-list > li::marker {
    font-weight: bold;
}

.sentiment-row {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 12px;
}

.sentiment-row > p,
.sentiment-row > div {
    flex-grow: 1;
    flex-basis: 300px;
}
