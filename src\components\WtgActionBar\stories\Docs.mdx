import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import actionBarOverflowImage from '../../../storybook/assets/actionbar-overflow.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgActionBar from './WtgActionBar.stories.ts';

<Meta of={WtgActionBar} />

<div className="component-header">
    <h1>Action bar</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=67-450&t=wvSv1AsTSZahlPuI-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    The action bar component is part of the core framework, it enables users to perform actions affecting the entire
    page or entity.
</p>

This component is a sub-component of the application layout components. For more information on how to use this component and its role within the, please refer to the [application layout](/docs/utilities-app--docs).

<Canvas className="canvas-preview" of={WtgActionBar.Default} />

## Behavior

### Primary actions

<p>
    Primary actions (fill primary buttons) are the most visually prominent, leading users toward the main action like
    saving, confirming, or submitting.
</p>

### Secondary actions

<p>
    Secondary actions (outline default buttons) are less prominent and provide alternative paths or adjacent tasks that
    may not drive the main workflow.
</p>

### Destructive actions

<p>
    Destructive actions are used for irreversible changes, such as deleting data, and should use the critical sentiment.
    Less severe actions, like "Back" or "Cancel," carry lower risk and should use an outline default button.
</p>

## Related components

-   [Button](/docs/components-button--docs)
-   [Navigation](/docs/components-navigation--docs)
-   [Masthead](/docs/components-masthead--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
