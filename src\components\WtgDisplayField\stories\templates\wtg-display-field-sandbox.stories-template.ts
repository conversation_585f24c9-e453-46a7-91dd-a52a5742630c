export const DisplayFieldSandboxTemplate = `
<WtgPanel layout="grid" max-width="600">
    <WtgDisplayField
        label="Date Field"
        value-type="date"
        :use-seconds="args.useSeconds"
        value="2024-05-24"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        label="DateTime Field"
        value-type="datetime"
        :use-seconds="args.useSeconds"
        value="2024-05-24T07:35:46.805Z"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        label="Time Field"
        value-type="time"
        :use-seconds="args.useSeconds"
        value="8:59:59"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        label="Duration Field"
        value-type="duration"
        :use-seconds="args.useSeconds"
        value="10:10"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        label="Number Field"
        value-type="number"
        :decimals="args.decimals ?? 2"
        :suppress-trailing-zeroes="args.suppressTrailingZeroes"
        :value="123456"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        label="Unit Field"
        value-type="measure"
        :decimals="args.decimals ?? 2"
        :suppress-trailing-zeroes="args.suppressTrailingZeroes"
        :value="{ magnitude: '123456', unit: 'KG' }"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
</WtgPanel>
<WtgPanel layout="grid" max-width="600" class="mt-2">
    <WtgDisplayField
        horizontal        
        label="Date Field"
        value-type="date"
        :use-seconds="args.useSeconds"
        value="2024-05-24"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        horizontal
        label="DateTime Field"
        value-type="datetime"
        :use-seconds="args.useSeconds"
        value="2024-05-24T07:35:46.805Z"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        horizontal
        label="Time Field"
        value-type="time"
        :use-seconds="args.useSeconds"
        value="08:59:59"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        horizontal
        label="Duration Field"
        value-type="duration"
        :use-seconds="args.useSeconds"
        value="10:10"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        horizontal
        label="Number Field"
        value-type="number"
        :decimals="args.decimals ?? 2"
        :suppress-trailing-zeroes="args.suppressTrailingZeroes"
        value="123456"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />
    <WtgDisplayField
        horizontal
        label="Unit Field"
        value-type="measure"
        :decimals="args.decimals ?? 2"
        :suppress-trailing-zeroes="args.suppressTrailingZeroes"
        :value="{ magnitude: '123456', unit: 'KG' }"
        :align="args.align"
        :typography="args.typography"
        columns="col-md-6"
    />

</WtgPanel>
<WtgPanel class="mt-6" max-width="800" layout="flex" flex-align="flex-align-center" flex-justify="justify-center" caption="Ensure no extra white space when 2 display fields are beside each other.">
    <div>
        <WtgDisplayField value="123456"/>
        <WtgDisplayField value="789" />
    </div>
</WtgPanel>
`;
