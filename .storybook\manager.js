import { addons } from '@storybook/manager-api';
import { create } from '@storybook/theming';
import brandImage from '../src/storybook/assets/storybook_logo.svg';

addons.setConfig({
    sidebar: {
        showRoots: true,
    },

    theme: create({
        base: 'light',
        brandTitle: 'Supply Design System',
        brandUrl: 'https://design.wtg.zone',
        brandImage,
        brandTarget: 'self',
        colorSecondary: '#371EE1',
        appBg: '#F8F8F7',
        appBorderColor: '#B5B5B3',
        appBorderRadius: 16,
        textColor: '#30302E',
        barTextColor: '#371EE1',
        fontBase: '"Inter", sans-serif',
    }),
});
