import WtgApp from '@components/WtgApp';
import WtgCol from '@components/WtgCol';
import WtgFooter from '@components/WtgFooter/WtgFooter.vue';
import WtgMain from '@components/WtgMain';
import WtgRow from '@components/WtgRow';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgFooter>;
const meta: Meta<typeof WtgFooter> = {
    title: 'Utilities/Footer',
    component: WtgFooter,
    parameters: {
        docs: {
            description: {
                component:
                    'The Footer component is used for displaying general information that a user might want to access from any page within your site.',
            },
        },
    },
    render: (args) => ({
        components: { WtgApp, WtgMain, WtgFooter, WtgCol, WtgRow },
        setup: () => ({ args }),
        methods: {
            changeAction: action('change'),
        },
        template:
            '<WtgFooter v-bind="args"><wtg-row no-gutters><wtg-col class="text-center mt-4" cols="12">{{ new Date().getFullYear() }} — <strong>SUPPLY</strong></wtg-col></wtg-row></WtgFooter>',
    }),
    decorators: [
        () => ({
            components: { WtgApp },
            template: '<WtgApp class="content-embedded-app"><story /></WtgApp>',
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {},
};
