import { ThemeOptions } from '../../../../theme';

export interface ThemeConfigurationManager {
    activePortal: string;
    currentContext: ThemeConfigurationContext;
    loadPortalsAsync: () => Promise<void>;
    loadSettingsAsync: () => Promise<void>;
    loadThemeConfigurationAsync: (portalPK: string) => Promise<ThemeOptions>;
    onSaveAsync: () => Promise<void>;
    updateThemeOptions: (themeOptions: ThemeOptions | undefined) => void;
    portals: ThemeConfigurationPortalItem[];
    themeOptions?: ThemeOptions;
    getCurrentThemeOptionsAsync: () => Promise<ThemeOptions>;
}

export interface ThemeConfigurationContext {
    portal: string;
}

export interface ThemeConfigurationPortalItem {
    text: string;
    value: string;
}

export enum FileType {
    PNG = 'image/png',
    SVG = 'image/svg+xml',
}
