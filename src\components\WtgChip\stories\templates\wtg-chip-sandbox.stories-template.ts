export const ChipSandboxTemplate = `
<WtgRow>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column" md="3">
        <WtgLayoutGrid>
            <b>Chip</b>
            <WtgChip v-bind="args" @click="action" 
                label="Label">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Disabled" 
                :disabled="true">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Primary" 
                sentiment="primary">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Success" 
                sentiment="success" 
                messages="Sample success message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Warning" 
                sentiment="warning" 
                messages="Sample warning message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Error" 
                sentiment="critical" 
                messages="Sample error message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Success and Disabled" 
                :disabled="true" 
                sentiment="success" 
                messages="Sample success message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Green" 
                sentiment="green">
            </WtgChip>
        </WtgLayoutGrid>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column" md="3">
        <WtgLayoutGrid>
            <b>Refinement</b>
            <WtgChip v-bind="args" @click="action" 
                variant="refinement"
                label="Label">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="refinement"
                label="Disabled" 
                :disabled="true">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="refinement"
                label="Primary" 
                sentiment="primary">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="refinement"
                label="Success" 
                sentiment="success" 
                messages="Sample success message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="refinement"
                label="Warning" 
                sentiment="warning" 
                messages="Sample warning message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="refinement"
                label="Error" 
                sentiment="critical" 
                messages="Sample error message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="refinement"
                label="Success and Disabled" 
                :disabled="true" 
                sentiment="success" 
                messages="Sample success message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="refinement"
                label="Green" 
                sentiment="green">
            </WtgChip>
        </WtgLayoutGrid>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column" md="3">
        <WtgLayoutGrid>
            <b>Dropdown</b>
            <WtgChip v-bind="args" @click="action" 
                variant="dropdown"
                label="Label">
                <template #dropdown>
                    <WtgList>
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="dropdown"
                label="Disabled" 
                :disabled="true">
                <template #dropdown>
                    <WtgList>
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="dropdown"
                label="Primary" 
                sentiment="primary">
                <template #dropdown>
                    <WtgList>
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="dropdown"
                label="Success" 
                sentiment="success" 
                messages="Sample success message">
                <template #dropdown>
                    <WtgList>
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="dropdown"
                label="Warning" 
                sentiment="warning" 
                messages="Sample warning message">
                <template #dropdown>
                    <WtgList>
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="dropdown"
                label="Error" 
                sentiment="critical" 
                messages="Sample error message">
                <template #dropdown>
                    <WtgList>
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="dropdown"
                label="Success and Disabled" 
                :disabled="true" 
                sentiment="success" 
                messages="Sample success message">
                <template #dropdown>
                    <WtgList>
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                variant="dropdown"
                label="Green" 
                sentiment="green">
                <template #dropdown>
                    <WtgList>
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
            </WtgChip>
        </WtgLayoutGrid>
    </WtgCol>
    <WtgCol style="padding-left: 32px; max-width: fit-content; gap: 8px;" class="d-flex flex-column" md="3">
        <WtgLayoutGrid>
            <b>Chip With Icon</b>
            <WtgChip v-bind="args" @click="action" 
                label="Label"
                leading-icon="s-icon-placeholder">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Disabled"
                leading-icon="s-icon-placeholder">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Primary"
                leading-icon="s-icon-placeholder" 
                sentiment="primary">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Success"
                leading-icon="s-icon-placeholder" 
                sentiment="success" messages="Sample success message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Warning"
                leading-icon="s-icon-placeholder" 
                sentiment="warning" 
                messages="Sample warning message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Error"
                leading-icon="s-icon-placeholder" 
                sentiment="critical" 
                messages="Sample error message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Success and Disabled"
                leading-icon="s-icon-placeholder" 
                :disabled="true" 
                sentiment="success" 
                messages="Sample success message">
            </WtgChip>
            <WtgChip v-bind="args" @click="action" 
                label="Green" 
                leading-icon="s-icon-placeholder" 
                sentiment="green">
            </WtgChip>
        </WtgLayoutGrid>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column" md="3">
        <WtgLayoutGrid>
            <b>Colors</b>
            <WtgChip v-bind="args" @click="action" color="green-darken-3">green</WtgChip>
            <WtgChip v-bind="args" @click="action" color="rgb(55,30,225)">rgb(55,30,225)</WtgChip>
            <WtgChip v-bind="args" @click="action" color="rgb(255,200,0)">rgb(255,200,0)</WtgChip>
            <WtgChip v-bind="args" @click="action" color="var(--s-error-txt-default)">var(--s-error-txt-default)</WtgChip>
        </WtgLayoutGrid>
    </WtgCol>
</WtgRow>`;
