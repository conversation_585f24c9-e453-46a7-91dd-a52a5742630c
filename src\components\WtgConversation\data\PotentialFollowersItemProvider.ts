import { SearchFieldDisplayMode, SearchFieldItem, SearchFieldItemProvider } from '@components/WtgSearchField/types';
import { Conversation, ConversationProvider, PotentialFollowersResponseType } from '../types';

export default class PotentialFollowersItemProvider implements SearchFieldItemProvider {
    provider: ConversationProvider;
    conversation: Conversation;
    userType: number;

    constructor(conversation: Conversation, userType: number, provider: ConversationProvider) {
        this.conversation = conversation;
        this.userType = userType;
        this.provider = provider;
    }

    getDisplayModeAsync(): Promise<SearchFieldDisplayMode> {
        return Promise.resolve(SearchFieldDisplayMode.DescOnly);
    }

    getSearchResultsDisplayModeAsync(): Promise<SearchFieldDisplayMode> {
        return Promise.resolve(SearchFieldDisplayMode.DescOnly);
    }

    getDisplayStringAsync(): Promise<string> {
        return Promise.resolve(SearchFieldDisplayMode.DescOnly);
    }

    getEmptyValueAsync(): Promise<string> {
        return Promise.resolve('');
    }

    getItemForValueAsync(): Promise<SearchFieldItem | undefined> {
        return Promise.resolve(undefined);
    }

    validateAsync(): Promise<boolean> {
        return Promise.resolve(true);
    }

    getItemAsync(): Promise<SearchFieldItem | undefined> {
        return Promise.resolve(undefined);
    }

    async getItemsAsync(search: string, skip: number): Promise<PotentialFollowersResponseType> {
        return this.provider.getPotentialFollowersAsync(this.conversation, this.userType, search, skip);
    }
}
