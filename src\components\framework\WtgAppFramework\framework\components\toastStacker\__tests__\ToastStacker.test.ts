import { WtgFramework } from '@components/framework/types';
import { Toast, ToastSentiment, WtgToastStacker } from '@components/WtgToast';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import { VSnackbar } from 'vuetify/lib/components/index.mjs';
import WtgUi from '../../../../../../../WtgUi';
import ToastStacker from '../ToastStacker.vue';

enableAutoUnmount(afterEach);
const wtgUi = new WtgUi();

describe('application snackbar', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is ToastStacker', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('ToastStacker');
    });

    test('it renders a WtgToastStacker', () => {
        const wrapper = mountComponent();
        const toastStacker = wrapper.findComponent(WtgToastStacker);
        expect(toastStacker.exists()).toBe(true);
    });

    test('it opens when the application toasts are populated', async () => {
        const wrapper = mountComponent();
        const snackbar = wrapper.findComponent(VSnackbar);
        expect(snackbar.props('modelValue')).toBe(false);
        application.toasts = [
            {
                id: 1,
                title: 'This is a title',
                description: 'This is a toast',
                sentiment: ToastSentiment.Info,
                timeout: 8000,
            },
            {
                id: 2,
                title: 'Toast 2',
                description: 'This is a new toast',
                sentiment: ToastSentiment.Success,
                timeout: 8000,
            },
        ];
        await nextTick();

        expect(snackbar.props('modelValue')).toBe(true);
    });

    test('it updates application toasts when toasts are dismissed', () => {
        const wrapper = mountComponent();
        const toastStacker = wrapper.findComponent(WtgToastStacker);
        application.toasts = [
            {
                id: 1,
                title: 'This is a title',
                description: 'This is a toast',
                sentiment: ToastSentiment.Info,
                timeout: 8000,
            },
            {
                id: 2,
                title: 'Toast 2',
                description: 'This is a new toast',
                sentiment: ToastSentiment.Success,
                timeout: 8000,
            },
        ];

        const updatedToasts: Toast[] = [
            {
                id: 1,
                title: 'This is a title',
                description: 'This is a toast',
                sentiment: ToastSentiment.Info,
                timeout: 8000,
            },
        ];
        toastStacker.vm.$emit('update:modelValue', updatedToasts);

        expect(application.toasts).toEqual(updatedToasts);
    });

    function mountComponent({ slots = { default: h(ToastStacker) } } = {}) {
        const wrapper = mount(VApp, {
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        return wrapper.findComponent(ToastStacker);
    }
});
