import { enableAutoUnmount, mount } from '@vue/test-utils';
import { h } from 'vue';
import { VCol } from 'vuetify/components/VGrid';
import { WtgCol } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgCol', () => {
    test('its name is WtgCol', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgCol');
    });

    test('it renders a VCol component', () => {
        const wrapper = mountComponent();
        const col = wrapper.findComponent(VCol);
        expect(col.exists()).toBe(true);
    });

    test('it passes its properties down to the VCol component', async () => {
        const wrapper = await mountComponent({
            props: {
                offset: '10',
                order: '1',
                alignSelf: 'end',
                cols: '12',
                sm: '1',
                md: '2',
                lg: '3',
                xl: '4',
                xxl: '5',
                offsetSm: '6',
                offsetMd: '7',
                offsetLg: '8',
                offsetXl: '9',
                offsetXxl: '10',
                orderSm: '11',
                orderMd: '12',
                orderLg: '1',
                orderXl: '2',
                orderXxl: '3',
            },
        });
        const props = wrapper.findComponent(VCol).props();
        expect(props.offset).toBe('10');
        expect(props.order).toBe('1');
        expect(props.alignSelf).toBe('end');
        expect(props.cols).toBe('12');
        expect(props.sm).toBe('1');
        expect(props.md).toBe('2');
        expect(props.lg).toBe('3');
        expect(props.xl).toBe('4');
        expect(props.xxl).toBe('5');
        expect(props.offsetSm).toBe('6');
        expect(props.offsetMd).toBe('7');
        expect(props.offsetLg).toBe('8');
        expect(props.offsetXl).toBe('9');
        expect(props.offsetXxl).toBe('10');
        expect(props.orderSm).toBe('11');
        expect(props.orderMd).toBe('12');
        expect(props.orderLg).toBe('1');
        expect(props.orderXl).toBe('2');
        expect(props.orderXxl).toBe('3');
    });

    test('it passes the default slot content to the VCol component', () => {
        const wrapper = mountComponent({
            slots: { default: h(WtgCol, null, { default: () => '<div class="my-div">Some Text</div>' }) },
        });
        expect(wrapper.html()).toContain('Some Text');
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgCol, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
