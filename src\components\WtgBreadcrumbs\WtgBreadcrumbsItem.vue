<template>
    <component :is="componentType" :class="computedClass" v-bind="props">
        {{ caption }}
    </component>
</template>

<script setup lang="ts">
import { makeRouterProps } from '@composables/router';
import { PropType, computed } from 'vue';

const props = defineProps({
    caption: {
        type: String,
        default: '',
    },
    variant: {
        type: String as PropType<'default' | 'current'>,
        default: 'default',
    },
    ...makeRouterProps(),
});

const componentType = computed(() => {
    if (props.to) {
        return 'router-link';
    } else if (props.href) {
        return 'a';
    } else {
        return 'span';
    }
});

const computedClass = computed(() => [
    {
        'wtg-breadcrumbs__item': props.variant !== 'current',
        'wtg-breadcrumbs__current-item': props.variant === 'current',
    },
]);
</script>

<style lang="scss">
.wtg-breadcrumbs__current-item {
    font-weight: var(--s-fontweight-600);
}

.wtg-breadcrumbs__item {
    cursor: pointer;
    text-decoration: underline;
    font-weight: var(--s-fontweight-500);
    color: var(--s-primary-txt-default);
}
</style>
