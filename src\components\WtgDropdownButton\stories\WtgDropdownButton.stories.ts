import WtgCol from '@components/WtgCol';
import WtgDropdownButton from '@components/WtgDropdownButton/WtgDropdownButton.vue';
import WtgIcon from '@components/WtgIcon/WtgIcon.vue';
import WtgList, { WtgListItem } from '@components/WtgList';
import WtgRow from '@components/WtgRow';
import { useSupplyPrefixIconsName } from '@composables/icon';
import { measureArgTypes } from '@composables/measure';
import { tooltipArgTypes } from '@composables/tooltip';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { DropdownButtonSandboxTemplate } from './templates/wtg-dropdown-button-sandbox.stories-template';
import { DropdownButtonWithIconsTemplate } from './templates/wtg-dropdown-button-with-icons.stories-template';
import { DropdownButtonWithSentimentsTemplate } from './templates/wtg-dropdown-button-with-sentiments.stories-template';
import { DropdownButtonWithVariantsTemplate } from './templates/wtg-dropdown-button-with-variants.stories-template';

type Story = StoryObj<typeof WtgDropdownButton>;

const icons = useSupplyPrefixIconsName();
const meta: Meta<typeof WtgDropdownButton> = {
    title: 'Components/Dropdown Button',
    component: WtgDropdownButton,
    parameters: {
        docs: {
            description: {
                component:
                    'A Dropdown button is a hybrid between a Dropdown Menu and a Button. It lets users choose from parallel actions and take action on their choice.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=59-1790&mode=design&t=FoGkzhcXiCnlZFCr-0',
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgDropdownButton, WtgList, WtgListItem },
        methods: {
            action: action('click'),
            dropdownAction: action('dropdown-click'),
        },
        setup: () => ({ args }),
        template: `<WtgDropdownButton v-bind="args" @click="action" @dropdown-click="dropdownAction">
            {{args.label}}
        </WtgDropdownButton>`,
    }),
    argTypes: {
        ...tooltipArgTypes,
        ...measureArgTypes,
        leadingIcon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
        variant: {
            options: ['', 'fill'],
            control: {
                type: 'select',
            },
        },
        openPosition: {
            options: ['top', 'bottom'],
            control: {
                type: 'select',
            },
        },
    },
    decorators: [
        () => ({
            template: `
            <div style="display: flex; flex-wrap: wrap;">
                <story/>
            </div>
            `,
        }),
    ],
};
export default meta;

export const Variants: Story = {
    render: (args) => ({
        components: { WtgDropdownButton, WtgList, WtgListItem },
        methods: {
            action: action('click'),
            dropdownAction: action('dropdown-click'),
        },
        setup: () => ({ args }),
        template: DropdownButtonWithVariantsTemplate,
    }),
};

export const Sentiments: Story = {
    name: 'Sentiments',
    render: (args) => ({
        components: { WtgDropdownButton, WtgCol, WtgRow, WtgList, WtgListItem },
        methods: {
            action: action('click'),
            dropdownAction: action('dropdown-click'),
        },
        setup: () => ({ args }),
        template: DropdownButtonWithSentimentsTemplate,
    }),
};

export const Icons: Story = {
    args: {
        leadingIcon: 's-icon-placeholder',
    },
    render: (args) => ({
        components: { WtgDropdownButton, WtgIcon, WtgCol, WtgRow, WtgList, WtgListItem },
        methods: {
            action: action('click'),
            dropdownAction: action('dropdown-click'),
        },
        setup: () => ({ args }),
        template: DropdownButtonWithIconsTemplate,
    }),
};

export const Disabled: Story = {
    args: {
        disabled: true,
    },
    render: (args) => ({
        components: { WtgDropdownButton, WtgList, WtgListItem },
        methods: {
            action: action('click'),
            dropdownAction: action('dropdown-click'),
        },
        setup: () => ({ args }),
        template: `
        <div style="flex: 1 1 100%; display: flex; flex-wrap: wrap; gap: 8px">
            <WtgDropdownButton 
                v-bind="args"
                @click="action"
                @dropdown-click="dropdownAction">
                    Outline

            </WtgDropdownButton>
            <WtgDropdownButton 
                v-bind="args"
                variant="fill"
                sentiment="primary"
                @click="action"
                @dropdown-click="dropdownAction">
                    Fill Primary
            </WtgDropdownButton>
        </div>`,
    }),
};

export const Sandbox: Story = {
    name: 'Sandbox',
    parameters: {
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgDropdownButton, WtgCol, WtgRow, WtgList, WtgListItem },
        methods: {
            action: action('click'),
            dropdownAction: action('dropdown-click'),
        },
        setup: () => ({ args }),
        template: DropdownButtonSandboxTemplate,
    }),
};
