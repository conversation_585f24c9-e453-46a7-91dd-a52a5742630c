export const ButtonSandboxTemplate = `
<WtgRow>
    <WtgCol md="3">
        <WtgLayoutGrid>
            <b>Icon Button Variants</b>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                variant="fill"
                sentiment="primary"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                variant="ghost"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
        </WtgLayoutGrid>
    </WtgCol>
    <WtgCol md="3">
        <WtgLayoutGrid>
            <b>Icon Button Sentiments</b>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                sentiment="success"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                sentiment="critical"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                variant="fill"
                sentiment="primary"
                data-testid="testSentimentsIconButton-primary"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                variant="fill"
                sentiment="success"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                variant="fill"
                sentiment="critical"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                variant="ghost"
                :icon="args.icon"
                @click="action">
            </WtgIconButton>
        </WtgLayoutGrid>
    </WtgCol>
        <WtgCol md="3">
        <WtgLayoutGrid>
            <b>Icon Button Sizes</b>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                :icon="args.icon"
                size="xs"
                icon-size="xs"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                :icon="args.icon"
                size="s"
                icon-size="s"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                :icon="args.icon"
                size="m"
                icon-size="m"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                :icon="args.icon"
                size="l"
                icon-size="l"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                :icon="args.icon"
                size="xl"
                icon-size="xl"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                :icon="args.icon"
                size="xxl"
                icon-size="xxl"
                @click="action">
            </WtgIconButton>
        </WtgLayoutGrid>
    </WtgCol>
</WtgRow>
<WtgPanel class="mt-6" style="gap: 8px" layout="flex" caption="This button uses the color property for utility color help">
    <WtgIconButton aria-label="Aria Name" color="green-darken-3" icon="s-icon-home" />
    <WtgIconButton aria-label="Aria Name" color="rgba(55,30,225,.8)" icon="s-icon-home" />
    <WtgIconButton aria-label="Aria Name" variant="ghost" color="green" icon="s-icon-home" />
    <WtgIconButton aria-label="Aria Name" variant="ghost" color="rgba(55,30,225,.8)" icon="s-icon-home" />
    <WtgIconButton aria-label="Aria Name" variant="fill" color="green" icon="s-icon-home" />
    <WtgIconButton aria-label="Aria Name" variant="fill" color="rgb(55,30,225)" icon="s-icon-home" />
    <WtgIconButton aria-label="Aria Name" variant="fill" color="rgb(255,200,0)" icon="s-icon-home" />
</WtgPanel>
<WtgPanel class="mt-6" style="gap: 8px" layout="flex" caption="This button uses an svg for the content">
    <WtgIconButton aria-label="Aria Name">
        <img :src="windowMaximize" alt="maximize" />
    </WtgIconButton>
</WtgPanel>
`;
