<template>
    <div v-if="!isMobile" class="wtg-breadcrumbs">
        <template v-if="items.length > 1">
            <WtgBreadcrumbsItem :caption="items[0].caption" :href="items[0].link" :to="items[0].to" />
        </template>
        <template v-if="items.length === 3">
            <WtgIcon>s-icon-forward-slash</WtgIcon>
            <WtgBreadcrumbsItem :caption="items[1].caption" :href="items[1].link" :to="items[1].to" />
        </template>
        <template v-if="items.length > 3">
            <WtgIcon>s-icon-forward-slash</WtgIcon>
            <WtgPopover location="bottom right" nudge-bottom="var(--s-padding-m)">
                <template #activator="{ props: activatorProps }: { props: Record<string, any> }">
                    <WtgButton
                        v-bind="activatorProps"
                        class="wtg-breadcrumbs__button"
                        :tooltip="moreCaption"
                        aria-haspopup="menu"
                        :aria-expanded="activatorProps.isActive ? 'true' : 'false'"
                        variant="ghost"
                    >
                        ...
                    </WtgButton>
                </template>
                <WtgList>
                    <WtgListItem
                        v-for="middleItem in middleItems"
                        :key="middleItem.caption"
                        role="menuitem"
                        :href="middleItem.link"
                        :to="middleItem.to"
                    >
                        <span>{{ middleItem.caption }}</span>
                    </WtgListItem>
                </WtgList>
            </WtgPopover>
        </template>
        <template v-if="items.length">
            <WtgIcon v-if="items.length > 1">s-icon-forward-slash</WtgIcon>
            <WtgBreadcrumbsItem :caption="items[items.length - 1].caption" variant="current" />
        </template>
    </div>
    <div v-else class="wtg-breadcrumbs--mobile">
        <WtgBreadcrumbsItem :caption="items[items.length - 1].caption" variant="current" />
    </div>
</template>

<script setup lang="ts">
import WtgBreadcrumbsItem from '@components/WtgBreadcrumbs/WtgBreadcrumbsItem.vue';
import WtgButton from '@components/WtgButton/WtgButton.vue';
import { WtgIcon } from '@components/WtgIcon';
import WtgList from '@components/WtgList/WtgList.vue';
import WtgListItem from '@components/WtgList/WtgListItem.vue';
import WtgPopover from '@components/WtgPopover/WtgPopover.vue';
import { useFramework } from '@composables/framework';
import { useLocale } from '@composables/locale';
import { computed } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * The breadcrumb items to display.
     * Each item should be an object with at least a `caption` property, and optionally `link` or `to`.
     * The first item is the root, the last is the current page.
     */
    items: {
        type: Array as () => Array<Partial<typeof WtgBreadcrumbsItem>>,
        default: () => [],
    },
});

//
// Composables
//
const { isMobile } = useFramework();
const { formatCaption } = useLocale();

//
// Computed
//
const moreCaption = computed(() => formatCaption('menu.more'));
const middleItems = computed(() => props.items.slice(1, -1));
</script>

<style lang="scss">
.wtg-breadcrumbs {
    display: inline-flex;
    align-items: center;
    gap: var(--s-spacing-s);
    line-height: var(--s-fontsize-500);
}

.wtg-breadcrumbs__button {
    height: var(--s-sizing-s);
    padding: var(--s-spacing-null);

    .wtg-button__content {
        text-decoration-line: underline;
        color: var(--s-primary-txt-default);
    }
}

.wtg-breadcrumbs__button.wtg-button--ghost.wtg-button:hover {
    background: unset;
}

.wtg-breadcrumbs--mobile {
    width: 100%;
}
</style>
