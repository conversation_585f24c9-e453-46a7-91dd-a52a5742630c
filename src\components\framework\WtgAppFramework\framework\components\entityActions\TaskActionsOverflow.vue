<template>
    <WtgPopover nudge-bottom="var(--s-padding-m)">
        <template #activator="args">
            <WtgIconButton
                icon="s-icon-menu-kebab"
                :tooltip="formatCaption('drawer.entityActions')"
                :variant="variant"
                :aria-label="ariaLabels.taskActionsOverflowIcon"
                aria-haspopup="menu"
                :aria-expanded="open ? 'true' : 'false'"
                v-bind="args.props"
            />
        </template>
        <template #default>
            <PopupMenuList :actions="actions" @action="onAction" />
        </template>
    </WtgPopover>
</template>

<script setup lang="ts">
import {
    WtgFrameworkAriaLabels,
    WtgFrameworkDocumentsMenuItem,
    WtgFrameworkTask,
    WtgFrameworkTaskGenericAction,
    WtgFrameworkTaskGenericActionPlacement,
    WtgFrameworkTaskStandardAction,
} from '@components/framework/types';
import WtgIconButton from '@components/WtgIconButton';
import { WtgActionItemData } from '@components/WtgMenu';
import { PopupMenuList } from '@components/WtgMenuBar/popup';
import WtgPopover from '@components/WtgPopover';
import { useApplication } from '@composables/application';
import { useLocale } from '@composables/locale';
import { computed, PropType, reactive, ref } from 'vue';

const props = defineProps({
    task: { type: Object as PropType<WtgFrameworkTask>, default: undefined },
    variant: {
        type: String as PropType<'fill' | 'ghost'>,
        default: undefined,
    },
});

const open = ref(false);

const application = useApplication();
const { formatCaption } = useLocale();

const ariaLabels = computed((): WtgFrameworkAriaLabels => {
    return application.ariaLabels;
});

const currentTask = computed((): WtgFrameworkTask => {
    return props.task ?? new WtgFrameworkTask();
});

const genericActions = computed((): WtgFrameworkTaskGenericAction[] => {
    return currentTask.value.genericActions;
});

const transitionTasks = computed((): WtgFrameworkTaskGenericAction[] => {
    const filteredActions = genericActions.value.filter(
        (action) => action.placement === WtgFrameworkTaskGenericActionPlacement.TaskAction
    );
    return filteredActions;
});

const actions = computed((): WtgActionItemData[] => {
    const taskActions: WtgActionItemData[] = [];
    transitionTasks.value.forEach((task) => taskActions.push(mapTaskActionToActions(task, task.icon)));
    if (currentTask.value.showEDocsAction.visible) {
        taskActions.push(mapTaskActionToActions(currentTask.value.showEDocsAction, 's-icon-eDocs'));
    }
    if (currentTask.value.documents.visible) {
        const item = reactive({
            actions: [],
            id: 'document',
            caption: currentTask.value.documents.caption,
            icon: '$fileDocument',
            loadDocumentsAsync: currentTask.value.documents.loadDocumentsAsync,
            loading: false,
            submenu: true,
        });
        taskActions.push(item);
    }
    if (currentTask.value.showLogsAction.visible) {
        taskActions.push(mapTaskActionToActions(currentTask.value.showLogsAction, 's-icon-logs'));
    }
    if (currentTask.value.showMessagesAction.visible) {
        taskActions.push(mapTaskActionToActions(currentTask.value.showMessagesAction, 's-icon-chat'));
    }
    if (currentTask.value.showNotesAction.visible) {
        taskActions.push(mapTaskActionToActions(currentTask.value.showNotesAction, 's-icon-notes'));
    }
    if (currentTask.value.showWorkflowActions.visible) {
        taskActions.push({
            caption: currentTask.value.showWorkflowActions.caption,
            icon: 's-icon-workflow',
            submenu: true,
            actions: currentTask.value.showWorkflowActions.menuItems.map((item): WtgActionItemData => {
                return {
                    caption: item.caption,
                    click: (): void => item.onInvoke(),
                };
            }),
        });
    }
    return taskActions;
});

let nextId = 1;
const onAction = async (action: WtgFrameworkDocumentsMenuItem): Promise<void> => {
    if (action.loadDocumentsAsync) {
        action.loading = true;
        const documentItems = await action.loadDocumentsAsync();
        action.actions = documentItems.map((item) =>
            reactive({
                actions: [],
                id: 'doc-' + nextId++,
                loading: false,
                ...item,
            })
        );

        action.loading = false;
    } else if (action.click) {
        action.click();
    }
};

function mapTaskActionToActions(
    action: WtgFrameworkTaskStandardAction | WtgFrameworkTaskGenericAction,
    icon?: string
): WtgActionItemData {
    return {
        caption: action.caption,
        click: (): void => action.onInvoke(),
        icon: icon,
    };
}
</script>
