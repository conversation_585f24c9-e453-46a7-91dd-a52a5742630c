import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgCallout } from '..';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgCallout', () => {
    test('it sets callout title text to the title property passes in', () => {
        const wrapper = mountComponent({
            propsData: {
                state: 'info',
                title: 'Callout Title',
            },
        });
        expect(wrapper.html()).toContain('Callout Title');
    });

    test('it sets callout title description to the description property passes in', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'My Callout Title',
                sentiment: 'info',
                description: 'My Callout Description',
            },
        });
        expect(wrapper.html()).toContain('My Callout Description');
    });

    test('it passes the default slot content to the callout container', () => {
        const wrapper = mountComponent({
            propsData: {
                sentiment: 'info',
            },
            slots: {
                default: () => '<div>Some Text</div>',
            },
        });
        expect(wrapper.html()).toContain('Some Text');
    });

    test('it display title and description while also passing the default slot content to the callout container and preserves white spaces', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'My Callout Title',
                sentiment: 'info',
                description: 'My Callout Description',
            },
            slots: {
                default: () => '<div>Some Text</div>',
            },
        });
        expect(wrapper.find('.wtg-callout__title').text()).toBe('My Callout Title');
        expect(wrapper.find('.wtg-callout__description').text()).toBe('My Callout Description');
        expect(wrapper.find('.wtg-callout__description').html()).toContain('white-space: pre-wrap');
        expect(wrapper.html()).toContain('Some Text');
    });

    test('it dismisses the callout correctly', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'title',
                dismissible: true,
            },
        });

        const icons = wrapper.findAllComponents({ name: 'WtgIcon' });
        expect(icons.length).toBe(2);

        const closeButton = icons.at(1);
        closeButton?.element.click();

        expect(wrapper.emitted()['close']).toBeTruthy();
    });

    test('it applies correct styles and classes when the Callout is default variant with sentiment info', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'title',
                sentiment: 'info',
            },
        });
        expect(wrapper.html()).toContain('wtg-callout__title');
        expect(wrapper.classes()).toContain('wtg-callout--info');
    });

    test('it applies correct styles and classes when the Callout is default variant with sentiment success', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'My Callout Title',
                sentiment: 'success',
            },
        });
        expect(wrapper.html()).toContain('wtg-callout__title');
        expect(wrapper.classes()).toContain('wtg-callout--success');
    });

    test('it applies correct styles and classes when the Callout is default variant with sentiment warning', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'My Callout Title',
                sentiment: 'warning',
            },
        });
        expect(wrapper.html()).toContain('wtg-callout__title');
        expect(wrapper.classes()).toContain('wtg-callout--warning');
    });

    test('it applies correct styles and classes when the Callout is default variant with sentiment critical', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'My Callout Title',
                sentiment: 'critical',
            },
        });
        expect(wrapper.html()).toContain('wtg-callout__title');
        expect(wrapper.classes()).toContain('wtg-callout--critical');
    });

    test('it applies correct styles and classes when the Callout is inline with sentiment info', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'My Callout Title',
                sentiment: 'info',
                variant: 'inline',
            },
        });
        expect(wrapper.html()).not.toContain('wtg-callout-title');
        expect(wrapper.html()).toContain('background: none;');
        expect(wrapper.classes()).toContain('wtg-callout--info');
    });

    test('it applies correct styles and classes when the Callout is inline with sentiment success', () => {
        const wrapper = mountComponent({
            propsData: {
                sentiment: 'success',
                variant: 'inline',
            },
        });
        expect(wrapper.html()).not.toContain('wtg-callout-title');
        expect(wrapper.html()).toContain('background: none;');
        expect(wrapper.classes()).toContain('wtg-callout--success');
    });

    test('it applies correct styles and classes when the Callout is inline with sentiment warning', () => {
        const wrapper = mountComponent({
            propsData: {
                sentiment: 'warning',
                variant: 'inline',
            },
        });
        expect(wrapper.html()).not.toContain('wtg-callout-title');
        expect(wrapper.html()).toContain('background: none;');
        expect(wrapper.classes()).toContain('wtg-callout--warning');
    });

    test('it applies correct styles and classes when the Callout is inline with sentiment critical', () => {
        const wrapper = mountComponent({
            propsData: {
                sentiment: 'critical',
                variant: 'inline',
            },
        });
        expect(wrapper.html()).not.toContain('wtg-callout-title');
        expect(wrapper.html()).toContain('background: none;');
        expect(wrapper.classes()).toContain('wtg-callout--critical');
    });

    test('v-show is used instead of v-if so child components still exist when modelValue is false', () => {
        const wrapper = mountComponent({
            propsData: {
                modelValue: false,
            },
        });
        expect(wrapper.html()).not.toBe('<!--v-if-->');
        expect(wrapper.html()).toContain('wtg-callout__container');
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgCallout, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
