import { WtgFrameworkTabsInfo } from '@components/framework/types';
import { WtgAppFrameworkTab } from '@components/framework/WtgAppFrameworkTabs';
import { WtgTab } from '@components/WtgTabs';
import { enableAutoUnmount, mount } from '@vue/test-utils';

import WtgUi from '../../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgAppFrameworkTab', () => {
    let appFrameworkTabsProvide: WtgFrameworkTabsInfo;

    beforeEach(() => {
        appFrameworkTabsProvide = { tabs: [], current: 0, visible: true };
    });

    test('its name is WtgAppFrameworkTab', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('WtgAppFrameworkTab');
    });

    test('it is a wrapper around a WtgTab component', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent(WtgTab).exists()).toBe(true);
    });

    test('it has a caption property that controls the tab content', () => {
        const wrapper = mountComponent({
            propsData: {
                caption: 'My Caption',
            },
        });

        expect(wrapper.text()).toBe('My Caption');
    });

    describe('it registers and de-registers its caption with the app framework', () => {
        test('when a single tab', () => {
            const wrapper = mountComponent({
                propsData: {
                    caption: 'My Caption',
                },
            });
            expect(appFrameworkTabsProvide.tabs.length).toBe(1);
            expect(appFrameworkTabsProvide.tabs[0].caption).toBe('My Caption');

            wrapper.unmount();
            expect(appFrameworkTabsProvide.tabs.length).toBe(0);
        });

        test('when multiple tabs', () => {
            const localProvide: WtgFrameworkTabsInfo = {
                tabs: [{ caption: 'Caption 0' }, { caption: 'Caption 1' }],
                current: 0,
                visible: true,
            };
            const wrapper = mountComponent(
                {
                    propsData: {
                        caption: 'Caption 2',
                    },
                },
                localProvide
            );

            expect(localProvide.tabs.length).toBe(3);
            expect(localProvide.tabs[0].caption).toBe('Caption 0');
            expect(localProvide.tabs[1].caption).toBe('Caption 1');
            expect(localProvide.tabs[2].caption).toBe('Caption 2');

            wrapper.unmount();
            expect(localProvide.tabs.length).toBe(2);
            expect(localProvide.tabs[0].caption).toBe('Caption 0');
            expect(localProvide.tabs[1].caption).toBe('Caption 1');
        });
    });

    test("tab's caption updates upon caption property changes", async () => {
        const wrapper = mountComponent({
            propsData: { caption: '' },
        });

        expect(appFrameworkTabsProvide.tabs[0].caption).toBe('');
        expect(wrapper.text()).toBe('');

        await wrapper.setProps({ caption: 'My Caption' });

        expect(appFrameworkTabsProvide.tabs[0].caption).toBe('My Caption');
        expect(wrapper.text()).toBe('My Caption');

        await wrapper.setProps({ caption: 'Another Caption' });

        expect(appFrameworkTabsProvide.tabs[0].caption).toBe('Another Caption');
        expect(wrapper.text()).toBe('Another Caption');
    });

    function mountComponent({ propsData = {} } = {}, provide = appFrameworkTabsProvide) {
        return mount(WtgAppFrameworkTab, {
            propsData,
            global: {
                plugins: [wtgUi],
                provide: { appFrameworkTabsProvide: provide },
            },
        });
    }
});
