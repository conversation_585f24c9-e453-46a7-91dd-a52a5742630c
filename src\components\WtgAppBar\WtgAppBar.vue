<template>
    <VAppBar
        v-model="model"
        :extension-height="extensionHeight"
        :height="height"
        :order="order"
        :flat="flat"
        :scroll-behavior="scrollBehavior"
        :class="appBarClasses"
    >
        <template v-if="slots.prepend" #prepend>
            <slot name="prepend"></slot>
        </template>
        <slot />
        <template v-if="slots.append" #append>
            <div class="wtg-app-bar__append">
                <slot name="append"></slot>
            </div>
        </template>
        <template v-if="slots.extension" #extension>
            <slot name="extension"></slot>
        </template>
    </VAppBar>
</template>

<script setup lang="ts">
import { PropType, computed, useSlots } from 'vue';
import { VAppBar } from 'vuetify/components/VAppBar';

//
// Properties
//
const props = defineProps({
    /**
     * The elevation level of the app bar. Controls the shadow depth.
     * Options: '100', '200', '300'.
     */
    elevation: {
        type: String as PropType<'100' | '200' | '300' | undefined>,
        default: undefined,
    },
    /**
     * The height of the extension area (in pixels or CSS units).
     */
    extensionHeight: {
        type: [String, Number],
        default: 48,
    },
    /**
     * Removes the app bar's box-shadow if true.
     */
    flat: {
        type: Boolean,
        default: undefined,
    },
    /**
     * The height of the app bar (in pixels or CSS units).
     */
    height: {
        type: [String, Number],
        default: 48,
    },
    /**
     * The stacking order (z-index) of the app bar.
     */
    order: {
        type: [String, Number],
        default: 0,
    },
    /**
     * Controls the scroll behavior of the app bar.
     * See Vuetify's scroll-behavior prop for options.
     */
    scrollBehavior: {
        type: String,
        default: undefined,
    },
});

const model = defineModel<boolean>({ default: true });

//
// Slots
//
const slots = useSlots();

//
// Computed
//
const appBarClasses = computed(() => ['wtg-app-bar', `wtg-elevation-${props.elevation}`]);
</script>

<style lang="scss">
.wtg-app-bar__append {
    display: inline-flex;
    align-items: center;
    gap: var(--s-spacing-m);
}
</style>
