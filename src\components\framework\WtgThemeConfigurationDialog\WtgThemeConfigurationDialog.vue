<template>
    <WtgDialog v-model="internalValue" max-width="500" persistent>
        <div class="wtg-modal__container">
            <div class="wtg-modal__title">
                <WtgLabel class="wtg-typography-title">
                    {{ dialogTitle }}
                </WtgLabel>
                <WtgSpacer />
                <WtgIconButton icon="s-icon-close" variant="ghost" :tooltip="cancelCaption" @click="onClose" />
            </div>
            <div class="wtg-modal__content-canvas">
                <WtgPanel layout="grid">
                    <WtgSelectField
                        v-model="portal"
                        :label="portalCaption"
                        :items="portals"
                        data-testid="active-portal-field"
                        :loading="loadingPortals"
                    />
                    <WtgSelectField
                        v-model="copyThemePortal"
                        :items="portals"
                        :label="copyThemeFromCaption"
                        data-testid="copy-from-field"
                        :loading="loadingPortals"
                    />
                </WtgPanel>
                <WtgPanel class="mt-2">
                    <WtgTabs v-model="openTab">
                        <WtgTab>
                            {{ logosCaption }}
                        </WtgTab>
                        <WtgTab>
                            {{ colorsCaption }}
                        </WtgTab>
                    </WtgTabs>
                    <WtgTabsWindow v-model="openTab">
                        <WtgTabsWindowItem>
                            <WtgLayoutGrid>
                                <WtgPanel class="mt-2">
                                    <div class="d-flex">
                                        <WtgLabel class="wtg-typography-title-small">
                                            {{ logoLightDescriptionCaption }}
                                        </WtgLabel>
                                        <WtgSpacer />
                                        <WtgButton
                                            leading-icon="s-icon-upload-file"
                                            variant="ghost"
                                            data-testid="select-light-logo-button"
                                            @click="onLogoSelectClick('light')"
                                        >
                                            {{ uploadCaption }}
                                        </WtgButton>
                                    </div>
                                    <WtgPanel>
                                        <WtgLayoutGrid>
                                            <WtgCard class="pa-0" color="#161615">
                                                <WtgBox
                                                    class="pa-10"
                                                    layout="flex"
                                                    flex-align="flex-align-center"
                                                    flex-justify="justify-center"
                                                >
                                                    <img
                                                        v-if="logo"
                                                        class="align-self-center"
                                                        :src="logoImage"
                                                        :alt="logoImageAltCaption"
                                                        height="44"
                                                        style="max-width: 220px"
                                                    />
                                                </WtgBox>
                                            </WtgCard>
                                            <WtgCallout
                                                v-if="darkImageFileSizeTooLarge"
                                                sentiment="critical"
                                                class="mb-0"
                                            >
                                                {{ fileSizeTooLargeCaption }}
                                            </WtgCallout>
                                        </WtgLayoutGrid>
                                    </WtgPanel>
                                </WtgPanel>
                                <input
                                    ref="light-logo-input"
                                    type="file"
                                    class="d-none"
                                    accept="image/svg+xml, image/png"
                                    @change="onLogoChange($event, 'light')"
                                />
                                <WtgPanel>
                                    <div class="d-flex">
                                        <WtgLabel class="wtg-typography-title-small">
                                            {{ logoDarkDescriptionCaption }}
                                        </WtgLabel>
                                        <WtgSpacer />
                                        <WtgButton
                                            leading-icon="s-icon-upload-file"
                                            variant="ghost"
                                            data-testid="select-dark-logo-button"
                                            @click="onLogoSelectClick('dark')"
                                        >
                                            {{ uploadCaption }}
                                        </WtgButton>
                                    </div>
                                    <WtgPanel>
                                        <WtgLayoutGrid>
                                            <WtgCard class="pa-0" color="#FFF">
                                                <WtgBox
                                                    class="pa-10"
                                                    columns="col-12"
                                                    layout="flex"
                                                    flex-align="flex-align-center"
                                                    flex-justify="justify-center"
                                                >
                                                    <img
                                                        v-if="logoDark"
                                                        class="align-self-center"
                                                        :src="logoDarkImage"
                                                        :alt="logoImageAltCaption"
                                                        height="44"
                                                        style="max-width: 220px"
                                                    />
                                                </WtgBox>
                                            </WtgCard>
                                            <WtgCallout
                                                v-if="lightImageFileSizeTooLarge"
                                                sentiment="critical"
                                                class="mb-0"
                                            >
                                                {{ fileSizeTooLargeCaption }}
                                            </WtgCallout>
                                        </WtgLayoutGrid>
                                    </WtgPanel>
                                </WtgPanel>
                                <input
                                    ref="dark-logo-input"
                                    type="file"
                                    class="d-none"
                                    accept="image/svg+xml, image/png"
                                    @change="onLogoChange($event, 'dark')"
                                />
                            </WtgLayoutGrid>
                        </WtgTabsWindowItem>
                        <WtgTabsWindowItem>
                            <WtgPanel class="mt-2" layout="grid" :caption="lightThemeCaption">
                                <WtgColorField
                                    v-model="primaryColor"
                                    :label="primaryCaption"
                                    :readonly="loadingSettings"
                                />
                                <WtgFieldLabel class="mb-n1" columns="col-6">
                                    {{ navigationCaption }}
                                </WtgFieldLabel>
                                <WtgFieldLabel class="mb-n1" columns="col-6">
                                    {{ textColorCaption }}
                                </WtgFieldLabel>
                                <WtgColorField v-model="brandColor" columns="col-6" :readonly="loadingSettings" />
                                <WtgRadioGroup v-model="darkBrand" columns="col-6" horizontal>
                                    <WtgRadio
                                        class="ma-0 pa-0"
                                        :value="true"
                                        :label="whiteTextCaption"
                                        :readonly="loadingSettings"
                                    />
                                    <WtgRadio
                                        class="ma-0 pa-0"
                                        :value="false"
                                        :label="blackTextCaption"
                                        :readonly="loadingSettings"
                                    />
                                </WtgRadioGroup>
                                <WtgFieldLabel class="mb-n1" columns="col-6">
                                    {{ brandLoginCaption }}
                                </WtgFieldLabel>
                                <WtgFieldLabel class="mb-n1" columns="col-6">
                                    {{ textColorCaption }}
                                </WtgFieldLabel>
                                <WtgColorField v-model="brandLoginColor" columns="col-6" :readonly="loadingSettings" />
                                <WtgRadioGroup v-model="darkBrandLogin" columns="col-6" horizontal>
                                    <WtgRadio
                                        class="ma-0 pa-0"
                                        :value="true"
                                        :label="whiteTextCaption"
                                        :readonly="loadingSettings"
                                    />
                                    <WtgRadio
                                        class="ma-0 pa-0"
                                        :value="false"
                                        :label="blackTextCaption"
                                        :readonly="loadingSettings"
                                    />
                                </WtgRadioGroup>
                            </WtgPanel>
                            <WtgPanel class="mt-2" layout="grid" :caption="darkThemeCaption">
                                <WtgColorField
                                    v-model="primaryColorDark"
                                    :label="primaryCaption"
                                    :readonly="loadingSettings"
                                />
                                <WtgFieldLabel class="mb-n1" columns="col-6">
                                    {{ navigationCaption }}
                                </WtgFieldLabel>
                                <WtgFieldLabel class="mb-n1" columns="col-6">
                                    {{ textColorCaption }}
                                </WtgFieldLabel>
                                <WtgColorField v-model="brandColorDark" columns="col-6" :readonly="loadingSettings" />
                                <WtgRadioGroup v-model="darkBrandDark" columns="col-6" horizontal>
                                    <WtgRadio
                                        class="ma-0 pa-0"
                                        :value="true"
                                        :label="whiteTextCaption"
                                        :readonly="loadingSettings"
                                    />
                                    <WtgRadio
                                        class="ma-0 pa-0"
                                        :value="false"
                                        :label="blackTextCaption"
                                        :readonly="loadingSettings"
                                    />
                                </WtgRadioGroup>
                                <WtgFieldLabel class="mb-n1" columns="col-6">
                                    {{ brandLoginCaption }}
                                </WtgFieldLabel>
                                <WtgFieldLabel class="mb-n1" columns="col-6">
                                    {{ textColorCaption }}
                                </WtgFieldLabel>
                                <WtgColorField
                                    v-model="brandLoginColorDark"
                                    columns="col-6"
                                    :readonly="loadingSettings"
                                />
                                <WtgRadioGroup v-model="darkBrandLoginDark" columns="col-6" horizontal>
                                    <WtgRadio
                                        class="ma-0 pa-0"
                                        :value="true"
                                        :label="whiteTextCaption"
                                        :readonly="loadingSettings"
                                    />
                                    <WtgRadio
                                        class="ma-0 pa-0"
                                        :value="false"
                                        :label="blackTextCaption"
                                        :readonly="loadingSettings"
                                    />
                                </WtgRadioGroup>
                            </WtgPanel>
                        </WtgTabsWindowItem>
                    </WtgTabsWindow>
                </WtgPanel>
            </div>
            <div class="wtg-modal__actions">
                <WtgButton :min-width="88" text class="mx-2" data-testid="reset-button" @click="onReset">
                    {{ resetCaption }}
                    <WtgDivider v-if="application.themeConfigurations.length > 0" class="ms-2 me-1" vertical />
                    <WtgPopover v-if="application.themeConfigurations.length > 0" offset-y>
                        <template #activator="args">
                            <WtgIcon v-bind="args.props"> s-icon-dropdown </WtgIcon>
                        </template>
                        <WtgList class="py-0">
                            <span>
                                {{ presetsCaption }}
                            </span>
                            <WtgDivider />
                            <WtgListItem
                                v-for="(theme, index) in application.themeConfigurations"
                                :key="index"
                                @click="onThemeClick(theme)"
                            >
                                {{ theme.name }}
                            </WtgListItem>
                        </WtgList>
                    </WtgPopover>
                </WtgButton>
                <WtgSpacer />
                <WtgButton :min-width="88" text outlined data-testid="cancel-button" @click="onClose">
                    {{ cancelCaption }}
                </WtgButton>
                <WtgButton
                    sentiment="primary"
                    variant="fill"
                    :min-width="88"
                    :loading="saving"
                    :disabled="saveDisabled"
                    data-testid="save-button"
                    @click="onSave"
                >
                    {{ saveCaption }}
                </WtgButton>
            </div>
        </div>
    </WtgDialog>
</template>

<script lang="ts">
import WtgBox from '@components/WtgBox';
import WtgButton from '@components/WtgButton';
import WtgCallout from '@components/WtgCallout';
import WtgCard from '@components/WtgCard';
import WtgColorField from '@components/WtgColorField';
import WtgDialog from '@components/WtgDialog';
import WtgDivider from '@components/WtgDivider';
import WtgIcon from '@components/WtgIcon';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel, { WtgFieldLabel } from '@components/WtgLabel';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgList, { WtgListItem } from '@components/WtgList';
import WtgPanel from '@components/WtgPanel';
import WtgPopover from '@components/WtgPopover';
import WtgRadio, { WtgRadioGroup } from '@components/WtgRadio';
import WtgSelectField from '@components/WtgSelectField';
import WtgSpacer from '@components/WtgSpacer';
import WtgTabs, { WtgTab, WtgTabsWindow, WtgTabsWindowItem } from '@components/WtgTabs';
import { useLocale } from '@composables/locale';
import { Base64 } from 'js-base64';
import { PropType, defineComponent } from 'vue';
import { useApplication } from '../../../composables/application';
import { ThemeOptions } from '../../../theme';
import { FileType, ThemeConfigurationContext, ThemeConfigurationManager, ThemeConfigurationPortalItem } from './types';

const emit = ['update:modelValue'];

export default defineComponent({
    name: 'WtgThemeConfigurationDialog',
    components: {
        WtgBox,
        WtgButton,
        WtgCallout,
        WtgCard,
        WtgColorField,
        WtgDialog,
        WtgDivider,
        WtgFieldLabel,
        WtgIcon,
        WtgIconButton,
        WtgLabel,
        WtgLayoutGrid,
        WtgList,
        WtgListItem,
        WtgPopover,
        WtgPanel,
        WtgSpacer,
        WtgSelectField,
        WtgRadio,
        WtgRadioGroup,
        WtgTab,
        WtgTabs,
        WtgTabsWindow,
        WtgTabsWindowItem,
    },
    props: {
        manager: {
            type: Object as PropType<ThemeConfigurationManager>,
            required: true,
        },
        modelValue: {
            type: Boolean,
            default: false,
        },
    },
    emits: emit,
    setup() {
        const { formatCaption } = useLocale();
        const application = useApplication();
        return {
            formatCaption,
            application,
        };
    },
    data() {
        return {
            currentThemeOptions: {} as ThemeOptions,
            internalValue: false,
            loadingPortals: false,
            loadingSettings: false,
            loadingConfigurationTemplate: false,
            primaryColor: '',
            primaryColorDark: '',
            logo: '',
            logoDark: '',
            openTab: 0,
            darkBrand: false,
            brandColor: '',
            darkBrandDark: false,
            brandColorDark: '',
            darkBrandLogin: false,
            brandLoginColor: '',
            darkBrandLoginDark: false,
            brandLoginColorDark: '',
            saving: false,
            themeVersion: 0,
            fileSizeLimit: 500000,
            fileTypeLightImage: '',
            fileTypeDarkImage: '',
            darkImageFileSizeTooLarge: false,
            lightImageFileSizeTooLarge: false,
            copyThemePortal: '',
            themeName: '' as string | undefined,
            portalPK: undefined as string | undefined,
        };
    },
    computed: {
        activePortal(): string {
            return this.manager?.activePortal ?? '';
        },
        currentContext(): ThemeConfigurationContext {
            return this.manager?.currentContext ?? {};
        },
        logoImage(): string {
            return this.getBlobURL(this.logo, 'light');
        },
        logoDarkImage(): string {
            return this.getBlobURL(this.logoDark, 'dark');
        },
        portal: {
            get(): string {
                return this.currentContext.portal ?? '';
            },
            set(value: string): void {
                this.currentContext.portal = value;
            },
        },
        portals(): ThemeConfigurationPortalItem[] {
            return this.manager?.portals ?? [];
        },
        saveDisabled(): boolean {
            return this.loadingPortals || this.loadingSettings;
        },
        dialogTitle(): string {
            return this.formatCaption('themeConfiguration.title');
        },
        resetCaption(): string {
            return this.formatCaption('themeConfiguration.reset');
        },
        cancelCaption(): string {
            return this.formatCaption('dialog.cancel');
        },
        portalCaption(): string {
            return this.formatCaption('themeConfiguration.portal');
        },
        copyThemeFromCaption(): string {
            return this.formatCaption('themeConfiguration.copyThemeFrom');
        },
        logosCaption(): string {
            return this.formatCaption('themeConfiguration.logos');
        },
        colorsCaption(): string {
            return this.formatCaption('themeConfiguration.colors');
        },
        logoLightDescriptionCaption(): string {
            return this.formatCaption('themeConfiguration.logoLightDescription');
        },
        uploadCaption(): string {
            return this.formatCaption('themeConfiguration.upload');
        },
        logoImageAltCaption(): string {
            return this.formatCaption('themeConfiguration.logoImageAlt');
        },
        fileSizeTooLargeCaption(): string {
            return this.formatCaption('themeConfiguration.fileSizeTooLarge');
        },
        logoDarkDescriptionCaption(): string {
            return this.formatCaption('themeConfiguration.logoDarkDescription');
        },
        lightThemeCaption(): string {
            return this.formatCaption('themeConfiguration.lightTheme');
        },
        primaryCaption(): string {
            return this.formatCaption('themeConfiguration.primary');
        },
        navigationCaption(): string {
            return this.formatCaption('themeConfiguration.navigation');
        },
        textColorCaption(): string {
            return this.formatCaption('themeConfiguration.textColor');
        },
        whiteTextCaption(): string {
            return this.formatCaption('themeConfiguration.whiteText');
        },
        blackTextCaption(): string {
            return this.formatCaption('themeConfiguration.blackText');
        },
        brandLoginCaption(): string {
            return this.formatCaption('themeConfiguration.brandLogin');
        },
        darkThemeCaption(): string {
            return this.formatCaption('themeConfiguration.darkTheme');
        },
        presetsCaption(): string {
            return this.formatCaption('themeConfiguration.presets');
        },
        configureThemeCaption(): string {
            return this.formatCaption('themeConfiguration.configureTheme');
        },
        saveThemeCaption(): string {
            return this.formatCaption('themeConfiguration.saveTheme');
        },
        applyChangesCaption(): string {
            return this.formatCaption('dialog.applyChanges');
        },
        saveCaption(): string {
            return this.formatCaption('dialog.save');
        },
    },
    watch: {
        modelValue: {
            handler(): void {
                this.internalValue = this.modelValue;
            },
            immediate: true,
        },
        internalValue(): void {
            this.$emit('update:modelValue', this.internalValue);
        },
        'currentContext.portal': {
            handler(): void {
                this.loadSettings();
            },
        },
        'manager.themeOptions': {
            handler(): void {
                this.dialogOptionsFromThemeOptions(this.manager?.themeOptions);
            },
            immediate: true,
        },
        copyThemePortal: {
            handler(): void {
                this.loadThemeConfiguration(this.copyThemePortal);
            },
        },
    },
    mounted() {
        this.loadPortals();
        this.loadSettings();
    },
    methods: {
        rgbFromrgba(color: string | undefined): string | undefined {
            if (color && color.length === 9 && color[0] === '#' && color[7] === 'F' && color[8] === 'F') {
                color = color.substring(0, 7);
            }
            return color;
        },
        dialogOptionsFromThemeOptions(themeOptions?: ThemeOptions): void {
            const baseTheme = this.$wtgUi.baseTheme;
            const lightColors = themeOptions?.colors?.light;
            const darkColors = themeOptions?.colors?.dark;
            const baseLightColors = baseTheme?.colors?.light;
            const baseDarkColors = baseTheme?.colors?.dark;

            this.primaryColor = lightColors?.primary ?? baseLightColors?.primary ?? '';
            this.primaryColorDark = darkColors?.primary ?? baseDarkColors?.primary ?? '';

            this.logo = themeOptions?.logoLightImage ?? baseTheme?.logoLightImage ?? '';
            this.logoDark = themeOptions?.logoDarkImage ?? baseTheme?.logoDarkImage ?? '';
            this.fileTypeLightImage = themeOptions?.logoLightImageFileType ?? baseTheme?.logoLightImageFileType ?? '';
            this.fileTypeDarkImage = themeOptions?.logoDarkImageFileType ?? baseTheme?.logoDarkImageFileType ?? '';

            this.themeVersion = themeOptions?.themeVersion ?? baseTheme?.themeVersion ?? 0;

            this.brandColor = lightColors?.app?.brandColor ?? baseLightColors?.app?.brandColor ?? '';
            this.brandColorDark = darkColors?.app?.brandColor ?? baseDarkColors?.app?.brandColor ?? '';
            this.darkBrand =
                lightColors?.app?.brandColorDark !== undefined
                    ? lightColors?.app?.brandColorDark
                    : baseLightColors?.app?.brandColorDark ?? false;
            this.darkBrandDark =
                darkColors?.app?.brandColorDark !== undefined
                    ? darkColors?.app?.brandColorDark
                    : baseDarkColors?.app?.brandColorDark ?? false;

            this.brandLoginColor = lightColors?.app?.brandLoginColor ?? baseLightColors?.app?.brandLoginColor ?? '';
            this.brandLoginColorDark = darkColors?.app?.brandLoginColor ?? baseDarkColors?.app?.brandLoginColor ?? '';
            this.darkBrandLogin =
                lightColors?.app?.brandLoginColorDark !== undefined
                    ? lightColors?.app?.brandLoginColorDark
                    : baseLightColors?.app?.brandLoginColorDark ?? false;
            this.darkBrandLoginDark =
                darkColors?.app?.brandLoginColorDark !== undefined
                    ? darkColors?.app?.brandLoginColorDark
                    : baseDarkColors?.app?.brandLoginColorDark ?? false;
        },
        themeOptionsFromDialogOptions(): ThemeOptions {
            const baseTheme = this.$wtgUi.baseTheme;
            const themeOptions: any = {
                colors: {
                    light: {
                        app: {},
                        controls: {},
                    },
                    dark: {
                        app: {},
                        controls: {},
                    },
                },
            };

            if (this.primaryColor && this.primaryColor !== baseTheme?.colors?.light?.primary) {
                themeOptions.colors.light.primary = this.rgbFromrgba(this.primaryColor);
            }
            if (this.primaryColorDark && this.primaryColorDark !== baseTheme?.colors?.dark?.primary) {
                themeOptions.colors.dark.primary = this.rgbFromrgba(this.primaryColorDark);
            }

            if (this.logo && this.logo !== baseTheme.logoLightImage) {
                themeOptions.logoLightImage = this.logo;
                themeOptions.logoLightImageFileType = this.fileTypeLightImage;
            }
            if (this.logoDark && this.logoDark !== baseTheme.logoDarkImage) {
                themeOptions.logoDarkImage = this.logoDark;
                themeOptions.logoDarkImageFileType = this.fileTypeDarkImage;
            }

            if (this.themeVersion) {
                themeOptions.themeVersion = this.themeVersion;
            }

            if (
                this.brandColor !== this.rgbFromrgba(baseTheme?.colors?.light?.app?.brandColor) ||
                baseTheme.colors.light.app.brandColorDark !== this.darkBrand
            ) {
                themeOptions.colors.light.app.brandColor = this.rgbFromrgba(this.brandColor);
                themeOptions.colors.light.app.brandColorDark = this.darkBrand;
            }
            if (
                this.brandColorDark !== this.rgbFromrgba(baseTheme?.colors?.dark?.app?.brandColor) ||
                baseTheme.colors.dark.app.brandColorDark !== this.darkBrandDark
            ) {
                themeOptions.colors.dark.app.brandColor = this.rgbFromrgba(this.brandColorDark);
                themeOptions.colors.dark.app.brandColorDark = this.darkBrandDark;
            }

            if (
                this.brandLoginColor !== this.rgbFromrgba(baseTheme?.colors?.light?.app?.brandLoginColor) ||
                baseTheme.colors.light.app.brandLoginColorDark !== this.darkBrandLogin
            ) {
                themeOptions.colors.light.app.brandLoginColor = this.rgbFromrgba(this.brandLoginColor);
                themeOptions.colors.light.app.brandLoginColorDark = this.darkBrandLogin;
            }
            if (
                this.brandLoginColorDark !== this.rgbFromrgba(baseTheme?.colors?.dark?.app?.brandLoginColor) ||
                baseTheme.colors.dark.app.brandLoginColorDark !== this.darkBrandLoginDark
            ) {
                themeOptions.colors.dark.app.brandLoginColor = this.rgbFromrgba(this.brandLoginColorDark);
                themeOptions.colors.dark.app.brandLoginColorDark = this.darkBrandLoginDark;
            }

            return themeOptions;
        },
        async loadPortals(): Promise<void> {
            try {
                this.loadingPortals = true;
                await this.manager?.loadPortalsAsync();
            } finally {
                this.loadingPortals = false;
            }
        },
        async loadSettings(): Promise<void> {
            try {
                this.loadingSettings = true;
                await this.manager?.loadSettingsAsync();
            } finally {
                this.loadingSettings = false;
            }
        },
        async loadThemeConfiguration(portalPK: string): Promise<void> {
            this.loadingSettings = true;

            const themeOptions = await this.manager?.loadThemeConfigurationAsync(portalPK);
            this.loadingSettings = false;

            this.dialogOptionsFromThemeOptions(themeOptions);
        },
        async loadThemeOptions(): Promise<void> {
            this.loadingSettings = true;

            try {
                const themeOptions = await this.manager.getCurrentThemeOptionsAsync();
                this.manager.updateThemeOptions(themeOptions);
            } finally {
                this.loadingSettings = false;
            }
        },
        onClose(): void {
            this.internalValue = false;
        },
        onReset(): void {
            this.manager.updateThemeOptions(undefined);
            this.dialogOptionsFromThemeOptions(this.manager.themeOptions);
        },
        async onSave(): Promise<void> {
            try {
                const themeOptions = this.themeOptionsFromDialogOptions();
                this.saving = true;
                if (this.portal === this.activePortal) {
                    this.application.applyThemeOptions(themeOptions);
                }
                this.manager.updateThemeOptions(themeOptions);
                await this.manager?.onSaveAsync();
            } finally {
                this.saving = false;
                this.internalValue = false;
            }
        },
        onThemeClick(options: ThemeOptions): void {
            this.dialogOptionsFromThemeOptions(options);
        },
        onLogoSelectClick(type: string): void {
            const fileInputElement = this.$refs[`${type}-logo-input`] as HTMLInputElement;
            if (fileInputElement) {
                fileInputElement.value = '';
                fileInputElement.click();
            }
        },
        onLogoChange(event: any, type: string): void {
            const logoFile = event.target.files[0];
            if (logoFile.size > this.fileSizeLimit) {
                if (type === 'light') {
                    this.darkImageFileSizeTooLarge = true;
                } else if (type === 'dark') {
                    this.lightImageFileSizeTooLarge = true;
                }
            } else {
                if (type === 'light') {
                    this.darkImageFileSizeTooLarge = false;
                    this.fileTypeLightImage = logoFile.type;
                } else if (type === 'dark') {
                    this.lightImageFileSizeTooLarge = false;
                    this.fileTypeDarkImage = logoFile.type;
                }

                const reader = new FileReader();
                reader.addEventListener('load', (event) => {
                    this.updateLogo(event.target, type);
                });
                reader.readAsArrayBuffer(logoFile);
            }
        },
        updateLogo(target: any, type: string): void {
            if (type === 'dark') {
                this.logoDark = target
                    ? (Base64.fromUint8Array(new Uint8Array(target.result)) as string)
                    : this.logoDark;
            } else {
                this.logo = target ? (Base64.fromUint8Array(new Uint8Array(target.result)) as string) : this.logo;
            }
        },
        getBlobURL(value: string, type: string): string {
            if (value) {
                const fileType = this.getFileTypeForImage(type);
                const data = Base64.isValid(value) ? Base64.toUint8Array(value) : value;
                if (fileType === FileType.SVG) {
                    const blob = new Blob([data], { type: FileType.SVG });
                    return URL.createObjectURL(blob);
                } else if (fileType === FileType.PNG) {
                    const blob = new Blob([data], { type: FileType.PNG });
                    return URL.createObjectURL(blob);
                }
            }
            return '';
        },
        getFileTypeForImage(imageType: string): string {
            if (imageType === 'dark') {
                return this.fileTypeDarkImage;
            } else {
                return this.fileTypeLightImage;
            }
        },
    },
});
</script>
