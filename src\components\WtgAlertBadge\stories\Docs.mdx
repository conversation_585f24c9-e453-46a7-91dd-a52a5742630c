import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import image from '../../../storybook/assets/component-button-decisiontree.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import WtgAlertBadge from './WtgAlertBadge.stories.ts';

import alertBadgeDoExample from '../../../assets/WtgAlertBadge/alert-badge-do-example.png';
import alertBadgeDontExample from '../../../assets/WtgAlertBadge/alert-badge-dont-example.png';
import alertBadgeSentiments from '../../../assets/WtgAlertBadge/alert-badge-sentiments.png';

import alertBadgeDoExample2 from '../../../assets/WtgAlertBadge/alert-badge-do-example2.png';
import alertBadgeDontExample2 from '../../../assets/WtgAlertBadge/alert-badge-dont-example2.png';

<Meta of={WtgAlertBadge} />

<div className="component-header">
    <h1>Alert badge</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-1025&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    The alert badge indicates status, often as a modifier on components like tabs or list items, to signal that something within their content requires attention. It can also be used inline as a standalone component in other contexts to reinforce status.

</p>

## API

<Canvas className="canvas-preview">
    <Story of={WtgAlertBadge} />
</Canvas>

## How to use

<div className="do-dont-pair">

    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-do">
            <img src={alertBadgeDoExample}  />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ✔️ <strong>Do</strong>
            </p>
            <p>Use the alert badge to reinforce status or to indicate status associated with a destination.</p>
        </div>
    </div>
    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-dont">
            <img src={alertBadgeDontExample}  />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ❌ <strong>Don't</strong>
            </p>
            <p>Use the alert badge when its meaning or association is unclear.</p>
        </div>

    </div>

        <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-do">
            <img src={alertBadgeDoExample2}  />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ✔️ <strong>Do</strong>
            </p>
            <p>Use the built-in info icon on text fields.</p>
        </div>
    </div>
    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-dont">
            <img src={alertBadgeDontExample2}  />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ❌ <strong>Don't</strong>
            </p>
            <p>Don't replace the built-in info icon with the info alert badge. </p>
        </div>

    </div>

</div>

## Sentiments

<img srcSet={`${alertBadgeSentiments} 3x`} alt="anatomy of drawer component" />
<ol className="anatomy-list">
    <li>
        <strong>Success: </strong> Conveys positive confirmation or reinforcement.
    </li>
    <li>
        <strong>Warning: </strong> Conveys a potential issue or caution. The issue might not be critical but it requires user awareness and/or acknowledgment of possible risks before proceeding.
    </li>
    <li>
         <strong>Critical:</strong> Conveys critical issues that demand immediate attention. 
    </li>
    <li>
         <strong>Info:</strong> Used to convey neutral, important information that aids user understanding or provides helpful context. Do not use in replacement of the built in info icon found on [text field](/docs/components-text-field--docs) labels.
    </li>

</ol>

## Related components

-   [Status](/docs/components-status--docs)
-   [Tag](/docs/components-tag--docs)
-   [Notification](/docs/components-notification--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
