import { execSync } from 'child_process';
import fg from 'fast-glob';
import { promises as fs } from 'fs';
import path from 'path';
import { createChecker } from 'vue-component-meta';

interface ComponentMetadata {
    props: Array<{
        name: string;
        description?: string;
    }>;
    events: Array<{
        name: string;
        description: string;
    }>;
    slots: Array<{
        name: string;
        description: string;
    }>;
}

const repoRoot = execSync('git rev-parse --show-toplevel', { encoding: 'utf-8' }).trim();

async function generateMetadata() {
    const checker = createChecker(path.join(repoRoot, 'tsconfigDocGen.json'), {
        forceUseTs: true,
        schema: { ignore: ['MyIgnoredNestedProps'] },
        printer: { newLine: 1 },
    });

    const files = await fg('src/components/**/Wtg*.vue', { absolute: true });

    const metadata: Record<string, ComponentMetadata> = {};

    for (const file of files) {
        const componentName = path.basename(file, '.vue');
        try {
            const meta = checker.getComponentMeta(file);

            const simplified = {
                props:
                    meta.props
                        ?.filter((prop) => !prop.global)
                        .map((prop) => ({
                            name: prop.name,
                            description: prop.description?.replace(/\r?\n/g, ' ').trim(),
                        })) || [],
                events:
                    meta.events?.map((event) => ({
                        name: event.name,
                        description: (event.description?.replace(/\r?\n/g, ' ') || '').trim(),
                    })) || [],
                slots:
                    meta.slots?.map((slot) => ({
                        name: slot.name,
                        description: (slot.description?.replace(/\r?\n/g, ' ') || '').trim(),
                    })) || [],
            };

            metadata[componentName] = simplified;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : err;
            console.warn(`Failed to generate metadata for ${componentName}:`, errorMessage);
        }
    }

    const outputDir = path.join(repoRoot, 'dist/metadata');
    await fs.mkdir(outputDir, { recursive: true });

    const prettyPath = path.join(outputDir, 'supply.json');
    const minifiedPath = path.join(outputDir, 'supply.min.json.txt');

    await fs.writeFile(prettyPath, JSON.stringify(metadata, null, 2), 'utf-8');
    await fs.writeFile(minifiedPath, JSON.stringify(metadata), 'utf-8');

    console.log(`Metadata generated for ${Object.keys(metadata).length} components.`);

    const markdownPath = path.join(outputDir, 'supply.md');

    function toMarkdown(metadata: Record<string, ComponentMetadata>): string {
        const lines: string[] = [];

        for (const [componentName, meta] of Object.entries(metadata)) {
            lines.push(`## ${componentName}`);
            lines.push('');

            if (meta.props?.length) {
                lines.push('### Props');
                lines.push('');
                for (const prop of meta.props) {
                    lines.push(`- **\`${prop.name}\`**${prop.description ? ` — ${prop.description}` : ''}`);
                }
                lines.push('');
            }

            if (meta.events?.length) {
                lines.push('### Events');
                lines.push('');
                for (const event of meta.events) {
                    lines.push(`- **\`${event.name}\`**${event.description ? ` — ${event.description}` : ''}`);
                }
                lines.push('');
            }

            if (meta.slots?.length) {
                lines.push('### Slots');
                lines.push('');
                for (const slot of meta.slots) {
                    lines.push(`- **\`${slot.name}\`**${slot.description ? ` — ${slot.description}` : ''}`);
                }
                lines.push('');
            }

            lines.push('---');
            lines.push('');
        }

        return lines.join('\n');
    }

    const markdownContent = toMarkdown(metadata);
    await fs.writeFile(markdownPath, markdownContent, 'utf-8');
    console.log(`✅ Markdown file written to ${markdownPath}`);
}

generateMetadata().catch((err) => {
    console.error('Failed to generate metadata:', err);
    process.exit(1);
});
