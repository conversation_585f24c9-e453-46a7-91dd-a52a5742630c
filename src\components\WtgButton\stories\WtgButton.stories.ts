import WtgButton from '@components/WtgButton/WtgButton.vue';
import WtgCol from '@components/WtgCol';
import WtgIcon from '@components/WtgIcon';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgPanel from '@components/WtgPanel';
import WtgRow from '@components/WtgRow';
import { useSupplyPrefixIconsName } from '@composables/icon';
import { measureArgTypes } from '@composables/measure';
import { sizeArgTypes } from '@composables/size';
import { tooltipArgTypes } from '@composables/tooltip';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { expect, userEvent, within } from '@storybook/test';
import { Meta, StoryObj } from '@storybook/vue3';
import { ButtonSandboxTemplate } from './templates/wtg-button-sandbox.stories-template';
import { ButtonWithIconsTemplate } from './templates/wtg-button-with-icons.stories-template';
import { ButtonWithSentimentsTemplate } from './templates/wtg-button-with-sentiments.stories-template';

const icons = useSupplyPrefixIconsName();
const meta: Meta<typeof WtgButton> = {
    title: 'Components/Button',
    component: WtgButton,
    parameters: {
        docs: {
            description: {
                component:
                    'Buttons are interactive components that trigger an action, such as submitting a form, confirming a decision, or initiating an operation. They are designed to communicate and action a clear path forward or logical next step of a user’s journey.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=59-1790&mode=design&t=FoGkzhcXiCnlZFCr-0',
        },
        layout: 'centered',
        slots: {
            default: {
                description: 'Default slot',
            },
        },
    },
    render: (args) => ({
        components: { WtgButton },
        methods: {
            onClick: action('click'),
        },
        setup: () => ({ args }),
        template: `
            <WtgButton v-bind="args" @click="onClick">
                {{ args.default }}
            </WtgButton>`,
    }),
    argTypes: {
        loading: {
            control: 'boolean',
        },
        leadingIcon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
        trailingIcon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
        sentiment: {
            control: 'select',
            options: ['', 'critical', 'primary', 'success'],
        },
        type: {
            control: 'select',
            options: ['submit', 'reset', 'button'],
        },
        variant: {
            control: 'select',
            options: ['', 'fill', 'ghost'],
        },
        ...measureArgTypes,
        ...sizeArgTypes,
        ...tooltipArgTypes,

        default: {
            control: 'text',
        },
    },
    decorators: [
        () => ({
            template: `
            <div style="display: flex; flex-wrap: wrap;">
                <story/>
            </div>
            `,
        }),
    ],
};
export default meta;

export const Default: StoryObj = {
    args: { variant: 'fill', sentiment: 'primary', default: 'Click here' },
};

export const Fill: StoryObj = {
    args: {
        label: 'Fill',
        variant: 'fill',
        sentiment: 'primary',
    },
    render: (args) => ({
        components: { WtgButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `
            <WtgButton v-bind="args"@click="action">
                {{args.label}}
            </WtgButton>`,
    }),
};

export const Outline: StoryObj = {
    args: { label: 'Outline' },
    render: (args) => ({
        components: { WtgButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `
            <WtgButton v-bind="args" @click="action">
                {{args.label}}
            </WtgButton>`,
    }),
};

export const Ghost: StoryObj = {
    args: { label: 'Ghost', variant: 'ghost' },
    render: (args) => ({
        components: { WtgButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `
            <WtgButton 
                v-bind="args"
                @click="action">
                    {{args.label}}
            </WtgButton>`,
    }),
};

export const Sentiments: StoryObj = {
    name: 'Sentiments',
    args: {},
    render: (args) => ({
        components: { WtgButton, WtgCol, WtgRow },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: ButtonWithSentimentsTemplate,
    }),
    play: async ({ canvasElement, step }) => {
        const canvas = within(canvasElement);
        const focusedButton = canvas.getByTestId('testSentimentsButton-primary');
        await step('Focus on button', async () => {
            focusedButton.focus();
            await step('Should show button outline', () => {
                const outline = getComputedStyle(focusedButton).outlineColor;
                expect(outline).toEqual('rgb(55, 30, 225)');
            });
        });
    },
};

export const Icons: StoryObj = {
    args: { label: 'Label', icon: 's-icon-plus' },
    render: (args) => ({
        components: { WtgButton, WtgIcon, WtgCol, WtgRow },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: ButtonWithIconsTemplate,
    }),
};

export const disabled: StoryObj = {
    args: {
        disabled: true,
    },
    render: (args) => ({
        components: { WtgButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `
            <div style="flex: 1 1 100%; display: flex; flex-wrap: wrap; gap: 8px">
                <WtgButton 
                    v-bind="args"
                    data-testid="test-disabled-button"
                    tooltip="Disabled button tooltip"
                    @click="action">
                        Outline
                </WtgButton>
                <WtgButton 
                    variant="fill"
                    sentiment="primary"
                    v-bind="args"
                    @click="action">
                        Fill
                </WtgButton>
                <WtgButton 
                    variant="ghost"
                    v-bind="args"
                    @click="action">
                        Ghost
                </WtgButton>
            </div>`,
    }),
    play: async ({ canvasElement, step }) => {
        const canvas = within(canvasElement);
        const body = within(document.body);
        const disabledButton = canvas.getByTestId('test-disabled-button');
        await step('Hover on button', async () => {
            await userEvent.hover(disabledButton);
            await step('Should show button tooltip', async () => {
                setTimeout(async () => {
                    await expect(body.getByText('Disabled button tooltip')).toBeVisible();
                }, 1000);
            });
        });
    },
};

export const Sandbox: StoryObj = {
    args: { icon: 's-icon-placeholder', label: 'Label' },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgButton, WtgCol, WtgRow, WtgPanel, WtgLayoutGrid },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: templateWithRtl(ButtonSandboxTemplate),
    }),
};
