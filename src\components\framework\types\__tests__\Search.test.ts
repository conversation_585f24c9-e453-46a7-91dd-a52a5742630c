import MockSearchProvider from './__mocks__/SearchProvider';
import { WtgFrameworkSearchHandler, WtgFrameworkSearchResultRecord, WtgFrameworkSearchResultEntity } from '../search';

describe('SearchHandler', () => {
    let result: WtgFrameworkSearchResultRecord[], searchHandler: WtgFrameworkSearchHandler;
    const searchProvider = new MockSearchProvider();

    beforeEach(() => {
        jest.spyOn(searchProvider, 'getItemsAsync');
        jest.spyOn(searchProvider.shorthandToEntityType, 'get');

        searchHandler = new WtgFrameworkSearchHandler(searchProvider);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('default values', () => {
        it('should not have shorthand', () => {
            expect(searchHandler.selectedShorthands).toEqual([]);
        });
    });

    describe('getItemsAsync', () => {
        describe.each([
            ['', []],
            ['a', []],
            ['ab', []],
            ['   a', []],
            ['DM2:', ['DM2']],
            ['DM2:a', ['DM2']],
            ['DM2:ab', ['DM2']],
            ['DM2:   a', ['DM2']],
            ['DM2:DUM:', ['DUM', 'DM2']],
            ['DM2:DUM:a', ['DUM', 'DM2']],
            ['DM2:DUM:ab', ['DUM', 'DM2']],
            ['DM2:DUM:   a', ['DUM', 'DM2']],
        ])('given a query less than 3 characters in the search string %s', (searchString, expectedShorthands) => {
            beforeEach(async () => {
                result = await searchHandler.getItemsAsync(searchString);
            });

            describe(`searching with ${searchString}`, () => {
                it('should return no items if empty search string', () => {
                    expect(result).toMatchObject([]);
                });

                it('should not execute any query', () => {
                    expect(searchProvider.getItemsAsync).not.toHaveBeenCalled();
                });

                it('should update selected shorthands', () => {
                    expect(searchHandler.selectedShorthands).toEqual(expectedShorthands);
                });
            });
        });

        describe('given valid search string', () => {
            let items: WtgFrameworkSearchResultEntity[];

            beforeEach(async () => {
                items = [
                    {
                        PK: 'some-guid',
                        entityType: 'IDummyBizo',
                        entityName: 'Dummy Bizo',
                        color: 'AABBCC',
                        jobReference: 'DUMMYREFERENCE',
                        matchedColumn: 'SEARCH1',
                        keyFields: [{ caption: 'FIELD1-CAPTION', value: 'FIELD1' }],
                    },
                    {
                        PK: 'some-guid-2',
                        entityType: 'IDummyBizo',
                        entityName: 'Dummy Bizo',
                        color: 'AABBCC',
                        jobReference: 'DUMMYREFERENCE',
                        matchedColumn: 'SEARCH1',
                        keyFields: [{ caption: 'FIELD1-CAPTION', value: 'FIELD1' }],
                    },
                ];

                jest.spyOn(searchProvider, 'getItemsAsync').mockImplementation(() => Promise.resolve(items));
                result = await searchHandler.getItemsAsync('some search');
            });

            it('should return results and result length', () => {
                expect(result).toBe(items);
            });

            it('should not call retrieve entity type with no shorthand', async () => {
                expect(searchProvider.shorthandToEntityType.get).not.toHaveBeenCalled();
            });

            it('should call searchProvider.getItemsAsync with search term and entity type', async () => {
                expect(searchProvider.getItemsAsync).toHaveBeenCalledWith('some search', []);
            });
        });

        describe('given query with shorthand', () => {
            const validShorthands: string[] = searchProvider.entities.map((config) =>
                config.shorthand?.toLowerCase()
            ) as string[];

            validShorthands.forEach((shorthand) => {
                describe(`given valid shorthand (${shorthand})`, () => {
                    const shorthandColoned = shorthand + ':';

                    describe('if shorthand provided with no query', () => {
                        beforeEach(async () => {
                            result = await searchHandler.getItemsAsync(shorthandColoned);
                        });

                        it('should return no items', () => {
                            expect(result).toMatchObject([]);
                        });

                        it('should not execute any query', () => {
                            expect(searchProvider.getItemsAsync).not.toHaveBeenCalled();
                        });

                        it('should set shorthand', () => {
                            expect(searchHandler.selectedShorthands).toEqual([shorthand.toUpperCase()]);
                        });
                    });

                    describe('if query and shorthand provided', () => {
                        beforeEach(async () => {
                            result = await searchHandler.getItemsAsync(shorthandColoned + 'some search');
                        });

                        it('should call getItemsAsync with correct entity type', () => {
                            expect(searchProvider.getItemsAsync).toHaveBeenCalledWith('some search', [
                                findEntityTypeFromConfig(shorthand),
                            ]);
                        });

                        it('should set shorthand', () => {
                            expect(searchHandler.selectedShorthands).toEqual([shorthand.toUpperCase()]);
                        });
                    });

                    describe('shorthands stored from previous search', () => {
                        beforeEach(async () => {
                            await searchHandler.getItemsAsync(shorthandColoned + 'some search');
                        });

                        describe('if next search has no shorthands', () => {
                            beforeEach(async () => {
                                result = await searchHandler.getItemsAsync('other search');
                            });

                            it('should call getItemsAsync with correct entity types', () => {
                                expect(searchProvider.getItemsAsync).toHaveBeenCalledWith('other search', [
                                    findEntityTypeFromConfig(shorthand),
                                ]);
                            });

                            it('should not change shorthand', () => {
                                expect(searchHandler.selectedShorthands).toEqual([shorthand.toUpperCase()]);
                            });
                        });

                        describe('if next search has shorthands', () => {
                            beforeEach(async () => {
                                searchProvider.shorthandToEntityType.set('ABC', 'ISomeOtherBizo');
                                searchProvider.shorthandToEntityType.set('XYZ', 'IAnotherOtherBizo');
                                result = await searchHandler.getItemsAsync('ABC: XYZ: other search');
                            });

                            it('should call getItemsAsync with all shorthands', () => {
                                expect(searchProvider.getItemsAsync).toHaveBeenCalledWith('other search', [
                                    findEntityTypeFromConfig(shorthand),
                                    findEntityTypeFromConfig('ABC'),
                                    findEntityTypeFromConfig('XYZ'),
                                ]);
                            });

                            it('should add shorthands to selectedShorthands', () => {
                                expect(searchHandler.selectedShorthands).toEqual([
                                    shorthand.toUpperCase(),
                                    'ABC',
                                    'XYZ',
                                ]);
                            });
                        });
                    });
                });
            });

            describe('given invalid shorthand', () => {
                beforeEach(async () => {
                    result = await searchHandler.getItemsAsync('TST:');
                });

                it('should not be treated like a shorthand query', () => {
                    expect(searchProvider.getItemsAsync).toHaveBeenCalledWith('TST:', []);
                });
            });
        });
    });

    function findEntityTypeFromConfig(shorthand: string) {
        return searchProvider.shorthandToEntityType.get(shorthand.toUpperCase());
    }
});
