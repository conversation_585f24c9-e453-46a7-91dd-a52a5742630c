import WtgHelpDialog from '@components/framework/WtgHelpDialog/WtgHelpDialog.vue';
import WtgButton from '@components/WtgButton';
import WtgCheckbox from '@components/WtgCheckbox';
import WtgDialog from '@components/WtgDialog';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import { DOMWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../../WtgUi';

enableAutoUnmount(afterEach);
const wtgUi = new WtgUi();

describe('WtgHelpDialog', () => {
    let el: HTMLElement;
    let helpContent: string;

    beforeEach(() => {
        wtgUi.breakpoint.lgAndUp = false;
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        helpContent = '<span class="help-content">content</span>';
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('it renders a component', () => {
        const wrapper = mountComponent();
        expect(wrapper.exists()).toBe(true);
    });

    describe('when the value property is set to true', () => {
        test('it brings up a dialog', () => {
            const wrapper = mountComponent({ propsData: { modelValue: true } });
            const dialog = wrapper.findComponent(WtgDialog);
            expect(dialog.props('modelValue')).toBe(true);
        });

        test('it displays the dialog title', () => {
            const wrapper = mountComponent({
                propsData: { modelValue: true, dialogTitle: 'Help', title: 'Order details' },
            });

            const labels = wrapper.findAllComponents(WtgLabel);
            expect(labels.at(0)!.text()).toBe('Help');
            expect(labels.at(1)!.text()).toBe('Order details');
        });

        test('it sets the width of the model based on teh viewport', async () => {
            const wrapper = mountComponent({ propsData: { value: true } });
            let dialog = wrapper.findComponent(WtgDialog);
            expect(dialog.props('width')).toBe('60vw');
            wtgUi.breakpoint.mdAndDown = false;
            await nextTick();

            dialog = wrapper.findComponent(WtgDialog);
            expect(dialog.props('width')).toBe('30vw');
        });

        test('it displays helpContent', () => {
            const wrapper = mountComponent({
                propsData: { modelValue: true, loading: false, loadingText: 'Loading', helpContent },
            });

            const content = new DOMWrapper(document.body).find('.help-content');
            expect(content.text()).toBe('content');

            const labels = wrapper.findAllComponents(WtgLabel);
            expect(labels.at(2)).toBeUndefined(); // No Loading Label
        });

        test('it displays loading text and hides help content when loading is true', () => {
            const wrapper = mountComponent({
                propsData: { modelValue: true, loading: true, loadingText: 'Loading help content', helpContent },
            });

            const labels = wrapper.findAllComponents(WtgLabel);
            expect(labels.at(2)!.text()).toBe('Loading help content');
            expect(wrapper.findComponent({ name: 'WtgProgressLinear' }).exists()).toBe(true);

            const content = new DOMWrapper(document.body).find('.help-content');
            expect(content.exists()).toBe(false);
        });

        test('it renders a checkbox', async () => {
            const wrapper = mountComponent({
                propsData: { modelValue: true, alwaysOpenHelp: true, alwaysOpenHelpCaption: 'Always Open Help' },
            });
            const checkbox = wrapper.findComponent(WtgCheckbox);
            expect(checkbox.props().label).toEqual('Always Open Help');
            expect(checkbox.props().modelValue).toEqual(true);
        });

        test('it should emit update:modelValue when "x" is clicked', async () => {
            const wrapper = mountComponent({ propsData: { closeTooltip: 'Close', modelValue: true } });
            const close = wrapper.findComponent(WtgIconButton);
            expect(close.props().tooltip).toEqual('Close');
            await close?.trigger('click');

            expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        });

        test('it should emit update:modelValue when close button is clicked', async () => {
            const wrapper = mountComponent({ propsData: { modelValue: true } });
            const close = wrapper.findAllComponents(WtgButton).at(1);
            await close?.trigger('click');

            expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        });
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgHelpDialog, {
            propsData: { ...propsData },
            wtgUi,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
