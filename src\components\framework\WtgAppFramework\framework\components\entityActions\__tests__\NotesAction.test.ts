import { WtgFrameworkTask, WtgFrameworkTaskStandardAction } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import NotesAction from '../NotesAction.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('notes-action', () => {
    let task: WtgFrameworkTask;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        task = new WtgFrameworkTask();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is NotesAction', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('NotesAction');
    });

    describe('when given a showNotesAction', () => {
        beforeEach(() => {
            task.showNotesAction = {
                visible: false,
                caption: 'Notes',
                label: 'Notes',
                onInvoke: jest.fn(),
            };
        });

        test('its does not render a button when the action visible is false', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            expect(buttons.length).toBe(0);
        });

        test('its does render a button with an icon when the action visible is true', () => {
            task.showNotesAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.exists()).toBe(true);
        });

        test('it renders the button with aria-haspopup="dialog" attribute', () => {
            task.showNotesAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.attributes('aria-haspopup')).toBe('dialog');
        });

        test('the button calls the action onInvoke method when clicked', async () => {
            task.showNotesAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            expect(task.showNotesAction.onInvoke).toBeCalledTimes(1);
        });

        describe('on a large screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = false;
                task.showNotesAction.visible = true;
            });

            test('it display as a ghost button', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().variant).toBe('ghost');
                expect(button.text()).toBe('Notes');
            });

            test('it displays the button with a leading icon', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().leadingIcon).toBe('s-icon-notes');
            });
        });

        describe('on a small screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = true;
                task.showNotesAction.visible = true;
            });

            test('it display as a icon button with a tooltip', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.props().icon).toBe('s-icon-notes');
                expect(button.props().tooltip).toBe('Notes');
            });
        });
    });

    describe('Property tests', () => {
        test('when props is passed', () => {
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task: task } });
            expect(wrapper.vm.action).toStrictEqual(task.showNotesAction);
        });

        test('when props is not passed and application.currentTask is not there', () => {
            const defaultValue: WtgFrameworkTaskStandardAction = {
                visible: false,
                caption: 'Notes',
                label: 'Notes',
                onInvoke: (): void => undefined,
            };
            const wrapper: VueWrapper<any> = mountComponent();
            expect(wrapper.vm.action.caption).toStrictEqual(defaultValue.caption);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(NotesAction, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
