import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import statusInProgress from '../../../storybook/assets/statusInProgress.svg';
import copyGuidelinesExample from '../../../storybook/assets/WtgEmptyState-copy-guidelines-example.png';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgEmptyState from './WtgEmptyState.stories.ts';

<Meta of={WtgEmptyState} />

<div className="component-header">
    <h1>Empty state</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=82-40428&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    An empty state occurs when a user interface has no data to display, such as during first-time use, after clearing
    data, or when an error prevents content from loading. Effective empty states help guide users forward, avoiding dead
    ends.
</p>

## API

<Canvas className="canvas-preview" of={WtgEmptyState.Default} />
<Controls of={WtgEmptyState.Default} sort={'alpha'} />

## Variants

### First use

Appears when a page or feature is loaded for the first time and requires user input to generate content. Is also used as the default empty state for data tables.

#### Examples

-   First load of a search page
-   A newly created entity or page with no generated data

#### Copy guidance

-   Reassure users why the empty state is there
-   Provide clear instructions on how to populate it

---

### User-cleared

Occurs when a user actively removes all items from a section, such as clearing an inbox or completing all tasks.

#### Examples

-   An empty task list after marking all tasks as done
-   A cleared inbox with no messages remaining

#### Copy guidance

-   Acknowledge what happened
-   Briefly explain why the section is empty
-   Provide a next step, if applicable (e.g., add a new task)

---

### Error

Appears when something goes wrong, preventing the expected content or workflow from loading. These should offer a recovery option whenever possible.

#### Examples

-   A system error preventing data from loading
-   A page that fails to process a request
-   A user attempting to access a restricted area

#### Copy guidance

-   Clearly state what went wrong
-   Briefly explain why it happened
-   Reassure the user where appropriate
-   Provide a way forward (e.g., retry, refresh, or contact support)

---

### No Data Found

Occurs when a section of the page has no content due to a user-generated action (e.g., a search with no matching results).

#### Examples

-   A search query returning no results
-   A filter that excludes all available data

#### Copy guidance

-   Explain why nothing is displayed
-   Provide reassurance (e.g., "Try adjusting your filters")
-   Suggest alternative actions, if applicable

---

### Missing Page

Occurs when a user navigates to a page that no longer exists or was removed.

#### Examples

-   A deleted or outdated link
-   A 404 error when a page is not found

#### Copy guidance

-   Clearly state that the page doesn’t exist
-   Offer navigation options to guide the user back (e.g., “Return to homepage”)

---

### Restricted

Occurs when a user does not have permission to access a page or feature.

#### Examples

-   A user without admin rights trying to access a restricted dashboard
-   An expired session requiring re-authentication

#### Copy guidance

-   Explain that access is restricted
-   Provide a reason, if possible (e.g., "You don’t have the required permissions")
-   Offer a way to request access or sign in

## Content guidelines

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview).

Empty state copy should quickly communicate why it appears and what users can do next. Keep messaging simple and direct, covering these key points:

-   What happened
-   Why it happened
-   Reassurance
-   Next steps

Not all points are necessary, assess what’s most relevant for the scenario.

<img src={copyGuidelinesExample}></img>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
