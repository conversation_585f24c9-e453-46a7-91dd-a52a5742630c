import WtgDataTableServer from '@components/WtgDataTable/WtgDataTableServer/WtgDataTableServer.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { VDataTableServer } from 'vuetify/components/VDataTable';
import WtgUi from '../../../../WtgUi';

const wtgUi = new WtgUi();

enableAutoUnmount(afterEach);

const headers = [
    { title: 'Plant', align: 'start', sortable: false, key: 'name' },
    { title: 'Light', align: 'end', key: 'light' },
    { title: 'Height', align: 'end', key: 'height' },
    { title: 'Pet Friendly', align: 'end', key: 'petFriendly' },
    { title: 'Price ($)', align: 'end', key: 'price' },
];

const items = [
    {
        name: 'Fern',
        light: 'Low',
        height: '20cm',
        petFriendly: 'Yes',
        price: 20,
    },
    {
        name: 'Snake Plant',
        light: 'Low',
        height: '50cm',
        petFriendly: 'No',
        price: 35,
    },
    {
        name: '<PERSON><PERSON>',
        light: 'Medium',
        height: '60cm',
        petFriendly: 'No',
        price: 50,
    },
    {
        name: 'Pothos',
        light: 'Low to medium',
        height: '40cm',
        petFriendly: 'Yes',
        price: 25,
    },
    {
        name: 'ZZ Plant',
        light: 'Low to medium',
        height: '90cm',
        petFriendly: 'Yes',
        price: 30,
    },
    {
        name: 'Spider Plant',
        light: 'Bright, indirect',
        height: '30cm',
        petFriendly: 'Yes',
        price: 15,
    },
    {
        name: 'Air Plant',
        light: 'Bright, indirect',
        height: '15cm',
        petFriendly: 'Yes',
        price: 10,
    },
    {
        name: 'Peperomia',
        light: 'Bright, indirect',
        height: '25cm',
        petFriendly: 'Yes',
        price: 20,
    },
    {
        name: 'Aloe Vera',
        light: 'Bright, direct',
        height: '30cm',
        petFriendly: 'Yes',
        price: 15,
    },
    {
        name: 'Jade Plant',
        light: 'Bright, direct',
        height: '40cm',
        petFriendly: 'Yes',
        price: 25,
    },
];

describe('WtgDataTableServer', () => {
    test('it renders a VDataTableServer component', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('v-table');
    });

    test('it initializes the VDataTableServer component with the correct appearance', () => {
        const wrapper = mountComponent();
        const props = wrapper.findComponent(VDataTableServer).props();
        expect(props.density).toBe('compact');
        expect(props.fixedHeader).toBe(true);
        expect(props.itemsPerPage).toBe(10);
        expect(props.page).toBe(1);
        expect(props.sortAscIcon).toBe('s-icon-arrow-up');
        expect(props.sortDescIcon).toBe('s-icon-arrow-down');
    });

    test('it passes all properties to the VDataTableServer component', () => {
        const wrapper = mountComponent({
            propsData: {
                density: 'comfortable',
                fixedHeader: false,
                itemsPerPage: 12,
                mobile: true,
                mobileBreakpoint: 'md',
                page: 2,
            },
        });

        const props = wrapper.findComponent(VDataTableServer).props();
        expect(props.density).toBe('comfortable');
        expect(props.fixedHeader).toBe(false);
        expect(props.itemsPerPage).toBe(12);
        expect(props.mobile).toBe(true);
        expect(props.mobileBreakpoint).toBe('md');
        expect(props.page).toBe(2);
    });

    test('it passes the correct props to the WtgDataPagination component', () => {
        const wrapper = mountComponent({ propsData: { itemsPerPageOptions: [10, 25, 50] } });
        expect(wrapper.findComponent({ name: 'WtgDataPagination' }).props('itemsPerPageOptions')).toStrictEqual([
            10, 25, 50,
        ]);
    });

    test('it applies the mobile classes when mobile prop is set', async () => {
        const wrapper = mountComponent();

        expect(wrapper.classes()).not.toContain('wtg-data-table--mobile');
        await wrapper.setProps({ mobile: true });
        expect(wrapper.classes()).toContain('wtg-data-table--mobile');
    });

    test('it applies the fill classes when fillAvailable is set', async () => {
        const wrapper = mountComponent();

        expect(wrapper.classes()).not.toContain('wtg-fill');
        expect(wrapper.classes()).not.toContain('wtg-fill-available-table');
        await wrapper.setProps({ fillAvailable: true });
        expect(wrapper.classes()).toContain('wtg-fill');
        expect(wrapper.classes()).toContain('wtg-fill-available-table');
    });

    test('it toggles editable styling through the editableStyling property', async () => {
        const wrapper = mountComponent();

        expect(wrapper.classes()).toContain('wtg-data-table-display');
        await wrapper.setProps({ editableStyling: true });
        expect(wrapper.classes()).toContain('wtg-data-table-editable');
    });

    test('it toggles pagination display through the hideDefaultFooter property', async () => {
        const wrapper = mountComponent();

        expect(wrapper.find('.wtg-data__pagination').exists()).toBe(true);
        await wrapper.setProps({ hideDefaultFooter: true });
        expect(wrapper.find('.wtg-data__pagination').exists()).toBe(false);
    });

    test('it passes all slots to the VDataTableServer component', () => {
        const wrapper = mountComponent({
            slots: {
                'body.append': '<div class="my-append-slot">Body Append Slot</div>',
                'body.prepend': '<div class="my-prepend-slot">Body Prepend Slot</div>',
            },
        });
        expect(wrapper.find('.my-append-slot').text()).toBe('Body Append Slot');
        expect(wrapper.find('.my-prepend-slot').text()).toBe('Body Prepend Slot');
    });

    test('it toggles selection styling through the selectionMode property', async () => {
        const wrapper = mountComponent();

        expect(wrapper.find('.wtg-data-table--selection-mode').exists()).toBe(false);
        await wrapper.setProps({ selectionMode: true });
        expect(wrapper.find('.wtg-data-table--selection-mode').exists()).toBe(true);
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgDataTableServer as any, {
            propsData: {
                ...propsData,
                items,
                headers,
            },
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
