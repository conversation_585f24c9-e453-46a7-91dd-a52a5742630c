import entityDrawer from '../../../storybook/assets/drawer/entity-drawer.png';
import navigatingDataTablesInDrawers from '../../../storybook/assets/drawer/navigating-data-tables-in-drawers.gif';
import nonEntityDrawer from '../../../storybook/assets/drawer/non-entity-drawer.png';
import pushDrawer from '../../../storybook/assets/drawer/push-drawer.gif';
import readOnlyDrawer from '../../../storybook/assets/drawer/read-only-drawer.png';
import slideOverDrawer from '../../../storybook/assets/drawer/slide-over-drawer.gif';
import userModifiedDrawerWidth from '../../../storybook/assets/drawer/user-modified-drawer-width.gif';
import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusInProgress from '../../../storybook/assets/statusInProgress.svg';

import drawerAnatomy from '../../../assets/WtgDrawer/drawer-anatomy.png';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgDrawer from './WtgDrawer.stories.ts';

<Meta of={WtgDrawer} />

<div className="component-header">
    <h1>Drawer</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img class="documentation" className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-16438&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img> With pending updates
            </td>
            <td>
                <img class="documentation" className="status-chip" src={statusAvailable}></img>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style={{ width: '33%' }}>
                [PRJ00051011](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/b0ec1ce6-52f3-44e6-8645-361ff027bcdb?LicenceCode=EDIAUSSYD&lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply Drawer- Move Up and Down buttons.</td>
        </tr>
        <tr>
            <td style={{ width: '33%' }}>
                [WI00923273](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/092815f4-fbd9-4049-be4f-1454d756e4b4?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply Drawer persistent\push support.</td>
        </tr>
        <tr>
            <td style={{ width: '33%' }}>
                [PRJ00050532](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/531c92d0-5835-42c7-a8fd-aa5591a004f6?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply Drawer - improved behaviour and examples.</td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    The drawer component slides out from the right-hand side of the interface, triggered by an element on the parent page. Its primary purpose is to display entity details from data table records without disrupting the users workflow. Users can also edit and save information if relevant to their task.
<p>Drawers can also be triggered by other components, such as links or buttons, to display supplemental information in a non-disruptive way.</p>

</p>

## API

<div class="docs-page">
    <Canvas className="canvas-preview" of={WtgDrawer.Default} />
</div>
<Controls of={WtgDrawer.Default} sort={'alpha'} />

## Drawer anatomy

<div className="d-flex flex-column align-center">
    <img srcSet={`${drawerAnatomy} 3x`} alt="anatomy of drawer component" />
</div>

<ol className="anatomy-list">
    <li>
        <strong>Content tabs (optional): </strong> Switch between different content sections.
    </li>
    <li>
        <strong>Title: </strong> Drawer header or entity name.
    </li>
    <li>
         <strong>Status (optional):</strong> Displays entity status if applicable.
    </li>
    <li>
         <strong>Favorite (optional):</strong> Mark the entity as a favorite or unfavorite.
    </li>
<li>
         <strong>Navigational arrows (optional):</strong> Navigate between data table records (if drawer being used as part of a data table).
    </li>
<li>
         <strong>Link (optional):</strong> Copy entity link to clipboard.
    </li>
<li>
         <strong>Open in new tab (optional): </strong> Opens the entity in a new tab for a full-screen experience.
    </li>
    <li>
         <strong>Entity actions (optional) </strong> Contains relevant entity actions.
    </li>
<li>
         <strong>Cancel: </strong> Closes drawer.
    </li>
<li>
         <strong>Content area: </strong> Drawer content contained in panel.
    </li>
    <li>
         <strong>Action bar </strong> Contains available actions realted to drawer content. 
    </li>

</ol>

## How to Use

### ✅ Do

<p>
    <ul>
        <li>
            Use a drawer to display smaller entities where a compact view is ideal for quick edits or reviewing
            information.
        </li>
        <li>Use a drawer when tasks can be completed more efficiently without leaving the current page.</li>
        <li>
            Use a drawer to provide a quick way for users to view or perform quick edits to an entity while navigating
            data table rows/records.
        </li>
        <li>Ensure drawer content is contained within a panel/s.</li>
        <li>
            Use a drawer to offer supplementary or referential information related to the parent page or an element on
            the parent page without being intrusive.
        </li>
        <li>
            Consider the impact of the drawer covering content. Drawers can obscure important UI elements and disrupt the user experience. Ensure critical content and actions remain accessible when the drawer is in use.

        </li>
    </ul>

</p>

### ❌ Don't

<p>
    <ul>
        <li>
            Use drawers to display highly detailed, multi-tabbed entities. Instead, consider a full-screen experience as
            the default for viewing these entities.
        </li>
        <li>Use a drawer for critical actions or information, for those cases refer to alert modal.</li>

        <li>Open a drawer from within another drawer. Only one drawer can be open at a time.</li>
        <li>
            ⚠️ Exercise caution using editable drawers within entities. This can be problematic when saving the content
            within the drawer vs saving the entity. Consider having this information the entity itself instead.
        </li>
    </ul>

</p>

## Behavior

### Dismissing a drawer

<p>
    <ul>
        <li>Press the cancel ('x') action top right.</li>
        <li>Press 'Close' or 'Cancel' in the bottom drawer actions.</li>
        <li>Press 'Escape on a keyboard.</li>
        <li>Clicking outside the drawer to close is currently not supported.</li>
    </ul>
</p>

### Responsive behavior

<p>
    <ul>
        <li>Default drawer width can be overridden if required. </li>
        <li>
            The drawer can either overlay content or push content, choose the behaviour that works best for the specific
            use case
        </li>
        <li>Examples below are Figma prototypes to demonstrate high level behavior.</li>
    </ul>
</p>

<strong>Overlay content (default) </strong>

<img class="documentation" src={slideOverDrawer} />

<strong>Push content </strong>(coming soon - work pending on this behavior)

<img class="documentation" src={pushDrawer} />

<strong>User adjustible width</strong>- Users can increase or decrease drawer width.

<img class="documentation" src={userModifiedDrawerWidth} />

### Drawer validation

<p>
    <ul>
        <li>
            Drawers that allow for editing (such as Drawers displaying an entity) leverage existing validation
            infrastructure
        </li>
        <li>Coming soon: Like modals, drawers will also support the alerts hub infrastructure</li>
        <li>Consider toast confirmations for successful ‘save' and 'save and close’ within drawers</li>
    </ul>
</p>

<style>
    {`
    img.documentation {
        display: block;
        margin: 40px auto;
    }
    `}
</style>

## Variants

The below are technically not 'variants' but they do help illustrate the primary purposes of the drawer.

### Entity drawer

-   A drawer that displays entity details e.g. selecting a record on a data table and the drawer sliding out to display the entity details related to that record.
-   Entity drawers come with entity functionality in the toolbar, which may include: entity status, add to favorites action, copy link to entity, open entity in new tab, and entity actions (see anatomy above for more details).
-   Drawers attached to a data table experience can also include navigational arrows which serve as a secondary mechanism for alloiwng the user to navigate data table records.

<strong>Navigating data table records example</strong>
<p>
    In CargoWise, double-clicking a record is a familiar way to open an entity in full screen. If implementing a drawer,
    consider maintaining that behavior while using a single click to open the drawer.
</p>

<img class="documentation" src={navigatingDataTablesInDrawers} />

### 'Non-entity' drawer

-   A drawer not directly tied to an entity.
-   May exist to provide supplemental information related to content on the parent page.
-   These drawers will not have entity-related actions or navigational buttons available in the toolbar.
-   These drawers typically contain read only information, and there for not require actions like 'Save'.

## Content guidelines

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview).

## Related components

-   [Modal](/docs/components-modal--docs)
-   [Alert modal](/docs/components-alert-modal--docs)
-   [Panel](/docs/components-panel--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
