import WtgDivider from '@components/WtgDivider';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgPanel from '@components/WtgPanel';
import WtgSpacer from '@components/WtgSpacer';
import { inputArgTypes } from '@composables/input';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import { WtgColorField } from '../';
import { ColorFieldSandboxTemplate } from './templates/wtg-color-field-sandbox.stories-template';

type Story = StoryObj<typeof WtgColorField>;
const meta: Meta<typeof WtgColorField> = {
    title: 'Components/Color Field',
    component: WtgColorField,
    parameters: {
        docs: {
            description: {
                component: 'Color field is a control that allows users to select a specific color.',
            },
        },
        layout: 'centered',
        controls: {
            sort: 'alpha',
        },
    },
    argTypes: {
        ...inputArgTypes,
    },
    decorators: [
        () => ({
            template: `
                <div style="max-width:400px">
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        label: 'Background color',
        modelValue: '#AA0000',
    },
    render: (args) => ({
        components: { WtgColorField },
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        setup: () => ({ args }),
        template: `<WtgColorField v-bind="args"
                        @update:model-value="updateModel"
                        @input="inputAction" 
                        @change="changeAction"
                        @focus="focusAction"
                        @blur="blurAction">
                    </WtgColorField>`,
    }),
};

export const Sandbox: Story = {
    args: {
        messages: 'Select a color',
        label: 'Color field with message',
        rules: [(value: string) => (value.length % 3 ? 'Invalid color code' : true)],
    },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgColorField, WtgDivider, WtgSpacer, WtgLayoutGrid, WtgPanel },
        setup: () => {
            const colorCode = ref('');
            return { args, colorCode };
        },
        template: templateWithRtl(ColorFieldSandboxTemplate),
    }),
};
