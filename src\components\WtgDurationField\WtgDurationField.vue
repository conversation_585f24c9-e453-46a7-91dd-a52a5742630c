<template>
    <WtgInput v-bind="baseInputProps" :input="input">
        <input
            :id="computedId"
            ref="input"
            autocomplete="off"
            :aria-label="ariaLabel"
            :aria-labelledby="ariaLabelledby"
            :disabled="disabled || displayOnly"
            :placeholder="placeholder"
            :readonly="readonly"
            type="text"
            :value="displayValue"
            @blur="onBlur"
            @change="onChange"
            @focus="onFocus"
            @input="onInput"
        />
    </WtgInput>
</template>

<script setup lang="ts">
import WtgInput from '@components/WtgInput';
import { useFocus } from '@composables/focus';
import { basePropsFromProps, makeInputProps } from '@composables/input';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import { makeValidationProps } from '@composables/validation';
import { PropType, computed, getCurrentInstance, onMounted, onUnmounted, ref, watch } from 'vue';
import { DurationDisplayFormat } from '../../language/formatters/types';

//
// Properties
//
const props = defineProps({
    /**
     * The display format for the duration value.
     * Determines how the duration is shown when spanning multiple days(e.g., "HHH:MM" or "DD:HH:MM").
     */
    displayFormat: {
        type: String as PropType<DurationDisplayFormat>,
        default: DurationDisplayFormat.HoursAndMinutes,
    },
    /**
     * The value of the field, as an ISO 8601 duration string.
     * Use v-model to bind this prop.
     */
    modelValue: {
        type: String,
        default: undefined,
    },
    ...makeInputProps(),
    ...makeValidationProps(),
    ...makeLayoutGridColumnProps(),

    /**
     * @deprecated Use `modelValue` instead.
     * Legacy prop for compatibility.
     */
    inputValue: {
        type: String,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    blur: [e: FocusEvent];
    focus: [e: FocusEvent];
    'update:modelValue': [value: string];
    'model-compat:input': [value: string];
    change: [value: string];
}>();

//
// State
//
const input = ref<HTMLElement>();
const internalValue = ref(props.modelValue ?? props.inputValue ?? '');
const displayValue = ref('');
const hasPendingChange = ref(false);

//
// Composables
//
const instance = getCurrentInstance();
const { isFocused, focus, blur } = useFocus(props);
const { durationFormatter } = useLocale();

useLayoutGridColumn(props);

//
// Computed
//
const computedId = computed(() => props.id || props.inputId || `input-${instance!.uid}`);

const baseInputProps = computed(() => {
    return {
        ...basePropsFromProps(props),
        filled: displayValue.value !== '',
        id: computedId.value,
        focused: isFocused.value,
    };
});

//
// Watchers
//
watch(
    () => props.modelValue ?? props.inputValue ?? '',
    (value: string) => {
        updateValue(value, false);
    }
);

//
// Event Handlers
//
function onChange(e: Event) {
    const value = (e.target as HTMLInputElement).value;
    displayValue.value = value;
    let parsedDate = parsedDurationFn(value);
    updateValue(parsedDate, true);
}

function onInput(e: Event) {
    const value = (e.target as HTMLInputElement).value;
    displayValue.value = value;
    hasPendingChange.value = true;
}

function onFocus(e: FocusEvent) {
    emit('focus', e);

    if (!isFocused.value) {
        focus();
    }
}

function onBlur(e: FocusEvent) {
    emit('blur', e);

    blur();
}

//
// Helpers
//
function parsedDurationFn(date: string): string {
    return !date ? '' : durationFormatter.value.parse(date);
}

function formatDuration(date: string): string {
    return !date ? '' : durationFormatter.value.format(date, props.displayFormat);
}

function updateValue(newValue: string, notify: boolean): void {
    if (internalValue.value !== newValue) {
        internalValue.value = newValue;
        if (notify) {
            emit('update:modelValue', internalValue.value);
            emit('model-compat:input', internalValue.value);
            emit('change', internalValue.value);
        }
    }
    displayValue.value = formatDuration(internalValue.value);
    hasPendingChange.value = false;
}

//
// Lifecycle
//
onMounted(() => {
    updateValue(props.modelValue ?? props.inputValue ?? '', false);
});

onUnmounted(() => {
    if (hasPendingChange.value) {
        updateValue(parsedDurationFn(displayValue.value), true);
    }
});
</script>
