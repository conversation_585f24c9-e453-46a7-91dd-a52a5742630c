import { WtgAvatar } from '../';
import WtgUi from '../../../WtgUi';
import { mount, enableAutoUnmount } from '@vue/test-utils';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgAvatar', () => {
    test('it sets the image to the avatar when image property is set ', () => {
        const wrapper = mountComponent({
            propsData: {
                image: 'ImagePath',
            },
        });
        const image = wrapper.findComponent({ name: 'WtgImage' });
        expect(image.props('src')).toContain('ImagePath');
    });

    test('it applies the icon when the icon property is provided', async () => {
        const wrapper = mountComponent({
            propsData: {
                icon: 's-icon-user',
            },
        });
        const userIcon = wrapper.find('i');
        expect(userIcon.attributes('class')).toContain('s-icon-user');
    });

    test('it applies the initials when the initials property is provided', async () => {
        const wrapper = mountComponent({
            propsData: {
                initials: 'MA',
            },
        });

        const avatarInitials = wrapper.find('span');
        expect(avatarInitials.text()).toBe('MA');
    });

    test('it applies the size of the avatar when size property is set', async () => {
        const wrapper = mountComponent({
            propsData: {
                initials: 'MA',
                size: 's',
            },
        });

        await wrapper.setProps({ size: 'xl' });

        expect(wrapper.classes()).toContain('wtg-avatar--xl');
    });

    test('it applies image,icon and initials in the given order', async () => {
        const wrapper = mountComponent({
            propsData: {
                image: 'ImagePath',
                icon: 's-icon-user',
                initials: 'MA',
            },
        });

        const WtgAvatar = wrapper.find('.wtg-avatar');
        expect(WtgAvatar.html()).toContain(
            '<img class="v-img__img v-img__img--contain" src="ImagePath" alt="" style="display: none;">'
        );

        await wrapper.setProps({ image: undefined });
        expect(WtgAvatar.html()).not.toContain(
            '<img class="v-img__img v-img__img--contain" src="ImagePath" alt="" style="display: none;">'
        );
        expect(WtgAvatar.html()).toContain('<i aria-hidden="true" class="wtg-icon s-icon-user wtg-avatar__icon">');

        await wrapper.setProps({ icon: undefined });
        expect(WtgAvatar.html()).not.toContain(
            '<img class="v-img__img v-img__img--contain" src="ImagePath" alt="" style="display: none;">'
        );
        expect(WtgAvatar.html()).not.toContain('<i aria-hidden="true" class="wtg-icon s-icon-user">');
        expect(WtgAvatar.html()).toContain('<span class="wtg-avatar__initials">MA</span>');
    });

    test('it renders the notication icon with primary sentiment when notificationIcon is provided', async () => {
        const wrapper = mountComponent({
            propsData: {
                image: 'ImagePath',
                notificationIcon: 's-icon-notification-icon',
                notificationSentiment: 'primary',
            },
        });

        const wtgAvatar = wrapper.find('.wtg-avatar');
        expect(wtgAvatar.html()).toContain(
            '<div class="wtg-avatar__notification wtg-avatar__notification--primary"><i aria-hidden="true" class="wtg-icon s-icon-notification-icon">'
        );
    });

    test('it renders the notication icon with critical sentiment when notificationIcon is provided', async () => {
        const wrapper = mountComponent({
            propsData: {
                image: 'ImagePath',
                notificationIcon: 's-icon-notification-icon',
                notificationSentiment: 'critical',
            },
        });

        const wtgAvatar = wrapper.find('.wtg-avatar');
        expect(wtgAvatar.html()).toContain(
            '<div class="wtg-avatar__notification wtg-avatar__notification--critical"><i aria-hidden="true" class="wtg-icon s-icon-notification-icon">'
        );
    });

    test('it renders the notication icon with success sentiment when notificationIcon is provided', async () => {
        const wrapper = mountComponent({
            propsData: {
                image: 'ImagePath',
                notificationIcon: 's-icon-notification-icon',
                notificationSentiment: 'success',
            },
        });

        const wtgAvatar = wrapper.find('.wtg-avatar');
        expect(wtgAvatar.html()).toContain(
            '<div class="wtg-avatar__notification wtg-avatar__notification--success"><i aria-hidden="true" class="wtg-icon s-icon-notification-icon">'
        );
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgAvatar, {
            wtgUi,
            propsData,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
