<template>
    <div ref="root" :class="computedClass">
        <div
            v-floating-vue-tooltip="!label ? tooltipDirective : undefined"
            :class="computedSelectionControlClass"
            @mouseenter="isHovered = true"
            @mouseleave="isHovered = false"
        >
            <input
                :id="computedId"
                :aria-label="ariaLabel"
                :aria-labelledby="ariaLabelledby"
                :aria-checked="isChecked ? 'true' : 'false'"
                type="checkbox"
                :disabled="disabled || restricted"
                :checked="isChecked"
                :tabindex="tabindex"
                @change="onChange"
                @click="onClick"
            />
            <WtgIcon class="wtg-checkbox__icon">{{ computedIcon }}</WtgIcon>
            <div
                v-if="readonly || disabled || (!isChecked && !props.indeterminate)"
                class="wtg-checkbox__selection-control-background"
            />
        </div>
        <label
            v-if="label"
            v-floating-vue-tooltip="tooltipDirective"
            :for="computedId"
            class="wtg-checkbox__label"
            @mouseenter="isHovered = true"
            @mouseleave="isHovered = false"
        >
            {{ label }}
        </label>
    </div>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { AlertLevel, ValidationState, useCurrentNotification } from '@composables/notifications';
import { makeRestrictedProps } from '@composables/restricted';
import { TooltipSentiment, convertContent, makeTooltipProps, useTooltip } from '@composables/tooltip';
import { PropType, computed, getCurrentInstance, ref, watch, watchEffect } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * The ARIA label for the checkbox element.
     */
    ariaLabel: {
        type: String,
        default: undefined,
    },

    /**
     * The ID of the element that labels the checkbox element.
     */
    ariaLabelledby: {
        type: String,
        default: undefined,
    },

    /**
     * Disables the checkbox, making it unclickable and visually indicating its disabled state.
     */
    disabled: {
        type: Boolean,
        default: false,
    },

    /**
     * The unique identifier for the checkbox element.
     */
    id: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the checkbox will be in an indeterminate state.
     */
    indeterminate: {
        type: Boolean,
        default: false,
    },

    /**
     * The label text to display next to the checkbox.
     */
    label: {
        type: String,
        default: '',
    },

    /**
     * The messages or validation feedback to display below the checkbox.
     * Can be a single string or an array of strings.
     */
    messages: {
        type: [Array, String] as PropType<string | string[]>,
        default: (): string[] => [],
    },

    /**
     * The current value of the checkbox, used for two-way binding.
     * Can be a boolean or an array of values if `multiple` is true.
     */
    modelValue: {
        type: [Boolean, Array] as PropType<boolean | any[]>,
        default: undefined,
    },

    /**
     * If true, multiple checkboxes can be selected at the same time.
     */
    multiple: {
        type: Boolean,
        default: false,
    },

    /**
     * Makes the checkbox read-only, preventing user interaction.
     */
    readonly: {
        type: Boolean,
        default: false,
    },

    /**
     * Marks the input as required, indicating that it must be filled out.
     */
    required: {
        type: Boolean,
        default: false,
    },

    /**
     * Specifies the sentiment or validation state of the checkbox.
     * Options include 'critical', 'warning', or 'success'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'warning' | 'success'>,
        default: undefined,
    },
    /**
     * Tabindex for the checkbox.
     */
    tabindex: {
        type: Number,
        default: undefined,
    },
    /**
     * The validation state of the checkbox, providing feedback on its validity.
     */
    validationState: {
        type: Object as PropType<Readonly<ValidationState>>,
        default: undefined,
    },

    /**
     * The value of the checkbox.
     */
    value: {
        type: [Boolean, Number, String],
        default: undefined,
    },

    ...makeLayoutGridColumnProps(),
    ...makeRestrictedProps(),
    ...makeTooltipProps(),

    /**
     * The unique identifier for the checkbox element.
     * @deprecated Use `id` instead.
     */
    inputId: {
        type: String,
        default: undefined,
    },

    /**
     * The value of the checkbox.
     * @deprecated Use `modelValue` instead.
     */
    inputValue: {
        type: [Boolean, Array],
        default: undefined,
    },

    /**
     * Indicates whether the checkbox is in an error state.
     * @deprecated Use `sentiment="critical"` instead.
     */
    error: {
        type: Boolean,
        default: false,
    },

    /**
     * The error messages to display below the checkbox.
     * Can be a single string or an array of strings.
     * @deprecated Use `sentiment="critical"` and `messages` instead.
     */
    errorMessages: {
        type: [Array, String] as PropType<string | string[]>,
        default: (): string[] => [],
    },
});

//
// Emits
//
const emit = defineEmits<{
    change: [value: any];
    'update:indeterminate': [value: boolean];
    'update:modelValue': [value: any];
    'model-compat:input': [value: any];
}>();

//
// State
//
const internalValue = ref(false);
const internalValueMultiple = ref<any[]>([]);
const internalIndeterminate = ref(props.indeterminate || false);
const isHovered = ref(false);
const root = ref<HTMLElement | null>(null);

//
// Composables
//
const instance = getCurrentInstance();
const { createTooltipDirective, tooltipDirective: customTooltipDirective } = useTooltip(props);
const { displayCurrentNotification } = useCurrentNotification(root, props.validationState);

useLayoutGridColumn(props);

//
// Computed
//
const alertLevel = computed(() => {
    return props.validationState?.alertLevel ?? AlertLevel.None;
});

const isChecked = computed(() => {
    const modelValue = props.modelValue ?? props.inputValue ?? false;
    if (props.multiple && Array.isArray(modelValue)) {
        return internalValueMultiple.value.includes(props.value);
    }
    return internalValue.value;
});

const computedClass = computed(() => ({
    'wtg-checkbox': true,
    'wtg-required': props.required,
    'wtg-checkbox--disabled': props.disabled,
    'wtg-checkbox--readonly': props.readonly,
    'wtg-checkbox--success': computedSentiment.value === 'success',
    'wtg-checkbox--critical': computedSentiment.value === 'critical',
    'wtg-checkbox--warning': computedSentiment.value === 'warning',
}));

const computedSelectionControlClass = computed(() => ({
    'wtg-checkbox__selection-control': true,
    'wtg-checkbox--indeterminate': props.indeterminate,
    'wtg-checkbox--default': !internalValue.value && !props.indeterminate,
    'wtg-checkbox--selected': internalValue.value && !props.indeterminate,
    'wtg-checkbox__current-notification': displayCurrentNotification.value,
}));

const computedId = computed(() => (props.id ?? props.inputId) || `checkbox-${instance!.uid}`);

const computedIcon = computed(() => {
    if (props.restricted) {
        return 's-icon-hide';
    } else if (internalIndeterminate.value) {
        if (props.readonly) {
            return 's-icon-checkbox-indeterminate-readonly';
        } else if (props.disabled) {
            return 's-icon-checkbox-indeterminate-disabled';
        } else {
            return 's-icon-checkbox-indeterminate';
        }
    } else if (isChecked.value) {
        if (props.readonly) {
            return 's-icon-checkbox-on-readonly';
        } else if (props.disabled) {
            return 's-icon-checkbox-on-disabled';
        } else {
            return 's-icon-checkbox-on';
        }
    } else {
        if (props.readonly) {
            return 's-icon-checkbox-off-readonly';
        } else if (props.disabled) {
            return 's-icon-checkbox-off-disabled';
        } else {
            return 's-icon-checkbox-off';
        }
    }
});

const computedSentiment = computed(() => {
    if (props.disabled) {
        return '';
    } else if (
        props.error ||
        props.errorMessages?.length > 0 ||
        props.sentiment === 'critical' ||
        alertLevel.value === AlertLevel.Error
    ) {
        return 'critical';
    } else if (
        props.sentiment === 'warning' ||
        alertLevel.value === AlertLevel.MessageError ||
        alertLevel.value === AlertLevel.Warning
    ) {
        return 'warning';
    } else if (props.sentiment === 'success') {
        return 'success';
    }
    return '';
});

const computedMessages = computed(() => {
    const messages = props.errorMessages?.length > 0 ? props.errorMessages : props.messages;
    let newMessages: string[] = [];
    if (typeof messages === 'string') {
        newMessages.push(messages);
    } else if (Array.isArray(messages)) {
        newMessages = newMessages.concat(messages);
    }
    if (props.validationState) {
        newMessages = newMessages.concat(props.validationState.messages);
    }
    return newMessages;
});

const tooltipDirective = computed(
    () =>
        createTooltipDirective({
            content: convertContent(computedMessages.value),
            popperClass: 'wtg-tooltip--validation wtg-tooltip--fit-content',
            sentiment: computedSentiment.value as TooltipSentiment,
            shown: isHovered.value || displayCurrentNotification.value,
        }) ||
        (customTooltipDirective.value && {
            ...customTooltipDirective.value,
            shown: isHovered.value,
        })
);

//
// Watchers
//
watchEffect(() => {
    const modelValue = props.modelValue ?? props.inputValue ?? props.value ?? false;
    if (props.multiple) {
        internalValueMultiple.value = Array.isArray(modelValue) ? modelValue : [];
        internalValue.value = internalValueMultiple.value.includes(props.value);
    } else {
        internalValue.value = !!modelValue;
        internalValueMultiple.value = [];
    }
});

watch(
    () => props.indeterminate,
    (newValue: any) => {
        internalIndeterminate.value = newValue;
    }
);

//
// Event Handlers
//
function onChange(e: Event) {
    change((e.target as HTMLInputElement).checked);
}

function onClick(e: Event) {
    if (props.readonly) {
        e.preventDefault();
    }
}

//
// Helpers
//
function change(newValue: boolean) {
    if (props.multiple) {
        const index = internalValueMultiple.value.indexOf(props.value);
        if (index !== -1) {
            internalValueMultiple.value.splice(index, 1);
        } else {
            internalValueMultiple.value.push(props.value);
        }
        emit('update:modelValue', internalValueMultiple.value);
        emit('model-compat:input', internalValueMultiple.value);
        emit('change', internalValueMultiple.value);
    } else {
        if (internalValue.value !== newValue || internalIndeterminate.value) {
            internalValue.value = newValue;
            if (internalIndeterminate.value) {
                internalIndeterminate.value = false;
                emit('update:indeterminate', internalIndeterminate.value);
            }
            emit('update:modelValue', internalValue.value);
            emit('model-compat:input', internalValue.value);
            emit('change', internalValue.value);
        }
    }
}
</script>

<style lang="scss">
.wtg-checkbox {
    display: flex;
    flex: 0 1 auto;
    flex-wrap: wrap;
    max-width: 100%;
    padding: var(--s-padding-s) 0px;
    align-items: center;
    gap: 0px;

    label {
        font: var(--s-text-md-default);
        cursor: pointer;
        padding: 0px var(--s-padding-s);
    }

    .wtg-checkbox__selection-control {
        display: inline-flex;
        flex: 0 0 auto;
        position: relative;

        .wtg-checkbox__selection-control-background {
            position: absolute;
            bottom: 4px;
            left: 4px;
            right: 4px;
            top: 4px;
            border: 1px solid transparent;
            border-radius: 1px;
        }

        .wtg-checkbox__icon {
            z-index: 1;
            font-size: var(--s-sizing-l);
            height: var(--s-sizing-icon-md);
            width: var(--s-sizing-icon-md);
            border-radius: var(--s-radius-s);
        }

        input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
            user-select: none;
            z-index: 2;

            &:disabled {
                pointer-events: none;
            }

            &:focus-visible + i {
                border-radius: var(--s-radius-s);
                outline: 2px solid var(--s-primary-border-default);
                outline-offset: -2px;
            }
        }

        &.wtg-checkbox--default input:hover:not(:disabled) + .wtg-checkbox__selection-control-background {
            background: var(--s-neutral-bg-weak-hover);
        }

        &.wtg-checkbox--selected input:hover + i {
            color: var(--s-primary-icon-hover);
        }

        &.wtg-checkbox--indeterminate input:hover + i {
            color: var(--s-primary-icon-hover);
        }
    }

    .wtg-checkbox--default > i {
        color: var(--s-neutral-border-default);
    }

    .wtg-checkbox--selected > i {
        color: var(--s-primary-border-default);
    }

    .wtg-checkbox--indeterminate > i {
        color: var(--s-primary-border-default);
    }

    &.wtg-checkbox--disabled {
        color: var(--s-neutral-txt-disabled);
        pointer-events: none;
    }

    &.wtg-checkbox--disabled label {
        color: var(--s-neutral-icon-disabled);
        pointer-events: none;
    }

    &.wtg-checkbox--disabled > .wtg-checkbox__selection-control {
        & .wtg-checkbox__selection-control-background {
            background: var(--s-neutral-bg-disabled);
        }

        & > i {
            color: var(--s-neutral-icon-disabled);
            pointer-events: none;
        }
    }

    &.wtg-checkbox--readonly {
        & > label {
            cursor: default;
        }

        > .wtg-checkbox__selection-control {
            & .wtg-checkbox__selection-control-background {
                background: var(--s-neutral-bg-disabled);
            }

            & > i {
                color: var(--s-neutral-border-disabled);
            }

            & > input {
                cursor: default;
            }
        }
    }

    &.wtg-required label::after {
        color: var(--s-error-icon-default);
        content: '*';
    }

    &.wtg-checkbox--success > .wtg-checkbox__selection-control {
        &.wtg-checkbox--default input:hover + i,
        &.wtg-checkbox__current-notification > i {
            background: var(--s-success-bg-weak-hover);
            color: var(--s-success-border-hover);
        }

        &.wtg-checkbox--selected input:hover + i,
        &.wtg-checkbox--indeterminate input:hover + i,
        &.wtg-checkbox__current-notification > i {
            background: var(--s-success-bg-weak-hover);
            color: var(--s-success-bg-hover);
        }
    }

    &.wtg-checkbox--success {
        & .wtg-checkbox__selection-control {
            & > i {
                color: var(--s-success-bg-default);
            }
        }

        & .wtg-checkbox__label {
            color: var(--s-success-txt-default);
        }
    }

    &.wtg-checkbox--critical > .wtg-checkbox__selection-control {
        &.wtg-checkbox--default input:hover + i,
        &.wtg-checkbox__current-notification > i {
            background: var(--s-error-bg-weak-hover);
            color: var(--s-error-border-hover);
        }

        &.wtg-checkbox--selected input:hover + i,
        &.wtg-checkbox--indeterminate input:hover + i,
        &.wtg-checkbox__current-notification > i {
            background: var(--s-error-bg-weak-hover);
            color: var(--s-error-bg-hover);
        }
    }

    &.wtg-checkbox--critical {
        & .wtg-checkbox__selection-control {
            & > i {
                color: var(--s-error-bg-default);
            }
        }

        & .wtg-checkbox__label {
            color: var(--s-error-txt-default);
        }
    }

    &.wtg-checkbox--warning > .wtg-checkbox__selection-control {
        &.wtg-checkbox--default input:hover + i,
        &.wtg-checkbox__current-notification > i {
            background: var(--s-warning-bg-weak-hover);
            color: var(--s-warning-icon-hover);
        }

        &.wtg-checkbox--selected input:hover + i,
        &.wtg-checkbox--indeterminate input:hover + i,
        &.wtg-checkbox__current-notification > i {
            background: var(--s-warning-bg-weak-hover);
            color: var(--s-warning-icon-hover);
        }
    }

    &.wtg-checkbox--warning {
        & .wtg-checkbox__selection-control {
            & > i {
                color: var(--s-warning-border-default);
            }
        }

        & .wtg-checkbox__label {
            color: var(--s-warning-txt-default);
        }
    }

    .wtg-checkbox__label {
        color: var(--s-neutral-txt-default);
        font: var(--s-text-md-default);
    }
}
</style>
