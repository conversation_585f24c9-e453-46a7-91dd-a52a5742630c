import WtgApp from '@components/WtgApp';
import { WtgAppBar, WtgAppBarTitle } from '@components/WtgAppBar';
import WtgCol from '@components/WtgCol';
import WtgIconButton from '@components/WtgIconButton';
import WtgMain from '@components/WtgMain';
import WtgRow from '@components/WtgRow';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgAppBar>;
const meta: Meta<typeof WtgAppBar> = {
    title: 'Utilities/App Bar',
    component: WtgAppBar,
    parameters: {
        docs: {
            description: {
                component:
                    'The AppBar component is used for displaying general information that a user might want to access from any page within your site.',
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: { WtgApp, WtgAppBar, WtgAppBarTitle, WtgMain, WtgCol, WtgRow, WtgIconButton },
        setup: () => ({ args }),
        methods: {
            changeAction: action('change'),
        },
        data: () => {
            return {
                drawer: undefined,
            };
        },
        template: `<WtgAppBar v-bind="args" >
              <template v-slot:prepend>
                <WtgIconButton aria-label="Navigation" variant="ghost" icon="s-icon-menu-hamburger" @click="drawer = !drawer"/>
              </template>
            
              <WtgAppBar-title>Application Bar</WtgAppBar-title>
            
              <template v-slot:append>
                    <WtgIconButton aria-label="Favorites" icon="s-icon-star-filled"></WtgIconButton>            
                    <WtgIconButton aria-label="Search" icon="s-icon-search"></WtgIconButton>            
                    <WtgIconButton aria-label="More" icon="s-icon-menu-kebab"></WtgIconButton>
              </template>
            </WtgAppBar>`,
    }),
    decorators: [
        () => ({
            components: { WtgApp },
            template: '<WtgApp class="content-embedded-app"><story /></WtgApp>',
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {},
};
