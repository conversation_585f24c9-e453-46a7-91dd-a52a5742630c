<template>
    <div class="wtg-date-picker-dialog" data-testid="date_picker">
        <VDatePicker
            :model-value="datePickerValue"
            :view-mode="datePickerView"
            :first-day-of-week="locale.firstDayOfWeek"
            show-adjacent-months
            hide-header
            next-icon="s-icon-caret-right"
            prev-icon="s-icon-caret-left"
            mode-icon="s-icon-caret-down"
            :max="max"
            :min="min"
            @update:model-value="onDatePickerSelect"
            @update:view-mode="onViewChange"
            @update:month="onMonthChange"
            @update:year="onYearChange"
        ></VDatePicker>
        <div v-if="!hideTodayButton" style="display: flex; justify-content: flex-end">
            <WtgButton data-testid="set_today" @click="onSetToday">{{ todayCaption }}</WtgButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import { WtgDateFormatter } from '@components/WtgDateField/types';
import { convertDateToInputDate, convertInputDateToDate } from '@components/WtgDateField/utils';
import { useLocale } from '@composables/locale';
import { computed, PropType, ref, watch } from 'vue';
import { VDatePicker } from 'vuetify/components/VDatePicker';

//
// Types
//
type DatePickerViews = 'month' | 'year' | 'months';

//
// Properties
//
const props = defineProps({
    /**
     * The formatter used to format and parse date values.
     */
    formatter: {
        type: Object as PropType<WtgDateFormatter>,
        default: undefined,
    },

    /**
     * If true, the date picker will only allow selecting a month and year.
     */
    monthYearOnly: {
        type: Boolean,
        default: undefined,
    },

    /**
     * The current value of the date picker, used for two-way binding.
     * Should be a string in the format supported by the formatter.
     */
    modelValue: {
        type: String,
        default: '',
    },

    /**
     * The maximum date that can be selected.
     * Should be a string in the format supported by the formatter.
     */
    max: {
        type: String,
        default: undefined,
    },

    /**
     * The minimum date that can be selected.
     * Should be a string in the format supported by the formatter.
     */
    min: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the "Today" button will be hidden.
     */
    hideTodayButton: {
        type: Boolean,
        default: false,
    },

    /**
     * A custom function to determine the "Today" date.
     * Should return a string in the format supported by the formatter.
     */
    todayDateFn: {
        type: Function as PropType<() => string>,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    close: [];
    'update:modelValue': [value: string];
}>();

//
// State
//
const datePickerValue = ref<Date>(convertInputDateToDate(props.modelValue, props.monthYearOnly));
const datePickerView = ref<DatePickerViews>(props.monthYearOnly ? 'months' : 'month');

//
// Composables
//
const { formatCaption, locale } = useLocale();

//
// Computed
//
const todayCaption = computed(() => formatCaption('dateField.today'));

//
// Watchers
//
watch(
    () => props.modelValue,
    () => {
        datePickerValue.value = convertInputDateToDate(props.modelValue, props.monthYearOnly);
    }
);

watch(
    () => props.monthYearOnly,
    () => {
        datePickerView.value = props.monthYearOnly ? 'months' : 'month';
    }
);

//
// Event Handlers
//
function onDatePickerSelect(newValue: Date) {
    datePickerValue.value = newValue;
    emit('update:modelValue', convertDateToInputDate(newValue));
    emit('close');
}

function onSetToday() {
    let dateValue: string;

    if (props.todayDateFn) {
        dateValue = props.todayDateFn();
    } else {
        const localToday = new Date();
        dateValue = convertDateToInputDate(localToday);
    }

    datePickerValue.value = new Date(dateValue);
    emit('update:modelValue', dateValue);
    emit('close');
}

function onMonthChange(month: number) {
    if (props.monthYearOnly) {
        datePickerValue.value.setMonth(month);
        emit('update:modelValue', convertDateToInputDate(datePickerValue.value));
        emit('close');
    }
}

function onYearChange(year: number) {
    if (props.monthYearOnly) {
        datePickerValue.value.setFullYear(year);
        datePickerView.value = 'months';
    }
}

function onViewChange(view: DatePickerViews) {
    if (props.monthYearOnly) {
        if (view !== 'month') {
            datePickerView.value = view;
        }
    } else {
        datePickerView.value = view;
    }
}
</script>

<style lang="scss">
.wtg-date-picker-dialog {
    display: inline-block;

    .v-date-picker-month {
        padding: 12px 0px 0px 0px;
    }

    .v-picker__body {
        background-color: var(--s-neutral-bg-default);
        color: var(--s-neutral-txt-default);

        .v-btn {
            color: var(--s-neutral-txt-default);
        }
    }
    .v-date-picker-month__day {
        height: 34px;
        width: 34px;
    }

    .v-date-picker-month__day .v-btn {
        --v-btn-height: 22px;
        --v-btn-size: 0.85rem;
    }

    .v-date-picker-month__day {
        .v-btn {
            background-color: var(--s-neutral-bg-default);
            color: var(--s-neutral-txt-default);
        }
    }

    .v-date-picker-month__day--selected {
        .v-btn {
            background-color: var(--s-primary-bg-active);
            color: var(--s-primary-txt-inv-active);
        }
    }

    .v-date-picker-years__content,
    .v-date-picker-months__content {
        .v-btn--active {
            background-color: var(--s-primary-bg-active);
            color: var(--s-primary-txt-inv-active);
        }
    }
}
</style>
