<template>
    <VCol v-bind="props">
        <slot />
    </VCol>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { VCol } from 'vuetify/components/VGrid';

//
// Properties
//
const props = defineProps({
    /**
     * Number of columns to offset the component from the left.
     */
    offset: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * The order of the column in the flex layout.
     */
    order: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Aligns the column vertically within the row.
     * Options: 'end', 'center', 'start', 'auto', 'stretch', 'baseline'.
     */
    alignSelf: {
        type: String as PropType<'end' | 'center' | 'start' | 'auto' | 'stretch' | 'baseline'>,
        default: undefined,
    },
    /**
     * Number of columns the component should span.
     */
    cols: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns the component should span on small screens.
     */
    sm: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns the component should span on medium screens.
     */
    md: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns the component should span on large screens.
     */
    lg: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns the component should span on extra large screens.
     */
    xl: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns the component should span on extra extra large screens.
     */
    xxl: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns to offset on small screens.
     */
    offsetSm: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns to offset on medium screens.
     */
    offsetMd: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns to offset on large screens.
     */
    offsetLg: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns to offset on extra large screens.
     */
    offsetXl: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * Number of columns to offset on extra extra large screens.
     */
    offsetXxl: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * The order of the column on small screens.
     */
    orderSm: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * The order of the column on medium screens.
     */
    orderMd: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * The order of the column on large screens.
     */
    orderLg: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * The order of the column on extra large screens.
     */
    orderXl: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
    /**
     * The order of the column on extra extra large screens.
     */
    orderXxl: {
        type: [String, Number, undefined] as PropType<string | number | undefined>,
        default: undefined,
    },
});
</script>
