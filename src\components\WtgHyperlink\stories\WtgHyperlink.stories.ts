import WtgCol from '@components/WtgCol';
import WtgHyperlink from '@components/WtgHyperlink/WtgHyperlink.vue';
import WtgRow from '@components/WtgRow';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { HyperlinkSandboxTemplate } from './templates/wtg-hyperlink-sandbox.stories-template';

type Story = StoryObj<typeof WtgHyperlink>;
const meta: Meta<typeof WtgHyperlink> = {
    title: 'Components/Hyperlink',
    component: WtgHyperlink,
    parameters: {
        docs: {
            description: {
                component:
                    'A Hyperlink is typically used for non destructive actions navigating users to another page or element inline with standard body copy.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=59-1790&mode=design&t=FoGkzhcXiCnlZFCr-0',
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgHyperlink },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `<WtgHyperlink v-bind="args" @click="action">SUPPLY</WtgHyperlink>`,
    }),
    argTypes: {
        target: {
            options: ['_blank', '_self', '_parent', '_top'],
            control: {
                type: 'select',
            },
        },
    },
};
export default meta;

export const Default: Story = {
    args: {
        href: 'https://design.wtg.zone/',
    },
};

export const Sandbox: Story = {
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgHyperlink, WtgCol, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            updateIndeterminate: action('update:indeterminate'),
        },
        template: templateWithRtl(HyperlinkSandboxTemplate),
    }),
};
