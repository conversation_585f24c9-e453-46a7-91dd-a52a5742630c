import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import info from '../../../storybook/assets/info.png';

import { Meta, Title, Description, Story, Canvas, Controls, ArgTypes } from '@storybook/blocks';
import * as WtgLayoutGrid from './WtgLayoutGrid.stories.ts';

<Meta of={WtgLayoutGrid} />

<div className="component-header">
    <h1>Layout Grid</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td></td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

<div className="banner-warning">

    <div>
        <h4>⚠️ Please note</h4>
        <p>Layout grid is configured to follow the Supply recommendations for gutters (8px on desktop). This is achieved through padding on the columns to ensure optimal flexibility across all breakpoints in our responsive designs.</p>
        <p class="mt-2">Individual forms should refrain from applying margins or padding in an attempt to mimic figma layout</p>
    </div>

</div>

## Guidelines

The Bootstrap grid system is a powerful tool for creating responsive and flexible layouts. It is based on a 12-column layout and utilizes row and col components to define and structure content alignment and distribution. The row component is used to create horizontal groups of columns, while the col component determines how content is divided across the grid and adapts dynamically to various screen sizes.

While the grid system provides a high degree of customization and control, it can sometimes feel cumbersome for developers, especially for simple use cases, as it requires familiarity with the intricacies of Bootstrap’s row and col components and their associated classes.

To address this, the layout grid component in Vue abstracts away the complexity by encapsulating the functionality of row and col within a single, easy-to-use interface. This encapsulation simplifies Vue templates by:

<b>Reducing boilerplate:</b> Instead of manually managing nested row and col components, developers can use layout grid to
create layouts with fewer lines of code.

<b>Ease of use:</b> Developers no longer need to have an in-depth understanding of how row and col interact or worry about
manually assigning grid classes. The layout grid handles this behind the scenes.

<b>Consistency:</b> By centralizing the grid behavior in the layout grid component, the implementation ensures consistent
layout behavior across the application.

<b>Optimized for simplicity:</b> It is especially well-suited for straightforward layouts, such as forms or small grids,
where the full flexibility of row and col is not necessary.

For scenarios requiring finer control over the layout or highly customized grids, developers can still fall back to the lower-level row and col components provided by Bootstrap. This gives teams the flexibility to leverage the abstraction of layout grid for simplicity while retaining the ability to customize as needed for complex use cases.

## When to use LayoutGrid

The LayoutGrid component is designed to simplify layout creation, particularly for teams working in low-code environments or for those who are less familiar with HTML and CSS. It provides a predictable, prop-based API that helps avoid common layout pitfalls like excessive nesting or inconsistent spacing.

Our layout templates, used in Supply’s low-code tools and onboarding flows, rely heavily on LayoutGrid and related layout props. These templates offer a guided starting point and help ensure consistency across experiences. They’re especially useful for building responsive views without having to manually apply utility classes or manage breakpoints.

That said, LayoutGrid is entirely optional. Experienced front-end developers are free to use semantic markup and utility classes directly—especially in application code where more control or flexibility is needed. The goal of these layout components is to offer a structured foundation for those who want it, not to enforce a specific implementation style.

Internally, a key motivation behind LayoutGrid is to reduce the gap between design and development. By aligning layout props with Figma outputs, we can reduce rework and miscommunication, making it easier to implement designs as intended.

We're also keeping an eye on longer-term improvements—such as modernizing our layout approach using native CSS Grid, rather than sticking with 12-column, Bootstrap-inspired systems. Feedback and experimentation on that front are always welcome.

## Related components

-   [Box](/docs/utilities-box--docs)
-   [Grid Implementation](/docs/guidelines-layout-grid-implementation--overview)
-   [Panel](/docs/components-panel--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
