import { WtgFrameworkMenuItem } from './framework';

interface BaseSearchResult {
    isActive?: boolean;
}

interface KeyField {
    caption: string;
    value: string;
}

export interface ParsedSearch {
    searchTerm?: string;
    shouldNotSearch?: boolean;
}

export interface WtgFrameworkSearchEntity {
    entityType: string;
    entityName: string;
    reference: string;
    color: string;
    searchableColumns: string[];
    keyFields: string[];
    shorthand: string | undefined;
}

export interface WtgFrameworkSearchResultEntity extends BaseSearchResult {
    PK: string;
    entityType: string;
    entityName: string;
    color: string;
    jobReference: string;
    matchedColumn: string;
    keyFields: KeyField[];
}

export interface WtgFrameworkSearchResultMenuItem extends WtgFrameworkMenuItem, BaseSearchResult {}

export interface WtgFrameworkSearchResultSuggestedFilter extends BaseSearchResult {
    shorthand: string;
}

export type WtgFrameworkSearchResultRecord =
    | WtgFrameworkSearchResultEntity
    | WtgFrameworkSearchResultMenuItem
    | WtgFrameworkSearchResultSuggestedFilter;

export class WtgFrameworkSearchHandler {
    selectedShorthands: string[] = [];
    searchProvider?: WtgFrameworkSearchProvider;

    constructor(searchProvider?: WtgFrameworkSearchProvider) {
        this.searchProvider = searchProvider;
    }

    getItemsAsync(search: string): Promise<WtgFrameworkSearchResultEntity[]> {
        return this._performSearchAsync(this._parseSearch(search));
    }

    async _performSearchAsync(parsedSearch: ParsedSearch): Promise<WtgFrameworkSearchResultEntity[]> {
        const { searchTerm, shouldNotSearch } = parsedSearch;

        if (!this.searchProvider || shouldNotSearch || !searchTerm) {
            return Promise.resolve([]);
        }
        const entityTypes: string[] = [];

        this.searchProvider.shorthandToEntityType.forEach((value, key) => {
            if (this.selectedShorthands.includes(key)) {
                entityTypes.push(value);
            }
        });
        return await this.searchProvider.getItemsAsync(searchTerm, entityTypes);
    }

    // Parse search term to retrieve shorthand if exists. e.g, "ORD:CLL:1234" has a shorthands of "ORD" and "CLL".
    // This is so we can restrict the search to particular entity types (ie those that are ORD or CLL).
    _parseSearch(searchTerm: string): ParsedSearch {
        if (!this.searchProvider || searchTerm.length < 3) {
            return { shouldNotSearch: true };
        }

        const foundShorthands = Array.from(this.searchProvider.shorthandToEntityType.keys()).filter((shorthand) =>
            searchTerm.toUpperCase().includes(shorthand.toUpperCase() + ':')
        );

        const newShorthands = foundShorthands.filter((shorthand) => !this.selectedShorthands.includes(shorthand));
        if (newShorthands.length) {
            this.selectedShorthands = [...this.selectedShorthands, ...newShorthands];
        }

        const shorthandColonKeysToMatch = foundShorthands.map((shorthand) => shorthand.toUpperCase() + ':').join('|');
        searchTerm = searchTerm.replace(new RegExp(shorthandColonKeysToMatch, 'gi'), '').trim();

        if (searchTerm.length < 3) return { shouldNotSearch: true };
        return { searchTerm };
    }
}

export interface WtgFrameworkSearchProvider {
    entities: WtgFrameworkSearchEntity[];
    shorthandToEntityType: Map<string, string>;
    getItemsAsync: (searchTerm: string, entityTypes: string[]) => Promise<WtgFrameworkSearchResultEntity[]>;
    onSearchItemClicked: (item: WtgFrameworkSearchResultEntity) => Promise<void>;
    getCaptionForProperty: (propertyName: string) => string;
}
