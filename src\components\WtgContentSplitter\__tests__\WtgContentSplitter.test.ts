import WtgContentSplitter from '@components/WtgContentSplitter/WtgContentSplitter.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgContentSplitter', () => {
    test('its name is WtgContentSplitter', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgContentSplitter');
    });

    test('it applies the wtg-content-splitter--solid class when variant="solid"', () => {
        const wrapper = mountComponent({
            props: { variant: 'solid' },
        });

        expect(wrapper.classes()).toContain('wtg-content-splitter--solid');
        expect(wrapper.classes()).not.toContain('wtg-content-splitter--dashed');
    });

    test('it applies the wtg-content-splitter--dashed class when variant="dashed"', () => {
        const wrapper = mountComponent({
            props: { variant: 'dashed' },
        });
        expect(wrapper.classes()).toContain('wtg-content-splitter--dashed');
        expect(wrapper.classes()).not.toContain('wtg-content-splitter--solid');
    });

    test('it should default to solid line style when the prop is omitted', () => {
        const wrapper = mountComponent();
        expect(wrapper.props().variant).toBe('solid');
    });

    test('it should have a presentational role', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.getAttribute('role')).toBe('presentation');
    });

    test('it should display the contents of the default slot', () => {
        const wrapper = mountComponent({
            slots: {
                default: 'Hello World',
            },
        });

        expect(wrapper.find('.wtg-content-splitter__content-wrapper').element.innerHTML).toBe('Hello World');
    });

    test('it applies the wtg-content-splitter--start class when justify="start"', () => {
        const wrapper = mountComponent({
            props: {
                justify: 'start',
            },
        });

        expect(wrapper.classes()).toContain('wtg-content-splitter--start');
        expect(wrapper.classes()).not.toContain('wtg-content-splitter--center');
        expect(wrapper.classes()).not.toContain('wtg-content-splitter--end');
    });

    test('it applies the wtg-content-splitter--center class when justify="center"', () => {
        const wrapper = mountComponent({
            props: {
                justify: 'center',
            },
        });

        expect(wrapper.classes()).not.toContain('wtg-content-splitter--start');
        expect(wrapper.classes()).toContain('wtg-content-splitter--center');
        expect(wrapper.classes()).not.toContain('wtg-content-splitter--end');
    });

    test('it applies the wtg-content-splitter--end class when justify="end"', () => {
        const wrapper = mountComponent({
            props: {
                justify: 'end',
            },
        });

        expect(wrapper.classes()).not.toContain('wtg-content-splitter--start');
        expect(wrapper.classes()).not.toContain('wtg-content-splitter--center');
        expect(wrapper.classes()).toContain('wtg-content-splitter--end');
    });

    test('it should default to justify="center" when the prop is omitted', () => {
        const wrapper = mountComponent();
        expect(wrapper.props().justify).toBe('center');
    });

    function mountComponent({ props = {} as InstanceType<typeof WtgContentSplitter>['$props'], slots = {} } = {}) {
        return mount(WtgContentSplitter, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
