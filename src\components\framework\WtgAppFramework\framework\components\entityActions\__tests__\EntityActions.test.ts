import { WtgFrameworkTask } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import EntityActions from '../EntityActions.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('entity-actions', () => {
    let el: HTMLElement;
    let task: WtgFrameworkTask;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        task = new WtgFrameworkTask();
        task.genericActions = [
            {
                id: 'someguid1',
                caption: 'Action 1',
                placement: 'default',
                onInvoke: jest.fn(),
            },
            {
                id: 'someguid2',
                caption: 'Action 2',
                placement: 'taskactions',
                icon: '$icon',
                onInvoke: jest.fn(),
            },
        ];
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is EntityActions', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('EntityActions');
    });

    describe('on mobile', () => {
        beforeEach(() => {
            wtgUi.breakpoint.smAndUp = false;
        });

        test('it renders the entity actions overflow', () => {
            const wrapper = mountComponent();
            const action = wrapper.findComponent({ name: 'TaskActionsOverflow' });
            expect(action.exists()).toBe(true);
            expect(action.props('variant')).toBe('ghost');
        });
    });

    describe('on desktop', () => {
        beforeEach(() => {
            wtgUi.breakpoint.smAndUp = true;
        });

        test('it renders the eDocs action', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const action = wrapper.findComponent({ name: 'EDocsAction' });
            expect(action.exists()).toBe(true);
            expect(action.vm.$props.task).toStrictEqual(task);
        });

        test('it renders the documents action', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const action = wrapper.findComponent({ name: 'DocumentsAction' });
            expect(action.exists()).toBe(true);
            expect(action.vm.$props.task).toStrictEqual(task);
        });

        test('it renders the logs action', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const action = wrapper.findComponent({ name: 'LogsAction' });
            expect(action.exists()).toBe(true);
            expect(action.vm.$props.task).toStrictEqual(task);
        });

        test('it renders the messages action', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const action = wrapper.findComponent({ name: 'MessagesAction' });
            expect(action.exists()).toBe(true);
            expect(action.vm.$props.task).toStrictEqual(task);
        });

        test('it renders the notes action', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const action = wrapper.findComponent({ name: 'NotesAction' });
            expect(action.exists()).toBe(true);
            expect(action.vm.$props.task).toStrictEqual(task);
        });

        test('it renders the workflows action', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const action = wrapper.findComponent({ name: 'WorkflowsAction' });
            expect(action.exists()).toBe(true);
            expect(action.vm.$props.task).toStrictEqual(task);
        });

        test('it renders task actions if a transition is placed as taskactions', () => {
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task } });
            const taskActions = wrapper.findAllComponents({ name: 'TaskAction' });
            expect(taskActions.length).toBe(1);
            expect(taskActions.at(0)?.vm.$props.action).toBe(wrapper.vm.transitionTasks[0]);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(EntityActions, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
