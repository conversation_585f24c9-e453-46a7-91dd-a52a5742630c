export const IconSandboxTemplate = `
<WtgPanel max-width="100%" layout="flex" flex-align="align-baseline ga-2" caption="Ensure correct icon alignment when parent specifies font-size">
    <WtgPanel style="display:flex;flex-direction: column;align-items:center;">
        <WtgIcon icon="s-icon-truck-loading" style="font-size:80px;"></WtgIcon>
        <WtgLabel>
            <strong>font-size:80px</strong>
        </WtgLabel>   
    </WtgPanel>
    <WtgPanel style="display:flex;flex-direction: column;align-items:center;">
        <WtgIcon icon="s-icon-truck-pending" style="font-size:60px;"></WtgIcon>
        <WtgLabel>
            <strong>font-size:60px</strong>
        </WtgLabel>       
    </WtgPanel>
    <WtgPanel style="display:flex;flex-direction: column;align-items:center;">
        <WtgIcon icon="s-icon-truck-completed" style="font-size:40px;"></WtgIcon>
        <WtgLabel>
            <strong>font-size:40px</strong>
        </WtgLabel>   
    </WtgPanel>
    <WtgPanel style="display:flex;flex-direction: column;align-items:center;">
        <WtgIcon icon="s-icon-warehouse" style="font-size:20px;"></WtgIcon>
        <WtgLabel>
            <strong>font-size:20px</strong>
        </WtgLabel>     
    </WtgPanel>
    <WtgPanel style="display:flex;flex-direction: column;align-items:center;">
        <WtgIcon icon="s-icon-wisetech-global" style="font-size:10px"></WtgIcon>
        <WtgLabel>
            <strong>font-size:10px</strong>
        </WtgLabel>      
    </WtgPanel>
</WtgPanel>
<WtgPanel class="mt-2" max-width="100%" layout="flex" caption="Ensure correct icons from property and slots">    
    <WtgIcon icon="$package" />
    <WtgIcon icon="s-icon-package" />
    <WtgIcon>$package</WtgIcon>
    <WtgIcon>s-icon-package</WtgIcon>
    <WtgIcon class="s-icon-">{{ "\u{e985}" }}</WtgIcon>
</WtgPanel>
<WtgPanel class="mt-2" max-width="100%" layout="flex" caption="Ensure correct icon color">    
    <WtgIcon icon="$package" color="text-amber"/>
    <WtgIcon icon="$package" color="amber"/>
    <WtgIcon icon="$package" color="text-amber"/>
    <WtgIcon icon="s-icon-package" color="#FF0000" />
    <WtgIcon color="rgba(0,255,0,.5)">$package</WtgIcon>
    <WtgIcon color="var(--s-error-txt-default)">$package</WtgIcon>
    <WtgIcon color="brand">$package</WtgIcon>
</WtgPanel>`;
