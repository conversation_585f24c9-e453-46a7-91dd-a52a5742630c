export const ContentSplitterSandboxTemplate = `
<h1>Default</h1>
<wtg-content-splitter>Hello World</wtg-content-splitter>
<h1>Variant: Solid</h1>
<wtg-content-splitter variant="solid">Hello World</wtg-content-splitter>
<h1>Variant: Dashed</h1>
<wtg-content-splitter variant="dashed">Hello World</wtg-content-splitter>

<h1>Justify: Start</h1>
<wtg-content-splitter justify="start">Hello World</wtg-content-splitter>
<h1>Justify: Center</h1>
<wtg-content-splitter justify="center">Hello World</wtg-content-splitter>
<h1>Justify: End</h1>
<wtg-content-splitter justify="end">Hello World</wtg-content-splitter>
`;
