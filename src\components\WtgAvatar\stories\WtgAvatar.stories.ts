import WtgAvatar from '@components/WtgAvatar/WtgAvatar.vue';
import WtgCol from '@components/WtgCol';
import WtgIcon from '@components/WtgIcon';
import WtgRow from '@components/WtgRow';
import { useSupplyPrefixIconsName } from '@composables/icon';
import { sizeArgTypesAvatar } from '@composables/size';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { Meta, StoryObj } from '@storybook/vue3';
import { AvatarSandboxTemplate } from './templates/wtg-avatar-sandbox.stories-template';

const icons = useSupplyPrefixIconsName();
type Story = StoryObj<typeof WtgAvatar>;
const meta: Meta<typeof WtgAvatar> = {
    title: 'Components/Avatar',
    component: WtgAvatar,
    parameters: {
        docs: {
            description: {
                component:
                    'An Avatar is used as a visual representation of a user, entity or portal. This can be represented with either a photo, icon or initials.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=305-20614&mode=design',
        },
        layout: 'centered',
    },
    argTypes: {
        ...sizeArgTypesAvatar,
        icon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
        upload: {
            control: 'boolean',
        },
        loading: {
            control: 'boolean',
        },
        disabled: {
            control: 'boolean',
        },
    },
};

export default meta;

export const Image: Story = {
    args: {
        image: 'avatarImage.png',
    },
    parameters: {
        controls: {
            exclude: ['color', 'icon', 'initials', 'loading', 'upload', 'notificationIcon', 'notificationSentiment'],
        },
    },
    render: (args) => ({
        components: { WtgAvatar, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <WtgRow>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="m" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="l" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="xl" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="xxl" v-bind="args"></WtgAvatar>
            </WtgCol>
        </WtgRow>`,
    }),
};

export const Initials: Story = {
    args: {
        initials: 'MA',
    },
    parameters: {
        controls: {
            exclude: ['alt', 'fallbackImage', 'icon', 'image', 'initials', 'notificationIcon', 'notificationSentiment'],
        },
    },
    render: (args) => ({
        components: { WtgAvatar, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <WtgRow>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="m" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="l" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="xl" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="xxl" v-bind="args"></WtgAvatar>
            </WtgCol>
        </WtgRow>`,
    }),
};

export const Icon: Story = {
    args: {
        icon: 's-icon-user',
    },
    parameters: {
        controls: {
            exclude: ['alt', 'fallbackImage', 'image', 'initials', 'notificationIcon', 'notificationSentiment'],
        },
    },
    render: (args) => ({
        components: { WtgAvatar, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <WtgRow>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar  size="m" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="l" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="xl" v-bind="args"></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar size="xxl" v-bind="args"></WtgAvatar>
            </WtgCol>
        </WtgRow>`,
    }),
};
export const Disabled: Story = {
    args: {
        disabled: true,
    },
    parameters: {
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgAvatar, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <WtgRow>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar image="avatarImage.png" v-bind="args" ></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar icon="s-icon-user" v-bind="args" ></WtgAvatar>
            </WtgCol>
            <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
                <WtgAvatar initials="MA" v-bind="args" ></WtgAvatar>
            </WtgCol>
        </WtgRow>`,
    }),
};

export const Sandbox: Story = {
    args: {
        image: 'avatarImage.png',
        icon: 's-icon-user',
        initials: 'MA',
    },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgAvatar, WtgCol, WtgRow, WtgIcon },
        setup: () => ({ args }),
        methods: {},
        template: templateWithRtl(AvatarSandboxTemplate),
    }),
};
