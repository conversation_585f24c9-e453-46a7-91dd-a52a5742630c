<template>
    <CountryFlag class="wtg-flag flag__margin" v-bind="props" />
</template>

<script setup lang="ts">
import CountryFlag from 'vue-country-flag-next';

const props = defineProps({
    /**
     * The country code (ISO 3166-1 alpha-2) for the flag to display.
     * Example: 'US' for the United States, 'FR' for France.
     */
    country: {
        type: String,
        required: true,
    },

    /**
     * The size of the flag.
     * Can be a predefined size (e.g., 'small', 'medium', 'large') or a custom value.
     */
    size: {
        type: String,
        default: undefined,
    },
});
</script>

<style lang="scss">
.wtg-flag {
    border-radius: var(--s-radius-xs);
    border: 1px solid;
    border-color: var(--s-neutral-border-weak-default);
}
</style>

<style lang="scss" scoped>
.flag__margin {
    &[class*='normal-flag'],
    &[class*='small-flag'],
    &[class*='big-flag'] {
        margin-right: -13px;
        margin-left: -13px;
        margin-top: 0px;
        margin-bottom: 0px;
    }
}
</style>
