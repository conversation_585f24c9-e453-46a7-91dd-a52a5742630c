import { enableAutoUnmount, mount, type VueWrapper } from '@vue/test-utils';
import WtgBreadcrumbs from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const testCases = [
    {
        description: 'with one item',
        items: [{ caption: 'Portal name' }],
        expectedSlashes: 0,
        expectedProps: [{ caption: 'Portal name', to: undefined, href: undefined, variant: 'current' }],
    },
    {
        description: 'with two items',
        items: [
            { caption: 'Portal name', to: '/portal' },
            { caption: 'Page one', to: '/page-one' },
        ],
        expectedSlashes: 1,
        expectedProps: [
            { caption: 'Portal name', to: '/portal', href: undefined, variant: 'default' },
            { caption: 'Page one', to: undefined, href: undefined, variant: 'current' },
        ],
    },
    {
        description: 'with three items',
        items: [
            { caption: 'Portal name', link: '/portal' },
            { caption: 'Page one', link: '/page-one' },
            { caption: 'Page two', link: '/page-two' },
        ],
        expectedSlashes: 2,
        expectedProps: [
            { caption: 'Portal name', to: undefined, href: '/portal', variant: 'default' },
            { caption: 'Page one', to: undefined, href: '/page-one', variant: 'default' },
            { caption: 'Page two', to: undefined, href: undefined, variant: 'current' },
        ],
    },
    {
        description: 'with more than three items',
        items: [
            { caption: 'Portal name', to: '/portal' },
            { caption: 'Page one', to: '/page-one' },
            { caption: 'Page two', link: '/page-two' },
            { caption: 'Page three' },
        ],
        expectedSlashes: 2,
        expectedProps: [
            { caption: 'Portal name', to: '/portal', href: undefined, variant: 'default' },
            { caption: 'Page three', to: undefined, href: undefined, variant: 'current' },
        ],
    },
];

const testBreadcrumbItem = (item: any, expectedProps: Object) => {
    expect(item.exists()).toBe(true);
    Object.entries(expectedProps).forEach(([key, value]) => {
        expect(item.props(key)).toBe(value);
    });
};

describe('WtgBreadcrumbs', () => {
    testCases.forEach(({ description, items, expectedSlashes, expectedProps }) => {
        describe(description, () => {
            let wrapper: VueWrapper;
            beforeEach(() => {
                wrapper = mountComponent(items);
            });

            test(`renders ${expectedSlashes} forward slashes exactly`, () => {
                const forwardSlashIcons = wrapper.findAllComponents({ name: 'WtgIcon' });
                expect(forwardSlashIcons.length).toBe(expectedSlashes);
            });

            test('renders WtgBreadcrumbsItem correctly', () => {
                const breadcrumbItems = wrapper.findAllComponents({ name: 'WtgBreadcrumbsItem' });
                breadcrumbItems.forEach((item, index) => {
                    const expectedProp = expectedProps[index];
                    testBreadcrumbItem(item, expectedProp);
                });
            });
        });
    });

    describe('with more than three items it should display ... button', () => {
        let wrapper: VueWrapper<any>;
        beforeEach(() => {
            wrapper = mountComponent(testCases[3].items);
        });

        test('the second item has ... as its content', () => {
            const moreButton = wrapper.findComponent({ name: 'WtgButton' });
            expect(moreButton.html()).toContain('...');
            expect(moreButton.classes()).toStrictEqual([
                'wtg-button--ghost',
                'wtg-button',
                'wtg-breadcrumbs__button',
                'v-popper--has-tooltip',
            ]);
        });

        test('the second item opens a dropdown when clicked', async () => {
            const moreButton = wrapper.findComponent({ name: 'WtgButton' });
            await moreButton.trigger('click');

            const menu = wrapper.findComponent({ name: 'WtgPopover' });
            const listItems = menu.findComponent({ name: 'WtgList' }).findAllComponents({ name: 'WtgListItem' });
            expect(listItems.length).toBe(2);
        });
    });

    const mountComponent = (items: Array<any>) => {
        return mount(WtgBreadcrumbs, {
            propsData: { items },
            global: { plugins: [wtgUi] },
        });
    };
});
