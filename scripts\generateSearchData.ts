import fs from 'fs';
import glob from 'glob';
import { execSync } from 'child_process';

interface SearchItem {
    text: string;
    title: string;
    path: string;
}

const repoRoot = execSync('git rev-parse --show-toplevel', { encoding: 'utf-8' }).trim();
const index: SearchItem[] = [];

const getTitle = (file: string): string | undefined => {
    const fileContent = fs.readFileSync(file, 'utf-8');
    const metaTitleMatch = /<Meta title="([^"]*)"/.exec(fileContent);

    if (metaTitleMatch) {
        return metaTitleMatch[1];
    } else {
        // Looking for title in story file if no Meta title is found in .mdx file
        const fileDir = file.substring(0, file.lastIndexOf('/'));
        const storyFiles = glob.sync(fileDir + '/*.stories.ts');

        if (storyFiles.length) {
            const storyContent = fs.readFileSync(storyFiles[0], 'utf-8');
            return /title: '([^']*)',/.exec(storyContent)?.[1];
        }
    }

    return undefined;
};

const calculatePath = (filePath: string, title: string): string => {
    const suffix = filePath.endsWith('Docs.mdx') ? '--docs' : '--overview';

    const clearTitle = title
        .replace(/[ /&]+/g, '-')
        .replace(/[^a-zA-Z-]/g, '')
        .toLowerCase();

    return `/?path=/docs/${clearTitle}${suffix}`;
};

glob('./**/*.mdx', { ignore: './node_modules/**' }, (err: Error | null, filePaths: string[]) => {
    for (const filePath of filePaths) {
        if (filePath.endsWith('documentation-template.mdx')) {
            continue;
        }

        const title = getTitle(filePath);

        if (!title) {
            console.warn(`No title found in file: ${filePath}`);
            continue;
        }

        const path = calculatePath(filePath, title);
        const text = fs
            .readFileSync(filePath, 'utf-8')
            .replace(/<[^>]+>/g, '')
            .replace(/import.*/g, '')
            .replace(/\s+/g, ' ')
            .trim();

        index.push({ text: text, title: title, path: path });
    }

    fs.writeFile(
        `${repoRoot}/src/storybook/search/searchData.json`,
        JSON.stringify(index),
        (err: Error | null) => err && console.error(err)
    );
});
