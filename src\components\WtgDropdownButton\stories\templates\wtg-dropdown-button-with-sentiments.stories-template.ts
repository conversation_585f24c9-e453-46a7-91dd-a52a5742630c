export const DropdownButtonWithSentimentsTemplate = `
<WtgRow>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgDropdownButton 
            v-bind="args"
            @click="action"
            @dropdown-click="dropdownAction">
            Outline Default
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            sentiment="success"
            @click="action"
            @dropdown-click="dropdownAction">
            Outline Success
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            sentiment="critical"
            @click="action"
            @dropdown-click="dropdownAction">
            Outline Critical
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
        </WtgDropdownButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            sentiment="primary"
            @click="action"
            @dropdown-click="dropdownAction">
            Fill Primary
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            sentiment="success"
            @click="action"
            @dropdown-click="dropdownAction">
            Fill Success
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            sentiment="critical"
            @click="action"
            @dropdown-click="dropdownAction">
            Fill Critical
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
        </WtgDropdownButton>
    </WtgCol>
</WtgRow>`;
