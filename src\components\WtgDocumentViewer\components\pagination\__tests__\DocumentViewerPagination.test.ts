import DocumentViewerPagination from '@components/WtgDocumentViewer/components/pagination/DocumentViewerPagination.vue';
import WtgLabel from '@components/WtgLabel';
import WtgTextField from '@components/WtgTextField';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('Document Viewer Pagination', () => {
    let wrapper: any;

    beforeEach(async () => {
        wrapper = mountComponent();
        await wrapper.setProps({ activePageNumber: 2, pageCount: 5 });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('renders a WtgTextField for active page', () => {
        const activePageField = wrapper.findComponent(WtgTextField);
        expect(activePageField.exists()).toBe(true);
        expect(activePageField.props('modelValue')).toBe('2');
    });

    it('renders a WtgLabel for page count', () => {
        const pageCountLabel = wrapper.findComponent(WtgLabel);
        expect(pageCountLabel.exists()).toBe(true);
        expect(pageCountLabel.text()).toBe('/ 5');
    });

    it('emits update-page event when active page number changes', async () => {
        const activePageField = wrapper.findComponent(WtgTextField);
        await activePageField.setValue(3);

        expect(wrapper.emitted('active-page-updated')).toBeTruthy();
        expect(wrapper.emitted('active-page-updated')[0][0]).toEqual(3);
    });

    it('updates active page number when active page number prop changes', async () => {
        const activePageField = wrapper.findComponent(WtgTextField);
        expect(activePageField.props('modelValue')).toBe('2');
        await wrapper.setProps({ activePageNumber: 4 });
        expect(activePageField.props('modelValue')).toBe('4');
    });
});

function mountComponent({ props = {} } = {}) {
    return mount(DocumentViewerPagination, {
        props,
        global: {
            plugins: [wtgUi],
        },
    });
}
