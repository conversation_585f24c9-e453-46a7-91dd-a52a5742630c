import WtgDateTimePicker from '@components/WtgDateTimePicker/WtgDateTimePicker.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
});

describe('WtgDateTimePicker', () => {
    beforeEach(() => jest.useFakeTimers().setSystemTime(new Date('2020-01-19T01:02:03')));

    afterEach(() => jest.resetAllMocks());

    test('it renders datepicker with expected components', () => {
        const wrapper = mountComponent();

        const datePicker = wrapper.findComponent({ name: 'WtgDatePicker' });
        expect(datePicker.exists()).toBe(true);

        const timePicker = wrapper.findComponent({ name: 'WtgTimePicker' });
        expect(timePicker.exists()).toBe(true);
    });

    test('it handles clicking the "today"', async () => {
        const wrapper = mountComponent({
            useSeconds: true,
        });

        const todayButton = wrapper.find('.wtg-button__content');
        expect(todayButton.exists()).toBe(true);
        expect(todayButton.text()).toBe('Today');

        await todayButton.trigger('click');
        expect(wrapper.emitted()).toHaveProperty('update:modelValue', [['2020-01-19 01:02:03']]);
    });

    test('it handles clicking the "today" when todayDateFn is passed', async () => {
        const wrapper = mountComponent({
            useSeconds: true,
            todayDateFn: () => '2025-01-21T12:00:00',
        });
        const todayButton = wrapper.find('.wtg-button__content');

        await todayButton.trigger('click');

        expect(wrapper.emitted()).toHaveProperty('update:modelValue', [['2025-01-21 12:00:00']]);
    });

    test('it should update the date when selecting a day', async () => {
        const wrapper = mountComponent();

        const day = wrapper.find('[data-v-date="2020-01-15"]');
        await day.find('button').trigger('click');

        expect(wrapper.emitted()).toHaveProperty('update:modelValue', [['2020-01-15 01:02']]);
    });

    test('it should update the time when selecting time', async () => {
        const wrapper = mountComponent();

        const selectors = wrapper.findAll('.wtg-time-picker__selector');
        const hrsSelector = selectors.at(0);
        const minsSelector = selectors.at(1);

        await hrsSelector?.findAll('.wtg-time-picker__selector-value').at(4)?.trigger('click');
        await minsSelector?.findAll('.wtg-time-picker__selector-value').at(55)?.trigger('click');

        expect(wrapper.emitted()).toHaveProperty('update:modelValue', [['2020-01-19 04:00'], ['2020-01-19 04:55']]);
    });

    test('it should update the date and time when selecting a day and time', async () => {
        const wrapper = mountComponent();

        const day = wrapper.find('[data-v-date="2020-01-15"]');
        await day.find('button').trigger('click');

        const selectors = wrapper.findAll('.wtg-time-picker__selector');
        const hrsSelector = selectors.at(0);
        const minsSelector = selectors.at(1);

        await hrsSelector?.findAll('.wtg-time-picker__selector-value').at(4)?.trigger('click');
        await minsSelector?.findAll('.wtg-time-picker__selector-value').at(55)?.trigger('click');

        expect(wrapper.emitted()).toHaveProperty('update:modelValue', [
            ['2020-01-15 01:02'],
            ['2020-01-15 04:02'],
            ['2020-01-15 04:55'],
        ]);
    });

    test('it should allow selecting seconds', async () => {
        const wrapper = mountComponent({
            useSeconds: true,
        });

        const selectors = wrapper.findAll('.wtg-time-picker__selector');
        const hrsSelector = selectors.at(0);
        const minsSelector = selectors.at(1);
        const secondsSelector = selectors.at(2);

        await hrsSelector?.findAll('.wtg-time-picker__selector-value').at(4)?.trigger('click');
        await minsSelector?.findAll('.wtg-time-picker__selector-value').at(55)?.trigger('click');
        await secondsSelector?.findAll('.wtg-time-picker__selector-value').at(55)?.trigger('click');

        expect(wrapper.emitted()).toHaveProperty('update:modelValue', [
            ['2020-01-19 04:00:00'],
            ['2020-01-19 04:55:00'],
            ['2020-01-19 04:55:55'],
        ]);
    });

    test('it should allow selecting meridiem', async () => {
        const wrapper = mountComponent();
        wtgUi.language.current = 'en-US';
        await wrapper.setProps({ useSeconds: true });

        const selectors = wrapper.findAll('.wtg-time-picker__selector');
        const hrsSelector = selectors.at(0);
        const minsSelector = selectors.at(1);
        const secondsSelector = selectors.at(2);
        const meridiemSelector = selectors.at(3);

        await hrsSelector?.findAll('.wtg-time-picker__selector-value').at(4)?.trigger('click');
        await minsSelector?.findAll('.wtg-time-picker__selector-value').at(55)?.trigger('click');
        await secondsSelector?.findAll('.wtg-time-picker__selector-value').at(55)?.trigger('click');
        await meridiemSelector?.findAll('.wtg-time-picker__selector-value').at(1)?.trigger('click');

        expect(wrapper.emitted()).toHaveProperty('update:modelValue', [
            ['2020-01-19 05:02:00 AM'],
            ['2020-01-19 05:55:00 AM'],
            ['2020-01-19 05:55:55 AM'],
            ['2020-01-19 05:55:55 PM'],
        ]);
    });

    describe('timezone functionality', () => {
        test('it shows timezone selector when showTimeZone is true', () => {
            const wrapper = mountComponent({
                showTimeZone: true,
            });

            const timezoneSelector = wrapper.findComponent({ name: 'WtgSelectField' });
            expect(timezoneSelector.exists()).toBe(true);
        });

        test('it hides timezone selector when showTimeZone is false', () => {
            const wrapper = mountComponent({
                showTimeZone: false,
            });

            const timezoneSelector = wrapper.findComponent({ name: 'WtgSelectField' });
            expect(timezoneSelector.exists()).toBe(false);
        });

        test('it initializes with current timezone when showTimeZone is true and no modelValue', async () => {
            const wrapper = mountComponent({
                showTimeZone: true,
                todayDateFn: () => '2025-01-21T12:00+03:00',
            });

            await wrapper.vm.$nextTick();
            const timezoneSelector = wrapper.findComponent({ name: 'WtgSelectField' });
            expect(timezoneSelector.props('modelValue')).toBeDefined();
            expect(timezoneSelector.props('modelValue')).toEqual('+03:00');
        });
    });

    function mountComponent(props = {}) {
        return mount(WtgDateTimePicker as any, {
            props,
            global: {
                plugins: [wtgUi],
                provide: {
                    darkMode: false,
                },
            },
        });
    }
});
