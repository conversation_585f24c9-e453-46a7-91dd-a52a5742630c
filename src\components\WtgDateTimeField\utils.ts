export const todayWithTz = (now: Date, useSeconds: boolean): { date: string; time: string; offset: string } => {
    const pad = (n: number) => n.toString().padStart(2, '0');

    const seconds = useSeconds ? `:${pad(now.getSeconds())}` : '';
    const date = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}`;
    const time = `${pad(now.getHours())}:${pad(now.getMinutes())}${seconds}`;

    const offsetMinutes = now.getTimezoneOffset();
    const sign = offsetMinutes > 0 ? '-' : '+';
    const absOffset = Math.abs(offsetMinutes);
    const offsetHours = pad(Math.floor(absOffset / 60));
    const offsetMins = pad(absOffset % 60);
    const offset = `${sign}${offsetHours}:${offsetMins}`;

    return {
        date,
        time,
        offset,
    };
};
