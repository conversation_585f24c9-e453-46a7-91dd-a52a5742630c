<template>
    <WtgBox class="d-flex wtg-w-100">
        <div class="d-flex align-center" style="gap: var(--s-spacing-m)">
            <WtgLabel v-if="headerCaption" :typography="isEditorTitle ? 'title-md-default' : 'text-md-default'">
                {{ headerCaption }}
            </WtgLabel>
            <WtgTag
                v-if="isCardAddressOverriden"
                :aria-label="cardAddressOverrideDescription"
                :tooltip="cardAddressOverrideDescription"
                :label="editedCaption"
                :dismissible="false"
                icon="s-icon-status-warning"
            />
        </div>
        <WtgSpacer />
        <WtgIconButton v-if="closeable" icon="s-icon-close" variant="ghost" @click="emit('cancel')"></WtgIconButton>
        <WtgHyperlink v-if="props.editable" style="cursor: pointer; text-decoration: underline" @click="emit('edit')">
            {{ editCaption }}
        </WtgHyperlink>
    </WtgBox>
</template>

<script setup lang="ts">
import WtgBox from '@components/WtgBox';
import WtgHyperlink from '@components/WtgHyperlink/WtgHyperlink.vue';
import WtgIconButton from '@components/WtgIconButton/WtgIconButton.vue';
import WtgLabel from '@components/WtgLabel';
import WtgSpacer from '@components/WtgSpacer';
import WtgTag from '@components/WtgTag';
import { useLocale } from '@composables/locale';
import { computed } from 'vue';

const props = defineProps({
    cardAddressOverrideDescription: {
        type: String,
        default: '',
    },
    closeable: {
        type: Boolean,
        default: false,
    },
    isCardAddressOverriden: {
        type: Boolean,
        default: false,
    },
    isEditorTitle: {
        type: Boolean,
        default: false,
    },
    editable: {
        type: Boolean,
        default: false,
    },
    required: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: undefined,
    },
});

const emit = defineEmits<{
    edit: [];
    cancel: [];
}>();
const { formatCaption } = useLocale();

const editCaption = computed(() => formatCaption('jobAddress.edit'));
const editedCaption = computed(() => formatCaption('jobAddress.edited'));

const headerCaption = computed(() => props.title ?? formatCaption('jobAddress.addressDialog'));
</script>

<style lang="scss">
.wtg-address-title__edit-link {
    cursor: pointer;
}
</style>
