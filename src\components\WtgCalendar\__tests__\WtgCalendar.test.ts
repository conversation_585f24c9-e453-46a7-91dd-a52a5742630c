import { enableAutoUnmount, mount } from '@vue/test-utils';
import { VCalendar } from 'vuetify/lib/labs/components.mjs';
import WtgCalendar from '..';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgCalendar', () => {
    test('its name is WtgCalendar', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgCalendar');
    });

    test('it renders a VCalendar component', () => {
        const wrapper = mountComponent();
        const Calendar = wrapper.findComponent(VCalendar);
        expect(Calendar.exists()).toBe(true);
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgCalendar, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
