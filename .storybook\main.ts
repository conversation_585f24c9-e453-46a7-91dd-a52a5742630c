import type { StorybookConfig } from '@storybook/vue3-vite';
import { dirname, join } from 'path';
import remarkGfm from 'remark-gfm';

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value) {
    return dirname(require.resolve(join(value, 'package.json')));
}

/** @type { import('@storybook/vue3-vite').StorybookConfig } */
const config: StorybookConfig = {
    stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
    staticDirs: ['../src/storybook/assets'],

    addons: [
        getAbsolutePath('@storybook/addon-links'),
        getAbsolutePath('@storybook/addon-actions'),
        getAbsolutePath('@storybook/addon-viewport'),
        getAbsolutePath('@storybook/addon-controls'),
        getAbsolutePath('@storybook/addon-backgrounds'),
        getAbsolutePath('@storybook/addon-toolbars'),
        getAbsolutePath('@storybook/addon-measure'),
        getAbsolutePath('@storybook/addon-outline'),
        getAbsolutePath('@storybook/addon-designs'),
        getAbsolutePath('@storybook/addon-a11y'),
        'storybook-addon-pseudo-states',
        {
            name: '@storybook/addon-docs',
            options: {
                mdxPluginOptions: {
                    mdxCompileOptions: {
                        remarkPlugins: [remarkGfm],
                    },
                },
            },
        },
        ...(process.env.NODE_ENV === 'production'
            ? [
                  {
                      name: 'storybook-design-token',
                      options: { designTokenGlob: '**/{supply-light-core,supply-desktop-core,supply-elevation}.css' },
                  },
              ]
            : []),
        '@chromatic-com/storybook',
        getAbsolutePath('@storybook/experimental-addon-test'),
    ],

    framework: {
        name: '@storybook/vue3-vite',
        options: {
            docgen: {
                plugin: 'vue-component-meta',
                tsconfig: 'tsconfigDocGen.json',
            },
            builder: {
                viteConfigPath: './vite.config.storybook.mjs',
            },
        },
    },

    docs: {
        defaultName: 'Overview',
    },
};
export default config;
