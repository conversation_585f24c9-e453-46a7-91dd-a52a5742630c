import { WtgFrameworkTask, WtgFrameworkTaskEntityStatusDisplayMode } from '@components/framework/types';
import WtgAppFrameworkTaskDialog from '@components/framework/WtgAppFrameworkTaskDialog/WtgAppFrameworkTaskDialog.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
// import WtgAppFrameworkTabs, { WtgAppFrameworkTab } from '../../WtgAppFrameworkTabs';
import WtgStatus from '@components/WtgStatus';
import WtgUi from '../../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgAppFrameworkTaskDialog', () => {
    let el: HTMLElement;
    let task: WtgFrameworkTask;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        task = new WtgFrameworkTask();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('it has a value property that controls the visibility of the dialog', async () => {
        const wrapper = mountComponent({
            props: {
                task,
                modelValue: true,
            },
        });
        const dialog = wrapper.findComponent({ name: 'wtg-modal' });
        expect(dialog.props('modelValue')).toBe(true);

        await wrapper.setProps({ modelValue: false });
        expect(dialog.props('modelValue')).toBe(false);
    });

    test('it has a (deprecated) value property that controls the visibility of the dialog', async () => {
        const wrapper = mountComponent({
            props: {
                task,
                value: true,
            },
        });
        const dialog = wrapper.findComponent({ name: 'wtg-modal' });
        expect(dialog.props('modelValue')).toBe(true);

        await wrapper.setProps({ value: false });
        expect(dialog.props('modelValue')).toBe(false);
    });

    test('it ensures the dialog is persistent', async () => {
        const wrapper = mountComponent({
            props: {
                task,
            },
        });
        const dialog = wrapper.findComponent({ name: 'wtg-dialog' });
        expect(dialog.props('persistent')).toBe(true);
    });

    test('it sets the fullscreen prop based on the viewport', async () => {
        const wtgUi = new WtgUi({ framework: 'mobile' });
        const wrapper = mountComponent({}, wtgUi);
        const dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.props('fullscreen')).toBe(true);
    });

    test('it ensures the dialog sizes to content', async () => {
        const wrapper = mountComponent({
            props: {
                task,
            },
        });
        const dialog = wrapper.findComponent({ name: 'wtg-modal' });
        expect(dialog.props('width')).toBe('auto');
    });

    test('it sets the max-height of the dialog, it does NOT set a min-height', async () => {
        const wrapper = mountComponent({
            props: {
                task,
                modelValue: true,
            },
        });
        const dialog = wrapper.findComponent({ name: 'wtg-modal' });
        expect(dialog.props('maxHeight')).toBe('90vh');
        expect(dialog.props('minHeight')).toBeUndefined();
    });

    test('it sets width to 90vw and min-height to 90vh when maximized is true', async () => {
        const wrapper = mountComponent({
            props: {
                task,
                modelValue: true,
                maximized: true,
            },
        });
        const dialog = wrapper.findComponent({ name: 'v-dialog' });
        expect(dialog.props('width')).toBe('90vw');
        expect(dialog.props('minHeight')).toBe('90vh');
    });

    describe('when given a title', () => {
        beforeEach(() => {
            task!.title = 'Dialog Title';
        });

        test('it shows it as the modal dialog title', () => {
            const wrapper = mountComponent({
                props: {
                    task,
                    modelValue: true,
                },
            });
            const dialog = wrapper.findComponent({ name: 'wtg-modal' });
            expect(dialog.props('title')).toBe('Dialog Title');
        });
    });

    describe('when rendering in a viewport', () => {
        test('it renders the desktop footer on a desktop', () => {
            wtgUi.breakpoint.smAndUp = true;
            const wrapper = mountComponent({
                props: {
                    task,
                    modelValue: true,
                },
            });
            const footer = wrapper.findComponent({ name: 'DesktopBar' });
            expect(footer.exists()).toBe(true);
        });

        // test('it renders the mobile footer on a mobile', () => {
        //     window.innerWidth = 412;
        //     const wrapper = mountComponent({
        //         props: {
        //             task,
        //             modelValue: true,
        //         },
        //     });
        //     const footer = wrapper.findComponent({ name: 'MobileBar' });
        //     expect(footer.exists()).toBe(true);
        // });
    });

    // describe('when given a task', () => {
    //     beforeEach(() => {
    //         task = {
    //             showFooter: true,
    //             cancelAction: {
    //                 visible: false,
    //                 caption: 'Cancel',
    //                 onInvoke: jest.fn(),
    //             },
    //             saveAction: {
    //                 visible: false,
    //                 caption: 'Save',
    //                 label: 'Save',
    //                 onInvoke: jest.fn(),
    //             },
    //             saveCloseAction: {
    //                 caption: 'Save and close',
    //                 label: 'Close',
    //                 onInvoke: jest.fn(),
    //             },
    //             genericActions: [],
    //             showEDocsAction: {
    //                 visible: true,
    //                 caption: 'eDocs',
    //                 label: 'eDocs',
    //                 onInvoke: jest.fn(),
    //             },
    //             documents: {
    //                 visible: true,
    //                 caption: 'Documents',
    //             },
    //             showLogsAction: {
    //                 visible: true,
    //                 caption: 'eDocs',
    //                 label: 'eDocs',
    //                 onInvoke: jest.fn(),
    //             },
    //             showMessagesAction: {
    //                 visible: true,
    //                 caption: 'eDocs',
    //                 label: 'eDocs',
    //                 onInvoke: jest.fn(),
    //             },
    //             showNotesAction: {
    //                 visible: true,
    //                 caption: 'eDocs',
    //                 label: 'eDocs',
    //                 onInvoke: jest.fn(),
    //             },
    //             showWorkflowActions: {
    //                 visible: true,
    //                 caption: 'eDocs',
    //                 label: 'eDocs',
    //                 menuItems: [],
    //             },
    //         } as any;
    //     });

    //     test('it passes it to the actions', () => {
    //         window.innerWidth = 1920;
    //         const wrapper = mountComponent({
    //             props: {
    //                 task,
    //                 value: true,
    //             },
    //         });
    //         const actionsBar = wrapper.findComponent({ name: 'DesktopBar' });
    //         expect(actionsBar.exists()).toBe(true);
    //     });

    //     test('it renders a task-action component', async () => {
    //         const wrapper = mountComponent({
    //             props: {
    //                 task,
    //                 value: true,
    //             },
    //         });

    //         const taskActions = wrapper.findAllComponents({ name: 'WtgEntityActions' });
    //         expect(taskActions.length).toBe(1);
    //         const eDocsButton = wrapper.findComponent({ name: 'wtg-btn' });
    //         expect(eDocsButton.text()).toBe('eDocs');
    //     });

    //     test('It changes the task actions and notification display order between mobile and large viewports', async () => {
    //         window.innerWidth = 412;
    //         const testNotifications = [
    //             {
    //                 id: '0262c565-c1c4-4c9b-9f2d-b188a20eb459',
    //                 caption: 'Caption 3',
    //                 text: 'Some error text',
    //                 type: WtgFrameworkNotificationType.Error,
    //             },
    //         ] as WtgFrameworkNotification[];
    //         task!.notifications = testNotifications;

    //         const wrapper = mountComponent({
    //             props: {
    //                 task,
    //                 value: true,
    //             },
    //         });

    //         const taskActions = wrapper.findComponent({ name: 'WtgEntityActions' });
    //         expect(taskActions.classes()).toContain('order-last');
    //         expect(taskActions.classes()).toContain('order-sm-0');
    //     });
    // });

    // describe('given the current task actions', () => {
    //     let taskAction = new WtgFrameworkTask();
    //     beforeEach(() => {
    //         taskAction = {
    //             showEDocsAction: { visible: false },
    //             documents: { visible: false },
    //             showLogsAction: { visible: false },
    //             showMessagesAction: { visible: false },
    //             showNotesAction: { visible: false },
    //             showWorkflowActions: { visible: false },
    //             genericActions: [],
    //         } as any;
    //     });

    //     test('it will set showTaskActions to true if any of the actions are visible and render the entity actions component', async () => {
    //         const wrapper = mountComponent({
    //             props: {
    //                 task: taskAction,
    //                 value: true,
    //             },
    //         });
    //         taskAction.showNotesAction.visible = true;
    //         await nextTick();
    //         const entityActions = wrapper.findComponent({ name: 'WtgEntityActions' });
    //         expect(entityActions.exists()).toBe(true);
    //     });

    //     test('it will set showTaskActions to false if any of the actions are not visible and not render the entity actions component', async () => {
    //         const wrapper = mountComponent({
    //             props: {
    //                 task: taskAction,
    //                 value: true,
    //             },
    //         });
    //         taskAction.showNotesAction.visible = false;
    //         await nextTick();
    //         const entityActions = wrapper.findComponent({ name: 'WtgEntityActions' });
    //         expect(entityActions.exists()).toBe(false);
    //     });
    // });

    // test('it renders the correct tabs when WtgAppFrameworkTabs is a child component', async () => {
    //     const wrapper = mountComponent({
    //         slots: {
    //             default: {
    //                 components: { WtgAppFrameworkTabs, WtgAppFrameworkTab },
    //                 template: '<WtgAppFrameworkTabs app><WtgAppFrameworkTab caption="Test tab"/></WtgAppFrameworkTabs>',
    //             },
    //         },
    //         props: {
    //             value: true,
    //         },
    //     });
    //     await Vue.nextTick();
    //     expect(wrapper.findComponent({ name: 'WtgTab' }).html()).toContain('Test tab');
    // });

    describe('given the current task has currentStatus', () => {
        beforeEach(() => {
            task.currentStatus = {
                code: 'TEST',
                label: 'Test Label',
                sentiment: 'info',
                variant: 'fill',
            };

            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Hidden;
            task.showTaskTitle = true;
        });

        test('it will not render the task current status when status label is falsy', () => {
            task.currentStatus!.label = '';
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.exists()).toBe(false);
        });

        test('it will render the task current status by status component', () => {
            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.exists()).toBe(true);
            expect(statusWrapper.vm.$props.label).toBe('Test Label');
            expect(statusWrapper.vm.$props.sentiment).toBe('info');
            expect(statusWrapper.vm.$props.variant).toBe('fill');
        });

        test('it will use info as default sentiment', () => {
            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
            task.currentStatus!.sentiment = '';
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.vm.$props.sentiment).toBe('info');
        });

        test('it will use info as default sentiment for items', () => {
            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
            task.statusItems = [{ code: 'Test', label: 'Test description', sentiment: undefined, variant: 'fill' }];
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.props('items')![0].sentiment).toBe('info');
        });

        test('it will use fill as default variant', () => {
            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
            task.currentStatus!.variant = '';
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.vm.$props.variant).toBe('fill');
        });

        test('it will use fill as default variant for items', () => {
            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
            task.statusItems = [{ code: 'Test', label: 'Test description', sentiment: 'critical', variant: undefined }];
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.props('items')![0].variant).toBe('fill');
        });

        test('it will not render the task current status when currentStatus is undefined', () => {
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            task.currentStatus = undefined;
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.exists()).toBe(false);
        });

        test('it will not render the task current status when status display mode is Hidden', () => {
            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Hidden;
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.exists()).toBe(false);
        });

        test('it will render the task current status as editable when status display mode is Editable', () => {
            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Editable;
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.exists()).toBe(true);
            expect(statusWrapper.vm.$props.editable).toBe(true);
        });

        test('it will render the task current status as ReadOnly when status display mode is ReadOnly', () => {
            task.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
            const wrapper = mountComponent({ props: { task, modelValue: true } });
            const statusWrapper = wrapper.findComponent(WtgStatus);
            expect(statusWrapper.exists()).toBe(true);
            expect(statusWrapper.vm.$props.editable).toBe(false);
        });
    });

    function mountComponent({ props = {}, slots = {} } = {}, localWtgUi?: WtgUi) {
        return mount(WtgAppFrameworkTaskDialog, {
            props,
            slots,
            global: {
                plugins: [localWtgUi ?? wtgUi],
            },
        });
    }
});
