import WtgLabel from '@components/WtgLabel';
import WtgLayoutGrid from '@components/WtgLayoutGrid/WtgLayoutGrid';
import { WtgRow } from '@components/WtgRow';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { defineComponent } from 'vue';
import { VCol } from 'vuetify/components/VGrid';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgLayoutGrid', () => {
    test('it renders a VRow component', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('v-row');
    });

    test('it passes its properties to the WtgRow component', () => {
        const wrapper = mountComponent({
            propsData: {
                noGutters: true,
                fillAvailable: true,
            },
        });
        const row = wrapper.findComponent(WtgRow);
        expect(row.props('noGutters')).toBe(true);
        expect(row.props('fillAvailable')).toBe(true);
    });

    test('it renders a VCol component around each child-component', () => {
        const wrapper = mountComponent({
            slots: {
                default: '<span>One</span><span>Two</span><span>Three</span>',
            },
        });
        const cols = wrapper.findAllComponents(VCol);
        expect(cols.length).toBe(3);
        expect(cols.at(0)?.text()).toBe('One');
        expect(cols.at(1)?.text()).toBe('Two');
        expect(cols.at(2)?.text()).toBe('Three');
    });

    test('it renders a VCol component around each child-component while passing through templates', () => {
        const wrapper = mountComponent({
            slots: {
                default: '<span>One</span><template v-if="true"><span>Two</span><span>Three</span></template>',
            },
        });
        const cols = wrapper.findAllComponents(VCol);
        expect(cols.length).toBe(3);
        expect(cols.at(0)?.text()).toBe('One');
        expect(cols.at(1)?.text()).toBe('Two');
        expect(cols.at(2)?.text()).toBe('Three');
    });

    test('it does not render child components that have been conditionally removed', () => {
        const component = defineComponent({
            components: { WtgLayoutGrid },
            template:
                '<wtg-layout-grid><span>One</span><span>Two</span><span>Three</span><span v-if="false">Four</span></wtg-layout-grid>',
        });
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });

        const cols = wrapper.findAllComponents(VCol);
        expect(cols.length).toBe(3);
        expect(cols.at(0)?.text()).toBe('One');
        expect(cols.at(1)?.text()).toBe('Two');
        expect(cols.at(2)?.text()).toBe('Three');
    });

    test('it does not render child components that are comments', () => {
        const component = defineComponent({
            components: { WtgLayoutGrid },
            template: '<wtg-layout-grid><span>One</span><span>Two</span><span>Three</span><!----></wtg-layout-grid>',
        });
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });

        const cols = wrapper.findAllComponents(VCol);
        expect(cols.length).toBe(3);
        expect(cols.at(0)?.text()).toBe('One');
        expect(cols.at(1)?.text()).toBe('Two');
        expect(cols.at(2)?.text()).toBe('Three');
    });

    test('it does not render child components that are plain text nodes', () => {
        const component = defineComponent({
            components: { WtgLayoutGrid },
            template: `
                <wtg-layout-grid>
                    <span>One</span> 
                    some text 
                    <span>
                        Two
                    </span>
                    <span>
                    Three
                    </span>
                </wtg-layout-grid>`,
        });
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });

        const cols = wrapper.findAllComponents(VCol);
        expect(cols.length).toBe(3);
        expect(cols.at(0)?.text()).toBe('One');
        expect(cols.at(1)?.text()).toBe('Two');
        expect(cols.at(2)?.text()).toBe('Three');
    });

    test('it has a columns property mixed in that allows it to be positioned inside another wtg-layout-grid', () => {
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
    });

    test('it does not wrap the data-wtg-layout-grid-ignore div element inside a column', async () => {
        const wrapper = mountComponent({
            slots: {
                default: '<span>One</span><span>Two</span><span>Three</span><span data-wtg-layout-grid-ignore></span>',
            },
        });
        const cols = wrapper.findAllComponents(VCol);

        expect(cols.length).toBe(3);
        expect(cols.at(0)?.text()).toBe('One');
        expect(cols.at(1)?.text()).toBe('Two');
        expect(cols.at(2)?.text()).toBe('Three');
    });

    test('it wraps a non iterable component correctly inside a column', async () => {
        const component = {
            components: { WtgLayoutGrid, WtgLabel },
            template: `<wtg-layout-grid><wtg-label>One</wtg-label></wtg-layout-grid>`,
        };
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });
        const cols = wrapper.findAllComponents(VCol);

        expect(cols.length).toBe(1);
        expect(cols.at(0)?.text()).toBe('One');
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgLayoutGrid, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
