<template>
    <WtgDialog v-model="model" scrollable width="auto" class="wtg-recent-dialog">
        <div class="wtg-recent-dialog__content">
            <div class="wtg-recent-dialog__content-list">
                <RecentList
                    :items="items"
                    :favorites-caption="favoritesCaption"
                    :recents-caption="recentsCaption"
                    @item-click="onItemClick"
                />
            </div>
            <WtgDivider />
            <div class="wtg-recent-dialog__content-actions">
                <WtgButton @click="onCloseClick">
                    {{ captionClose }}
                </WtgButton>
            </div>
        </div>
    </WtgDialog>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgDialog from '@components/WtgDialog';
import WtgDivider from '@components/WtgDivider';
import RecentList from '@components/framework/WtgNavigationRecentMenu/RecentList.vue';
import { PropType } from 'vue';
import { Items } from '../types';

const emit = defineEmits<{
    'item-click': [item: any];
}>();

defineProps({
    items: {
        type: Object as PropType<Items>,
        default: () => ({
            favorites: [],
            recents: [],
        }),
    },
    favoritesCaption: {
        type: String,
        default: 'Favorites',
    },
    recentsCaption: {
        type: String,
        default: 'Recent',
    },
    captionClose: {
        type: String,
        default: 'Close',
    },
});

const model = defineModel<boolean>({ default: false });

const onCloseClick = (): void => {
    model.value = false;
};

const onItemClick = (item: any): void => {
    onCloseClick();
    emit('item-click', item);
};
</script>
<style lang="scss">
.wtg-recent-dialog {
    .wtg-recent-dialog__content {
        background: var(--s-neutral-bg-default);
        border-radius: var(--s-radius-m);
        color: var(--s-neutral-txt-default);
        overflow-y: auto;
        display: flex;
        flex-direction: column;

        .wtg-recent-dialog__content-list {
            padding: var(--s-padding-xl);
            overflow-y: auto;
        }
    }

    .wtg-recent-dialog__content-actions {
        display: flex;
        justify-content: end;
        padding: var(--s-padding-m);
    }
}
</style>
