<template>
    <VLazy v-bind="props">
        <slot />
    </VLazy>
</template>

<script setup lang="ts">
import { VLazy } from 'vuetify/components/VLazy';

const props = defineProps({
    /**
     * Height of the lazy container (in pixels or CSS units).
     */
    height: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Maximum height of the lazy container (in pixels or CSS units).
     */
    maxHeight: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Maximum width of the lazy container (in pixels or CSS units).
     */
    maxWidth: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Controls the visibility of the content. Use v-model to bind this prop.
     */
    modelValue: {
        type: Boolean,
        default: false,
    },
    /**
     * IntersectionObserver options for controlling when the content is loaded.
     * See: https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API
     */
    options: {
        type: Object,
        default: () => ({
            root: undefined,
            rootMargin: undefined,
            threshold: undefined,
        }),
    },
    /**
     * HTML tag to use for the container element.
     */
    tag: {
        type: String,
        default: 'div',
    },
    /**
     * Transition effect to use when showing or hiding the content.
     */
    transition: {
        type: String,
        default: 'fade-transition',
    },
    /**
     * Width of the lazy container (in pixels or CSS units).
     */
    width: {
        type: [Number, String],
        default: undefined,
    },
});
</script>
