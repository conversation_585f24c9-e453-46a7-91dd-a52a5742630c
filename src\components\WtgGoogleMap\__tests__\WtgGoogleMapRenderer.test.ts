import { WtgGoogleMapRenderer } from '../utils/WtgGoogleMapRenderer';
import { initialize, Marker } from '@googlemaps/jest-mocks';
import ColorVariant from '../../../theme/colors/ColorVariant';
import { Cluster, ClusterStats, DefaultRenderer } from '@googlemaps/markerclusterer';

const colorOptions = {
    primary: '#000000',
    secondary: '#000001',
    accent: '#000002',
    critical: '#000003',
    info: '#000004',
    success: '#000005',
    warning: '#000006',
    controls: {
        icon: {
            color: '#00000A',
        },
        panel: {
            caption: '#00000B',
        },
    },
};

// parse svg and obtain attribute from svg element
function parseFromStringToAttributeValue(url: string, attribute: string): any {
    const base64EncodedSvg = url.substring('data:image/svg+xml;base64,'.length);

    // Decode base64 string to SVG string
    const decodedSvgString = window.atob(base64EncodedSvg);

    // Parse SVG string
    const parser = new DOMParser();
    const svgDoc = parser.parseFromString(decodedSvgString, 'image/svg+xml');
    const svgElement = svgDoc.querySelector('svg');

    return svgElement ? svgElement.getAttribute(attribute) || '' : undefined;
}

describe('WtgGoogleMapRenderer', () => {
    describe('render', () => {
        beforeEach(() => {
            initialize();
        });

        test('should render marker with DefaultRenderer from google if no theme primary color is set', () => {
            const renderer = new WtgGoogleMapRenderer();

            const map = new (window as any).google.maps.Map(document.createElement('div'));
            const cluster = new Cluster({
                markers: [],
                position: { lat: 0, lng: 0 },
            });
            const clusterStats = new ClusterStats([], []);

            const expectedMarker = new Marker();
            jest.spyOn(DefaultRenderer.prototype, 'render').mockReturnValue(expectedMarker);
            const result = renderer.render(cluster, clusterStats, map);

            expect(result).toBe(expectedMarker);
        });

        test('should render marker with DefaultRenderer from google if the theme primary color is empty', () => {
            const renderer = new WtgGoogleMapRenderer(new ColorVariant({ primary: '' }));

            const map = new (window as any).google.maps.Map(document.createElement('div'));
            const cluster = new Cluster({
                markers: [],
                position: { lat: 0, lng: 0 },
            });
            const clusterStats = new ClusterStats([], []);

            const expectedMarker = new Marker();
            jest.spyOn(DefaultRenderer.prototype, 'render').mockReturnValue(expectedMarker);
            const result = renderer.render(cluster, clusterStats, map);

            expect(result).toBe(expectedMarker);
        });

        test('should render marker with position of custom cluster', () => {
            const renderer = new WtgGoogleMapRenderer(new ColorVariant(colorOptions));

            const map = new (window as any).google.maps.Map(document.createElement('div'));
            const clusterPosition = { lat: 10, lng: 10 };
            const cluster = new Cluster({
                markers: [],
                position: clusterPosition,
            });
            const clusterStats = new ClusterStats([], []);

            const renderedMarker: any = renderer.render(cluster, clusterStats, map);

            expect(renderedMarker.setPosition).toHaveBeenCalledWith(expect.any((window as any).google.maps.LatLng));
        });

        test('should render marker with icon color if theme primary color is set', () => {
            const renderer = new WtgGoogleMapRenderer(new ColorVariant(colorOptions));

            const map = new (window as any).google.maps.Map(document.createElement('div'));
            const clusterPosition = { lat: 10, lng: 10 };
            const cluster = new Cluster({
                markers: [],
                position: clusterPosition,
            });
            const clusterStats = new ClusterStats([], []);

            const renderedMarker: any = renderer.render(cluster, clusterStats, map);
            const setIconArguments = renderedMarker.setIcon.mock.calls[0][0];
            const expectedFillColor = parseFromStringToAttributeValue(setIconArguments.url, 'fill');

            expect(expectedFillColor).toBe(colorOptions.primary);
            expect(setIconArguments.scaledSize).toBeInstanceOf((window as any).google.maps.Size);
        });

        test('should render marker with ZIndex of custom cluster', () => {
            const renderer = new WtgGoogleMapRenderer(new ColorVariant(colorOptions));

            const map = new (window as any).google.maps.Map(document.createElement('div'));
            const clusterPosition = { lat: 10, lng: 10 };
            const cluster = new Cluster({
                markers: [],
                position: clusterPosition,
            });
            const clusterStats = new ClusterStats([], []);

            const renderedMarker: any = renderer.render(cluster, clusterStats, map);

            expect(renderedMarker.setZIndex).toHaveBeenCalledWith(expect.any(Number));
        });
    });
});
