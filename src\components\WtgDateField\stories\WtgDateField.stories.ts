import WtgCol from '@components/WtgCol';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgRow from '@components/WtgRow';
import { inputArgTypes } from '@composables/input';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import WtgDateField from '..';

type Story = StoryObj<typeof WtgDateField>;
const meta: Meta<typeof WtgDateField> = {
    title: 'Components/Date Field',
    component: WtgDateField,
    parameters: {
        docs: {
            description: {
                component:
                    'DateField is a specifically designed to allow users to enter or select a date from a dropdown.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=383-43226',
        },
        layout: 'centered',
        controls: {
            sort: 'alpha',
        },
    },
    argTypes: {
        ...inputArgTypes,
    },
    render: (args) => ({
        components: { WtgDateField },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `<WtgDateField
                        v-bind="args"
                        @update:model-value="updateModel"
                        @change="changeAction"
                        @input="inputAction"
                        @focus="focusAction"
                        @blur="blurAction">
                    </WtgDateField>`,
    }),
    decorators: [
        () => ({
            template: `
                <div style="max-width:300px">
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        label: 'Date of birth',
        modelValue: '1974-02-07',
    },
};

export const Info: Story = {
    args: {
        label: 'Date of birth',
        modelValue: '1974-02-07',
        info: 'Some helpful information',
    },
};

export const Sentiments: Story = {
    args: {
        modelValue: '2024-01-08',
    },
    render: (args) => ({
        components: { WtgDateField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
        <WtgRow>
        <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column col-md-3">
            <WtgLayoutGrid>
                <WtgDateField v-bind="args" 
                    label="Default"
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateField>
                <WtgDateField v-bind="args" 
                    label="Success" 
                    sentiment="success" 
                    messages="Sample success message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateField>
                <WtgDateField v-bind="args" 
                    label="Warning" 
                    sentiment="warning" 
                    messages="Sample warning message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateField>
                <WtgDateField v-bind="args" 
                    label="Error" 
                    sentiment="critical" 
                    messages="Sample error message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateField>
                <WtgDateField v-bind="args" 
                    label="Info" 
                    sentiment="info" 
                    messages="Sample info message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateField>
            </WtgLayoutGrid>
        </WtgCol>
    </WtgRow>`,
    }),
};

export const ReadOnly: Story = {
    args: {
        label: 'Read only',
        readonly: true,
        modelValue: '2024-01-08',
    },
    render: (args) => ({
        components: { WtgDateField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
                <WtgDateField v-bind="args"   
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateField>`,
    }),
};

export const Disabled: Story = {
    args: {
        label: 'Disabled',
        modelValue: '2024-01-08',
        disabled: true,
    },
    render: (args) => ({
        components: { WtgDateField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
                <WtgDateField v-bind="args"   
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateField>`,
    }),
};

export const Native: Story = {
    args: {
        label: 'Date of birth',
        modelValue: '1974-02-07',
        native: true,
    },
};
