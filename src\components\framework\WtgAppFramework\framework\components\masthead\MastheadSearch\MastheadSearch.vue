<template>
    <WtgDialog
        :model-value="overlay"
        transition="slide-y-transition"
        retain-focus
        content-class="search-overlay-content"
        @update:model-value="$emit('update:overlay', $event)"
    >
        <WtgCard :aria-label="ariaLabels.search" @click-outside="toggleOverlay">
            <WtgTextField
                ref="search"
                v-model="searchValue"
                append-icon=""
                class="search wtg-content-background rounded-lg px-4 pt-4 pb-2"
                clearable
                outlined
                :placeholder="formatCaption('search.placeholder')"
                :loading="searching"
                role="searchbox"
                @input="onSearchInputUpdate"
                @keydown="onInputKeyDown"
            >
                <template #leading>
                    <WtgBox layout="flex" height="24">
                        <WtgBox layout="flex" class="mr-1" width="24">
                            <WtgBox layout="flex" flex-justify="justify-center" flex-align="align-center" width="24">
                                <WtgProgressCircular
                                    v-if="searching"
                                    color="accent"
                                    indeterminate
                                    rotate="0"
                                    width="2"
                                    size="18"
                                />
                            </WtgBox>

                            <WtgIcon v-if="!searching" class="ml-1 mr-2" color="accent">s-icon-search</WtgIcon>
                        </WtgBox>

                        <WtgTag
                            v-if="searchHandler.selectedShorthands.length === 1"
                            class="mx-1"
                            :color="getEntityFromShorthand(searchHandler.selectedShorthands[0]).color"
                            dismissible
                            @click="onRemoveFilter"
                        >
                            {{ getEntityFromShorthand(searchHandler.selectedShorthands[0]).entityName }}
                        </WtgTag>
                        <WtgTag
                            v-if="searchHandler.selectedShorthands.length > 1"
                            leading-icon="s-icon-info-circle"
                            class="mx-1"
                            :tooltip="multipleChipTooltip"
                            dismissible
                            @click="onRemoveFilter"
                        >
                            {{ formatCaption('search.multipleChip') }}
                        </WtgTag>
                    </WtgBox>
                </template>
            </WtgTextField>

            <WtgBox>
                <WtgBox ref="content" class="menu">
                    <WtgBox
                        v-if="showNoResults"
                        layout="flex"
                        flex-justify="justify-center"
                        flex-align="align-center"
                        flex-direction="flex-column"
                        height="300px"
                        role="alert"
                    >
                        <WtgIcon class="pb-2" size="xl" color="accent"> $search </WtgIcon>

                        {{ formatCaption('search.noResults') }}
                    </WtgBox>

                    <WtgSheet>
                        <WtgBox v-if="suggestedFilters.length > 0">
                            <WtgDivider />

                            <WtgBox class="px-4 pb-2">
                                <WtgBox class="mt-1 mb-1">
                                    <WtgLabel typography="label" font-weight="medium"> Suggested Filters </WtgLabel>
                                </WtgBox>

                                <SearchItem
                                    v-for="(item, index) in suggestedFilters"
                                    :key="item.shorthand"
                                    :item="item"
                                    :index="index"
                                    :action-caption="
                                        searchHandler.selectedShorthands.includes(item.shorthand)
                                            ? formatCaption('search.remove')
                                            : formatCaption('search.add')
                                    "
                                    @click="onClickResult"
                                >
                                    <WtgTag class="ml-0 mr-2" aria-hidden="true">
                                        <WtgLabel text-color="secondary" font-weight="bold">
                                            {{ item.shorthand }}:
                                        </WtgLabel>
                                    </WtgTag>

                                    <WtgLabel color="secondary-text" typography="label">
                                        {{ restrictionCaption(item.shorthand) }}
                                    </WtgLabel>
                                </SearchItem>
                            </WtgBox>
                        </WtgBox>

                        <WtgBox v-if="!searching && !searchHandler.selectedShorthands.length && menuItems.length > 0">
                            <WtgDivider />

                            <WtgBox class="px-4 pb-2">
                                <WtgBox class="mt-1 mb-1">
                                    <WtgLabel typography="label" font-weight="medium"> Jump To </WtgLabel>
                                </WtgBox>

                                <SearchItem
                                    v-for="(item, index) in menuItems"
                                    :key="item.caption + item.icon"
                                    :item="item"
                                    :index="index"
                                    :action-caption="formatCaption('search.jump')"
                                    @click="onClickResult"
                                >
                                    <WtgLabel>
                                        <WtgIcon class="mr-1" size="s">
                                            {{ item.icon }}
                                        </WtgIcon>

                                        {{ item.caption }}
                                    </WtgLabel>
                                </SearchItem>
                            </WtgBox>
                        </WtgBox>

                        <WtgBox v-if="!searching && searchResults.length > 0">
                            <WtgDivider />

                            <WtgBox class="px-4 pb-2">
                                <WtgBox class="mt-1 mb-1">
                                    <WtgLabel typography="label" font-weight="medium"> Search Results </WtgLabel>
                                </WtgBox>

                                <SearchItem
                                    v-for="(item, index) in searchResults"
                                    :key="item.PK"
                                    :item="item"
                                    :index="index"
                                    :action-caption="formatCaption('search.open')"
                                    @click="onClickResult"
                                >
                                    <WtgBox>
                                        <WtgBox layout="flex" flex-align="align-center">
                                            <WtgTag :color="item.color">
                                                {{ item.entityName }}
                                            </WtgTag>
                                        </WtgBox>

                                        <WtgBox class="mt-1">
                                            <WtgLabel typography="title-large" font-weight="black">
                                                {{ item.jobReference }}
                                                <WtgIcon
                                                    size="s"
                                                    :tooltip="`${formatCaption('search.matchedOn')} ${
                                                        item.matchedColumn
                                                    }`"
                                                >
                                                    s-icon-info-circle
                                                </WtgIcon>
                                            </WtgLabel>
                                        </WtgBox>

                                        <WtgBox
                                            class="mt-1 mb-1"
                                            layout="flex"
                                            flex-direction="flex-row"
                                            flex-wrap="flex-wrap"
                                        >
                                            <WtgBox
                                                v-for="keyField in item.keyFields"
                                                :key="keyField.caption"
                                                class="mr-8 mb-1"
                                                layout="flex"
                                                flex-direction="flex-column"
                                            >
                                                <WtgLabel
                                                    font-weight="medium"
                                                    typography="label"
                                                    color="secondary-text"
                                                >
                                                    {{ keyField.caption }}
                                                </WtgLabel>
                                                <WtgLabel>
                                                    {{ keyField.value }}
                                                </WtgLabel>
                                            </WtgBox>
                                        </WtgBox>
                                    </WtgBox>
                                </SearchItem>
                            </WtgBox>
                        </WtgBox>
                    </WtgSheet>
                </WtgBox>
            </WtgBox>

            <WtgDivider v-if="!searching" />

            <WtgSheet class="px-4 py-3">
                <WtgButton variant="ghost" leading-icon="s-icon-help" size="s" @click="toggleSearchHelp">
                    {{ formatCaption('search.searchableFields') }}
                </WtgButton>

                <WtgModal v-model="searchHelp" :title="formatCaption('search.searchableFields')" width="800">
                    <WtgBox
                        v-for="[shorthand] in searchProvider?.shorthandToEntityType"
                        :key="shorthand"
                        layout="flex"
                        style="gap: 5px"
                        flex-wrap="flex-wrap"
                        class="py-2"
                    >
                        <WtgTag :color="getEntityFromShorthand(shorthand).color">
                            <WtgLabel> {{ getEntityFromShorthand(shorthand).entityName }}: </WtgLabel>
                        </WtgTag>

                        <WtgTag
                            v-for="searchableColumn in getEntityFromShorthand(shorthand).searchableColumns"
                            :key="searchableColumn"
                            sentiment="default"
                        >
                            {{ searchProvider?.getCaptionForProperty(searchableColumn) }}
                        </WtgTag>
                    </WtgBox>

                    <template #actions>
                        <WtgSpacer />
                        <WtgButton color="primary" text-color="white" @click="toggleSearchHelp">
                            {{ formatCaption('search.close') }}
                        </WtgButton>
                    </template>
                </WtgModal>
            </WtgSheet>
        </WtgCard>
    </WtgDialog>
</template>

<script lang="ts">
import {
    WtgFrameworkAriaLabels,
    WtgFrameworkMenuItem,
    WtgFrameworkMenuItemType,
    WtgFrameworkSearchEntity,
    WtgFrameworkSearchHandler,
    WtgFrameworkSearchProvider,
    WtgFrameworkSearchResultEntity,
    WtgFrameworkSearchResultMenuItem,
    WtgFrameworkSearchResultRecord,
    WtgFrameworkSearchResultSuggestedFilter,
} from '@components/framework/types';
import WtgBox from '@components/WtgBox';
import WtgButton from '@components/WtgButton';
import WtgCard from '@components/WtgCard';
import WtgDialog from '@components/WtgDialog';
import WtgDivider from '@components/WtgDivider';
import WtgIcon from '@components/WtgIcon';
import WtgLabel from '@components/WtgLabel';
import WtgModal from '@components/WtgModal';
import WtgProgressCircular from '@components/WtgProgressCircular';
import WtgSheet from '@components/WtgSheet';
import WtgSpacer from '@components/WtgSpacer';
import WtgTag from '@components/WtgTag';
import WtgTextField from '@components/WtgTextField';
import { useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { useLocale } from '@composables/locale';
import { computed, defineComponent, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import SearchItem from './SearchItem.vue';
import { searchItemIsEntity, searchItemIsMenuItem, searchItemIsSuggestedFilter, TileDirection } from './types';

export default defineComponent({
    name: 'MastheadSearch',
    components: {
        SearchItem,
        WtgBox,
        WtgButton,
        WtgCard,
        WtgDialog,
        WtgDivider,
        WtgIcon,
        WtgLabel,
        WtgModal,
        WtgProgressCircular,
        WtgSheet,
        WtgSpacer,
        WtgTag,
        WtgTextField,
    },
    props: {
        overlay: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['update:overlay'],
    setup(props, { emit }) {
        const { isMobile } = useFramework();
        const { formatCaption } = useLocale();
        const application = useApplication();

        const searchProvider = computed((): WtgFrameworkSearchProvider | undefined => {
            return application.searchProvider;
        });

        const content = ref(null);
        const currentItem = ref(-1);
        const isNavigating = ref(false);
        const search = ref();
        const searching = ref(false);
        const searchHelp = ref(false);
        const searchResults = ref([] as WtgFrameworkSearchResultEntity[]);
        const searchValue = ref('');
        const tiles = ref([] as HTMLElement[]);
        const timeout = ref(null as null | number);
        const availableShorthands = ref(
            Array.from(searchProvider.value?.shorthandToEntityType?.keys?.() ?? []).map((shorthand) => {
                return { shorthand, isActive: false };
            })
        );

        const buttonVariant = computed((): 'ghost' | 'fill' | undefined => {
            return isMobile.value ? 'ghost' : undefined;
        });

        const ariaLabels = computed((): WtgFrameworkAriaLabels => {
            return application.ariaLabels;
        });

        const searchHandler = computed((): WtgFrameworkSearchHandler => {
            return application.searchHandler;
        });

        const navigationMenu = computed((): WtgFrameworkMenuItem[] => {
            return application.menu;
        });

        const flattenedMenuItems = ref(recursivelyFlattenMenu(navigationMenu.value));

        watch(
            () => searchHandler.value.selectedShorthands,
            (selectedShorthands) => {
                const shorthandColonKeysToMatch = selectedShorthands.map((shorthand) => shorthand + ':').join('|');
                searchValue.value = searchValue.value.replace(new RegExp(shorthandColonKeysToMatch, 'gi'), '');
            }
        );

        watch(
            () => props.overlay,
            (newVal) => {
                if (newVal) {
                    nextTick(() => {
                        window.requestAnimationFrame(() => {
                            findComboInput()?.focus();
                            getTiles();
                        });
                    });
                }
            }
        );

        watch(currentItem, (nextVal, oldVal) => {
            if (nextVal in tiles.value) {
                const tile = tiles.value[nextVal];
                tile.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                setActiveItem(nextVal, true);
            }

            if (oldVal in tiles.value) {
                setActiveItem(oldVal, false);
            }
        });

        watch(tiles, (newVal, oldVal) => {
            if (newVal.length !== oldVal.length) {
                setAllToInactive();
                currentItem.value = -1;
            }
        });

        const menuItems = computed((): WtgFrameworkSearchResultMenuItem[] => {
            return searchHandler.value.selectedShorthands.length || searchValue.value === ''
                ? []
                : flattenedMenuItems.value.filter((menuItem) =>
                      menuItem.caption.toLowerCase().includes(searchValue.value.toLowerCase())
                  );
        });

        const suggestedFilters = computed((): WtgFrameworkSearchResultSuggestedFilter[] => {
            return searchHandler.value.selectedShorthands.length && searchValue.value !== ''
                ? []
                : availableShorthands.value.filter(({ shorthand }) =>
                      shorthand.toLowerCase().includes(searchValue.value.toLowerCase())
                  );
        });

        const searchResultsTotal = computed((): number => {
            return menuItems.value.length + suggestedFilters.value.length + searchResults.value.length;
        });

        watch(searchResultsTotal, () => {
            nextTick(() => {
                getTiles();
            });
        });

        const showNoResults = computed((): boolean => {
            return !searching.value && searchResultsTotal.value === 0;
        });

        onMounted(() => {
            document.addEventListener('keyup', onKeyUp);
            document.addEventListener('keydown', onKeyDown);
        });

        onBeforeUnmount(() => {
            document.removeEventListener('keyup', onKeyUp);
            document.removeEventListener('keydown', onKeyDown);
        });

        function setActiveItem(index: number, valueToSet: boolean): void {
            const itemsToCheck = [suggestedFilters.value, menuItems.value, searchResults.value];

            for (const items of itemsToCheck) {
                if (index < 0 || index >= items.length) {
                    index -= items.length;
                } else {
                    items[index].isActive = valueToSet;
                    return;
                }
            }
        }

        function setAllToInactive(): void {
            const itemsToCheck = [suggestedFilters.value, menuItems.value, searchResults.value];

            for (const items of itemsToCheck) {
                for (const item of items) {
                    item.isActive = false;
                }
            }
        }

        function findComboInput(): HTMLInputElement | undefined {
            let result;
            const combo = search.value;
            if (combo) {
                result = combo.$el.getElementsByTagName('input')[0];
            }
            return result;
        }

        function getTiles(): void {
            if (!content.value) return;
            tiles.value = Array.from((content.value as any).$el.querySelectorAll('.search-item, .suggested-filter'));
        }

        function onKeyUp(event: KeyboardEvent): void {
            if (!props.overlay && event.ctrlKey && event.key === '/') {
                emit('update:overlay', true);
            }
        }

        function onKeyDown(event: KeyboardEvent): void {
            if (props.overlay && (event.key === 'ArrowUp' || event.key === 'ArrowDown' || event.key === 'Enter')) {
                getTiles();

                if (event.key === 'ArrowUp') {
                    moveTile(TileDirection.Previous);
                } else if (event.key === 'ArrowDown') {
                    moveTile(TileDirection.Next);
                } else if (event.key === 'Enter' && currentItem.value !== -1) {
                    tiles.value[currentItem.value]?.click();
                }

                event.preventDefault();
            }
        }

        function moveTile(direction: TileDirection): void {
            const increment = direction === TileDirection.Next ? 1 : -1;
            const tile = tiles.value[currentItem.value + increment];

            if (!tile) {
                if (!tiles.value.length) return;

                currentItem.value = direction === TileDirection.Next ? -1 : tiles.value.length;
                moveTile(direction);

                return;
            }

            currentItem.value += increment;
            if (tile.tabIndex === -1) moveTile(direction);
        }

        function recursivelyFlattenMenu(
            menuItems: WtgFrameworkSearchResultMenuItem[],
            parentOverrides = { caption: '', icon: '' }
        ): WtgFrameworkSearchResultMenuItem[] {
            return menuItems.reduce<WtgFrameworkSearchResultMenuItem[]>((acc, menuItem) => {
                const { caption, icon } = parentOverrides;

                const overrides = {
                    caption: caption ? `${caption} > ${menuItem.caption}` : menuItem.caption,
                    icon: menuItem.icon || icon,
                    isActive: false,
                };

                return menuItem.action === WtgFrameworkMenuItemType.Menu && menuItem.items
                    ? [...acc, ...recursivelyFlattenMenu(menuItem.items, overrides)]
                    : [...acc, { ...menuItem, ...overrides }];
            }, []);
        }

        const getEntityFromShorthand = (shorthand: string): WtgFrameworkSearchEntity => {
            const entities = searchProvider.value?.entities.find(
                (config: WtgFrameworkSearchEntity) => config.shorthand === shorthand
            );

            if (!entities) {
                throw new Error(`Unable to find entity from shorthand "${shorthand}".`);
            }

            return entities;
        };

        const multipleChipTooltip = computed((): string => {
            return Array.from(searchProvider.value?.shorthandToEntityType?.keys?.() ?? [])
                .filter((shorthand) => searchHandler.value.selectedShorthands.includes(shorthand))
                .map((shorthand) => getEntityFromShorthand(shorthand).entityName)
                .join(', ');
        });

        const restrictionCaption = (shorthand: string): string => {
            if (searchHandler.value.selectedShorthands.includes(shorthand)) {
                return `${formatCaption('search.remove')} ${
                    getEntityFromShorthand(shorthand).entityName
                } ${formatCaption('search.removeSearchRestriction')}`;
            } else {
                return `${formatCaption('search.add')} ${getEntityFromShorthand(shorthand).entityName} ${formatCaption(
                    'search.addSearchRestriction'
                )}`;
            }
        };

        const onSearchInputUpdate = (searchText: string | undefined): void => {
            searchValue.value = searchText || '';
            if (timeout.value) {
                window.clearTimeout(timeout.value);
            }
            if (searchValue.value.length > 2) {
                searching.value = true;
            }
            searchResults.value = [];
            timeout.value = window.setTimeout(() => {
                searchHandler.value
                    .getItemsAsync(searchValue.value)
                    .then((response: WtgFrameworkSearchResultEntity[]) => {
                        if (response) {
                            searchResults.value = response;
                            searching.value = false;
                        }
                    });
            }, 500);
        };

        const onSuggestedFilterClick = (shorthand: string): void => {
            if (!searchHandler.value.selectedShorthands.includes(shorthand)) {
                searchHandler.value.selectedShorthands.push(shorthand);
            } else {
                searchHandler.value.selectedShorthands = searchHandler.value.selectedShorthands.filter(
                    (s) => s.toUpperCase() !== shorthand.toUpperCase()
                );
            }

            findComboInput()?.focus();

            if (searchValue.value !== '') {
                onSearchInputUpdate(searchValue.value);
            }
        };

        const onClickResult = (item: WtgFrameworkSearchResultRecord): void => {
            if (searchItemIsEntity(item)) {
                emit('update:overlay', false);
                searchProvider.value?.onSearchItemClicked(item as WtgFrameworkSearchResultEntity);
            } else if (searchItemIsMenuItem(item)) {
                const menuItem = item as WtgFrameworkSearchResultMenuItem;
                window.location.href = menuItem.href ?? '';
                emit('update:overlay', false);
            } else if (searchItemIsSuggestedFilter(item)) {
                onSuggestedFilterClick((item as WtgFrameworkSearchResultSuggestedFilter).shorthand);
            }
        };

        const onInputKeyDown = (event: KeyboardEvent): void => {
            if (
                searchValue.value === '' &&
                event.key === 'Backspace' &&
                searchHandler.value.selectedShorthands.length
            ) {
                searchHandler.value.selectedShorthands = [];
            }
        };

        const onRemoveFilter = (): void => {
            searchHandler.value.selectedShorthands = [];

            if (searchValue.value !== '') {
                onSearchInputUpdate(searchValue.value);
            }
        };

        const toggleOverlay = (): void => {
            emit('update:overlay', !props.overlay);
        };
        const toggleSearchHelp = (): void => {
            searchHelp.value = !searchHelp.value;
        };

        return {
            ariaLabels,
            buttonVariant,
            content,
            currentItem,
            formatCaption,
            getEntityFromShorthand,
            multipleChipTooltip,
            isNavigating,
            menuItems,
            navigationMenu,
            restrictionCaption,
            onClickResult,
            onInputKeyDown,
            onRemoveFilter,
            onSearchInputUpdate,
            onSuggestedFilterClick,
            search,
            searchHelp,
            searching,
            searchHandler,
            searchProvider,
            searchResults,
            searchValue,
            showNoResults,
            suggestedFilters,
            tiles,
            timeout,
            toggleOverlay,
            toggleSearchHelp,
        };
    },
});
</script>
<style lang="scss" scoped>
.search-entry {
    width: 300px;
    border-style: solid;
    border-width: thin;
}
.search-entry:hover,
.suggested-filter:hover {
    cursor: pointer;
}
.suggested-filter .v-chip,
.search-entry .v-chip {
    pointer-events: none;
    user-select: none;
}
.search :deep(.v-input__prepend-inner) {
    margin: 0px !important;
    align-self: center;
}
.menu {
    max-height: 60vh;
    overflow-x: hidden;
}
</style>
<style lang="scss">
.v-overlay__content.search-overlay-content {
    align-self: flex-start;
    margin: 0;
    width: 75%;
    & > .v-card {
        border-radius: 14px;
    }
}
</style>
