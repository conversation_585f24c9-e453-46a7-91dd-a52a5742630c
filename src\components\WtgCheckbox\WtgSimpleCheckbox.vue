<template>
    <div :class="computedClass">
        <div
            v-floating-vue-tooltip="!label ? tooltipDirective : undefined"
            :class="computedSelectionControlClass"
            @mouseenter="isHovered = true"
            @mouseleave="isHovered = false"
        >
            <input
                :id="computedId"
                :aria-label="ariaLabel"
                :aria-labelledby="ariaLabelledby"
                :aria-checked="isChecked ? 'true' : 'false'"
                type="checkbox"
                :disabled="disabled || restricted"
                :checked="isChecked"
                @click.prevent="onClick"
            />
            <WtgIcon class="wtg-checkbox__icon">{{ computedIcon }}</WtgIcon>
            <div v-if="readonly || disabled || !isChecked" class="wtg-checkbox__selection-control-background" />
        </div>
        <label
            v-if="label"
            v-floating-vue-tooltip="tooltipDirective"
            :for="computedId"
            class="wtg-checkbox__label"
            @mouseenter="isHovered = true"
            @mouseleave="isHovered = false"
        >
            {{ label }}
        </label>
    </div>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { AlertLevel, ValidationState } from '@composables/notifications';
import { makeRestrictedProps } from '@composables/restricted';
import { TooltipSentiment, convertContent, makeTooltipProps, useTooltip } from '@composables/tooltip';
import { PropType, computed, getCurrentInstance, ref } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * The aria-label attribute for the checkbox input.
     */
    ariaLabel: {
        type: String,
        default: undefined,
    },
    /**
     * The aria-labelledby attribute for the checkbox input.
     */
    ariaLabelledby: {
        type: String,
        default: undefined,
    },
    /**
     * If true, the checkbox is disabled and cannot be interacted with.
     */
    disabled: {
        type: Boolean,
        default: false,
    },
    /**
     * The unique id for the checkbox. If not provided, a unique id is generated.
     */
    id: {
        type: String,
        default: undefined,
    },
    /**
     * The label text displayed next to the checkbox.
     */
    label: {
        type: String,
        default: '',
    },
    /**
     * Validation or help messages to display below the checkbox. Can be a string or an array of strings.
     */
    messages: {
        type: [Array, String] as PropType<string | string[]>,
        default: (): string[] => [],
    },
    /**
     * The value of the checkbox. Can be a boolean or an array for group selection.
     */
    modelValue: {
        type: [Boolean, Array] as PropType<boolean | any[]>,
        default: undefined,
    },
    /**
     * If true, the checkbox is read-only and cannot be changed by the user.
     */
    readonly: {
        type: Boolean,
        default: false,
    },
    /**
     * The sentiment of the checkbox, affecting color and icon. Can be 'critical', 'warning', or 'success'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'warning' | 'success'>,
        default: undefined,
    },
    /**
     * The validation state object, which may include alert level and validation messages.
     */
    validationState: {
        type: Object as PropType<Readonly<ValidationState>>,
        default: undefined,
    },
    ...makeLayoutGridColumnProps(),
    ...makeTooltipProps(),
    ...makeRestrictedProps(),

    /**
     * @deprecated Use modelValue instead.
     * The value of the checkbox (legacy prop).
     */
    inputValue: {
        type: [Boolean, Array],
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    change: [value: any];
    'update:modelValue': [value: any];
}>();

//
// State
//
const isHovered = ref(false);

//
// Composables
//
const instance = getCurrentInstance();
const { createTooltipDirective, tooltipDirective: customTooltipDirective } = useTooltip(props);

useLayoutGridColumn(props);

//
// Computed
//
const alertLevel = computed(() => {
    return props.validationState?.alertLevel ?? AlertLevel.None;
});

const isChecked = computed(() => props.modelValue ?? props.inputValue);

const computedSentiment = computed(() => {
    if (props.disabled) {
        return '';
    } else if (props.sentiment === 'critical' || alertLevel.value === AlertLevel.Error) {
        return 'critical';
    } else if (
        props.sentiment === 'warning' ||
        alertLevel.value === AlertLevel.Warning ||
        alertLevel.value === AlertLevel.MessageError
    ) {
        return 'warning';
    } else if (props.sentiment === 'success') {
        return 'success';
    } else {
        return '';
    }
});

const computedClass = computed(() => ({
    'wtg-checkbox': true,
    'wtg-checkbox--disabled': props.disabled,
    'wtg-checkbox--readonly': props.readonly,
    'wtg-checkbox--success': computedSentiment.value === 'success',
    'wtg-checkbox--critical': computedSentiment.value === 'critical',
    'wtg-checkbox--warning': computedSentiment.value === 'warning',
}));

const computedSelectionControlClass = computed(() => ({
    'wtg-checkbox__selection-control': true,
    'wtg-checkbox--default': !isChecked.value,
    'wtg-checkbox--selected': isChecked.value,
}));

const computedId = computed(() => props.id || `checkbox-${instance!.uid}`);

const computedIcon = computed(() => {
    if (props.restricted) {
        return 's-icon-hide';
    } else if (isChecked.value) {
        if (props.readonly) {
            return 's-icon-checkbox-on-readonly';
        } else if (props.disabled) {
            return 's-icon-checkbox-on-disabled';
        } else {
            return 's-icon-checkbox-on';
        }
    } else {
        if (props.readonly) {
            return 's-icon-checkbox-off-readonly';
        } else if (props.disabled) {
            return 's-icon-checkbox-off-disabled';
        } else {
            return 's-icon-checkbox-off';
        }
    }
});

const computedMessages = computed(() => {
    let messages: string[] = [];
    if (typeof props.messages === 'string') {
        messages.push(props.messages);
    } else if (Array.isArray(props.messages)) {
        messages = messages.concat(props.messages);
    }
    if (props.validationState) {
        messages = messages.concat(props.validationState.messages);
    }
    return messages;
});

const tooltipDirective = computed(
    () =>
        createTooltipDirective({
            autoSize: true,
            content: convertContent(computedMessages.value),
            popperClass: 'wtg-tooltip--validation wtg-tooltip--fit-content',
            sentiment: computedSentiment.value as TooltipSentiment,
            shown: isHovered.value,
        }) ||
        (customTooltipDirective.value && { ...customTooltipDirective.value, shown: isHovered.value })
);

//
// Event Handlers
//
function onClick() {
    if (!props.readonly) {
        emit('change', !isChecked.value);
        emit('update:modelValue', !isChecked.value);
    }
}
</script>

<style lang="scss">
.wtg-checkbox {
    display: flex;
    flex: 0 1 auto;
    flex-wrap: wrap;
    max-width: 100%;
    padding: var(--s-padding-s) 0px;
    align-items: center;
    gap: 0px;

    label {
        font: var(--s-text-md-default);
        cursor: pointer;
        padding: 0px var(--s-padding-s);
    }

    .wtg-checkbox__selection-control {
        display: inline-flex;
        flex: 0 0 auto;
        position: relative;

        .wtg-checkbox__selection-control-background {
            position: absolute;
            bottom: 4px;
            left: 4px;
            right: 4px;
            top: 4px;
            border: 1px solid transparent;
            border-radius: 1px;
        }

        .wtg-checkbox__icon {
            z-index: 1;
            font-size: var(--s-sizing-l);
            height: var(--s-sizing-icon-md);
            width: var(--s-sizing-icon-md);
            border-radius: var(--s-radius-s);
        }

        input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
            user-select: none;
            z-index: 2;

            &:disabled {
                pointer-events: none;
            }

            &:focus-visible + i {
                border-radius: var(--s-radius-s);
                outline: 2px solid var(--s-primary-border-default);
                outline-offset: -2px;
            }
        }

        &.wtg-checkbox--default input:hover:not(:disabled) + .wtg-checkbox__selection-control-background {
            background: var(--s-neutral-bg-weak-hover);
        }

        &.wtg-checkbox--selected input:hover + i {
            color: var(--s-primary-icon-hover);
        }

        &.wtg-checkbox--indeterminate input:hover + i {
            color: var(--s-primary-icon-hover);
        }
    }

    .wtg-checkbox--default > i {
        color: var(--s-neutral-border-default);
    }

    .wtg-checkbox--selected > i {
        color: var(--s-primary-border-default);
    }

    .wtg-checkbox--indeterminate > i {
        color: var(--s-primary-border-default);
    }

    &.wtg-checkbox--disabled {
        color: var(--s-neutral-txt-disabled);
        pointer-events: none;
    }

    &.wtg-checkbox--disabled label {
        color: var(--s-neutral-icon-disabled);
        pointer-events: none;
    }

    &.wtg-checkbox--disabled > .wtg-checkbox__selection-control {
        & .wtg-checkbox__selection-control-background {
            background: var(--s-neutral-bg-disabled);
        }

        & > i {
            color: var(--s-neutral-icon-disabled);
            pointer-events: none;
        }
    }

    &.wtg-checkbox--readonly {
        & > label {
            cursor: default;
        }

        > .wtg-checkbox__selection-control {
            & .wtg-checkbox__selection-control-background {
                background: var(--s-neutral-bg-disabled);
            }

            & > i {
                color: var(--s-neutral-border-disabled);
            }
        }
    }

    &.wtg-checkbox--success > .wtg-checkbox__selection-control {
        &.wtg-checkbox--default input:hover + i {
            background: var(--s-success-bg-weak-hover);
            color: var(--s-success-border-hover);
        }

        &.wtg-checkbox--selected input:hover + i,
        &.wtg-checkbox--indeterminate input:hover + i {
            background: var(--s-success-bg-weak-hover);
            color: var(--s-success-bg-hover);
        }
    }

    &.wtg-checkbox--success {
        & .wtg-checkbox__selection-control {
            & > i {
                color: var(--s-success-bg-default);
            }
        }

        & .wtg-checkbox__label {
            color: var(--s-success-txt-default);
        }
    }

    &.wtg-checkbox--critical > .wtg-checkbox__selection-control {
        &.wtg-checkbox--default input:hover + i {
            background: var(--s-error-bg-weak-hover);
            color: var(--s-error-border-hover);
        }

        &.wtg-checkbox--selected input:hover + i,
        &.wtg-checkbox--indeterminate input:hover + i {
            background: var(--s-error-bg-weak-hover);
            color: var(--s-error-bg-hover);
        }
    }

    &.wtg-checkbox--critical {
        & .wtg-checkbox__selection-control {
            & > i {
                color: var(--s-error-bg-default);
            }
        }

        & .wtg-checkbox__label {
            color: var(--s-error-txt-default);
        }
    }

    &.wtg-checkbox--warning > .wtg-checkbox__selection-control {
        &.wtg-checkbox--default input:hover + i {
            background: var(--s-warning-bg-weak-hover);
            color: var(--s-warning-icon-hover);
        }

        &.wtg-checkbox--selected input:hover + i,
        &.wtg-checkbox--indeterminate input:hover + i {
            background: var(--s-warning-bg-weak-hover);
            color: var(--s-warning-icon-hover);
        }
    }

    &.wtg-checkbox--warning {
        & .wtg-checkbox__selection-control {
            & > i {
                color: var(--s-warning-border-default);
            }
        }

        & .wtg-checkbox__label {
            color: var(--s-warning-txt-default);
        }
    }

    .wtg-checkbox__label {
        color: var(--s-neutral-txt-default);
        font: var(--s-text-md-default);
    }
}
</style>
