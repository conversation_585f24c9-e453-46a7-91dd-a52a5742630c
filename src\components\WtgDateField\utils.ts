import { useLocale } from '@composables/locale';
import { WtgDateFormatter } from './types';

export const convertInputDateToDate = (dateString: string, monthYearOnly?: boolean): Date => {
    if (monthYearOnly) {
        const [year, month] = dateString.split('-').map(Number);
        return new Date(year, month - 1);
    }
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(year, month - 1, day);
};

export const convertDateToInputDate = (date: Date) => date.toLocaleDateString('en-GB').split('/').reverse().join('-');

export const formatMonthYearDisplayValue = (date: string) => {
    const dateArray = date.split('-');
    return dateArray && dateArray.length >= 2 ? `${dateArray[1]}-${dateArray[0]}` : '';
};

export const formatMonthYearParsedValue = (date: string) => {
    const dateArray = date.split('-');
    return dateArray && dateArray.length >= 2 ? `${dateArray[0]}-${dateArray[1]}` : date;
};

export const formatDate = (date: string, formatter?: WtgDateFormatter) => {
    const { dateFormatter } = useLocale();

    return formatter ? formatter.formatDate(date) : dateFormatter.value.format(date);
};

export const parseDate = (date: string, formatter?: WtgDateFormatter): string | null => {
    const { dateFormatter } = useLocale();

    return formatter ? formatter.parseDate(date) : dateFormatter.value.parse(date);
};

export const today = (formatter?: WtgDateFormatter): string => {
    const { dateFormatter } = useLocale();
    return formatter ? formatter.today() : dateFormatter.value.today();
};
