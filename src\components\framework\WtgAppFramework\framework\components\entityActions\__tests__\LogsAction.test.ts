import { WtgFrameworkTask, WtgFrameworkTaskStandardAction } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import LogsAction from '../LogsAction.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('logs-action', () => {
    let task: WtgFrameworkTask;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        task = new WtgFrameworkTask();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is LogsAction', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('LogsAction');
    });

    describe('when given a showLogsAction', () => {
        beforeEach(() => {
            task.showLogsAction = {
                visible: false,
                caption: 'Logs',
                label: 'Logs',
                onInvoke: jest.fn(),
            };
        });

        test('its does not render a button when the action visible is false', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            expect(buttons.length).toBe(0);
        });

        test('its does render a button with an icon when the action visible is true', () => {
            task.showLogsAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.exists()).toBe(true);
        });

        test('it renders the button with aria-haspopup="dialog" attribute', () => {
            task.showLogsAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.attributes('aria-haspopup')).toBe('dialog');
        });

        test('the button calls the action onInvoke method when clicked', async () => {
            task.showLogsAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            expect(task.showLogsAction.onInvoke).toBeCalledTimes(1);
        });

        describe('on a large screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = false;
                task.showLogsAction.visible = true;
            });

            test('it display as a ghost button', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().variant).toBe('ghost');
                expect(button.text()).toBe('Logs');
            });

            test('it displays the button with a leading icon', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().leadingIcon).toBe('s-icon-logs');
            });
        });

        describe('on a small screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = true;
                task.showLogsAction.visible = true;
            });

            test('it display as a icon button with a tooltip', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.props().icon).toBe('s-icon-logs');
                expect(button.props().tooltip).toBe('Logs');
            });
        });
    });

    describe('Property tests', () => {
        test('when props is passed', () => {
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task: task } });
            expect(wrapper.vm.action).toStrictEqual(task.showLogsAction);
        });

        test('when props is not passed', () => {
            const defaultValue: WtgFrameworkTaskStandardAction = {
                visible: false,
                caption: 'Logs',
                label: 'Logs',
                onInvoke: (): void => undefined,
            };
            const wrapper: VueWrapper<any> = mountComponent();
            expect(wrapper.vm.action.caption).toStrictEqual(defaultValue.caption);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(LogsAction, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
