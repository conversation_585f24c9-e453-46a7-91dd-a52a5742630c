<template>
    <WtgInput v-bind="baseInputProps" class="wtg-file-input">
        <input
            :id="computedId"
            ref="inputRef"
            :aria-label="ariaLabel"
            :aria-labelledby="ariaLabelledby"
            type="file"
            class="wtg-file-input__file-input"
            :disabled="disabled || displayOnly || readonly"
            :multiple="multiple"
            :accept="computedAccept"
            @change="onChange"
        />
        <WtgLabel class="wtg-file-input__label">{{ displayString }}</WtgLabel>
    </WtgInput>
</template>

<script setup lang="ts">
import WtgInput from '@components/WtgInput';
import { WtgLabel } from '@components/WtgLabel';
import { basePropsFromProps, makeInputProps } from '@composables/input';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { makeValidationProps } from '@composables/validation';
import { computed, getCurrentInstance, PropType, ref, watch } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * If true, multiple files can be selected.
     */
    multiple: {
        type: Boolean,
        default: false,
    },

    /**
     * Specifies the accepted file types for the file input.
     * Example: '.jpg,.png,.pdf' or 'image/*'.
     */
    acceptedFiles: {
        type: [String, Array] as PropType<string | string[]>,
        default: '',
    },

    ...makeInputProps(),
    ...makeLayoutGridColumnProps(),
    ...makeValidationProps(),
});

const model = defineModel<File | File[] | undefined>({ default: undefined });

//
// State
//
const inputRef = ref<HTMLInputElement>();

//
// Composables
//
const instance = getCurrentInstance();

useLayoutGridColumn(props);

//
// Computed
//
const computedId = computed(() => props.id || props.inputId || `input-${instance!.uid}`);

const baseInputProps = computed(() => {
    return {
        ...basePropsFromProps(props),
        id: computedId.value,
    };
});

const displayString = computed(() => {
    let result = '';
    if (model.value) {
        if (Array.isArray(model.value)) {
            result = model.value.map((file) => file.name).join(', ');
        } else {
            result = model.value.name;
        }
    }
    return result;
});

const computedAccept = computed(() => {
    if (Array.isArray(props.acceptedFiles)) {
        return props.acceptedFiles.join(',');
    }

    return props.acceptedFiles;
});

//
// Watchers
//
watch(model, (newValue) => {
    const clearFileInput = !newValue || (Array.isArray(newValue) && newValue.length === 0);

    if (inputRef.value && clearFileInput) {
        inputRef.value.value = '';
    }
});

//
// Event Handlers
//
function onChange(event: Event) {
    const target = event.target as HTMLInputElement | null;
    const fileList = target?.files;

    if (props.multiple) {
        model.value = Array.from(fileList ?? []);
    } else {
        model.value = fileList?.item(0) || undefined;
    }
}
</script>

<style lang="scss">
.wtg-file-input {
    .wtg-file-input__file-input {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        opacity: 0;
    }

    .wtg-file-input__label {
        min-height: 1lh;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    &.wtg-input--disabled {
        .wtg-file-input__label {
            color: var(--s-neutral-txt-disabled);
        }
    }
}
</style>
