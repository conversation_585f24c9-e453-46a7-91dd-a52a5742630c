import { VueWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../../WtgUi';
import { Conversation, ConversationFollower } from '../../types';
import RemoveFollowerDialog from '../RemoveFollowerDialog.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const dislikeMessageAsync = jest.fn();
const followConversationAsync = jest.fn();
const likeMessageAsync = jest.fn();
const sendMessageAsync = jest.fn();
const unfollowConversationAsync = jest.fn();
const sendUnsentMessageAsync = jest.fn();
const removeFollowerFromConversationAsync = jest.fn();
const getPotentialFollowersAsync = jest.fn();
const addFollowersAsync = jest.fn();

const conversation: Conversation = {
    id: '1',
    name: 'Conversation One',
    followerUserTypes: [],
    type: '',
    followers: [] as ConversationFollower[],
    following: false,
    loadingMessages: false,
    messages: {},
    provider: {
        dislikeMessageAsync,
        followConversationAsync,
        likeMessageAsync,
        sendMessageAsync,
        unfollowConversationAsync,
        sendUnsentMessageAsync,
        removeFollowerFromConversationAsync,
        getPotentialFollowersAsync,
        addFollowersAsync,
    },
};

const follower: ConversationFollower = {
    id: '1',
    name: 'Uno',
    icon: '',
};

describe('RemoveFollowerDialog', () => {
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is RemoveFollowerDialog', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('RemoveFollowerDialog');
    });

    test("it displays the follower's name", async () => {
        mountComponent();
        const message = document.querySelector('#alertMessage');
        expect(message?.innerHTML).toContain('Are you sure you want to remove follower Uno?');
    });

    test('when no button clicked sets value to false', async () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.internalValue).toBe(true);

        const buttons = wrapper.findAllComponents({ name: 'wtg-button' });
        await buttons.at(1)?.trigger('click');

        expect(wrapper.vm.internalValue).toBe(false);
    });

    describe('when yes button clicked', () => {
        let removeFollowerResolve: any, removeFollowerReject: any, wrapper: VueWrapper<any>, yesButton: VueWrapper<any>;

        beforeEach(async () => {
            removeFollowerFromConversationAsync.mockReturnValue(
                new Promise((resolve, reject) => {
                    removeFollowerResolve = resolve;
                    removeFollowerReject = reject;
                })
            );

            wrapper = mountComponent();
            yesButton = wrapper.findAllComponents({ name: 'wtg-button' }).at(1)!;
        });

        test('it calls conversation provider removeFollowerFromConversationAsync', async () => {
            await yesButton.trigger('click');
            await nextTick();

            expect(conversation.provider.removeFollowerFromConversationAsync).toHaveBeenCalledWith(
                follower,
                conversation
            );
        });

        test('loading while removing', async () => {
            await yesButton.trigger('click');
            await nextTick();

            expect(yesButton.props('loading')).toBe(true);
        });

        test('stops loading once removeFollowerFromConversationAsync resolved', async () => {
            removeFollowerResolve();
            await yesButton.trigger('click');
            await nextTick();

            expect(yesButton.props('loading')).toBe(false);
        });

        test('stops loading once removeFollowerFromConversationAsync rejected', async () => {
            const error = new Error();
            removeFollowerReject(error);
            await expect(wrapper.vm.onYesClick).rejects.toThrowError(error);
            expect(yesButton.props('loading')).toBe(false);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(RemoveFollowerDialog, {
            propsData: {
                conversation,
                follower,
                value: true,
                ...propsData,
            },
            global: {
                plugins: [wtgUi],
            },
            slots,
        });
    }
});
