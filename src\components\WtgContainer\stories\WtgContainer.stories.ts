import { WtgApp } from '@components/WtgApp';
import WtgBox from '@components/WtgBox';
import WtgPanel from '@components/WtgPanel';
import { layoutArgTypes } from '@composables/layoutGrid';
import { layoutGridColumnArgTypes } from '@composables/layoutGridColumn';
import { measureArgTypes } from '@composables/measure';
import { StoryObj } from '@storybook/vue3';
import WtgContainer from '../';

type Story = StoryObj<typeof WtgContainer>;

// More on how to set up stories at: https://storybook.js.org/docs/vue/writing-stories/introduction
export default {
    title: 'Utilities/Container',
    component: WtgContainer,
    parameters: {
        docs: {
            description: {
                component:
                    'Container is a sub-component used to ensure the main content pages of an application display with the correct margins, padding and sizing. Container should be used as the direct child of the Main component and there should only be a single Container visible at any one time.',
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: { WtgBox, WtgPanel },
        setup: () => ({ args }),
        template: '<WtgContainer v-bind="args"></WtgContainer>',
    }),
    argTypes: {
        ...layoutArgTypes,
        ...layoutGridColumnArgTypes,
        ...measureArgTypes,
    },
    decorators: [
        () => ({
            components: { WtgApp },
            template: '<WtgApp class="content-embedded-app"><story /></WtgApp>',
        }),
    ],
} as Story;

export const Default: Story = {
    tags: ['!dev'],
    args: {},
};
