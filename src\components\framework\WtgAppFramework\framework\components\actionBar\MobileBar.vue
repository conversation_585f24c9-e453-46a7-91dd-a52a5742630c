<template>
    <div class="d-flex flex-grow-1" style="gap: 8px; width">
        <WtgButton
            v-if="defaultAction.caption"
            variant="fill"
            max-height="36px"
            class="flex-grow-1"
            :sentiment="defaultAction.sentiment"
            :disabled="isProcessing"
            @click="defaultAction.click"
        >
            {{ defaultAction.caption }}
        </WtgButton>
        <ActionsMenuButton v-if="menuActions.length > 0" :actions="menuActions" :disabled="isProcessing" />
    </div>
</template>

<script setup lang="ts">
import { WtgButton } from '@components/WtgButton';
import {
    WtgFrameworkTask,
    WtgFrameworkTaskGenericAction,
    WtgFrameworkTaskGenericActionPlacement,
    WtgFrameworkTaskStandardAction,
} from '@components/framework/types';
import { WtgActionItemData } from '@components/WtgMenu';
import { computed, PropType } from 'vue';
import ActionsMenuButton from './ActionsMenuButton.vue';

let nextID = 1;

const props = defineProps({
    task: {
        type: Object as PropType<WtgFrameworkTask>,
        default: (): WtgFrameworkTask => new WtgFrameworkTask(),
    },
});

const cancelAction = computed((): WtgFrameworkTaskStandardAction => {
    return props.task.cancelAction;
});

const saveAction = computed((): WtgFrameworkTaskStandardAction => {
    return props.task!.saveAction;
});

const saveCloseAction = computed((): WtgFrameworkTaskStandardAction => {
    return props.task!.saveCloseAction;
});

const genericActions = computed((): WtgFrameworkTaskGenericAction[] => {
    return props.task.genericActions;
});

const isProcessing = computed((): boolean | undefined => {
    return saveAction.value.loading || saveCloseAction.value.loading || cancelAction.value.loading;
});

const actionItems = computed((): WtgActionItemData[] => {
    let primaryActions: WtgActionItemData[] = [];
    let secondaryActions: WtgActionItemData[] = [];
    let defaultActions: WtgActionItemData[] = [];
    let destructiveActions: WtgActionItemData[] = [];
    const defaultCategoryActions: WtgActionItemData[] = [];
    const defaultCategories: Record<string, any> = {};

    if (saveAction.value?.visible) {
        primaryActions.push(mapStandardAction(saveAction.value, 'primary'));
    } else {
        if (cancelAction.value?.visible) {
            primaryActions.push(mapStandardAction(cancelAction.value, 'primary'));
        }
    }
    genericActions.value
        ?.filter((action) => action.placement === WtgFrameworkTaskGenericActionPlacement.Primary)
        .map((action) => primaryActions.push(mapGenericAction(action)));

    if (saveAction.value?.visible) {
        secondaryActions.push(mapStandardAction(saveCloseAction.value));
        if (cancelAction.value?.visible) {
            secondaryActions.push(mapStandardAction(cancelAction.value));
        }
    }
    genericActions.value
        ?.filter((action) => action.placement === WtgFrameworkTaskGenericActionPlacement.Secondary)
        .map((action) => secondaryActions.push(mapGenericAction(action)));

    const filteredActions = genericActions.value?.filter(
        (action) => action.placement === WtgFrameworkTaskGenericActionPlacement.Default || action.category
    );
    filteredActions.forEach((action) => {
        if (action.category) {
            if (defaultCategories[action.category]) {
                defaultCategories[action.category].actions.push(mapGenericAction(action));
            } else {
                defaultCategories[action.category] = mapGenericCategory(action);
                defaultCategoryActions.push(defaultCategories[action.category]);
                defaultCategories[action.category].actions.push(mapGenericAction(action));
            }
        } else {
            defaultActions.push(mapGenericAction(action));
        }
    });

    destructiveActions = secondaryActions?.filter((action) => action.caption === 'Cancel');
    destructiveActions.forEach((action) => secondaryActions.splice(secondaryActions.indexOf(action), 1));

    return [
        ...primaryActions,
        ...secondaryActions,
        ...defaultActions,
        ...defaultCategoryActions,
        ...destructiveActions,
    ];
});

const defaultAction = computed((): WtgActionItemData => {
    return actionItems.value.length > 0
        ? actionItems.value[0]
        : {
              caption: '',
              click: (): void => {
                  return;
              },
          };
});

const menuActions = computed((): WtgActionItemData[] => {
    return actionItems.value.slice(1);
});

function mapGenericAction(action: WtgFrameworkTaskGenericAction): WtgActionItemData {
    return {
        caption: action.caption,
        sentiment: action.placement === WtgFrameworkTaskGenericActionPlacement.Primary ? 'primary' : undefined,
        id: action.id,
        click: (): void => action.onInvoke(),
    };
}

function mapGenericCategory(action: WtgFrameworkTaskGenericAction): WtgActionItemData {
    return {
        caption: action.category ?? '',
        id: '' + nextID++,
        actions: [],
    };
}

function mapStandardAction(
    action: WtgFrameworkTaskStandardAction,
    sentiment?: 'critical' | 'primary' | 'success'
): WtgActionItemData {
    return {
        caption: action.caption,
        sentiment,
        id: '' + nextID++,
        click: (): void => action.onInvoke(),
    };
}
</script>
