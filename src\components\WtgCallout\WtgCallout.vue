<template>
    <div v-show="model" :class="computedClass" :style="computedCalloutStyle">
        <div class="wtg-callout__icon-container">
            <WtgIcon>{{ computedIcon }}</WtgIcon>
        </div>
        <div class="wtg-callout__container">
            <div v-if="title && !computedInline" class="wtg-callout__title">
                {{ title }}
            </div>
            <div
                v-if="description"
                style="white-space: pre-wrap"
                class="wtg-callout__description"
                v-text="description"
            />
            <slot />
        </div>
        <div v-if="dismissible && !computedInline" class="wtg-callout__close-icon-container" @click="onDismissClick">
            <WtgIcon>s-icon-close</WtgIcon>
        </div>
    </div>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { computed, PropType } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * The description text to display inside the callout.
     */
    description: {
        type: String,
        default: '',
    },

    /**
     * Determines if the callout can be dismissed by the user.
     * When true, a close icon will be displayed.
     */
    dismissible: {
        type: Boolean,
        default: false,
    },

    /**
     * The title text to display inside the callout.
     */
    title: {
        type: String,
        default: '',
    },

    /**
     * Defines the sentiment or visual style of the callout.
     * Options include 'critical', 'info', 'warning', or 'success'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'info' | 'warning' | 'success'>,
        default: 'info',
    },

    /**
     * Specifies the variant of the callout.
     * Options include 'default' for a standard callout or 'inline' for a compact inline style.
     */
    variant: {
        type: String as PropType<'default' | 'inline'>,
        default: 'default',
    },
});

const model = defineModel<boolean>({ default: true });

//
// Emits
//
const emit = defineEmits<{
    close: [];
}>();

//
// Computed
//
const computedInline = computed(() => props.variant === 'inline');

const computedIcon = computed(() => {
    let icon = '';
    switch (props.sentiment) {
        case 'info':
            icon = 's-icon-info-circle';
            break;
        case 'success':
            icon = 's-icon-status-success';
            break;
        case 'warning':
            icon = 's-icon-status-warning';
            break;
        case 'critical':
            icon = 's-icon-status-critical';
            break;
    }
    return icon;
});

const computedCalloutStyle = computed(() => {
    if (!computedInline.value) {
        return { padding: 'var(--s-padding-m)' };
    } else {
        return {
            padding: 'var(--s-padding-null)',
            border: 'none',
            background: 'none',
        };
    }
});

const computedClass = computed(() => ({
    'wtg-callout--info': props.sentiment === 'info',
    'wtg-callout--success': props.sentiment === 'success',
    'wtg-callout--warning': props.sentiment === 'warning',
    'wtg-callout--critical': props.sentiment === 'critical',
    'wtg-callout--inline': computedInline.value,
    'wtg-callout': true,
}));

//
// Event Handlers
//
function onDismissClick(): void {
    model.value = false;
    emit('close');
}
</script>

<style lang="scss">
.wtg-callout {
    display: flex;
    align-items: flex-start;
    gap: var(--s-spacing-m);
    border-radius: var(--s-radius-s);

    .wtg-callout__icon-container {
        display: flex;
    }

    .wtg-callout__container {
        flex: 1 1 auto;

        .wtg-callout__title {
            font: var(--s-title-sm-default);
        }

        .wtg-callout__description {
            color: var(--s-neutral-txt-default);
        }
    }

    .wtg-callout__close-icon-container {
        display: flex;
        &:hover {
            opacity: 0.5;
            cursor: pointer;
        }
    }

    &.wtg-multiline-callout {
        white-space: pre-line;
    }

    &--inline {
        font: var(--s-text-xs-default);
        gap: var(--s-spacing-s);
        align-items: center;
    }

    &--info {
        .wtg-callout__title {
            color: var(--s-info-txt-default);
        }

        .wtg-callout__icon-container {
            color: var(--s-info-icon-default);
        }

        border-left: 4px solid var(--s-info-border-default);
        background: var(--s-info-bg-weak-default);
    }

    &--success {
        .wtg-callout__title {
            color: var(--s-success-txt-default);
        }

        .wtg-callout__icon-container {
            color: var(--s-success-icon-default);
        }

        border-left: 4px solid var(--s-success-border-default);
        background: var(--s-success-bg-weak-default);
    }

    &--warning {
        .wtg-callout__title {
            color: var(--s-warning-txt-default);
        }

        .wtg-callout__icon-container {
            color: var(--s-warning-icon-default);
        }

        border-left: 4px solid var(--s-warning-border-default);
        background: var(--s-warning-bg-weak-default);
    }

    &--critical {
        .wtg-callout__title {
            color: var(--s-error-txt-default);
        }

        .wtg-callout__icon-container {
            color: var(--s-error-icon-default);
        }

        border-left: 4px solid var(--s-error-border-default);
        background: var(--s-error-bg-weak-default);
    }
}
</style>
