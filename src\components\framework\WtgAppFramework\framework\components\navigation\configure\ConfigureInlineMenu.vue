<template>
    <WtgListGroup
        v-if="currentUser.onThemeConfiguration"
        role="menuitem"
        :aria-label="application.captions.configure"
        aria-haspopup="menu"
    >
        <template #activator="{ isOpen }">
            <WtgListItem>
                <template #leading>
                    <WtgIcon>s-icon-settings</WtgIcon>
                </template>
                {{ application.captions.configure }}
                <template #trailing>
                    <WtgIcon>
                        {{ isOpen ? 's-icon-caret-up' : 's-icon-caret-down' }}
                    </WtgIcon>
                </template>
            </WtgListItem>
        </template>
        <WtgListItem
            v-if="showThemeConfig"
            role="menuitem"
            :aria-label="application.captions.theme"
            aria-haspopup="menu"
            @click="onThemeConfigClick"
        >
            {{ application.captions.theme }}
        </WtgListItem>
    </WtgListGroup>
</template>

<script setup lang="ts">
import WtgIcon from '@components/WtgIcon';
import { WtgListGroup, WtgListItem } from '@components/WtgList';
import { WtgFrameworkUser } from '@components/framework/types';
import { useApplication } from '@composables/application';
import { computed } from 'vue';

const application = useApplication();

const emit = defineEmits<{
    'item-click': [];
}>();

const currentUser = computed((): WtgFrameworkUser => {
    return application.user ?? { name: '', image: {} };
});

const showThemeConfig = computed((): boolean | undefined => {
    return !!currentUser.value.onThemeConfiguration;
});

function onThemeConfigClick(): void {
    if (currentUser.value && currentUser.value.onThemeConfiguration) {
        currentUser.value.onThemeConfiguration();
        emit('item-click');
    }
}
</script>
