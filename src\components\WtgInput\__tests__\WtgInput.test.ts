import { InputAlignment } from '@composables/input';
import * as notifications from '@composables/notifications';
import * as tooltip from '@composables/tooltip/tooltip';
import { AlertLevel } from '@composables/notifications';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { computed, ComputedRef, nextTick, Ref, ref } from 'vue';
import { WtgInput } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

jest.mock('@composables/notifications');

describe('WtgInput', () => {
    beforeEach(() => {
        jest.spyOn(notifications, 'useCurrentNotification').mockReturnValue({
            currentNotification: ref({}),
            displayCurrentNotification: computed(() => false),
        });
    });

    test('its name is WtgInput', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgInput');
    });

    test('it applies the wtg-input__content-slot style to the default slot', () => {
        const wrapper = mountComponent({
            slots: {
                default: '<span id="text">Some text</span>',
            },
        });
        const slotContent = wrapper.find('.wtg-input__content-slot');
        expect(slotContent.html()).toContain('<span id="text">Some text</span>');
    });

    test('it applies the wtg-input__leading-content style to the leading slot', () => {
        const wrapper = mountComponent({
            slots: {
                leading: '<span id="text">leading content</span>',
            },
        });
        const slotContent = wrapper.find('.wtg-input__leading-content');
        expect(slotContent.html()).toContain('<span id="text">leading content</span>');
    });

    test('it renders the messages list passed in the messages property and contains style to preserve whitespaces', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                messages: ['this is a test message'],
            },
        });
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('this is a test message');
    });

    test('it renders the message passed into the messages property when it is a string', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                messages: 'this is a test message',
            },
        });
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('this is a test message');
    });

    test('it renders the validationState', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                validationState: {
                    alertLevel: AlertLevel.Warning,
                    messages: ['this is a validation message'],
                },
            },
        });
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('this is a validation message');
    });

    test('it merges validationState and messages if both were to be used simultaniously', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                messages: 'this is a test message',
                validationState: {
                    alertLevel: AlertLevel.Warning,
                    messages: ['this is a validation message'],
                },
            },
        });
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('this is a test message');
        expect(vm.tooltipDirective.content).toContain('this is a validation message');
    });

    test('it does not render messages when hideMessages, disabled, displayOnly', () => {
        const scenarios = [{ hideMessages: true }, { disabled: true }, { displayOnly: true }];

        scenarios.forEach((props) => {
            const wrapper = mountComponent({
                props: {
                    ...props,
                    focused: true,
                    messages: 'this is a test message',
                },
            });

            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(false);
        });
    });

    test('it renders messages when focused', async () => {
        const wrapper = mountComponent({
            props: {
                messages: 'this is a test message',
            },
        });

        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(false);

        await wrapper.setProps({ focused: true });
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('this is a test message');

        await wrapper.setProps({ focused: false });
        expect(vm.tooltipDirective.shown).toBe(false);
    });

    test('it renders messages when hovered', async () => {
        const wrapper = mountComponent({
            props: {
                messages: 'this is a test message',
            },
        });

        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(false);

        await wrapper.find('.wtg-input__content').trigger('mouseenter');
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('this is a test message');

        await wrapper.find('.wtg-input__content').trigger('mouseleave');
        expect(vm.tooltipDirective.shown).toBe(false);
    });

    test('it adds the wtg-input__label div when the label prop is set', () => {
        const wrapper = mountComponent({
            props: { label: 'label text' },
        });
        const labelContent = wrapper.find('.wtg-input__label');
        expect(labelContent.html()).toContain('label text');
    });

    test('it does not add the wtg-input__label div when the label prop is not set', () => {
        const wrapper = mountComponent();
        const labelContent = wrapper.find('.wtg-input__label');
        expect(labelContent.exists()).toBe(false);
    });

    test('it adds the required class to the wtg-input__label div when the required prop is set', async () => {
        const wrapper = mountComponent({
            props: {
                label: 'label text',
            },
        });
        const labelContent = wrapper.find('.wtg-input__label');
        expect(labelContent.classes()).not.toContain('wtg-required');

        await wrapper.setProps({ required: true });

        expect(labelContent.classes()).toContain('wtg-required');
    });

    test('it set the icon props to the appropriate slots', () => {
        const wrapper = mountComponent({
            props: {
                leadingIcon: 's-icon-phone',
                trailingIcon: 's-icon-plane',
            },
        });

        expect(wrapper.find('.wtg-input__leading-content').html()).toContain('wtg-icon s-icon-phone');
        expect(wrapper.find('.wtg-input__trailing-icon-content').html()).toContain('wtg-icon s-icon-plane');
    });

    test('it applies the correct style when sentiment is set to success', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                sentiment: 'success',
                messages: ['Test success message.'],
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--success');
    });

    test('it applies the correct style when sentiment is set to warning', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                sentiment: 'warning',
                messages: ['Test warning message.'],
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--warning');
    });

    test('it applies the correct style when sentiment is set to critical', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                sentiment: 'critical',
                messages: ['Test critical message.'],
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--critical');
    });

    test('it applies the correct style when messages alert level is error', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                validationState: {
                    alertLevel: AlertLevel.Error,
                    messages: ['Test error message.'],
                },
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--critical');
    });

    test('it applies the correct style when messages alert level is warning', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                validationState: {
                    alertLevel: AlertLevel.Warning,
                    messages: ['Test warning message.'],
                },
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--warning');
    });

    test('it applies the correct style when messages alert level is messageError', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                validationState: {
                    alertLevel: AlertLevel.MessageError,
                    messages: ['Test message error message.'],
                },
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--warning');
    });

    test('it applies the correct style when messages alert level is information', () => {
        const wrapper = mountComponent({
            props: {
                focused: true,
                validationState: {
                    alertLevel: AlertLevel.Information,
                    messages: ['Test message information message.'],
                },
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--info');
    });

    test('it applies the correct style when WtgInput is set to disabled', () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--disabled');
    });

    test('it applies the correct style when WtgInput is set to readonly', () => {
        const wrapper = mountComponent({
            props: {
                readonly: true,
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--readonly');
    });

    test('it applies the correct style when WtgInput is set to loading', () => {
        const wrapper = mountComponent({
            props: {
                loading: true,
            },
        });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--loading');
    });

    test('it renders the restricted display field in default slot when restricted is set to true ', () => {
        const wrapper = mountComponent({
            props: {
                restricted: true,
            },
        });
        const defaultSlotContent = wrapper.find('.wtg-input__content-slot');
        expect(defaultSlotContent.find('input').attributes('disabled')).toBeDefined();
    });

    describe('validation', () => {
        test('it validates the input when blur', async () => {
            const wrapper = mountComponent({
                props: {
                    modelValue: '',
                    rules: [(value: string) => !!value || 'Required.'],
                },
            });

            wrapper.setProps({
                focused: true,
            });

            await nextTick();

            wrapper.setProps({
                focused: false,
            });

            await nextTick();
            await nextTick();

            expect(wrapper.classes()).toContain('wtg-input--critical');
            await wrapper.find('.wtg-input__content').trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('Required.');
        });

        test('it validates the input when input changes', async () => {
            const wrapper = mountComponent({
                props: {
                    modelValue: '',
                    rules: [(value: string) => value?.length >= 3 || 'Minimum length is 3.'],
                },
            });

            wrapper.setProps({
                focused: true,
            });

            await nextTick();

            wrapper.setProps({
                modelValue: 'ab',
            });

            await nextTick();
            await nextTick();

            expect(wrapper.classes()).toContain('wtg-input--critical');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('Minimum length is 3.');

            wrapper.setProps({
                modelValue: 'abc',
            });

            await nextTick();
            await nextTick();

            expect(wrapper.classes()).not.toContain('wtg-input--critical');
            expect(vm.tooltipDirective).toBe(undefined);
        });
    });

    test('it applies the correct style when cell is set to true', () => {
        const wrapper = mountComponent({ props: { cell: true } });
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--cell');
    });

    test('it renders the validation icon in the leading slot and applies the correct style when alignment is set to end', () => {
        const wrapper = mountComponent({ props: { alignment: InputAlignment.End, sentiment: 'critical' } });
        expect(wrapper.find('.wtg-input__leading-content').findComponent({ name: 'WtgIcon' }).exists()).toBe(true);
        expect(wrapper.find('.wtg-input__trailing-content').exists()).toBe(false);
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--align-end');
    });

    test('it renders the validation icon after the input and applies the correct style when alignment is set to center', () => {
        const wrapper = mountComponent({ props: { alignment: InputAlignment.Center, sentiment: 'critical' } });
        expect(wrapper.find('.wtg-input__trailing-content').findComponent({ name: 'WtgIcon' }).exists()).toBe(true);
        expect(wrapper.find('.wtg-input__leading-content').exists()).toBe(false);
        expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--align-center');
    });

    test('it renders the validation icon after the input and applies the correct style when alignment is set to start', () => {
        const wrapper = mountComponent({ props: { alignment: InputAlignment.Center, sentiment: 'critical' } });
        expect(wrapper.find('.wtg-input__trailing-content').findComponent({ name: 'WtgIcon' }).exists()).toBe(true);
        expect(wrapper.find('.wtg-input__leading-content').exists()).toBe(false);
    });

    test('it renders the info icon and the tooltip', async () => {
        const wrapper = mountComponent({
            props: {
                label: 'label text',
                info: 'info',
            },
        });
        expect(wrapper.html()).toContain('s-icon-info-circle');
        expect(wrapper.html()).toContain('has-tooltip');
    });

    test('it renders divider prompter slot is not empty', () => {
        const wrapper = mountComponent({
            slots: {
                prompter: '<div>prompter</div>',
            },
        });
        expect(wrapper.find('.wtg-input__divider').exists()).toBe(true);
    });

    test('it does not render divider prompter slot is empty', () => {
        const wrapper = mountComponent({});
        expect(wrapper.find('.wtg-input__divider').exists()).toBe(false);
    });

    test('it emits a click event when the div is clicked', async () => {
        const wrapper = mountComponent();
        wrapper.trigger('click');
        expect(wrapper.emitted('click')).toBeTruthy();
    });

    describe('when error or errorMessages are set', () => {
        test('it applies the correct style when error is set to true', () => {
            const wrapper = mountComponent({
                props: { focused: true, error: true, messages: ['Test error message.'] },
            });
            expect(wrapper.classes()).toContain('wtg-input--critical');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('Test error message.');
        });

        test('it applies the correct style when error is changed', async () => {
            const wrapper = mountComponent({
                props: { focused: true, error: true, messages: ['Test error message.'] },
            });
            await wrapper.setProps({ focused: true, error: false, messages: [] });
            expect(wrapper.classes()).not.toContain('wtg-input--critical');

            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective).toBe(undefined);
        });

        test('it applies the correct style when errorMessages is set', () => {
            const wrapper = mountComponent({ props: { focused: true, errorMessages: ['Test error message.'] } });
            expect(wrapper.classes()).toContain('wtg-input--critical');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('Test error message.');
        });

        test('it applies the correct style when errorMessages is changed', async () => {
            const wrapper = mountComponent({ props: { focused: true, errorMessages: ['Test error message.'] } });
            await wrapper.setProps({ errorMessages: ['Test error message2.'] });
            expect(wrapper.classes()).toContain('wtg-input--critical');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('Test error message2.');
        });

        test('it applies the correct style when sentiment and messages are set', () => {
            const wrapper = mountComponent({
                props: { focused: true, sentiment: 'warning', messages: ['Test warning message.'] },
            });
            expect(wrapper.classes()).toContain('wtg-input--warning');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('Test warning message.');
        });

        test('it applies the correct style when messages is an empty string', async () => {
            const wrapper = mountComponent({ props: { focused: true, messages: '' } });
            expect(wrapper.classes()).not.toContain('wtg-input--critical');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective).toBe(undefined);
        });

        test('it applies the correct style when WtgInput is set to filled', () => {
            const wrapper = mountComponent({
                props: {
                    filled: true,
                },
            });
            expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--filled');
        });

        test('it does not apply filled styling when the WtgInput is readonly', () => {
            const wrapper = mountComponent({
                props: {
                    filled: true,
                    readonly: true,
                },
            });
            expect(wrapper.find('.wtg-input').classes()).not.toContain('wtg-input--filled');
        });

        test('it does not apply filled styling when the WtgInput is disabled', () => {
            const wrapper = mountComponent({
                props: {
                    filled: true,
                    disabled: true,
                },
            });
            expect(wrapper.find('.wtg-input').classes()).not.toContain('wtg-input--filled');
        });

        test('it does not apply filled styling when dark mode is active', async () => {
            const testWtgUi = new WtgUi();
            const wrapper = mountComponent(
                {
                    props: {
                        filled: true,
                    },
                },
                testWtgUi
            );
            expect(wrapper.find('.wtg-input').classes()).toContain('wtg-input--filled');
            testWtgUi.appearance = 'dark';
            await nextTick();
            expect(wrapper.find('.wtg-input').classes()).not.toContain('wtg-input--filled');
        });
    });

    describe('when given a prefix or suffix', () => {
        test('it displays the prefix', () => {
            const wrapper = mountComponent({
                props: {
                    prefix: 'min',
                },
            });
            const prefix = wrapper.find('.wtg-input__content-prefix');
            expect(prefix.exists()).toBeTruthy();
            expect(prefix.text()).toBe('min');
        });

        test('it does not display the suffix', () => {
            const wrapper = mountComponent({
                props: {
                    suffix: 'max',
                },
            });
            const suffix = wrapper.find('.wtg-input__content-suffix');
            expect(suffix.exists()).toBeTruthy();
        });
    });

    describe('useCurrentNotification', () => {
        let mockdisplayCurrentNotificationRef: Ref<boolean>;
        let mockdisplayCurrentNotification: ComputedRef<boolean>;
        let createTooltipDirectiveSpy: jest.SpyInstance;

        beforeEach(() => {
            mockdisplayCurrentNotificationRef = ref(false);
            mockdisplayCurrentNotification = computed(() => mockdisplayCurrentNotificationRef.value);

            jest.spyOn(notifications, 'useCurrentNotification').mockReturnValue({
                currentNotification: ref({}),
                displayCurrentNotification: mockdisplayCurrentNotification,
            });

            jest.spyOn(tooltip, 'useTooltip').mockImplementationOnce((props) => {
                const { useTooltip } = jest.requireActual('@composables/tooltip/tooltip');
                const result = useTooltip(props) as ReturnType<typeof tooltip.useTooltip>;
                createTooltipDirectiveSpy = jest.spyOn(result, 'createTooltipDirective');
                return result;
            });
        });

        test('it should have validation state prop from inputProps', async () => {
            const wrapper = mountComponent({
                props: {
                    validationState: {
                        alertLevel: AlertLevel.Warning,
                        messages: ['this is a test notification message'],
                    },
                },
            }).findComponent({ name: 'WtgInput' });
            expect(wrapper.props('validationState')).not.toBeUndefined();
        });

        test('it should have root ref for useCurrentNotification', () => {
            const wrapper = mountComponent();
            expect(wrapper.vm.$refs.root).not.toBeUndefined();
        });

        test('it should change the background color to the hover state and display message when isCurrentNotification is true', async () => {
            const wrapper = mountComponent({
                props: {
                    messages: ['this is a test message'],
                },
            });
            mockdisplayCurrentNotificationRef.value = true;
            await nextTick();
            const background = wrapper.find('.wtg-input__current-notification');
            expect(background.exists()).toBe(true);
            expect(createTooltipDirectiveSpy.mock.calls.at(-1)[0].shown).toBe(true);
            expect(createTooltipDirectiveSpy.mock.calls.at(-1)[0].content).toContain('this is a test message');
        });
    });

    function mountComponent({ props = {}, slots = {} } = {}, wtgUiInstance?: WtgUi) {
        return mount(WtgInput as any, {
            props,
            slots,
            global: {
                plugins: [wtgUiInstance ?? wtgUi],
            },
        });
    }
});
