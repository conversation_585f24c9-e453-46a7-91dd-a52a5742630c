export const AlertBadgeBehaviorsTemplate = `
<WtgLayoutGrid class="pb-12" style="gap: 12px">
    <WtgAlertBadge variant="error">
        <WtgButton leading-icon="s-icon-vessel">New Orders</WtgButton>
    </WtgAlertBadge>
    <WtgAlertBadge variant="warning">
        <WtgTab>Tab One</WtgTab>
    </WtgAlertBadge>
    <WtgAlertBadge variant="info">
        <WtgIconButton aria-label="Aria Name" icon="s-icon-bell"></WtgIconButton>
    </WtgAlertBadge>
    <WtgAlertBadge variant="success">
        <WtgIconButton aria-label="Aria Name" icon="s-icon-bell"></WtgIconButton>
    </WtgAlertBadge>
</WtgLayoutGrid>
`;
