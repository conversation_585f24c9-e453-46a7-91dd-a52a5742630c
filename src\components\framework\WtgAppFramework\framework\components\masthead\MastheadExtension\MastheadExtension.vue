<template>
    <div class="flex-grow-1 d-flex flex-column" style="height: inherit; width: 100%">
        <div>
            <div
                v-if="!isMobile && (showTaskTitle || showTaskActions || showEntityNavigation)"
                class="d-flex"
                :style="titleActionsStyle"
            >
                <div
                    v-if="showTaskTitle"
                    class="d-flex align-center"
                    style="min-width: 0px"
                    data-testid="title-actions"
                >
                    <WtgIconButton
                        v-if="showBackButton"
                        icon="s-icon-arrow-left"
                        :aria-label="ariaLabels.backButton"
                        @click="onTaskCloseClick"
                    />
                    <WtgLabel class="ml-2" typography="title-md-default" :style="noWrappingText">
                        {{ entityCaption }}
                    </WtgLabel>
                    <WtgStatus
                        v-if="!isStatusHidden"
                        :value="currentStatus?.value"
                        :label="currentStatus?.label"
                        :sentiment="currentStatus?.sentiment"
                        :variant="currentStatus?.variant"
                        :editable="isStatusEditable"
                        :items="statusItems"
                        class="ml-2"
                        @update:model-value="updateStatus($event)"
                    />
                </div>
                <WtgSpacer />
                <div class="d-flex align-center" style="gap: var(--s-spacing-m)">
                    <EntityActions v-if="showTaskActions" :task="currentTask" :aria-labels="ariaLabels" />
                    <EntityNavigation v-if="showEntityNavigation" :task="currentTask" />
                </div>
            </div>
            <div v-if="hasTabs" class="flex-shrink-1" style="min-width: 0">
                <WtgTabs v-model="tabInfo.current" show-arrows :height="tabHeight" class="mx-0">
                    <WtgTab v-for="(tab, index) in tabInfo.tabs" :key="index">
                        {{ tab.caption }}
                    </WtgTab>
                </WtgTabs>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    WtgFrameworkAriaLabels,
    WtgFrameworkTabsInfo,
    WtgFrameworkTask,
    WtgFrameworkTaskGenericActionPlacement,
} from '@components/framework/types';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import WtgSpacer from '@components/WtgSpacer';
import WtgStatus from '@components/WtgStatus';
import { WtgTab, WtgTabs } from '@components/WtgTabs';
import { useAppLevelTabs, useApplication, useTaskStatus } from '@composables/application';
import { computed, type CSSProperties } from 'vue';
import EntityActions from '../../entityActions';
import EntityNavigation from '../../entityNavigation';
import { useFramework } from '@composables/framework';

const application = useApplication();
const { isMobile } = useFramework();

const currentTask = computed((): WtgFrameworkTask => {
    return application.currentTask ?? new WtgFrameworkTask();
});

const { currentStatus, statusItems, isStatusEditable, isStatusHidden, updateStatus } = useTaskStatus(currentTask);

const atHomePage = computed((): boolean => {
    const currentMenuItem = application.menu.find((item) => item.active && item.home);
    return currentMenuItem ? true : false;
});

const appLevelTabs = useAppLevelTabs();
const tabs = computed((): WtgFrameworkTabsInfo => {
    return (
        appLevelTabs.value ?? {
            tabs: [],
            current: 0,
            visible: false,
        }
    );
});

const ariaLabels = computed((): WtgFrameworkAriaLabels => {
    return application.ariaLabels;
});

const showCloseButton = computed((): boolean => {
    return !atHomePage.value;
});

const entityCaption = computed((): string => {
    return currentTask.value.entityName ?? '';
});

const tabInfo = computed((): WtgFrameworkTabsInfo => {
    return tabs.value ?? { tabs: [], current: 0, visible: false };
});

const hasTabs = computed((): boolean => {
    return tabInfo.value.tabs.length > 0;
});

const tabHeight = computed((): string => {
    return isMobile.value ? '36px' : '28px';
});

const titleActionsStyle = computed((): CSSProperties => {
    return {
        maxHeight: '36px',
        margin: hasTabs.value ? 'var(--s-padding-l) 0 var(--s-padding-l) 0' : 'var(--s-padding-l) 0 0 0',
    };
});

const noWrappingText = computed((): CSSProperties => {
    return { textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' };
});

const showTaskTitle = computed((): boolean => {
    return (currentTask.value.showTaskTitle ?? false) && showCloseButton.value;
});

const showBackButton = computed((): boolean => {
    return !application.hideBackButton && (currentTask.value?.showBackButton ?? false);
});

const showTaskActions = computed((): boolean => {
    return (
        (currentTask.value.showEDocsAction.visible ||
            currentTask.value.documents.visible ||
            currentTask.value.showLogsAction.visible ||
            currentTask.value.showMessagesAction.visible ||
            currentTask.value.showNotesAction.visible ||
            currentTask.value.showWorkflowActions.visible ||
            currentTask.value.genericActions.some(
                (action) => action.placement === WtgFrameworkTaskGenericActionPlacement.TaskAction
            )) ??
        false
    );
});

const showEntityNavigation = computed((): boolean => {
    return currentTask.value?.others.visible ?? false;
});

const onTaskCloseClick = (): void => {
    if (currentTask.value.cancelAction.onInvoke) {
        currentTask.value.cancelAction.onInvoke();
    }
};
</script>

<style lang="scss" scoped>
:deep(.v-slide-group__next) {
    min-width: 0px;
}
:deep(.v-slide-group__prev) {
    min-width: 0px;
}
</style>
