<template>
    <WtgList>
        <WtgLabel v-if="items.favorites.length > 0" typography="title-sm-default">
            {{ favoritesCaption }}
        </WtgLabel>
        <WtgDivider v-if="items.favorites.length > 0" />
        <RecentListItem
            v-for="favorite in items.favorites"
            :key="favorite.id"
            :item="favorite"
            @item-click="onItemClicked"
        />
        <WtgLabel typography="title-sm-default">
            {{ recentsCaption }}
        </WtgLabel>
        <WtgDivider />
        <RecentListItem
            v-for="recentItem in items.recents"
            :key="recentItem.id"
            :item="recentItem"
            @item-click="onItemClicked"
        />
    </WtgList>
</template>

<script setup lang="ts">
import WtgDivider from '@components/WtgDivider';
import WtgLabel from '@components/WtgLabel';
import { WtgList } from '@components/WtgList';
import { PropType } from 'vue';
import RecentListItem from './RecentListItem.vue';
import { Items, WtgRecentItem } from './types/index';

const emit = defineEmits<{
    'item-click': [item: WtgRecentItem];
}>();
defineProps({
    items: {
        type: Object as PropType<Items>,
        default: () => ({
            favorites: [],
            recents: [],
        }),
    },
    favoritesCaption: {
        type: String,
        default: 'Favorites',
    },
    recentsCaption: {
        type: String,
        default: 'Recent',
    },
});

function onItemClicked(item: WtgRecentItem) {
    emit('item-click', item);
}
</script>
