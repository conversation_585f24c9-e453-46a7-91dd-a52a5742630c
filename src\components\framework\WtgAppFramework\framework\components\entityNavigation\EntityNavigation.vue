<template>
    <div v-if="action.visible" class="wtg-entity-navigation">
        <WtgIconButton
            :disabled="!action.hasPrevious"
            icon="$arrowLeft"
            :aria-label="formatCaption('navigation.previous')"
            variant="ghost"
            @click="action.onPrevious()"
        >
        </WtgIconButton>
        <div class="align-content-center text-md-strong-num">
            {{ action.indexCountText }}
        </div>
        <WtgIconButton
            :disabled="!action.hasNext"
            icon="$arrowRight"
            :aria-label="formatCaption('navigation.next')"
            variant="ghost"
            @click="action.onNext()"
        >
        </WtgIconButton>
    </div>
</template>

<script setup lang="ts">
import { useLocale } from '@composables/locale';
import WtgIconButton from '@components/WtgIconButton';
import { WtgFrameworkTask, WtgFrameworkEntityNavigation } from '@components/framework/types';
import { computed, PropType } from 'vue';

//
// Properties
//
const props = defineProps({
    task: { type: Object as PropType<WtgFrameworkTask>, default: undefined },
});

//
// Composables
//
const { formatCaption } = useLocale();

//
// Computed
//
const action = computed((): WtgFrameworkEntityNavigation => {
    return (
        props.task?.others ?? {
            visible: false,
            hasNext: false,
            hasPrevious: false,
            indexCountText: '',
            onNext: (): void => undefined,
            onPrevious: (): void => undefined,
        }
    );
});
</script>

<style lang="scss">
.wtg-entity-navigation {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: var(--s-padding-m);
    border: 1px solid var(--s-neutral-border-weak-default, '#6B6B6880');
    border-radius: var(--s-radius-s, 4px);
}
</style>
