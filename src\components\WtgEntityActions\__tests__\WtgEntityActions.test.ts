import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgEntityActions } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgEntityActions', () => {
    test('it passes the default slot content to be rendered', () => {
        const wrapper = mountComponent({
            slots: {
                default: 'Entity Actions Content',
            },
        });

        const slotContent = wrapper.find('.wtg-entity-actions');
        expect(slotContent.html().trim()).toContain('Entity Actions Content');
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgEntityActions as any, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
