<template>
    <div class="wtg-google-map" :style="computedMapStyles">
        <WtgProgressLinear v-if="loading" indeterminate class="wtg-google-map-loading"></WtgProgressLinear>
        <div ref="mapRef" style="width: 100%; height: 100%"></div>
        <WtgMenu v-if="activeMapCluster?.markers?.length" location="top center" open-on-hover close-delay="100">
            <template #activator="{ props: activatorProps }">
                <button
                    v-bind="activatorProps"
                    class="map-tooltip__activator"
                    :style="computedMapTooltipPosition"
                    @click="handleClusterClick"
                ></button>
            </template>
            <div class="map-tooltip">
                <div class="map-tooltip__content">
                    <WtgList class="map-tooltip__items overflow-y-auto pa-l">
                        <template v-for="(item, _index) in activeMapCluster?.markers" :key="item.title + _index">
                            <WtgListItem @click="handleClusterItemClick(item)">
                                {{ item.title }}
                            </WtgListItem>
                            <WtgListItem :type="'divider'" />
                        </template>
                    </WtgList>
                    <div class="map-tooltip__count">
                        {{ mapResultsFoundCaption }}
                    </div>
                </div>
            </div>
        </WtgMenu>
        <WtgCallout
            v-if="alertMessage"
            class="map-alert-message"
            dismissible
            :sentiment="WtgCalloutSentimentType.Warning"
            :description="alertMessage"
            @close="alertMessage = undefined"
        />
    </div>
</template>

<script setup lang="ts">
import WtgCallout, { WtgCalloutSentimentType } from '@components/WtgCallout';
import WtgList from '@components/WtgList';
import WtgListItem from '@components/WtgList/WtgListItem.vue';
import WtgMenu from '@components/WtgMenu';
import WtgProgressLinear from '@components/WtgProgressLinear';
import { useWtgUi } from '@composables/global';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import { useTheme } from '@composables/theme';
import { MarkerClusterer, MarkerClustererEvents } from '@googlemaps/markerclusterer';
import { computed, onBeforeUnmount, onMounted, ref, shallowRef, watch } from 'vue';
import { darkStyles } from './utils/darkStyles';
import loadMapsAsync from './utils/googleMapsLoader';
import { lightStyles } from './utils/lightStyles';
import { WtgGoogleMapRenderer } from './utils/WtgGoogleMapRenderer';

const props = defineProps({
    apiKey: {
        type: String,
        default: undefined,
    },
    bounds: {
        type: Object,
        default: undefined,
    },
    boundsPadding: {
        type: Number,
        default: undefined,
    },
    center: {
        type: Object,
        default: undefined,
    },
    circles: {
        type: Array,
        default: (): [] => [],
    },
    clusterer: {
        type: Object,
        default: undefined,
    },
    height: {
        type: String,
        default: '100px',
    },
    languageCode: {
        type: String,
        default: undefined,
    },
    markers: {
        type: Array,
        default: (): [] => [],
    },
    options: {
        type: Object,
        default: undefined,
    },
    polygons: {
        type: Array,
        default: (): [] => [],
    },
    polylines: {
        type: Array,
        default: (): [] => [],
    },
    rectangles: {
        type: Array,
        default: (): [] => [],
    },
    width: {
        type: String,
        default: '100%',
    },
    directionsRenderOptions: {
        type: Object,
        default: undefined,
    },
    directionsRequest: {
        type: Object,
        default: undefined,
    },
    zoom: {
        type: Number,
        default: undefined,
    },
    ...makeLayoutGridColumnProps(),
});

useLayoutGridColumn(props);

const emit = defineEmits<{
    'circle-click': [circle: any];
    'api-loaded': [];
    'marker-click': [marker: any];
    'cluster-click': [cluster: any];
    'polyline-click': [polyline: any];
    'polygon-click': [polygon: any];
    'rectangle-click': [rectangle: any];
    'bounds-changed': [bounds: any];
    'center-changed': [center: any];
    click: [evt: any];
    'zoom-changed': [zoom: any];
}>();
const { themeColors, darkMode } = useTheme();
const { formatCaption } = useLocale();
const wtgUi = useWtgUi();
let gClusterer: MarkerClusterer | undefined | null = undefined;
const gMap = shallowRef<undefined | any>();
const gCircles = ref<Array<any>>([]);
const gMarkers: Array<any> = [];
const gPolygons: Array<any> = [];
const gPolylines: Array<any> = [];
const gRectangles: Array<any> = [];
const gDirectionsService = ref<undefined | any>(undefined);
const gDirectionsRenderer = ref<undefined | any>(undefined);
const resizeObserver = ref<ResizeObserver | undefined>();
const newBoundsToApply = ref<boolean | undefined>();

const mapRef = ref<Element>();
const activeMapCluster = ref<undefined | any>();
const alertMessage = ref<undefined | string>();

const mapResultsFoundCaption = computed(() =>
    formatCaption('map.resultsFound', activeMapCluster?.value.markers?.length ?? 0)
);
const loading = computed<boolean>(() => {
    return !gMap.value;
});

const computedMapStyles = computed(() => ({
    height: `${props.height}`,
    width: `${props.width}`,
}));

const handleClusterClick = (): void => {
    if (!activeMapCluster.value?.markers) {
        return;
    }

    emit('cluster-click', {
        markers: activeMapCluster.value.markers,
        position: {
            lat: activeMapCluster.value.position.lat(),
            lng: activeMapCluster.value.position.lng(),
        },
    });
};

const handleClusterItemClick = (marker: any): void => {
    emit('marker-click', marker);
};

const computedMapTooltipPosition = computed(() => {
    if (!activeMapCluster.value?.markers) {
        return {};
    }

    const overlay = new window.google.maps.OverlayView();
    overlay.setMap(gMap.value);

    const anchorPixel = overlay.getProjection()?.fromLatLngToContainerPixel(activeMapCluster.value.position);

    const mapElement = (overlay.getMap() as any)?.getDiv();

    if (!anchorPixel || !mapElement) {
        return {};
    }

    const { offsetTop: mapOffsetTop, offsetLeft: mapOffsetLeft } = mapElement;

    const top = anchorPixel.y + mapOffsetTop;
    const left = anchorPixel.x + mapOffsetLeft;

    return {
        top: top + 'px',
        left: left + 'px',
    };
});

const resolvedOptions = computed<Record<string, any>>(() => {
    const result = props.options ? { ...props.options } : {};
    if (!result.styles) {
        result.styles = wtgUi.dark ? darkStyles : lightStyles;
    }
    return result;
});

const onResize = (): void => {
    if (isMapVisible() && newBoundsToApply.value) {
        applyBounds();
    }
};

const applyBounds = (): void => {
    if (gMap.value && props.bounds) {
        if (props.boundsPadding) {
            gMap.value.fitBounds(props.bounds, props.boundsPadding);
        } else {
            gMap.value.fitBounds(props.bounds);
        }
    }
    newBoundsToApply.value = false;
};

const applyCircles = (): void => {
    if (gMap.value) {
        gCircles?.value.forEach((circle: any): void => {
            circle.setMap(null);
        });
        gCircles.value = [];
        props.circles.forEach((circleOption: any): void => {
            const circle = new window.google.maps.Circle({
                ...circleOption,
                map: gMap.value,
            });
            circle.addListener('click', () => {
                emit('circle-click', circleOption);
            });
            gCircles.value.push(circle);
        });
    }
};

const onMapClusterEnter = (cluster: any) => {
    activeMapCluster.value = cluster;
};

const applyClusterer = (): void => {
    if (gMap.value) {
        if (gClusterer) {
            gClusterer.onRemove && gClusterer.onRemove();
            gClusterer = null;
        }

        if (props.clusterer) {
            let wtgKey = 0;
            const markerOptions = props.clusterer.markers?.map((markerOption: any): any => ({
                ...markerOption,
                wtgKey: wtgKey++,
            }));
            const gMarkers = markerOptions?.map((markerOption: any): any => {
                markerOption.icon =
                    markerOption.icon?.light && markerOption.icon?.dark
                        ? darkMode.value
                            ? markerOption.icon.light
                            : markerOption.icon.dark
                        : markerOption.icon;
                const marker = new window.google.maps.Marker(markerOption);
                marker.addListener('click', () => {
                    emit('marker-click', markerOption);
                });

                return marker;
            });

            const gClustererOptions = {
                ...props.clusterer,
                map: gMap.value,
                markers: gMarkers,
                renderer: new WtgGoogleMapRenderer(themeColors.value),
            };

            gClusterer = new MarkerClusterer(gClustererOptions);

            window.google.maps.event.addListener(gClusterer, MarkerClustererEvents.CLUSTERING_END, (clusterer: any) => {
                clusterer.clusters.forEach((cluster: any) => {
                    if (cluster.markers.length > 1) {
                        window.google.maps.event.addListener(cluster.marker, 'mouseover', () => {
                            onMapClusterEnter(cluster);
                        });
                    }
                });
            });
        }
    }
};

const applyDirectionsRequest = (): void => {
    if (gMap.value && props.directionsRequest) {
        gDirectionsService.value = gDirectionsService.value ?? new (window as any).google.maps.DirectionsService();
        gDirectionsRenderer.value = gDirectionsRenderer.value ?? new (window as any).google.maps.DirectionsRenderer();

        gDirectionsService.value.route(props.directionsRequest, (response: object, status: string) => {
            if (status === (window as any).google.maps.DirectionsStatus.OK) {
                gDirectionsRenderer.value.setDirections(response);
                gDirectionsRenderer.value.setMap(gMap.value);
            } else {
                alertMessage.value = formatCaption('map.routeByRoadNotDisplayed');
            }
        });
    }
};

const applyDirectionsRenderOptions = (): void => {
    if (gMap.value) {
        gDirectionsRenderer.value = gDirectionsRenderer.value ?? new (window as any).google.maps.DirectionsRenderer();
        gDirectionsRenderer.value.setOptions(props.directionsRenderOptions);
    }
};

const applyMarkers = () => {
    if (gMap.value) {
        gMarkers.forEach((marker) => {
            marker.setMap(null);
        });
        gMarkers.splice(0, gMarkers.length);
        props.markers.forEach(async (markerOption: any) => {
            const marker = new window.google.maps.Marker({
                ...markerOption,
                map: gMap.value,
            });

            marker.addListener('click', () => {
                emit('marker-click', markerOption);
            });
            gMarkers.push(marker);
        });
    }
};

const applyPolylines = () => {
    if (gMap.value) {
        gPolylines.forEach((polyline: any): void => {
            polyline.setMap(null);
        });
        gPolylines.splice(0, gPolylines.length);
        props.polylines.forEach((polylineOption: any): void => {
            const polyline = new window.google.maps.Polyline({
                ...polylineOption,
                map: gMap.value,
            });
            polyline.addListener('click', () => {
                emit('polyline-click', polylineOption);
            });
            gPolylines.push(polyline);
        });
    }
};

const applyPolygons = () => {
    if (gMap.value) {
        gPolygons.forEach((polygon: any): void => {
            polygon.setMap(null);
        });
        gPolygons.splice(0, gPolygons.length);
        props.polygons.forEach((polygonOption: any): void => {
            const polygon = new window.google.maps.Polygon({
                ...polygonOption,
                map: gMap.value,
            });
            polygon.addListener('click', () => {
                emit('polygon-click', polygonOption);
            });
            gPolygons.push(polygon);
        });
    }
};

const applyRectangles = () => {
    if (gMap.value) {
        gRectangles.forEach((rectangle: any): void => {
            rectangle.setMap(null);
        });
        gRectangles.splice(0, gRectangles.length);
        props.rectangles.forEach((rectangleOption: any): void => {
            const rectangle = new window.google.maps.Rectangle({
                ...rectangleOption,
                map: gMap.value,
            });
            rectangle.addListener('click', () => {
                emit('rectangle-click', rectangleOption);
            });
            gRectangles.push(rectangle);
        });
    }
};

const attachMapEvents = () => {
    if (!gMap.value) return;

    gMap.value.addListener('bounds_changed', () => {
        const bounds = gMap.value?.getBounds();
        if (bounds) {
            emit('bounds-changed', bounds);
        }
    });

    gMap.value.addListener('center_changed', () => {
        const center = gMap.value?.getCenter();
        if (center) {
            emit('center-changed', center);
        }
    });

    gMap.value.addListener('click', (evt: {}) => {
        emit('click', evt);
    });

    gMap.value.addListener('zoom_changed', () => {
        const zoom = gMap.value?.getZoom();
        if (typeof zoom === 'number') {
            emit('zoom-changed', zoom);
        }
    });
};

const setOptions = (): void => {
    if (gMap.value) {
        gMap.value.setOptions(resolvedOptions.value);
    }
};

const setMapAsync = async (): Promise<void> => {
    if (props.apiKey === undefined) {
        return Promise.resolve();
    }

    if (gMap.value) {
        return Promise.reject(new Error('Cannot load google maps more than once.'));
    }

    await loadMapsAsync(props.apiKey, undefined, props.languageCode);
    const $map = mapRef.value as HTMLElement;
    const options = {
        ...resolvedOptions.value,
        center: props.center ?? props.options?.center,
        zoom: props.zoom ?? props.options?.zoom,
    };
    gMap.value = new (window as any).google.maps.Map($map, options);
    applyBounds();
    applyCircles();
    applyClusterer();
    applyMarkers();
    applyPolylines();
    applyPolygons();
    applyRectangles();
    attachMapEvents();
    emit('api-loaded');
};

const isMapVisible = (): boolean => {
    const $map = mapRef.value as HTMLElement;
    return $map && $map.clientHeight > 0 && $map.clientWidth > 0;
};

const areSameBounds = (newBounds?: any, oldBounds?: any): boolean => {
    return (
        newBounds &&
        oldBounds &&
        newBounds.east === oldBounds.east &&
        newBounds.west === oldBounds.west &&
        newBounds.north === oldBounds.north &&
        newBounds.south === oldBounds.south
    );
};

watch(
    () => props.apiKey,
    () => {
        setMapAsync();
    }
);
watch(
    () => props.bounds,
    (newBounds, oldBounds) => {
        if (areSameBounds(newBounds, oldBounds)) {
            return;
        }
        newBoundsToApply.value = true;
        if (isMapVisible()) {
            applyBounds();
        }
    },
    { deep: true }
);
watch(
    () => props.center,
    () => {
        const center = props.center ?? props.options?.center;
        if (gMap.value && (gMap.value.getCenter().lat !== center?.lat || gMap.value.getCenter().lng !== center?.lng)) {
            gMap.value.setCenter(center);
        }
    }
);
watch(
    () => props.circles,
    () => {
        applyCircles();
    },
    { deep: true }
);

watch(
    () => props.clusterer,
    () => {
        applyClusterer();
    },
    { deep: true }
);

watch(
    () => props.options,
    () => {
        setOptions();
    },
    { deep: true }
);

watch(
    () => props.markers,
    () => {
        applyMarkers();
    },
    { deep: true }
);

watch(
    () => props.polygons,
    () => {
        applyPolygons();
    },
    { deep: true }
);

watch(
    () => props.polylines,
    () => {
        applyPolylines();
    },
    { deep: true }
);

watch(
    () => props.rectangles,
    () => {
        applyRectangles();
    },
    { deep: true }
);
watch(
    themeColors,
    () => {
        applyClusterer();
    },
    { deep: true }
);
watch(
    () => props.directionsRequest,
    () => {
        applyDirectionsRequest();
    },
    { deep: true }
);
watch(
    () => props.directionsRenderOptions,
    () => {
        applyDirectionsRenderOptions();
    },
    { deep: true }
);
watch(
    () => props.zoom,
    () => {
        const zoom = props.zoom ?? props.options?.zoom;
        if (gMap.value && gMap.value.getZoom() !== zoom) {
            gMap.value.setZoom(zoom);
        }
    }
);

onMounted(() => {
    const $map = mapRef.value as HTMLElement;
    resizeObserver.value = new ResizeObserver(onResize);
    resizeObserver.value.observe($map);

    setMapAsync();
});

onBeforeUnmount(() => {
    if (resizeObserver.value) {
        resizeObserver.value.unobserve(mapRef.value as HTMLElement);
    }
});
</script>

<style lang="scss">
.wtg-google-map {
    display: flex;
    position: relative;

    .wtg-google-map-loading {
        position: absolute;
    }
}

.map-tooltip__content {
    width: 300px;
    min-width: 180px;
    max-width: 500px;
}

.map-tooltip__items {
    max-height: 184px;
}

.map-tooltip__items > div:last-child {
    display: none;
}

.map-tooltip__count {
    line-height: var(--s-lineheight-400);
    padding: var(--s-padding-m) var(--s-padding-l);
    border-top: 1px solid var(--s-neutral-border-weak-default);
    background-color: var(--s-neutral-bg-disabled);
    color: var(--s-neutral-txt-disabled);
}

.map-tooltip__activator {
    position: absolute;
    height: 50px;
    width: 50px;
    translate: -50% -100%;
}

.map-alert-message {
    top: 0;
    right: 0;
    left: 0;
    width: 50%;
    margin-left: auto;
    margin-right: auto;
    position: absolute;
    display: flex;
    align-items: start;
    margin-top: 10px;
    justify-content: center;
}
</style>
