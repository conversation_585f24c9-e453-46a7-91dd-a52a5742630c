import {
    WtgFramework,
    WtgFrameworkMenuItemType,
    WtgFrameworkNotificationType,
    WtgFrameworkTask,
} from '@components/framework/types';
import WtgApp from '@components/WtgApp';
import { setApplication, tabsInfoInjectionKey } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount, VueWrapper } from '@vue/test-utils';
import { h, nextTick, reactive, ref } from 'vue';
import WtgUi from '../../../../../../../WtgUi';
import Masthead from '../Masthead.vue';
import MockSearchProvider from '@components/framework/types/__tests__/__mocks__/SearchProvider';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('wtg-masthead', () => {
    let el: HTMLElement;
    let application: WtgFramework;
    let currentTask: WtgFrameworkTask;
    const tabsInfo = ref();

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.pageHelp = { name: '' } as any;
        application.navDrawer = {
            visible: false,
            isRailActive: true,
            open: jest.fn(),
            close: jest.fn(),
        } as any;
        tabsInfo.value = {
            visible: false,
            tabs: [],
            current: 0,
        };
        currentTask = reactive(new WtgFrameworkTask());
        application.currentTask = currentTask;
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is WtgMasthead', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('Masthead');
    });

    test('it renders the masthead with aria-label attribute', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.attributes('aria-label')).toBe('Head');
    });

    test('it applies the wtg-typography class to ensure the (pseudo) breadcrumbs render with the Inter font in GLOW', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.classes('wtg-typography')).toBe(true);
    });

    test('it does not render a masthead if hideAppBar is true, (hideAppBar really means hideFrame)', async () => {
        application.hideAppBar = true;
        const wrapper = await mountComponentAsync();
        let WtgAppBar = wrapper.findComponent({ name: 'WtgMasthead' });
        expect(WtgAppBar.exists()).toBe(false);

        application.hideAppBar = false;
        await nextTick();
        WtgAppBar = wrapper.findComponent({ name: 'WtgMasthead' });
        expect(WtgAppBar.exists()).toBe(true);
    });

    test('it sets the correct props for the masthead to be part of the app framework, the navigation drawer (and the entity drawer) take precedence over masthead and footer', async () => {
        const wrapper = await mountComponentAsync();
        const masthead = wrapper.findComponent({ name: 'WtgMasthead' });
        expect(masthead.props('order')).toBe(3);
    });

    test('it renders a navigation icon on mobile', async () => {
        wtgUi.breakpoint.mdAndDown = false;
        let wrapper = await mountComponentAsync();
        let iconButtons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(iconButtons.length).toBe(1);
        wtgUi.breakpoint.mdAndDown = true;
        wrapper = await mountComponentAsync();
        iconButtons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(iconButtons?.length).toBe(2);
    });

    test('it renders masthead-title', async () => {
        const wrapper = await mountComponentAsync();
        const mastheadTitle = wrapper.findComponent({ name: 'MastheadTitle' });
        expect(mastheadTitle.exists()).toBe(true);
    });

    test('it renders masthead-creation-menu', async () => {
        const wrapper = await mountComponentAsync();
        const creationMenu = wrapper.findComponent({ name: 'MastheadCreationMenu' });
        expect(creationMenu.exists()).toBe(true);
    });

    test('it renders masthead-notifications', async () => {
        const wrapper = await mountComponentAsync();
        const notifications = wrapper.findComponent({ name: 'MastheadNotifications' });
        expect(notifications.exists()).toBe(true);
    });

    test('it renders a search icon button that opens the masthead search if a search provider exists', async () => {
        const wrapper = await mountComponentAsync();
        let search = wrapper.find("[data-testid='search-button']");
        expect(search.exists()).toBe(false);
        application.searchProvider = new MockSearchProvider();
        await nextTick();

        search = wrapper.find("[data-testid='search-button']");
        expect(search.exists()).toBe(true);
        search.trigger('click');
        await nextTick();
        const mastheadSearch = wrapper.findComponent({ name: 'MastheadSearch' });
        expect(mastheadSearch.props('overlay')).toBe(true);
    });

    test('it renders the extension when required', async () => {
        const wrapper = await mountComponentAsync();
        let extension = wrapper.findComponent({ name: 'MastheadExtension' });
        expect(extension.exists()).toBe(false);
        application.currentTask!.showTaskTitle = true;
        await nextTick();

        extension = wrapper.findComponent({ name: 'MastheadExtension' });
        expect(extension.exists()).toBe(true);
        application.currentTask!.showTaskTitle = false;
        application.currentTask!.showEDocsAction.visible = true;
        await nextTick();

        expect(extension.exists()).toBe(true);
        application.currentTask!.showTaskTitle = false;
        application.currentTask!.showEDocsAction.visible = false;
        tabsInfo.value.visible = true;
        await nextTick();

        extension = wrapper.findComponent({ name: 'MastheadExtension' });
        expect(extension.exists()).toBe(true);
    });
    test('it sets the height of the extension', async () => {
        const wrapper: VueWrapper<any> = await mountComponentAsync();
        expect(wrapper.vm.extensionHeight).toBe(48);
        tabsInfo.value.tabs.push('Tab 1');
        currentTask.showTaskTitle = true;
        await nextTick();

        expect(wrapper.vm.extensionHeight).toBe(88);
    });

    test('it sets the style of the nav button icon based on the screen size', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.findAllComponents({ name: 'WtgIconButton' }).at(0)?.vm.$attrs.style).toStrictEqual({
            color: 'var(--s-primary-icon-default)',
        });
        wtgUi.breakpoint.smAndUp = false;
        await nextTick();
        expect(wrapper.findAllComponents({ name: 'WtgIconButton' }).at(0)?.vm.$attrs.style).toStrictEqual({
            marginLeft: '8px',
        });
    });

    describe('when on mobile screen size', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(async () => {
            wtgUi.breakpoint.smAndUp = false;
            wrapper = await mountComponentAsync();
        });

        afterEach(() => {
            wtgUi.breakpoint.smAndUp = true;
        });

        test('it sets the height of the extension', () => {
            expect(wrapper.vm.extensionHeight).toBe(36);
        });

        test('it renders the extension when there are tabs', async () => {
            let extension = wrapper.findComponent({ name: 'MastheadExtension' });
            expect(extension.exists()).toBe(false);
            currentTask.showTaskTitle = true;
            currentTask.showEDocsAction.visible = false;
            tabsInfo.value!.visible = false;
            await nextTick();

            extension = wrapper.findComponent({ name: 'MastheadExtension' });
            expect(extension.exists()).toBe(false);
            currentTask.showTaskTitle = false;
            currentTask.showEDocsAction.visible = true;
            tabsInfo.value!.visible = false;
            await nextTick();

            expect(extension.exists()).toBe(false);
            currentTask.showTaskTitle = false;
            currentTask.showEDocsAction.visible = false;
            tabsInfo.value!.visible = true;
            await nextTick();

            extension = wrapper.findComponent({ name: 'MastheadExtension' });
            expect(extension.exists()).toBe(true);
        });

        describe('back button', () => {
            test('it does not render a back button if there is no task title to be consistent with responsive framework', async () => {
                currentTask.showTaskTitle = false;
                await nextTick();
                const backButton = wrapper.find("[data-testid='mobile-masthead-back-button']");
                expect(backButton.exists()).toBe(false);
            });

            test('it renders a back button if there is a task title', async () => {
                currentTask.showTaskTitle = true;
                await nextTick();
                const backButton = wrapper.find("[data-testid='mobile-masthead-back-button']");
                expect(backButton.exists()).toBe(true);
            });

            test('it does not render a back button if showBackButton is false', async () => {
                currentTask.showBackButton = false;
                currentTask.showTaskTitle = true;
                await wrapper.vm.$nextTick();
                const backButton = wrapper.find("[data-testid='mobile-masthead-back-button']");
                expect(backButton.exists()).toBe(false);
            });

            test('it does not render a back button if there is a task title but we are on the home page to be consistent with responsive framework', () => {
                currentTask.showTaskTitle = true;
                application.menu = [
                    {
                        active: true,
                        home: true,
                        id: 'home',
                        caption: 'Home',
                        action: WtgFrameworkMenuItemType.None,
                    },
                ];
                const backButton = wrapper.find("[data-testid='mobile-masthead-back-button']");
                expect(backButton.exists()).toBe(false);
                application.menu = [];
            });

            test('it has an aria-label attribute', async () => {
                currentTask.showTaskTitle = true;
                await nextTick();
                const backButton = wrapper.find("[data-testid='mobile-masthead-back-button']");
                expect(backButton.attributes('aria-label')).toBe('Back');
                currentTask.showTaskTitle = false;
            });

            test('it will call the cancel action onInvoke when clicked', async () => {
                currentTask.showTaskTitle = true;
                const temp = currentTask.cancelAction.onInvoke;
                currentTask.cancelAction.onInvoke = jest.fn();
                await nextTick();
                const backButton = wrapper.find("[data-testid='mobile-masthead-back-button']");
                expect(backButton.exists()).toBe(true);
                await backButton.trigger('click');

                expect(currentTask.cancelAction.onInvoke).toHaveBeenCalledTimes(1);
                currentTask.showTaskTitle = false;
                currentTask.cancelAction.onInvoke = temp;
            });

            test('it does not render a back button when application hideBackButton is true', async () => {
                application.hideBackButton = true;
                currentTask.showTaskTitle = true;
                await nextTick();
                const backButton = wrapper.find("[data-testid='mobile-masthead-back-button']");
                expect(backButton.exists()).toBe(false);
                application.hideBackButton = false;
            });
        });

        describe('mobile masthead menu', () => {
            test('it passes the current task to the mobile masthead menu', async () => {
                const notifications = [
                    {
                        id: '86c93025-ccad-4482-a136-aa454c60a888',
                        caption: 'Field Name 1',
                        propertyName: '',
                        text: 'This is the message text for 1',
                        type: WtgFrameworkNotificationType.Information,
                        requiresAcknowledgement: false,
                        isAcknowledged: false,
                        toggleIsAcknowledged: jest.fn(),
                    },
                    {
                        id: '8e86feef-9098-48e6-bc3f-ebcc6da4302d',
                        caption: 'Field Name 2',
                        propertyName: '',
                        text: 'This is the message text for 2',
                        type: WtgFrameworkNotificationType.Warning,
                        requiresAcknowledgement: false,
                        isAcknowledged: false,
                        toggleIsAcknowledged: jest.fn(),
                    },
                    {
                        id: 'f147f4b0-216f-4256-bc71-0874bdb7595c',
                        caption: 'Field Name 3',
                        propertyName: '',
                        text: 'This is the message text for 3',
                        type: WtgFrameworkNotificationType.MessageError,
                        requiresAcknowledgement: false,
                        isAcknowledged: false,
                        toggleIsAcknowledged: jest.fn(),
                    },
                    {
                        id: '6cfb18a7-8df6-4d2c-9daf-e1a5f256989c',
                        caption: 'Field Name 4',
                        propertyName: '',
                        text: 'This is the message text for 4',
                        type: WtgFrameworkNotificationType.Error,
                        requiresAcknowledgement: false,
                        isAcknowledged: false,
                        toggleIsAcknowledged: jest.fn(),
                    },
                ] as any;
                currentTask.notifications = notifications;
                currentTask.showNotifications = true;
                await nextTick();
                const mastheadMenu = wrapper.findComponent({ name: 'MobileMastheadMenu' });
                expect(mastheadMenu.props('task')).toEqual(currentTask);
                currentTask.notifications = [];
            });

            test('it toggles the search overlay when the search icon button in the mobile masthead menu is clicked', async () => {
                application.searchProvider = new MockSearchProvider();
                await nextTick();
                const mastheadMenu = wrapper.findComponent({ name: 'MobileMastheadMenu' });
                mastheadMenu.findComponent({ name: 'WtgIconButton' }).trigger('click');
                await nextTick();
                expect(wrapper.findComponent({ name: 'MastheadSearch' }).props('overlay')).toBe(true);
            });
        });

        test('it renders a hamburger menu icon button that opens the navigation drawer when clicked', async () => {
            const navButton = wrapper.findAllComponents({ name: 'WtgIconButton' }).at(0);
            expect(navButton!.props('icon')).toBe('s-icon-menu-hamburger');
            expect(application.navDrawer.open).toHaveBeenCalledTimes(0);
            navButton?.trigger('click');
            expect(application.navDrawer.open).toHaveBeenCalledTimes(1);
        });
    });

    test('it toggles visibility of masthead notifications based on showNotifications in the task', async () => {
        const wrapper = await mountComponentAsync();
        let notifications = wrapper.findComponent({ name: 'MastheadNotifications' });

        expect(application.currentTask!.showNotifications).toBe(false);
        expect(notifications.exists()).toBe(true);
        expect((notifications.element as HTMLElement).style.display).toBe('none');

        application.currentTask!.showNotifications = true;
        await nextTick();

        notifications = wrapper.findComponent({ name: 'MastheadNotifications' });
        expect(notifications.exists()).toBe(true);
        expect((notifications.element as HTMLElement).style.display).toBe('');

        application.currentTask!.showNotifications = false;
        await nextTick();

        expect(notifications.exists()).toBe(true);
        expect((notifications.element as HTMLElement).style.display).toBe('none');
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(Masthead) } } = {}) {
        const wrapper = mount(WtgApp, {
            provide: {
                [tabsInfoInjectionKey]: tabsInfo,
            },
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(Masthead);
    }
});
