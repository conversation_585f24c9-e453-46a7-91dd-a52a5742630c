<template>
    <WtgContainer class="text-center d-flex flex-grow-1 justify-center flex-column">
        <WtgLabel v-if="label" class="text-left">
            {{ label }}
        </WtgLabel>
        <WtgProgressCircular v-if="isLoading && !showUnsupported" indeterminate style="margin: auto" />
        <template v-if="objectUrl && !unsupported">
            <WtgImage
                v-if="canShowImage"
                :src="objectUrl"
                :contain="true"
                max-height="100%"
                @error="error"
                @load="load"
            />
            <iframe
                v-if="canShowInIframe"
                v-show="!isLoading"
                :src="objectUrl + '#toolbar=0&navpanes=0'"
                style="min-height: 100%"
                @error="error"
                @load="load"
            />
            <WtgTifViewer
                v-if="canShowTif"
                :src="objectUrl"
                @tif-decode-error="tifDecodeError"
                @error="error"
                @load="load"
            />
        </template>
        <WtgBox
            v-if="showUnsupported"
            layout="flex"
            flex-align="align-center"
            flex-direction="flex-column"
            class="text-center"
        >
            <WtgIcon size="m" color="primary" icon="s-icon-file" />
            <WtgLabel>Preview is not supported</WtgLabel>
        </WtgBox>
        <WtgBox v-if="showDocumentNotAvailable" class="d-flex flex-column justify-center text-center">
            <WtgIcon size="m" color="primary" icon="s-icon-hide" />
            <WtgLabel>No document available</WtgLabel>
        </WtgBox>
        <WtgCallout v-if="errorMessages.length > 0" class="d-block mt-4" :sentiment="WtgCalloutSentimentType.Critical">
            <WtgLabel v-for="message in errorMessages" :key="message" class="d-block">
                {{ message }}
            </WtgLabel>
        </WtgCallout>
    </WtgContainer>
</template>

<script setup lang="ts">
import { WtgBox } from '@components/WtgBox';
import WtgCallout, { WtgCalloutSentimentType } from '@components/WtgCallout';
import { WtgContainer } from '@components/WtgContainer';
import { WtgIcon } from '@components/WtgIcon';
import { WtgImage } from '@components/WtgImage';
import { WtgLabel } from '@components/WtgLabel';
import { WtgProgressCircular } from '@components/WtgProgressCircular';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { makeRestrictedProps } from '@composables/restricted';
import { computed, onBeforeUnmount, PropType, ref, watch } from 'vue';
import { DisposableUrl, UrlProvider } from './types';
import WtgTifViewer from './WtgTifViewer.vue';

//
// Properties
//
const props = defineProps({
    errorMessages: {
        type: Array as PropType<string[]>,
        default: (): string[] => [],
    },
    urlProvider: {
        type: Object as PropType<UrlProvider>,
        default: undefined,
    },
    src: {
        type: String,
        default: '',
    },
    fileExtension: {
        type: String,
        default: '',
    },
    label: {
        type: String,
        default: undefined,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    ...makeLayoutGridColumnProps(),
    ...makeRestrictedProps(),
});

//
// State
//
const supportedImageFormats = ['BMP', 'GIF', 'JPG', 'JPEG', 'PNG'];
const supportedIframeFormats = ['TXT', 'PDF'];
const unsupported = ref(false);
const loadingSource = ref(false);
const objectUrl = ref('');
const showTifAsImage = ref(false);
const disposableUrl = ref(undefined as DisposableUrl | undefined);

//
// Composables
//
useLayoutGridColumn(props);

//
// Computed
//
const canShowImage = computed((): boolean => {
    return supportedImageFormats.includes(props.fileExtension) || showTifAsImage.value;
});
const canShowInIframe = computed((): boolean => {
    return supportedIframeFormats.includes(props.fileExtension);
});
const canShowTif = computed((): boolean => {
    return props.fileExtension === 'TIF' && !showTifAsImage.value;
});
const showUnsupported = computed((): boolean => {
    return (!!props.src && !canShowImage.value && !canShowTif.value && !canShowInIframe.value) || unsupported.value;
});
const isLoading = computed((): boolean => {
    return props.loading || loadingSource.value;
});
const showDocumentNotAvailable = computed((): boolean => {
    return !props.src && !unsupported.value && !isLoading.value;
});

//
// Watchers
//
watch(
    () => props.src,
    async () => {
        objectUrl.value = '';
        unsupported.value = false;
        showTifAsImage.value = false;

        if (canShowImage.value || canShowInIframe.value || canShowTif.value) {
            loadingSource.value = true;
            if (props.urlProvider) {
                if (disposableUrl.value?.dispose) {
                    disposableUrl.value.dispose();
                }
                try {
                    disposableUrl.value = await props.urlProvider.getUrlAsync(props.src, props.fileExtension);
                    if (disposableUrl.value) {
                        props.fileExtension === 'PDF'
                            ? (objectUrl.value = disposableUrl.value.url.split('?file=')[1])
                            : (objectUrl.value = disposableUrl.value.url);
                    } else {
                        error();
                    }
                } catch (err) {
                    error();
                }
            } else {
                objectUrl.value = props.src;
            }
        }
    },
    { immediate: true }
);

//
// Helpers
//
const error = (): void => {
    unsupported.value = true;
    loadingSource.value = false;
};
const load = (): void => {
    loadingSource.value = false;
};
const tifDecodeError = (): void => {
    showTifAsImage.value = true;
};

//
// Lifecycle
//
onBeforeUnmount(() => {
    if (disposableUrl.value?.dispose) {
        disposableUrl.value.dispose();
    }
});
</script>
