import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import hyperlinkDefault from '../../../assets/WtgHyperlink/Hyperlink-default.png';
import hyperlinkDoExample from '../../../assets/WtgHyperlink/hyperlink-do-example.png';
import hyperlinkDoExample2 from '../../../assets/WtgHyperlink/hyperlink-do-example2.png';
import hyperlinkDontExample from '../../../assets/WtgHyperlink/hyperlink-dont-example.png';
import hyperlinkDontExample2 from '../../../assets/WtgHyperlink/hyperlink-dont-example2.png';
import hyperlinkFocus from '../../../assets/WtgHyperlink/Hyperlink-focus.png';
import hyperlinkHover from '../../../assets/WtgHyperlink/hyperlink-hover.png';
import * as WtgHyperlink from './WtgHyperlink.stories.ts';

<Meta of={WtgHyperlink} />

<div className="component-header">
    <h1>Hyperlink</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-2947&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    A hyperlink is interactive text that lets users navigate to a new page or anchor location, changing the URL.
</p>

## API

<Canvas className="canvas-preview" of={WtgHyperlink.Default} />
<Controls of={WtgHyperlink.Default} sort={'alpha'} />

## How to use

<div className="do-dont-pair">

     <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-do">
            <img src={hyperlinkDoExample}  />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ✔️ <strong>Do</strong>
            </p>
            <p>Use hyperlinks for inline, lower-priority navigation or references within text.</p>
        </div>
    </div>
    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-dont">
            <img src={hyperlinkDontExample} />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ❌ <strong>Don't</strong>
            </p>
            <p>Use hyperlinks for primary or secondary actions - see [buttons](/docs/components-button--docs) instead.</p>
        </div>
    </div>

    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-do">
            <img src={hyperlinkDoExample2}  />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ✔️ <strong>Do</strong>
            </p>
            <p>Provide descriptive link text that clearly indicates the destination or purpose.</p>
        </div>
    </div>
    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-dont">
            <img src={hyperlinkDontExample2} />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ❌ <strong>Don't</strong>
            </p>
            <p>Be vague or ambiguous about where the link leads.</p>
        </div>

    </div>

</div>

## Behavior

<p>
    <strong>Inline hyperlink:</strong> Intergrated into body text.
</p>
<p>
    <strong>Standalone hyperlink:</strong> Used outside of body copy, standalone hyperlinks appear in callouts, toasts,
    and other contexts.
</p>

### States

<table width="100%" className="component-summary-table">
    <thead>
        <tr>
            <th>State</th>
            <th>Description</th>
            
           
        </tr>
    </thead>
    <tbody>

           <tr>
        <td>
                <p>Default</p><img srcSet={`${hyperlinkDefault} 3x`} />
            </td>
            <td style={{ width: '33%' }}>
                <p>Link in its standard style.</p>
            </td>
        </tr>

        <tr>
        <td>
                <p>Focus</p><img srcSet={`${hyperlinkHover} 3x`} />
            </td>
            <td>
                <p> Lets the user know that the link is interactive.</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>Focus</p><img srcSet={`${hyperlinkFocus} 3x`} />
            </td>
            <td>
                <p>When link is accessed via keyboard (e.g. tab)</p>
            </td>
        </tr>


    </tbody>

</table>

## Content guidelines

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview).

-   Hyperlink text should clearly convey the action or destination without relying on surrounding context, making it easy to scan for the correct click target.
-   For example, in the sentence "Learn more about imported shipments," the hyperlink should be on "imported shipments" rather than "learn more."
-   Never use "click here" in hyperlinks, buttons, or anywhere in the interface.

## Related components

-   [Button](/docs/components-button--docs)
-   [Icon button](/docs/components-icon-button--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
