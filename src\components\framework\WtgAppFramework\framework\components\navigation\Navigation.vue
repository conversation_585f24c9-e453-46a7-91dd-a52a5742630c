<!-- eslint-disable vue/multi-word-component-names -->
<template>
    <WtgNavigation
        v-if="!application.hideAppBar"
        v-model="application.navDrawer.visible"
        v-model:collapsed="application.navDrawer.isRailActive"
        :drawer-aria-label="application.ariaLabels.navigationDrawer"
        :title-aria-label="application.ariaLabels.applicationTitle + ' ' + application.title"
        :order="1"
        :width="width"
    >
        <div v-if="isTabletOrMobile" class="d-flex flex-column fill-height">
            <NavigationMenu @item-click="onClick()" />
            <WtgSpacer />
            <NavigationFooter @item-click="onClick()" />
        </div>
        <NavigationMenu v-if="!isTabletOrMobile" @group-click="onGroupClick" @item-click="onClick()" />
        <template v-if="!isTabletOrMobile" #append>
            <NavigationFooter @item-click="onClick()" />
        </template>
    </WtgNavigation>
</template>

<script setup lang="ts">
import WtgNavigation from '@components/WtgNavigation';
import WtgSpacer from '@components/WtgSpacer';
import { useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { computed, onBeforeMount } from 'vue';
import NavigationFooter from './NavigationFooter.vue';
import NavigationMenu from './NavigationMenu.vue';

//
// Composables
//
const { isTabletOrMobile } = useFramework();
const application = useApplication();

//
// Computed
//
const width = computed((): number => {
    return isTabletOrMobile.value ? 328 : 270;
});

//
// Event Handlers
//
function onClick(): void {
    if (isTabletOrMobile.value) {
        application.navDrawer.close();
    }
}

function onGroupClick(): void {
    if (application.navDrawer.isRailActive) {
        application.navDrawer.isRailActive = false;
    }
}

//
// Lifecycle
//
onBeforeMount(() => {
    if (!isTabletOrMobile.value) {
        application.navDrawer.visible = true;
    }
});
</script>
