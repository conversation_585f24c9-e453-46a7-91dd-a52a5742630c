import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgDivider from './WtgDivider.stories.ts';

<Meta of={WtgDivider} />

<div className="component-header">
    <h1>Divider</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img>[Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-16352&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    Dividers are non-interactive elements used to create visual separation within content. They help establish structure by allowing a break within a single cohesive section.

</p>

## API

<Canvas className="canvas-preview" of={WtgDivider.Default} />
<Controls of={WtgDivider.Default} sort={'alpha'} />

## How to use

### ✅ Do

-   Use the divider intentionally and sparingly to create visual separation.
-   Provide adequate spacing between content and dividers to enhance visual hierarchy, making it easier for users to scan information.

### ❌ Don't

-   Use the divider excessively to try and force hierarchy.
-   Place dividers too close to content, reducing their effectiveness in establishing hierarchy and creating clear visual distinctions.

## Behavior

### Direction

Dividers can be oriented horizontally or vertically, depending on the layout and design requirements.

<Canvas sourceState="none" of={WtgDivider.Direction} />

### Solid vs dashed

While there is no universal rule for choosing between dashed and solid dividers, dashed dividers are typically used to create a softer separation within a section, whereas solid dividers suggest a more definitive boundary.

<Canvas sourceState="none" of={WtgDivider.DashedVsSolid} />

## Accessibility

### Decorative Property

Use the decorative property when needed to indicate that a component is purely decorative. When set to `true`, accessibility-related attributes are adjusted to remove the rendered element from the accessibility tree, ensuring it does not interfere with assistive technologies

## Related components

-   [Content splitter](/docs/components-content-splitter--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
