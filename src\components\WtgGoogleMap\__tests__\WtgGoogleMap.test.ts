class MockGoogleObject {
    readonly listeners: Record<string, Function>;
    readonly options: any;

    constructor($map?: any, options?: any) {
        if (options) {
            this.options = options;
        }
        this.listeners = {};
    }
    addListener(name: string, callBack: Function) {
        this.listeners[name] = callBack;
    }
    mock(name: string) {
        this.listeners[name]();
    }
}

class Map extends MockGoogleObject {
    public fitBounds = jest.fn();
    public getBounds = jest.fn();
    public getCenter = jest.fn();
    public getZoom = jest.fn();
    public setOptions = jest.fn();
    public setCenter = jest.fn();
    public setZoom = jest.fn();
    public getDiv = jest.fn(() => {
        return {
            offsetTop: 0,
            offsetLeft: 0,
            offsetWidth: 1400,
        };
    });
}

class Circle extends MockGoogleObject {}
class Marker extends MockGoogleObject {}
class Polyline extends MockGoogleObject {}
class Polygon extends MockGoogleObject {}
class Rectangle extends MockGoogleObject {}

const clusterer: any = {
    marker: {
        position: { lat: -33.916400249771485, lng: 151.19477471328673 },
    },
    markers: [
        {
            position: { lat: -33.916400249771485, lng: 151.19477471328673 },
            title: 'Wisetech Global Sydney',
        },
        {
            position: { lat: 42.04231, lng: -88.05361 },
            title: 'Wisetech Global Chicago',
        },
    ],
};
class MockMarkerClusterer extends MockGoogleObject {
    readonly args: Record<string, any>;
    readonly clusters = [clusterer];

    constructor(args: Record<string, any>) {
        super({});
        this.args = args;
    }
}

const mockFromLatLngToContainerPixel = jest.fn();
class MockProjection {
    fromLatLngToContainerPixel = mockFromLatLngToContainerPixel;
}

class MockOverlayView {
    public getProjection = jest.fn(() => {
        return new MockProjection();
    });
    public setMap = jest.fn();
    public getMap = jest.fn(() => {
        return new Map(undefined);
    });
}

const mockRoute = jest.fn();
class MockDirectionsService {
    public route = mockRoute;
}

class MockDirectionsRenderer {
    public setDirections = jest.fn();
    public setMap = jest.fn();
    public setOptions = jest.fn();
}

enum DirectionsStatus {
    OK = 'OK',
    ZERO_RESULTS = 'ZERO_RESULTS',
}

jest.mock('../utils/googleMapsLoader', () => jest.fn());

jest.mock('@googlemaps/markerclusterer', () => {
    return {
        ...jest.requireActual('@googlemaps/markerclusterer'),
        MarkerClusterer: jest.fn().mockImplementation((args) => new MockMarkerClusterer(args)),
    };
});

jest.mock('../utils/WtgGoogleMapRenderer');

global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

import WtgCallout from '@components/WtgCallout';
import WtgGoogleMap from '@components/WtgGoogleMap/WtgGoogleMap.vue';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import WtgMenu from '@components/WtgMenu';
import { WtgProgressLinear } from '@components/WtgProgressLinear';
import { useLocale } from '@composables/locale';
import { event } from '@googlemaps/jest-mocks';
import { VueWrapper, flushPromises, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../WtgUi';
import loadMapsAsync from '../utils/googleMapsLoader';
import { lightStyles } from '../utils/lightStyles';
import { WtgGoogleMapRenderer } from '../utils/WtgGoogleMapRenderer';

const wtgUi = new WtgUi();

const apiKey = 'yourapikeyvalue';

const circles: any = [
    {
        strokeColor: '#FF0000',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#FF0000',
        fillOpacity: 0.35,
        center: { lat: -33.863160333699874, lng: 151.20741034156273 },
        radius: 90000,
    },
];

const bounds: any = {
    north: 37.7749, // The northernmost latitude
    south: 37.7049, // The southernmost latitude
    east: -122.4194, // The easternmost longitude
    west: -122.5194, // The westernmost longitude
};

const center: any = {
    lat: -34.397,
    lng: 150.644,
};

const zoom = 8;

const options: any = {
    center: {
        lat: -34.397,
        lng: 150.644,
    },
    zoom: 8,
};

const markers: Array<any> = [
    {
        position: { lat: -33.916400249771485, lng: 151.19477471328673 },
        title: 'Wisetech Global',
    },
];

const polylines: Array<any> = [
    {
        geodesic: true,
        strokeColor: '#FF0000',
        strokeOpacity: 1.0,
        strokeWeight: 2,
        path: [
            { lat: 37.772, lng: -122.214 },
            { lat: 21.291, lng: -157.821 },
            { lat: -18.142, lng: 178.431 },
            { lat: -27.467, lng: 153.027 },
        ],
    },
];

const polygons: Array<any> = [
    {
        paths: [
            { lat: 25.774, lng: -80.19 },
            { lat: 18.466, lng: -66.118 },
            { lat: 32.321, lng: -64.757 },
            { lat: 25.774, lng: -80.19 },
        ],
        strokeColor: '#FF0000',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#FF0000',
        fillOpacity: 0.35,
    },
];

const rectangles: Array<any> = [
    {
        strokeColor: '#371ee1',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#371ee1',
        fillOpacity: 0.35,
        bounds: {
            north: -34.87852266601754,
            south: -35.69774540756135,
            east: 149.5523404102291,
            west: 148.6809423717045,
        },
    },
];

describe('WtgGoogleMap', () => {
    beforeEach(() => {
        (loadMapsAsync as jest.Mock).mockImplementationOnce(() => {
            window.google = {
                maps: {
                    Circle,
                    Map,
                    Marker,
                    Polygon,
                    Polyline,
                    Rectangle,
                    OverlayView: MockOverlayView,
                    event,
                    DirectionsService: MockDirectionsService,
                    DirectionsStatus,
                    DirectionsRenderer: MockDirectionsRenderer,
                },
            } as any;

            return Promise.resolve();
        });
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    test('it renders a div to contain the map', () => {
        const propsData = {};
        const wrapper = mountComponent({ propsData });

        const mapDiv = wrapper.vm.$refs['mapRef'] as HTMLElement;
        expect(mapDiv.tagName).toBe('DIV');
    });

    test('it renders a wtg-callout to contain the error message', async () => {
        const propsData = {};
        const messageToDisplay = 'Test alert message';
        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        wrapper.vm.alertMessage = messageToDisplay;
        await wrapper.vm.$nextTick();

        const messageWrapper = wrapper.findComponent(WtgCallout);
        expect(messageWrapper.exists()).toBe(true);
        expect(messageWrapper.text()).toBe(messageToDisplay);
    });

    test('emits a marker-click event with the correct marker when a popover list item is clicked', () => {
        const propsData: any = {
            apiKey,
            clusterer,
        };
        const wrapper = mountComponent({ propsData });
        const selectedMarker = clusterer.markers[0];

        wrapper.vm.handleClusterItemClick(selectedMarker);

        expect(wrapper.emitted('marker-click')).toBeTruthy();
        const emittedEvents = wrapper.emitted('marker-click');
        expect(emittedEvents && emittedEvents[0]).toEqual([selectedMarker]);
    });

    describe('apiKey', () => {
        let propsData: Record<string, any>;

        describe('when set initially', () => {
            beforeEach(() => {
                propsData = {
                    apiKey,
                };
            });

            test('when it mounts the loadMapsAsync function is called with an apiKey & modules', async () => {
                const wrapper = mountComponent({ propsData });
                await wrapper.vm.$nextTick();

                expect(loadMapsAsync).toHaveBeenCalledWith(apiKey, undefined, undefined);
            });

            test('when the loading promise resolves, a google map is created', async () => {
                const wrapper = mountComponent({ propsData });
                await wrapper.vm.$nextTick();

                expect(wrapper.vm.gMap).toBeInstanceOf(window.google.maps.Map);
            });

            test('when the loading promise resolves, an api-loaded event is emitted', async () => {
                const wrapper = mountComponent({ propsData });

                await wrapper.vm.$nextTick();
                expect(wrapper.emitted()['api-loaded']!.length).toBe(1);
            });

            test('displays loading indicator until google maps is loaded', async () => {
                const wrapper = mountComponent({ propsData });
                const progress = wrapper.findComponent(WtgProgressLinear);
                expect(progress.exists()).toBe(true);

                await flushPromises();
                await wrapper.vm.$nextTick();
                expect(progress.exists()).toBe(false);
            });

            test('throws error if map is loaded a second time', async () => {
                const wrapper = mountComponent({ propsData });
                await wrapper.vm.$nextTick();

                await expect(wrapper.vm.setMapAsync()).rejects.toThrow('Cannot load google maps more than once.');
            });
        });

        describe('when set dynamically', () => {
            beforeEach(() => {
                propsData = {};
            });

            test('when it mounts the loadMapsAsync function is called with an apiKey & modules', async () => {
                const wrapper = mountComponent({ propsData });
                await wrapper.vm.$nextTick();

                await wrapper.setProps({ apiKey });
                await wrapper.vm.$nextTick();

                expect(loadMapsAsync).toHaveBeenCalledWith(apiKey, undefined, undefined);
            });

            test('when the loading promise resolves, a google map is created', async () => {
                const wrapper = mountComponent({ propsData });
                await wrapper.vm.$nextTick();

                await wrapper.setProps({ apiKey });
                await wrapper.vm.$nextTick();

                expect(wrapper.vm.gMap).toBeInstanceOf(window.google.maps.Map);
            });

            test('when the loading promise resolves, an api-loaded event is emitted', async () => {
                const wrapper = mountComponent({ propsData });

                await wrapper.vm.$nextTick();

                await wrapper.setProps({ apiKey });
                await wrapper.vm.$nextTick();

                expect(wrapper.emitted()['api-loaded']!.length).toBe(1);
            });

            test('displays loading indicator until apiKey is set and google maps is loaded', async () => {
                const wrapper = mountComponent({ propsData });
                const progress = wrapper.findComponent(WtgProgressLinear);
                expect(progress.exists()).toBe(true);

                await wrapper.setProps({ apiKey });
                await wrapper.vm.$nextTick();
                expect(progress.exists()).toBe(false);
            });

            test('throws error if map is loaded a second time', async () => {
                const wrapper = mountComponent({ propsData });
                await wrapper.vm.$nextTick();

                await wrapper.setProps({ apiKey });

                await expect(wrapper.vm.setMapAsync()).rejects.toThrow('Cannot load google maps more than once.');
            });
        });
    });

    describe('resizeObserver', () => {
        let propsData: Record<string, any>;

        beforeEach(() => {
            propsData = {
                apiKey,
            };
        });

        test('Resize observer setup when mount component', async () => {
            const wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();

            expect(wrapper.vm.resizeObserver.observe).toHaveBeenCalled();
        });
    });

    describe('when the options do not specify styles', () => {
        let newMapOptions: any, wrapper: VueWrapper<any>;

        beforeEach(async () => {
            const propsData: any = {
                apiKey,
                center,
                options,
                zoom,
            };
            newMapOptions = options ? { ...options } : {};
            newMapOptions.zoom = 10;

            wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();

            jest.spyOn(wrapper.vm.gMap, 'setOptions');
        });

        test('and we are in a light theme, we pass lightStyles array into setOptions', async () => {
            await wrapper.setProps({ options: newMapOptions });

            const expected = { ...newMapOptions, styles: lightStyles };
            expect(wrapper.vm.gMap.setOptions).toHaveBeenCalledWith(expected);
        });
    });

    describe('map tooltip', () => {
        const eventCallbacks: Record<string, any> = {};
        const propsData: any = {
            apiKey,
            clusterer,
        };

        let wrapper: VueWrapper<any>;
        let markerToTrigger: any;

        beforeEach(() => {
            (event.addListener as any).mockImplementation((component: any, name: string, callback: any) => {
                if (name === 'clusteringend') {
                    callback(component);
                    return;
                }

                eventCallbacks[name] = callback;
                markerToTrigger = component;
            });
        });

        test('if there are no markers, the tooltip is not rendered', async () => {
            const clusters: any = {
                marker: {},
                markers: [],
            };
            const propsData: any = {
                apiKey,
                clusters,
            };
            const wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();
            const tooltip = wrapper.findComponent(WtgMenu);
            expect(tooltip.exists()).toBe(false);
        });

        test('it renders an element to contain the map tooltip', async () => {
            const wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();
            eventCallbacks['mouseover'](markerToTrigger);
            await wrapper.vm.$nextTick();

            const tooltip = wrapper.findComponent(WtgMenu);
            expect(tooltip.exists()).toBe(true);
        });

        test('it moves the activator to the same place of the cluster when hovering', async () => {
            wrapper = mountComponent({ propsData });

            const clusterPosition = { x: 100, y: 200 };
            mockFromLatLngToContainerPixel.mockReturnValue(clusterPosition);
            await wrapper.vm.$nextTick();

            eventCallbacks['mouseover'](markerToTrigger);
            await wrapper.vm.$nextTick();

            const computedPosition = wrapper.vm.computedMapTooltipPosition;
            const expectedPosition = {
                left: `${clusterPosition.x}px`,
                top: `${clusterPosition.y}px`,
            };

            expect(computedPosition).toEqual(expectedPosition);
        });

        test('it should add the tooltip content to activeMapCluster when hovering', async () => {
            wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();

            eventCallbacks['mouseover'](markerToTrigger);
            await wrapper.vm.$nextTick();

            const activeMapCluster = wrapper.vm.activeMapCluster;
            expect(activeMapCluster).toEqual(clusterer);
        });

        test('it should emit a cluster click event when the activator is clicked', async () => {
            const wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();

            wrapper.vm.activeMapCluster = {
                count: 2,
                markers: clusterer.markers,
                position: { lat: () => -33.863160333699874, lng: () => 151.20741034156273 },
            };

            await wrapper.vm.handleClusterClick();

            const params: any = wrapper.emitted()['cluster-click']![0];
            expect(params[0].markers.length).toEqual(2);
            expect(params[0].markers[0].title).toEqual('Wisetech Global Sydney');
            expect(params[0].markers[1].title).toEqual('Wisetech Global Chicago');
            expect(params[0].position).toEqual({ lat: -33.863160333699874, lng: 151.20741034156273 });
        });
    });

    test('when bounds are initially set fitBounds is called', async () => {
        const bounds = {
            east: 100.5,
            north: 42.8,
            south: -33.3,
            west: 1.0,
        };
        const propsData: any = {
            apiKey,
            center,
            options,
            bounds,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.gMap.fitBounds).toHaveBeenCalledWith(bounds);
    });

    test('when bounds prop change, but new value is the same as old value, newBoundsToApply does not change to true', async () => {
        const bounds = {
            east: 100.5,
            north: 42.8,
            south: -33.3,
            west: 1.0,
        };

        const propsData: any = {
            apiKey,
            center,
            options,
            bounds,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const newBounds = {
            east: 100.5,
            north: 42.8,
            south: -33.3,
            west: 1.0,
        };

        await wrapper.setProps({ bounds: newBounds });

        expect(wrapper.vm.newBoundsToApply).not.toBeTruthy();
    });

    test('when bounds prop changes, newBoundsToApply flag set to true', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const newBounds = {
            east: 100.5,
            north: 42.8,
            south: -33.3,
            west: 1.0,
        };

        await wrapper.setProps({ bounds: newBounds });

        expect(wrapper.vm.newBoundsToApply).toBeTruthy();
    });

    test('when bounds changes, map width or height are 0, fitBounds is not called', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const newBounds = {
            east: 100.5,
            north: 42.8,
            south: -33.3,
            west: 1.0,
        };
        jest.spyOn(wrapper.vm.$refs.mapRef, 'clientHeight', 'get').mockImplementation(() => 10);
        jest.spyOn(wrapper.vm.$refs.mapRef, 'clientWidth', 'get').mockImplementation(() => 0);

        await wrapper.setProps({ bounds: newBounds });

        expect(wrapper.vm.gMap.fitBounds).not.toHaveBeenCalled();

        const newBounds2 = {
            east: 100.5,
            north: 42.8,
            south: -33.3,
            west: 2.0,
        };

        jest.spyOn(wrapper.vm.$refs.mapRef, 'clientHeight', 'get').mockImplementation(() => 10);
        jest.spyOn(wrapper.vm.$refs.mapRef, 'clientWidth', 'get').mockImplementation(() => 0);

        await wrapper.setProps({ bounds: newBounds2 });

        expect(wrapper.vm.gMap.fitBounds).not.toHaveBeenCalled();
    });

    test('when bounds changes, map width and height are not 0, fitBounds is called and newBoundsToApply flag is set to false', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const newBounds = {
            east: 100.5,
            north: 42.8,
            south: -33.3,
            west: 1.0,
        };
        jest.spyOn(wrapper.vm.$refs.mapRef, 'clientHeight', 'get').mockImplementation(() => 10);
        jest.spyOn(wrapper.vm.$refs.mapRef, 'clientWidth', 'get').mockImplementation(() => 10);

        await wrapper.setProps({ bounds: newBounds });

        expect(wrapper.vm.gMap.fitBounds).toHaveBeenCalledWith(newBounds);
        expect(wrapper.vm.newBoundsToApply).toBe(false);
    });

    test('when boundsPadding is set, fitBounds is call with padding parameter', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const newBounds = {
            east: 100.5,
            north: 42.8,
            south: -33.3,
            west: 1.0,
        };

        jest.spyOn(wrapper.vm.$refs.mapRef, 'clientHeight', 'get').mockImplementation(() => 10);
        jest.spyOn(wrapper.vm.$refs.mapRef, 'clientWidth', 'get').mockImplementation(() => 10);

        await wrapper.setProps({ boundsPadding: 40 });
        await wrapper.setProps({ bounds: newBounds });

        expect(wrapper.vm.gMap.fitBounds).toHaveBeenCalledWith(newBounds, 40);
    });

    test('when the mapMarkers are passed a marker of the type google.maps.Markers', async () => {
        const propsData: any = {
            apiKey,
            markers,
        };

        const wrapper = mountComponent({ propsData });

        await wrapper.vm.$nextTick();
        expect(wrapper.vm.gMarkers[0]).toBeInstanceOf(window.google.maps.Marker);
    });

    test('when the polygons are passed a polygon is of the type google.maps.Polygons', async () => {
        const propsData: any = {
            apiKey,
            polygons,
        };

        const wrapper = mountComponent({ propsData });

        await wrapper.vm.$nextTick();
        expect(wrapper.vm.gPolygons[0]).toBeInstanceOf(window.google.maps.Polygon);
    });

    test('when the polylines are passed a polyline is of the type google.maps.Polylines', async () => {
        const propsData: any = {
            apiKey,
            polylines,
        };

        const wrapper = mountComponent({ propsData });

        await wrapper.vm.$nextTick();
        expect(wrapper.vm.gPolylines[0]).toBeInstanceOf(window.google.maps.Polyline);
    });

    test('when the circles are passed a circle is of the type google.maps.Circle', async () => {
        const propsData: any = {
            apiKey,
            circles,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.gCircles[0]).toBeInstanceOf(window.google.maps.Circle);
    });

    test('when the rectangles are passed a rectangle is of the type google.maps.Rectangle', async () => {
        const propsData: any = {
            apiKey,
            rectangles,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.gRectangles[0]).toBeInstanceOf(window.google.maps.Rectangle);
    });

    test('when the clusterer is passed a cluster is of the type MarkerClusterer', async () => {
        const propsData: any = {
            apiKey,
            clusterer,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.gClusterer).toBeInstanceOf(MockMarkerClusterer);
    });

    test('when the renderer is passed a renderer is of the type WtgGoogleMapRenderer', async () => {
        const propsData: any = {
            apiKey,
            clusterer,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.gClusterer.args.renderer).toBeInstanceOf(WtgGoogleMapRenderer);
    });

    test('when the themeColor is passed a renderer is of the type WtgGoogleMapRenderer', async () => {
        const propsData: any = {
            apiKey,
            clusterer,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.gClusterer.args.renderer).toBeInstanceOf(WtgGoogleMapRenderer);
        expect(WtgGoogleMapRenderer).toHaveBeenCalled();
    });

    test('when the clusterer markers are passed a marker is of the type google.maps.Marker', async () => {
        const propsData: any = {
            apiKey,
            clusterer,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        expect(wrapper.vm.gClusterer.args.markers[0]).toBeInstanceOf(window.google.maps.Marker);
    });

    test('when the height changes the container div style changes', async () => {
        const propsData: any = {
            apiKey,
            center,
            height: '450px',
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });

        await wrapper.setProps({ height: '600px' });

        const googleMap = wrapper.findComponent(WtgGoogleMap);
        expect(googleMap.html()).toContain('style="height: 600px');
    });

    test('when the width changes the container div style changes', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            width: '100%',
            zoom,
        };

        const wrapper = mountComponent({ propsData });

        await wrapper.setProps({ width: '600px' });
        expect(wrapper.html()).toContain('600px');
    });

    test('it should emit a click event when the map is clicked', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const map = wrapper.vm.gMap;
        await map.mock('click');

        expect(wrapper.emitted().click).toBeTruthy();
    });

    test('it should emit a center change event when the map center changed event fires', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const map = wrapper.vm.gMap;
        map.getCenter.mockReturnValue(center);
        await map.mock('center_changed');

        expect(wrapper.emitted()['center-changed']).toBeTruthy();
    });

    test('it should NOT emit a center change event when the map center changed event fires but getCenter returns null', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const map = wrapper.vm.gMap;
        map.getCenter.mockReturnValue(null);
        await map.mock('center_changed');

        expect(wrapper.emitted()['center-changed']).toBeFalsy();
    });

    test('it should emit a bounds change event when the map bounds changed event fires', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const map = wrapper.vm.gMap;
        map.getBounds.mockReturnValue(bounds);
        await map.mock('bounds_changed');

        expect(wrapper.emitted()['bounds-changed']).toBeTruthy();
    });

    test('it should emit a bounds change event when the map bounds changed event fires but getBounds returns null', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const map = wrapper.vm.gMap;
        map.getBounds.mockReturnValue(null);
        await map.mock('bounds_changed');

        expect(wrapper.emitted()['bounds-changed']).toBeFalsy();
    });

    test('it should emit a zoom change event when the map zoom event fires', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const map = wrapper.vm.gMap;
        map.getZoom.mockReturnValue(zoom);
        await map.mock('zoom_changed');

        expect(wrapper.emitted()['zoom-changed']).toBeTruthy();
    });

    test('it should emit a zoom change event when the map zoom event fires but getZoom returns undefined', async () => {
        const propsData: any = {
            apiKey,
            center,
            options,
            zoom,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        const map = wrapper.vm.gMap;
        map.getZoom.mockReturnValue(undefined);
        await map.mock('zoom_changed');

        expect(wrapper.emitted()['zoom-changed']).toBeFalsy();
    });

    test('when the circles are clicked then a circle click event is emitted', async () => {
        const propsData: any = {
            apiKey,
            circles,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        await wrapper.vm.gCircles[0].mock('click');

        expect(wrapper.emitted()['circle-click']).toBeTruthy();
    });

    test('when the markers are clicked then a marker click event is emitted', async () => {
        const propsData: any = {
            apiKey,
            markers,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        await wrapper.vm.gMarkers[0].mock('click');

        expect(wrapper.emitted()['marker-click']).toBeTruthy();
    });

    test('when the polygons are clicked then a polygon click event is emitted', async () => {
        const propsData: any = {
            apiKey,
            polygons,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        await wrapper.vm.gPolygons[0].mock('click');

        expect(wrapper.emitted()['polygon-click']).toBeTruthy();
    });

    test('when the polylines are clicked then a polyline click event is emitted', async () => {
        const propsData: any = {
            apiKey,
            polylines,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        await wrapper.vm.gPolylines[0].mock('click');

        expect(wrapper.emitted()['polyline-click']).toBeTruthy();
    });

    test('when the rectangles are clicked then a rectangle click event is emitted', async () => {
        const propsData: any = {
            apiKey,
            rectangles,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        await wrapper.vm.gRectangles[0].mock('click');

        expect(wrapper.emitted()['rectangle-click']).toBeTruthy();
    });

    test('when the markers in clusterer are clicked then a marker click event is emitted', async () => {
        const propsData: any = {
            apiKey,
            clusterer,
        };

        const wrapper = mountComponent({ propsData });
        await wrapper.vm.$nextTick();

        await wrapper.vm.gClusterer.args.markers[0].mock('click');

        expect(wrapper.emitted()['marker-click']).toBeTruthy();
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    describe('language', () => {
        let propsData: Record<string, any>;

        describe('when set initially', () => {
            beforeEach(() => {
                propsData = { apiKey, languageCode: 'fr' };
            });

            test('when it mounts the loadMapsAsync function is called with the language code', async () => {
                const wrapper = mountComponent({ propsData });
                await wrapper.vm.$nextTick();

                expect(loadMapsAsync).toHaveBeenCalledWith(apiKey, undefined, 'fr');
            });
        });

        describe('when set dynamically', () => {
            beforeEach(() => {
                propsData = {};
            });

            test('when it mounts the loadMapsAsync function is called with the language code', async () => {
                const wrapper = mountComponent({ propsData });
                await wrapper.vm.$nextTick();

                await wrapper.setProps({ apiKey, languageCode: 'de' });
                await wrapper.vm.$nextTick();

                expect(loadMapsAsync).toHaveBeenCalledWith(apiKey, undefined, 'de');
            });
        });
    });

    describe('when directionsRequest changes', () => {
        const propsData: any = {
            apiKey,
        };
        let wrapper: any;

        test('when there is a valid route returned it should call setDirections and setMap as well as clear error message', async () => {
            const mockDirectionsResult = { routes: [] };
            mockRoute.mockImplementation((_, callback) => {
                callback(mockDirectionsResult, DirectionsStatus.OK);
            });
            wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();

            await wrapper.setProps({ directionsRequest: { origin: undefined } });

            expect(wrapper.vm.gDirectionsRenderer.setDirections).toHaveBeenCalledWith(mockDirectionsResult);
            expect(wrapper.vm.gDirectionsRenderer.setMap).toHaveBeenCalledWith(wrapper.vm.gMap);
            expect(wrapper.vm.alertMessage).toBe(undefined);
        });

        test('when there is no valid route returned should set an error message ', async () => {
            const { formatCaption } = useLocale();
            const mockDirectionsResult = { routes: [] };
            mockRoute.mockImplementation((_, callback) => {
                callback(mockDirectionsResult, DirectionsStatus.ZERO_RESULTS);
            });
            wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();

            await wrapper.setProps({ directionsRequest: { origin: undefined } });

            expect(wrapper.vm.gDirectionsRenderer.setDirections).not.toHaveBeenCalled();
            expect(wrapper.vm.alertMessage).toBe(formatCaption('map.routeByRoadNotDisplayed'));
        });
    });

    describe('when directionsRenderOptions changes', () => {
        const propsData: any = {
            apiKey,
        };
        let wrapper: any;

        test('should call setOptions', async () => {
            const directionsRenderOptions = { draggable: true };
            wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();

            await wrapper.setProps({ directionsRenderOptions });

            expect(wrapper.vm.gDirectionsRenderer.setOptions).toHaveBeenCalledWith(directionsRenderOptions);
        });
    });

    describe('when the center changes', () => {
        let wrapper: VueWrapper<any, any>;
        const newCenter = {
            lat: -36.258,
            lng: 151.133,
        };
        const options = {
            center: {
                lat: -33.868,
                lng: 151.209,
            },
        };

        beforeEach(async () => {
            const propsData: any = {
                apiKey,
                center,
                options,
            };

            wrapper = mountComponent({ propsData });

            await wrapper.vm.$nextTick();
        });

        test('when it is not equal to gmap.getCenter() it is passed into setCenter', async () => {
            wrapper.vm.gMap.getCenter.mockReturnValue(center);

            await wrapper.setProps({ center: newCenter });

            expect(wrapper.vm.gMap.setCenter).toHaveBeenCalledWith(newCenter);
        });

        test('when only the longitude is not equal to gmap.getCenter() it is passed into setCenter', async () => {
            wrapper.vm.gMap.getCenter.mockReturnValue(newCenter);

            await wrapper.setProps({ center: { ...newCenter, lng: 151.209 } });

            expect(wrapper.vm.gMap.setCenter).toHaveBeenCalledWith({ ...newCenter, lng: 151.209 });
        });

        test('when only the latitude is not equal to gmap.getCenter() it is passed into setCenter', async () => {
            wrapper.vm.gMap.getCenter.mockReturnValue(newCenter);

            await wrapper.setProps({ center: { ...newCenter, lng: -33.868 } });

            expect(wrapper.vm.gMap.setCenter).toHaveBeenCalledWith({ ...newCenter, lng: -33.868 });
        });

        test('when it is equal to gmap.getCenter() it is not passed into setCenter', async () => {
            wrapper.vm.gMap.getCenter.mockReturnValue(newCenter);

            await wrapper.setProps({ center: newCenter });

            expect(wrapper.vm.gMap.setCenter).not.toHaveBeenCalled();
        });

        test('when it is set to undefined it uses the options center', async () => {
            wrapper.vm.gMap.getCenter.mockReturnValue(newCenter);

            await wrapper.setProps({ center: undefined });

            expect(wrapper.vm.gMap.setCenter).toHaveBeenCalledWith(options.center);
        });
    });

    describe('when the zoom changes', () => {
        let wrapper: VueWrapper<any, any>;
        const zoom = 8;
        const newZoom = 11;
        const options = {
            zoom: 10,
        };

        beforeEach(async () => {
            const propsData: any = {
                apiKey,
                zoom,
                options,
            };

            wrapper = mountComponent({ propsData });
            await wrapper.vm.$nextTick();
        });

        test('when it is not equal to gmap.getZoom() it is passed into setZoom', async () => {
            wrapper.vm.gMap.getZoom.mockReturnValue(zoom);

            await wrapper.setProps({ zoom: newZoom });

            expect(wrapper.vm.gMap.setZoom).toHaveBeenCalledWith(newZoom);
        });

        test('when it is equal to gmap.getZoom() it is not passed into setZoom', async () => {
            wrapper.vm.gMap.getZoom.mockReturnValue(newZoom);

            await wrapper.setProps({ zoom: newZoom });

            expect(wrapper.vm.gMap.setZoom).not.toHaveBeenCalled();
        });

        test('when it is set to undefined it uses the options zoom', async () => {
            wrapper.vm.gMap.getZoom.mockReturnValue(newZoom);

            await wrapper.setProps({ zoom: undefined });

            expect(wrapper.vm.gMap.setZoom).toHaveBeenCalledWith(options.zoom);
        });
    });

    test('when center and zoom are not set, it uses the options center and zoom', async () => {
        const center = { lat: -33.868, lng: 151.209 };
        const zoom = 10;
        const wrapper = mountComponent({
            propsData: {
                apiKey,
                options: {
                    center,
                    zoom,
                },
            },
        });
        await nextTick();
        expect(wrapper.vm.gMap.options.center).toStrictEqual(center);
        expect(wrapper.vm.gMap.options.zoom).toBe(zoom);
    });

    function mountComponent({ propsData = {}, provide = {} } = {}): VueWrapper<any> {
        return mount(WtgGoogleMap, {
            propsData,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
