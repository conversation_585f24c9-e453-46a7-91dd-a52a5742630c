<template>
    <div v-if="action.visible">
        <WtgIconButton
            v-if="isTabletOrMobile"
            variant="ghost"
            icon="s-icon-logs"
            :aria-label="action.caption"
            aria-haspopup="dialog"
            :tooltip="action.caption"
            @click="action.onInvoke"
        >
        </WtgIconButton>
        <WtgButton v-else variant="ghost" leading-icon="s-icon-logs" aria-haspopup="dialog" @click="action.onInvoke">
            {{ action.caption }}
        </WtgButton>
    </div>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgIconButton from '@components/WtgIconButton';
import { WtgFrameworkTask, WtgFrameworkTaskStandardAction } from '@components/framework/types';
import { useFramework } from '@composables/framework';
import { computed, PropType } from 'vue';

const props = defineProps({
    task: { type: Object as PropType<WtgFrameworkTask>, default: undefined },
});

const { isTabletOrMobile } = useFramework();

const action = computed((): WtgFrameworkTaskStandardAction => {
    return (
        props.task?.showLogsAction ?? {
            visible: false,
            caption: 'Logs',
            label: 'Logs',
            onInvoke: (): void => undefined,
        }
    );
});
</script>
