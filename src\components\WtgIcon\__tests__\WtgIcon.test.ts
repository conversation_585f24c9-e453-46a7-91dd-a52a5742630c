import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { WtgIcon } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgIcon', () => {
    test('it renders a <i> component', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('I');
    });

    test('it parses the default slot to apply the supply icon class when it starts with s-icon', () => {
        const wrapper = mountComponent({
            slots: {
                default: 's-icon-home',
            },
        });
        expect(wrapper.classes()).toContain('s-icon-home');
        expect(wrapper.classes()).toContain('wtg-icon');
    });

    test('it parses the default slot to apply the supply icon class when it starts with $', () => {
        const wrapper = mountComponent({
            slots: {
                default: '$home',
            },
        });
        expect(wrapper.classes()).toContain('s-icon-home');
        expect(wrapper.classes()).toContain('wtg-icon');
    });

    test('it uses the property to apply the supply icon class when it starts with s-icon', () => {
        const wrapper = mountComponent({ propsData: { icon: 's-icon-home' } });
        expect(wrapper.classes()).toContain('s-icon-home');
        expect(wrapper.classes()).toContain('wtg-icon');
    });

    test('it does not apply the wtg-icon class when there is no icon specified ensuring a width and height of (0,0) ', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).not.toContain('wtg-icon');
    });

    test('it renders the icon passed to the icon prop when it starts with s-icon', () => {
        const wrapper = mountComponent({ propsData: { icon: 's-icon-caret-down' } });
        expect(wrapper.classes()).toContain('s-icon-caret-down');
    });

    test('it renders the icon passed to the icon prop when it starts with $', () => {
        const wrapper = mountComponent({ propsData: { icon: 's-icon-caret-down' } });
        expect(wrapper.classes()).toContain('s-icon-caret-down');
    });

    describe('when color is set', () => {
        test('it sets the element style from valid css style prop colors', () => {
            const wrapper = mountComponent({
                propsData: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('text-red');
        });

        test('it sets the element style from vars', () => {
            const wrapper = mountComponent({
                propsData: {
                    color: 'var(--s-error-txt-default)',
                },
            });
            expect((wrapper.vm as any).computedStyle.color).toBe('var(--s-error-txt-default)');
        });
    });

    test('it has tooltip capability mixed in', () => {
        const wrapper: VueWrapper<any> = mountComponent({
            propsData: { tooltip: { content: 'Some tooltip', placement: 'top' } },
        });
        expect(wrapper.vm.tooltipDirective.content).toBe('Some tooltip');
        expect(wrapper.vm.tooltipDirective.placement).toBe('top');
    });

    test('it applies the disabled class when the disabled prop is set', async () => {
        const wrapper = mountComponent({ propsData: { disabled: false } });
        expect(wrapper.classes()).not.toContain('wtg-icon--disabled');
        await wrapper.setProps({ disabled: true });
        expect(wrapper.classes()).toContain('wtg-icon--disabled');
    });

    test('it emits a click event when the <i> is clicked', async () => {
        const wrapper = mountComponent();
        const button = wrapper.find('i');
        expect(wrapper.emitted('click')).toBeUndefined();
        await button.trigger('click');
        expect(wrapper.emitted('click')!.length).toBe(1);
        expect(wrapper.emitted('click')![0][0]).toBeInstanceOf(MouseEvent);
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgIcon, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
