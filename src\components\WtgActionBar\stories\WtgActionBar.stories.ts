import { WtgButton } from '@components/WtgButton';
import WtgDivider from '@components/WtgDivider/WtgDivider.vue';
import WtgIconButton from '@components/WtgIconButton/WtgIconButton.vue';
import WtgPopover from '@components/WtgPopover/WtgPopover.vue';
import WtgSpacer from '@components/WtgSpacer/WtgSpacer.vue';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import WtgActionBar from '..';

type Story = StoryObj<typeof WtgActionBar>;

const meta: Meta<typeof WtgActionBar> = {
    title: 'Components/Action Bar',
    component: WtgActionBar,
    parameters: {
        docs: {
            description: {
                component:
                    "The Action Bar component is a key element of the core framework. The Action Bar, divided into 'Main Actions' and 'Destructive Actions' by a visual divider, enables users to perform tasks affecting the entire page or entity.",
            },
            design: {
                type: 'figma',
                url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components',
            },
        },
    },
    render: (args) => ({
        components: { WtgActionBar, WtgButton, WtgDivider, WtgSpacer },
        setup: () => ({ args }),
        methods: {
            cancelAction: action('cancel'),
            saveAction: action('save'),
            secondaryAction: action('secondary action'),
        },
        template: `
            <wtg-action-bar>
                <div class="d-flex">
                    <wtg-button @click="() => action('cancel')">Cancel</wtg-button>
                    <wtg-divider vertical class="ml-4 mr-3" style ="border: 0px; border-right: 1px solid var(--s-neutral-border-weak-default);"/>
                    <wtg-button @click="() => action('secondaryAction')">Secondary action</wtg-button>
                    <wtg-button sentiment="primary" variant="fill" @click="() => action('save')" class="ml-2">Save changes</wtg-button>
                </div>                
            </wtg-action-bar>`,
    }),
};

export default meta;

export const Default: Story = {};

export const Medium: Story = {
    args: {},
    render: (args) => ({
        components: { WtgActionBar, WtgButton, WtgDivider, WtgSpacer, WtgIconButton, WtgPopover },
        setup: () => ({ args }),
        methods: {
            cancelAction: action('cancel'),
            saveAction: action('save'),
            secondaryAction: action('secondary action'),
        },
        template: `
        <div style="height: 300px; position: relative;">
            <wtg-action-bar style="position: absolute; bottom:0;">
                    <div class="d-flex">
                        <wtg-button @click="() => action('cancel')">Cancel</wtg-button>
                        <wtg-divider vertical class="ml-4 mr-3" style ="border: 0px; border-right: 1px solid var(--s-neutral-border-weak-default);"/>
                        <wtg-button >Secondary action</wtg-button>
                        <wtg-button class="ml-2 mr-2">Secondary action</wtg-button>
                        <wtg-popover :close-on-content-click=false location="top end" nudge-top="var(--s-padding-m)">
                            <template #activator="{props}">                                          
                                <WtgIconButton v-bind="props" aria-label="More" icon="s-icon-menu-meatballs"></WtgIconButton>                        
                            </template>
                            <div style="display: flex; align-items: center; justify-content: center; flex-direction: column; padding: var(--s-padding-xl)">
                                <div><wtg-button variant="ghost" class="mb-2">Secondary action</wtg-button></div>
                                <div><wtg-button variant="ghost" class="mb-2">Secondary action</wtg-button></div>
                                <div><wtg-button variant="ghost" class="mb-2">Secondary action</wtg-button></div>
                            </div>
                        </wtg-popover>
                        <wtg-button sentiment="primary" variant="fill" @click="() => action('save')" class="ml-2">
                            Save changes
                        </wtg-button>
                    </div>                
            </wtg-action-bar>
        </div>`,
    }),
};

export const Small: Story = {
    render: (args) => ({
        components: { WtgActionBar, WtgButton, WtgDivider, WtgSpacer, WtgIconButton, WtgPopover },
        setup: () => ({ args }),
        methods: {
            cancelAction: action('cancel'),
            saveAction: action('save'),
            secondaryAction: action('secondary action'),
        },
        template: `
        <div style="height: 300px; position: relative;">
            <wtg-action-bar style="position: absolute; bottom:0; border: 0; box-shadow: var(--s-elevation-0);
                gap: var(--s-spacing-L);
                border-top: 1px solid var(--s-neutral-border-weak-default, rgba(107, 107, 104, 0.5));
                background: var(--s-neutral-bg-default, rgba(255, 255, 255, 1));
                border-radius: 0;">   
                    <div class="d-flex" >
                        <wtg-button width="276px" sentiment="primary" variant="fill" @click="() => action('save')" class="ml-2 mr-2">
                            Save changes
                        </wtg-button>
                        <wtg-popover :close-on-content-click=false location="top end" nudge-top="17px">
                            <template #activator="{props}">                                          
                                <WtgIconButton v-bind="props" aria-label="More" icon="s-icon-menu-meatballs"></WtgIconButton>                        
                            </template>
                            <div style="width: 312px; padding: var(--s-padding-l); gap: var(--s-spacing-l); 
                                display: flex; align-items: center; justify-content: center; flex-direction: column; 
                                background: var(--s-neutral-bg-default, rgba(255, 255, 255, 1));
                                box-shadow: 0px -4px 8px 0px rgba(17, 17, 16, 0.16);">
                                <div><wtg-button style=" width:290px;">Cancel</wtg-button></div>
                                <div><wtg-button style=" width:290px;">Secondary action</wtg-button></div>
                                <div><wtg-button style=" width:290px;">Secondary action</wtg-button></div>
                            </div>
                        </wtg-popover>
                    </div>                
            </wtg-action-bar>
        </div>`,
    }),
};

export const DefaultActions: Story = {
    args: {},
    render: (args) => ({
        components: { WtgActionBar, WtgButton, WtgDivider, WtgSpacer },
        setup: () => ({ args }),
        methods: {
            defaultAction: action('default action'),
            cancelAction: action('cancel'),
            saveAction: action('save'),
            secondaryAction: action('secondary action'),
        },
        template: `
        <wtg-action-bar>   
            <div class="d-flex flex-grow-1">
                <wtg-button variant="ghost" @click="() => action('cancel')">Default action</wtg-button>
                <wtg-spacer />
                <div class="d-flex">
                    <wtg-button @click="() => action('cancel')">Cancel</wtg-button>
                    <wtg-divider vertical class="ml-4 mr-3" style ="border: 0px; border-right: 1px solid var(--s-neutral-border-weak-default);"/>
                    <wtg-button @click="() => action('secondaryAction')">Secondary action</wtg-button>
                    <wtg-button sentiment="primary" variant="fill" @click="() => action('save')" class="ml-2">
                        Save changes
                    </wtg-button>
                </div>
            </div>                         
        </wtg-action-bar>`,
    }),
};
