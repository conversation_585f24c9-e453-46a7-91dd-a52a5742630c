<template>
    <WtgAlertModal
        v-model="internalValue"
        persistent
        width="auto"
        max-width="560"
        min-width="280"
        :title="title"
        sentiment="warning"
    >
        <div id="alertMessage" class="mt-2 mb-2">{{ message }}</div>
        <template #actions>
            <WtgSpacer />
            <WtgButton :min-width="88" @click="onNoClick">
                {{ no }}
            </WtgButton>
            <WtgButton :loading="isRemoving" sentiment="primary" :min-width="88" variant="fill" @click="onYesClick">
                {{ yes }}
            </WtgButton>
        </template>
    </WtgAlertModal>
</template>

<script lang="ts">
import WtgAlertModal from '@components/WtgAlertModal';
import { WtgButton } from '@components/WtgButton';
import { WtgSpacer } from '@components/WtgSpacer';
import { useLocale } from '@composables/locale';
import { defineComponent, PropType } from 'vue';
import { Conversation, ConversationFollower } from '../types';

export default defineComponent({
    name: 'RemoveFollowerDialog',
    components: {
        WtgAlertModal,
        WtgButton,
        WtgSpacer,
    },
    props: {
        conversation: {
            type: Object as PropType<Conversation>,
            default: undefined,
        },
        follower: {
            type: Object as PropType<ConversationFollower>,
            default: undefined,
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['input', 'close'],
    setup() {
        const { formatCaption } = useLocale();

        return { formatCaption };
    },
    data() {
        return {
            isRemoving: false,
            internalValue: false,
        };
    },
    computed: {
        message(): string {
            return this.formatCaption('conversation.dialog.removeFollower.areYourSure', this.follower?.name ?? '');
        },
        no(): string {
            return this.formatCaption('conversation.dialog.removeFollower.no');
        },
        title(): string {
            return this.formatCaption('conversation.dialog.removeFollower.title');
        },
        yes(): string {
            return this.formatCaption('conversation.dialog.removeFollower.yes');
        },
    },
    watch: {
        internalValue(): void {
            this.$emit('input', this.internalValue);
        },
        value: {
            handler(): void {
                this.internalValue = this.value;
            },
            immediate: true,
        },
    },
    methods: {
        onNoClick(): void {
            this.internalValue = false;
            this.$emit('close');
        },
        async onYesClick(): Promise<void> {
            this.isRemoving = true;

            try {
                if (this.follower) {
                    await this.conversation?.provider.removeFollowerFromConversationAsync(
                        this.follower,
                        this.conversation
                    );
                    this.internalValue = false;
                    this.$emit('close');
                }
            } finally {
                this.isRemoving = false;
            }
        },
    },
});
</script>
