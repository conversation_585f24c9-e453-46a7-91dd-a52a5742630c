export const AlertModalSandboxTemplate = `
<div class="d-flex flex-column" style="gap: 8px; max-width: 600px;">
<WtgAlertModal persistent title="Tasks cleared" sentiment="success">
    <template v-slot:activator="{ props: activatorProps }">
        <WtgButton variant="fill" sentiment="primary" v-bind="activatorProps">Open Success Single Action Modal</WtgButton>
    </template>

    <template #default>
        All tasks in your channel for this week have been cleared.
    </template>
    <template #actions="{ isActive }">
        <WtgButton variant="fill" sentiment="primary" @click="isActive.value = false">Okay</WtgButton>
    </template>
</WtgAlertModal>
    
<WtgAlertModal title="Screen entities review required (10)" sentiment="warning">
    <template v-slot:activator="{ props: activatorProps }">
        <WtgButton variant="fill" sentiment="primary" v-bind="activatorProps">Open Warning Basic Modal</WtgButton>
    </template>
    <template #default>
        <div class="py-1">
            <p>
                Proceeding with this action will screen entities using the current compliance list configuration of this group and system settings.
            </p>
            <br />
            <p>
                To continue, please type "I understand"
            </p>
            <wtg-text-field />
        </div>
    </template>
    <template #actions="{ isActive }">
        <WtgButton variant="ghost" @click="isActive.value = false">Cancel</WtgButton>
        <WtgButton variant="fill" sentiment="primary" @click="isActive.value = false">Screen entities</WtgButton>
    </template>
</WtgAlertModal>
    
<WtgAlertModal title="Branch not configured" sentiment="warning">
    <template v-slot:activator="{ props: activatorProps }">
        <WtgButton variant="fill" sentiment="primary" v-bind="activatorProps">Open Warning Basic Modal</WtgButton>
    </template>
    <template #default>
        <div>
            <p>
                The branch you have specified is not configured for the company <b>WiseTech Global</b>.
            </p>
        </div>
    </template>
    <template #actions="{ isActive }">
        <WtgButton variant="ghost" @click="isActive.value = false">Cancel</WtgButton>
        <WtgButton variant="fill" sentiment="primary" @click="isActive.value = false">Screen entities</WtgButton>
    </template>
</WtgAlertModal>
    
<WtgAlertModal title="Task in progess" sentiment="warning" persistent>
    <template v-slot:activator="{ props: activatorProps }">
        <WtgButton variant="fill" sentiment="primary" v-bind="activatorProps">Open Warning Persistent Modal</WtgButton>
    </template>
    <template #default>
        Task is currently in progress, are you sure you want to cancel it?
    </template>
    <template #actions="{ isActive }">
        <WtgButton variant="ghost" @click="isActive.value = false">Discard</WtgButton>
        <WtgButton variant="fill" sentiment="primary" @click="isActive.value = false">Cancel task</WtgButton>
    </template>
</WtgAlertModal>
    
<WtgAlertModal :scrollable="true" title="Save failed due to validation errors" sentiment="error">
    <template v-slot:activator="{ props: activatorProps }">
        <WtgButton variant="fill" sentiment="primary" v-bind="activatorProps">Open Error Flexible Content Modal</WtgButton>
    </template>
    <template #default>
        <div>
            <b>Booking type</b>
            <p>Field can not be empty.</p>
            <br />
            <b>Transport company</b>
            <p>Field can not be empty.</p>
            <br />
            <b>Gate movement bookings</b>
            <p>The Gate booking must have at least one gate movement booking.</p>
            <br />
            <b>Vechicle driver bookings</b>
            <p>The Gate booking must have at least one vehicle driver booking.</p>
            <br />
            <b>Vehicle movement bookings</b>
            <p>The Gate booking must have at least one vehicle movement booking.</p>
        </div>
    </template>
    <template #actions="{ isActive }">
        <WtgButton variant="fill" sentiment="primary" @click="isActive.value = false">Okay</WtgButton>
    </template>
</WtgAlertModal>
    
<WtgAlertModal title="Delete branch" sentiment="error">
    <template v-slot:activator="{ props: activatorProps }">
        <WtgButton variant="fill" sentiment="primary" v-bind="activatorProps">Open Error Basic Modal</WtgButton>
    </template>
    <template #default>
        <div>
            <p>
                You're about to delete the branch you have configured for the company <b>WiseTech Global</b>.
            </p>
        </div>
    </template>
    <template #actions="{ isActive }">
        <WtgButton variant="ghost" @click="isActive.value = false">Cancel</WtgButton>
        <WtgButton variant="fill" sentiment="critical" @click="isActive.value = false">Delete branch</WtgButton>
    </template>
</WtgAlertModal>
    
<WtgAlertModal title="Exception error" sentiment="error" persistent>
    <template v-slot:activator="{ props: activatorProps }">
        <WtgButton variant="fill" sentiment="primary" v-bind="activatorProps">Open Error Persistent Modal</WtgButton>
    </template>
    <template #default>
        A critical error has occurred, your session will be terminated
    </template>
    <template #actions="{ isActive }">
        <WtgButton variant="fill" sentiment="primary" @click="isActive.value = false">Okay</WtgButton>
    </template>
</WtgAlertModal>
    
<WtgAlertModal title="Alert reason" sentiment="success">
    <template v-slot:activator="{ props: activatorProps }">
        <WtgButton variant="fill" sentiment="primary" v-bind="activatorProps">Open With Popup Content Modal</WtgButton>
    </template>
    <template #default>
        <p>Modal with popover content</p>
        <div style="display: flex; justify-content: center; align-items: center; padding: 16px 0px">
            <wtg-popover>
                <template #activator="{props}">
                    <WtgButton v-bind="props">
                        Open popup
                    </WtgButton>
                </template>
                <template #default="{isActive8}">
                    <div class="pa-2">
                    I am a popup
                    </div>
                </template>
            </wtg-popover>
        </div>
    </template>
    <template #actions="{ isActive }">
        <WtgButton variant="ghost" @click="isActive.value = false">Cancel</WtgButton>
        <WtgButton @click="isActive.value = false">Default</WtgButton>
        <WtgButton variant="fill" sentiment="primary" @click="isActive.value = false">Primary</WtgButton>
    </template>
</WtgAlertModal>
</div>
`;
