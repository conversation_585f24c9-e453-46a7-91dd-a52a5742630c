import {
    WtgFrameworkSearchResultMenuItem,
    WtgFrameworkSearchResultEntity,
    WtgFrameworkSearchResultRecord,
    WtgFrameworkSearchResultSuggestedFilter,
} from '@components/framework/types';

enum TileDirection {
    Next,
    Previous,
}

function searchItemIsEntity(item: WtgFrameworkSearchResultRecord): item is WtgFrameworkSearchResultEntity {
    return (<WtgFrameworkSearchResultEntity>item).entityType !== undefined;
}

function searchItemIsMenuItem(item: WtgFrameworkSearchResultRecord): item is WtgFrameworkSearchResultMenuItem {
    return (<WtgFrameworkSearchResultMenuItem>item).id !== undefined;
}

function searchItemIsSuggestedFilter(
    item: WtgFrameworkSearchResultRecord
): item is WtgFrameworkSearchResultSuggestedFilter {
    return (<WtgFrameworkSearchResultSuggestedFilter>item).shorthand !== undefined;
}

export { TileDirection, searchItemIsEntity, searchItemIsMenuItem, searchItemIsSuggestedFilter };
