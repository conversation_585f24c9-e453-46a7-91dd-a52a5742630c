import { SearchFieldItem } from '@components/WtgSearchField/types';
import { StatusSentiment } from '@components/WtgStatus/types';

export interface AddressSearchItem extends SearchFieldItem {
    address?: AddressLookupSearchContent;
}

export interface AddressLookupSearchContent {
    guid?: string;
    companyGuid?: string;
    company?: string;
    companyCode?: string;
    code?: string;
    street?: string;
    streetAlt?: string;
    city?: string;
    postcode?: string;
    state?: string;
    countryCode?: string;
    unloco?: string;
    phone?: string;
    mobile?: string;
    email?: string;
    additionalInfo?: string;
    isActive?: boolean;
    isAddressOverriden?: boolean;
}

export interface ContactSearchItem extends SearchFieldItem {
    contact?: ContactLookupSearchContent;
}

export interface ContactLookupSearchContent {
    guid?: string;
    name?: string;
    phone?: string;
    mobile?: string;
    email?: string;
}

export interface AddressFormatter {
    formatAddress: (address: object) => string;
}

export interface AddressVerificationStatus {
    label?: string;
    sentiment?: StatusSentiment;
}
