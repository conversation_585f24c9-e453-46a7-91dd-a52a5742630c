<template>
    <WtgIconButton
        v-if="isTabletOrMobile"
        variant="ghost"
        :icon="computedAction.icon"
        :aria-label="computedAction.caption"
        :tooltip="computedAction.caption"
        @click="computedAction.onInvoke"
    >
    </WtgIconButton>
    <WtgButton v-else variant="ghost" :leading-icon="computedAction.icon" @click="computedAction.onInvoke">
        {{ computedAction.caption }}
    </WtgButton>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgIconButton from '@components/WtgIconButton';
import { WtgFrameworkTaskGenericAction } from '@components/framework/types';
import { useFramework } from '@composables/framework';
import { PropType, computed } from 'vue';

const props = defineProps({
    action: { type: Object as PropType<WtgFrameworkTaskGenericAction>, default: undefined },
});

const { isTabletOrMobile } = useFramework();

const computedAction = computed((): WtgFrameworkTaskGenericAction => {
    return (
        props.action ?? {
            id: '',
            caption: '',
            placement: '',
            onInvoke: (): void => undefined,
        }
    );
});
</script>
