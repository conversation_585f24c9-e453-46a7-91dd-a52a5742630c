import WtgTifPage from '@components/WtgDocumentPreview/WtgTifPage.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import 'jest-canvas-mock';
import UTIF from 'utif';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgTifPage', () => {
    test('its name is WtgTifPage', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgTifPage');
    });

    test('should decode tif to rgb data', () => {
        const ifd = { id: 123 };
        const ifdDecoded = { id: 123, width: 600, height: 200, data: new Uint8Array([165]) };

        const spyUtifDecodeImage = jest.spyOn(UTIF, 'decodeImage').mockImplementation((buffer, page) => {
            Object.assign(page, ifdDecoded);
        });

        let decodeImageWasCalledBeforeToRGBA8 = false;
        const spyUtifRgb = jest.spyOn(UTIF, 'toRGBA8').mockImplementation(() => {
            decodeImageWasCalledBeforeToRGBA8 = spyUtifDecodeImage.mock.calls.length > 0;
            return new Uint8Array();
        });

        const wrapper = mountComponent({ props: { buffer: {}, ifd: ifd } });

        expect(spyUtifDecodeImage).toHaveBeenCalled();
        const decodeImageParam1 = spyUtifDecodeImage.mock.calls[0][0];
        const decodeImageParam2 = spyUtifDecodeImage.mock.calls[0][1];
        expect(decodeImageParam1).toBe(wrapper.vm.buffer);
        expect(decodeImageParam2).toEqual(ifd);

        expect(spyUtifRgb).toHaveBeenCalled();
        expect(decodeImageWasCalledBeforeToRGBA8).toBe(true);
        const decodedPage = spyUtifRgb.mock.calls[0][0];
        expect(decodedPage).toEqual(ifd);
    });

    test('when decode fails should emit error', async () => {
        const spyUtifDecodeImage = jest.spyOn(UTIF, 'decodeImage').mockImplementation(() => {
            throw new Error();
        });

        const wrapper = mountComponent();
        expect(spyUtifDecodeImage).toHaveBeenCalled();
        expect(wrapper.emitted('error')?.length).toBe(1);
    });

    test('when decoded image is invalid should emit decodeError', async () => {
        const ifdsDecoded = {
            id: 123,
            width: undefined,
            height: undefined,
            data: new Uint8Array([165]),
        };
        const spyUtifDecodeImage = jest.spyOn(UTIF, 'decodeImage').mockImplementation((buffer, page) => {
            Object.assign(page, ifdsDecoded);
        });

        const wrapper = mountComponent();
        expect(spyUtifDecodeImage).toHaveBeenCalled();
        expect(wrapper.emitted('tif-decode-error')?.length).toBe(1);
    });

    test('paints image on canvas', () => {
        jest.spyOn(UTIF, 'decodeImage').mockImplementation((buffer, page) => {
            Object.assign(page, {
                width: 4,
                height: 4,
            });
        });
        jest.spyOn(UTIF, 'toRGBA8').mockReturnValue(new Uint8Array(getSampleImageData()));

        const wrapper = mountComponent();
        const canvas = wrapper.element as HTMLCanvasElement;
        const ctx = canvas.getContext('2d')!;

        expect(canvas.width).toBe(4);
        expect(canvas.height).toBe(4);
        expect(ctx.createImageData).toHaveBeenCalled();
        expect(ctx.putImageData).toBeCalled();
        expect(Array.from((ctx.putImageData as jest.Mock).mock.calls[0][0].data)).toEqual(getSampleImageData());
    });

    test('when no canvas context should emit error', () => {
        jest.spyOn(UTIF, 'decodeImage').mockImplementation((buffer, page) => {
            Object.assign(page, {
                width: 4,
                height: 4,
            });
        });
        jest.spyOn(UTIF, 'toRGBA8').mockReturnValue(new Uint8Array(getSampleImageData()));
        jest.spyOn(HTMLCanvasElement.prototype, 'getContext').mockReturnValue(null);

        const wrapper = mountComponent();
        expect(wrapper.emitted('error')?.length).toBe(1);
    });

    function getSampleImageData() {
        return [
            127, 127, 127, 255, 127, 127, 127, 255, 127, 127, 127, 255, 127, 127, 127, 255, 237, 28, 36, 255, 237, 28,
            36, 255, 237, 28, 36, 255, 237, 28, 36, 255, 163, 73, 164, 255, 163, 73, 164, 255, 163, 73, 164, 255, 163,
            73, 164, 255, 153, 217, 234, 255, 153, 217, 234, 255, 153, 217, 234, 255, 153, 217, 234, 255,
        ];
    }

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgTifPage, {
            slots,
            props: {
                buffer: {},
                ifd: {} as UTIF.IFD,
                ...props,
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
