import { WtgImage } from '../';
import WtgUi from '../../../WtgUi';
import { mount, enableAutoUnmount, VueWrapper } from '@vue/test-utils';
import { VImg } from 'vuetify/components/VImg';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgImage', () => {
    test('its name is WtgImage', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgImage');
    });

    test('it renders a VImg component', () => {
        const wrapper = mountComponent();
        expect(wrapper.html()).toContain('v-img');
    });

    test('it passes all properties to the v-Img component', () => {
        const wrapper = mountComponent({
            propsData: {
                alt: 'alt text',
                aspectRatio: '1/7',
                contentClass: 'content class',
                eager: true,
                gradient: 'my gradient',
                height: 300,
                lazySrc: 'my lazy src',
                maxHeight: 400,
                maxWidth: 410,
                minHeight: 200,
                minWidth: 210,
                sizes: 'my sizes',
                src: 'MyImg',
                srcset: 'MyImg MyImg2',
                transition: 'slideLeft',
                width: 310,
            },
        });
        const props = wrapper.findComponent(VImg).props();
        expect(props.alt).toBe('alt text');
        expect(props.aspectRatio).toBe('1/7');
        expect(props.eager).toBe(true);
        expect(props.gradient).toBe('my gradient');
        expect(props.height).toBe(300);
        expect(props.lazySrc).toBe('my lazy src');
        expect(props.maxHeight).toBe(400);
        expect(props.maxWidth).toBe(410);
        expect(props.minHeight).toBe(200);
        expect(props.minWidth).toBe(210);
        expect(props.sizes).toBe('my sizes');
        expect(props.src).toBe('MyImg');
        expect(props.srcset).toBe('MyImg MyImg2');
        expect(props.transition).toBe('slideLeft');
        expect(props.width).toBe(310);
    });

    test('it passes the default slot to the VImg component', () => {
        const wrapper = mountComponent({
            slots: {
                default: '<div class="my-div">Some Text</div>',
            },
        });
        expect(wrapper.find('.my-div').text()).toBe('Some Text');
    });

    test('it uses the fallback image if the src image does not load', () => {
        const wrapper: VueWrapper<any> = mountComponent({
            propsData: {
                fallbackImage: 'fallbackImage.png',
                src: 'image.png',
            },
        });
        expect(wrapper.vm.computedSrc).toBe('image.png');
        wrapper.vm.onError();
        expect(wrapper.vm.computedSrc).toBe('fallbackImage.png');
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgImage as any, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
