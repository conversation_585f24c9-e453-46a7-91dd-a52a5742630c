import {
    WtgFramework,
    WtgFrameworkDialogs,
    WtgFrameworkNavigationDrawer,
    WtgFrameworkNotificationsDrawer,
} from '../framework';
import WtgFrameworkTask, { WtgFrameworkTaskGenericAction } from '../task';

describe('WtgFramework', () => {
    let application: WtgFramework;

    beforeEach(() => {
        application = new WtgFramework();
        application.user.name = 'User1';
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('it initializes the application', () => {
        expect(application.hideAppBar).toBe(false);
        expect(application.hideBackButton).toBe(false);
        expect(application.hideFooter).toBe(false);
        expect(application.helpItems).toStrictEqual([]);
        expect(application.href).toBe('#/index');
        expect(application.loading).toBe(false);
        expect(application.title).toBe('');
        expect(application.logoDark).toBe('');
        expect(application.logoLight).toBe('');
        expect(application.menu).toStrictEqual([]);
        expect(application.favorites).toStrictEqual([]);
        expect(application.footerVisible).toBe(false);
        expect(application.closeButtonVisible).toBe(false);
        expect(application.recentItems).toStrictEqual([]);
        expect(application.themes).toStrictEqual([]);
        expect(application.themeConfigurations).toStrictEqual([]);
        expect(application.portalSwitcherItems).toStrictEqual([]);
        expect(application.currentTask).toBe(null);
        expect(application.companyURL).toBe('https://www.cargowise.com');
        expect(application.navDrawer).toStrictEqual(new WtgFrameworkNavigationDrawer());
        expect(application.notificationsDrawer).toStrictEqual(new WtgFrameworkNotificationsDrawer());
        expect(application.toasts).toStrictEqual([]);
        expect(application.entityCreationMenu).toStrictEqual([]);
        expect(application.dialogs).toStrictEqual(new WtgFrameworkDialogs());
        expect(application.about).toStrictEqual({
            UTCLabel: 'UTC Time',
            applicationVersionLabel: 'Application',
            applicationVersion: '0.0.1',
            dialogClose: 'Close About',
            dialogTitle: 'About',
            frameworkVersion: '0.0.1',
            frameworkVersionLabel: 'Framework',
            portalName: 'Portal Name',
            portalURL: '',
            portalURLLabel: 'Portal',
            resourcesLabel: 'Resource Locations',
            serviceURL: '',
            serviceURLLabel: 'Service',
            versionLabel: 'Version',
            versionsLabel: 'Version',
        });
        expect(application.ariaLabels).toStrictEqual({
            alertHubIcon: 'Toggle Alert Hub',
            applicationTitle: 'Title',
            backButton: 'Back',
            breadcrumbs: 'Breadcrumbs',
            closeButton: 'Close',
            logoImageAlt: 'Logo',
            masthead: 'Head',
            navigationDrawer: 'Drawer',
            navigationDrawerIcon: 'Toggle Navigation Drawer',
            search: 'Search',
            searchDialogIcon: 'Toggle Search',
            taskActionsOverflowIcon: 'Toggle Actions',
            userAccountPrefix: 'User Account for',
        });
        expect(application.captions).toStrictEqual({
            about: 'About',
            actions: 'Actions',
            addNew: 'Add new',
            appearance: 'Appearance',
            configure: 'Configure',
            dark: 'Dark',
            density: 'Density',
            densityDefault: 'Default',
            densitySpacious: 'Spacious',
            densityComfortable: 'Comfortable',
            densityCompact: 'Compact',
            densityTight: 'Tight',
            errorReporting: 'An error has occurred',
            changeBranchDepartment: 'Change branch/department',
            impersonateContactUser: 'Impersonate Contact User',
            changePassword: 'Change password',
            choose: 'Choose',
            close: 'Close',
            darkMode: 'Dark Mode',
            dense: 'Dense',
            favorites: 'Favorites',
            help: 'Help',
            light: 'Light',
            lightMode: 'Light Mode',
            logOff: 'Log Out',
            manageAccount: 'Manage my account',
            mutedMode: 'Muted Mode',
            myAccount: 'My Account',
            myProfile: 'My Profile',
            alerts: 'Alerts',
            alertsAll: 'All alerts',
            alertsEmpty: 'No alerts to display',
            alertErrors: 'Error',
            alertInfos: 'Info',
            alertWarnings: 'Warning',
            pageHelp: 'Page Help',
            portalSwitcherMenu: 'Application Portals',
            primaryColor: 'Primary Color',
            recent: 'Recent',
            reset: 'Reset',
            resetAllSettings: 'Reset All Settings',
            settings: 'Settings',
            toggleFavorite: 'Toggle Favorite',
            unsavedChanges: 'You have unsaved changes',
            user: 'User',
            userImageAlt: 'User Image',
            theme: 'Theme',
        });
        expect(application.user).toStrictEqual({
            image: {
                image: '',
                fallbackImage: '',
            },
            name: 'User1',
            orgCode: '',
        });
        expect(application.searchProvider).toBeUndefined;
        expect(application.searchHandler).toBeUndefined;
    });

    describe('it has a footerVisible property indicating when the framework footer is actually visible', () => {
        describe('footer should be invisible', () => {
            test('when currentTask is null', async () => {
                application.currentTask = null;

                expect(application.footerVisible).toBe(false);
            });

            test('when showFooter is false and cancelAction is available', async () => {
                application.currentTask = {} as WtgFrameworkTask;
                application.currentTask!.cancelAction = {
                    visible: true,
                    caption: 'Cancel changes',
                    label: 'Cancel',
                    onInvoke: jest.fn(),
                };
                application.currentTask!.genericActions = [{} as WtgFrameworkTaskGenericAction];
                application.currentTask!.showFooter = false;

                expect(application.footerVisible).toBe(false);
            });

            test('when showFooter is false and saveAction is available', async () => {
                application.currentTask = {} as WtgFrameworkTask;
                application.currentTask!.saveAction = {
                    visible: true,
                    caption: 'Save changes',
                    label: 'Save',
                    onInvoke: jest.fn(),
                };
                application.currentTask!.genericActions = [{} as WtgFrameworkTaskGenericAction];
                application.currentTask!.showFooter = false;

                expect(application.footerVisible).toBe(false);
            });

            test('when showFooter is true but no visible content', async () => {
                application.currentTask = {} as WtgFrameworkTask;
                application.currentTask!.showFooter = true;

                expect(application.footerVisible).toBe(false);
            });
        });

        describe('footer should be visible', () => {
            beforeEach(() => {
                application.currentTask = {} as WtgFrameworkTask;
                application.currentTask!.showFooter = true;
            });

            test('when cancelAction is visible', async () => {
                application.currentTask!.cancelAction = {
                    visible: true,
                    caption: 'Cancel changes',
                    label: 'Cancel',
                    onInvoke: jest.fn(),
                };

                expect(application.footerVisible).toBe(true);
            });

            test('when saveAction is visible', async () => {
                application.currentTask!.saveAction = {
                    visible: true,
                    caption: 'Save changes',
                    label: 'Save',
                    onInvoke: jest.fn(),
                };

                expect(application.footerVisible).toBe(true);
            });

            test('when has genericActions', async () => {
                application.currentTask!.genericActions = [{} as WtgFrameworkTaskGenericAction];

                expect(application.footerVisible).toBe(true);
            });
        });
    });

    describe('it has a closeButtonVisible property indicating when the close button is actually visible', () => {
        describe('close button should be invisible', () => {
            test('when currentTask is null', async () => {
                application.currentTask = null;

                expect(application.closeButtonVisible).toBe(false);
            });

            test('when hideFooter in application is true', async () => {
                application.hideFooter = true;
                application.currentTask = {} as WtgFrameworkTask;
                application.currentTask!.cancelAction = {
                    visible: true,
                    caption: 'Cancel',
                    label: 'Cancel',
                    onInvoke: jest.fn(),
                };

                expect(application.closeButtonVisible).toBe(false);
            });
        });

        describe('close button should be visible', () => {
            beforeEach(() => {
                application.currentTask = {} as WtgFrameworkTask;
            });

            test('when cancelAction is visible', async () => {
                application.currentTask!.cancelAction = {
                    visible: true,
                    caption: 'Cancel',
                    label: 'Cancel',
                    onInvoke: jest.fn(),
                };

                expect(application.closeButtonVisible).toBe(true);
            });
        });
    });
});
