import { WtgFrameworkTask, WtgFrameworkTaskStandardAction } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import eDocsAction from '../EDocsAction.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('e-docs-action', () => {
    let task: WtgFrameworkTask;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        task = new WtgFrameworkTask();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is EDocsAction', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('EDocsAction');
    });

    describe('when given a showEDocsAction', () => {
        beforeEach(() => {
            task.showEDocsAction = {
                visible: false,
                caption: 'eDocs',
                label: 'eDocs',
                onInvoke: jest.fn(),
            };
        });

        test('its does not render a button when the action visible is false', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            expect(buttons.length).toBe(0);
        });

        test('it renders the button with aria-label and aria-haspopup="dialog" attributes', async () => {
            task.showEDocsAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.attributes('aria-haspopup')).toBe('dialog');
        });

        test('its does render a button with an icon when the action visible is true', () => {
            task.showEDocsAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.exists()).toBe(true);
        });

        test('the button calls the action onInvoke method when clicked', async () => {
            task.showEDocsAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            expect(task.showEDocsAction.onInvoke).toBeCalledTimes(1);
        });

        describe('on a large screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = false;
                task.showEDocsAction.visible = true;
            });

            test('it display as a ghost button', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().variant).toBe('ghost');
                expect(button.text()).toBe('eDocs');
                expect(button.attributes('aria-haspopup')).toBe('dialog');
            });

            test('it displays the button with a leading icon', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().leadingIcon).toBe('s-icon-eDocs');
            });
        });

        describe('on a small screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = true;
                task.showEDocsAction.visible = true;
            });

            test('it display as a icon button with a tooltip', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.props().icon).toBe('s-icon-eDocs');
                expect(button.props().tooltip).toBe('eDocs');
            });
        });
    });

    describe('when the task property is passed', () => {
        test('when props is passed', () => {
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task: task } });
            expect(wrapper.vm.action).toStrictEqual(task?.showEDocsAction);
        });

        test('when props is not passed', () => {
            const defaultValue: WtgFrameworkTaskStandardAction = {
                visible: false,
                caption: 'eDocs',
                label: 'eDocs',
                onInvoke: (): void => undefined,
            };
            const wrapper: VueWrapper<any> = mountComponent();
            expect(wrapper.vm.action.caption).toStrictEqual(defaultValue.caption);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(eDocsAction, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
