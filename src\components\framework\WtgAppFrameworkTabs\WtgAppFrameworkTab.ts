import { WtgTab } from '@components/WtgTabs';
import { WtgFrameworkTabInfo } from '@components/framework/types';
import { computed, defineComponent, h, inject, onBeforeUnmount, onMounted, reactive } from 'vue';

export default defineComponent({
    name: 'WtgAppFrameworkTab',
    props: {
        caption: {
            type: String,
            default: '',
        },
    },
    setup(props) {
        const appFrameworkTabsProvide = inject('appFrameworkTabsProvide', {
            tabs: [] as Array<WtgFrameworkTabInfo>,
        });

        onMounted(() => {
            const tabs = appFrameworkTabsProvide?.tabs;
            if (tabs) {
                const tab = reactive({ caption: computed(() => props.caption) });
                tabs.push(tab);
            }
        });

        onBeforeUnmount(() => {
            const tabs = appFrameworkTabsProvide?.tabs;
            if (tabs) {
                tabs.splice(
                    tabs.findIndex((tab) => tab.caption === props.caption),
                    1
                );
            }
        });
        return () => {
            return h(WtgTab, {}, () => props.caption);
        };
    },
});
