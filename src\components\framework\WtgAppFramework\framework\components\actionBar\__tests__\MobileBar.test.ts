import { WtgFrameworkTask, WtgFrameworkTaskGenericActionPlacement } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { reactive } from 'vue';
import WtgUi from '../../../../../../../WtgUi';
import MobileBar from '../MobileBar.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('mobile-bar', () => {
    let el: HTMLElement;
    let wrapper: VueWrapper<any>;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is MobileBar', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('MobileBar');
    });

    test('it will initially display no buttons', () => {
        wrapper = mountComponent();
        const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
        expect(buttons.length).toBe(0);
    });

    describe('when handling actions', () => {
        let task: WtgFrameworkTask;

        beforeEach(() => {
            task = reactive(new WtgFrameworkTask());
            task = {
                showFooter: true,
                cancelAction: {
                    visible: true,
                    caption: 'Close',
                    onInvoke: jest.fn(),
                },
                saveAction: {
                    visible: false,
                    caption: 'Save',
                    label: 'Save',
                    onInvoke: jest.fn(),
                },
                saveCloseAction: {
                    caption: 'Save and close',
                    label: 'Close',
                    onInvoke: jest.fn(),
                },
                genericActions: [],
            } as any;
        });

        test('it will make the default action highest priority action', async () => {
            wrapper = mountComponent({ props: { task } });
            let actions = wrapper.findComponent({ name: 'WtgButton' });
            expect(actions.text()).toBe('Close');

            task.cancelAction.caption = 'Cancel';
            task.saveAction.visible = true;

            wrapper = mountComponent({ props: { task } });
            actions = wrapper.findComponent({ name: 'WtgButton' });
            expect(actions.text()).toBe('Save');

            task.cancelAction.visible = false;
            task.saveAction.visible = false;
            task.genericActions.push({
                caption: 'Generic Action',
                placement: WtgFrameworkTaskGenericActionPlacement.Secondary,
                id: 'genericaction1',
                onInvoke: jest.fn(),
            });

            wrapper = mountComponent({ props: { task } });
            actions = wrapper.findComponent({ name: 'WtgButton' });
            expect(actions.text()).toBe('Generic Action');
        });

        test('it will pass the lower priority actions to the overflow menu', async () => {
            wrapper = mountComponent({ props: { task } });
            let actionsMenu = wrapper.findComponent({ name: 'ActionsMenuButton' });
            expect(actionsMenu.exists()).toBe(false);

            task.saveAction.visible = true;
            task.cancelAction.caption = 'Cancel';
            task.genericActions.push({
                caption: 'Generic Action',
                placement: WtgFrameworkTaskGenericActionPlacement.Secondary,
                id: 'genericaction1',
                onInvoke: jest.fn(),
            });

            wrapper = mountComponent({ props: { task } });
            actionsMenu = wrapper.findComponent({ name: 'ActionsMenuButton' });
            expect(actionsMenu.exists()).toBe(true);
            const menuItems = actionsMenu.props().actions;
            expect(menuItems.length).toBe(3);
            expect(menuItems[0].caption).toBe('Save and close');
            expect(menuItems[1].caption).toBe('Generic Action');
            expect(menuItems[2].caption).toBe('Cancel');
        });

        test('it will display the highest priority action as a button and also display in category if configured to be in category', async () => {
            task.cancelAction.visible = false;
            task.saveAction.visible = false;
            task.genericActions.push({
                caption: 'Generic Action',
                placement: WtgFrameworkTaskGenericActionPlacement.Secondary,
                category: 'Category',
                id: 'genericaction1',
                onInvoke: jest.fn(),
            });

            wrapper = mountComponent({ props: { task } });
            const actions = wrapper.findComponent({ name: 'WtgButton' });
            expect(actions.text()).toBe('Generic Action');
            const popUpMenu = wrapper.findComponent({ name: 'WtgPopupMenu' });
            expect(popUpMenu.exists()).toBe(true);
            expect(popUpMenu.props().actions.length).toBe(1);
            expect(popUpMenu.props().actions[0].caption).toBe('Category');
            expect(popUpMenu.props().actions[0].actions.length).toBe(1);
            expect(popUpMenu.props().actions[0].actions[0].caption).toBe('Generic Action');
        });
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(MobileBar, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
