<template>
    <div :class="computedClass" :style="computedStyles">
        <div v-if="hasDefaultSlotContent" class="wtg-avatar__default-slot">
            <slot></slot>
        </div>
        <WtgImage v-if="computedShowImg" aspect-ratio="1" :alt="alt" :src="image" :fallback-image="fallbackImage" />

        <WtgIcon v-if="computedShowAddIcon" :style="computedAddIconStyle"> s-icon-add </WtgIcon>

        <WtgLoader v-if="computedShowSpinnerIcon" :style="computedSpinnerIconStyle" />

        <WtgIcon v-if="computedShowIcon" class="wtg-avatar__icon">
            {{ icon }}
        </WtgIcon>

        <span v-if="computedShowInitials" class="wtg-avatar__initials">
            {{ initials }}
        </span>
        <div class="wtg-avatar__notification-wrapper">
            <div v-if="notificationIcon" :class="computedNotificationClass" :style="computedNotificationStyle">
                <WtgIcon> {{ notificationIcon }} </WtgIcon>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { WtgImage } from '@components/WtgImage';
import { WtgLoader } from '@components/WtgLoader';
import { useSize } from '@composables/size';
import { computed, PropType, ref, useSlots, watch } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * Alternative text for the avatar image.
     */
    alt: {
        type: String,
        default: '',
    },
    /**
     * Background color of the avatar. Accepts any valid CSS color string.
     */
    color: {
        type: String,
        default: undefined,
    },
    /**
     * If true, the avatar is disabled and cannot be interacted with.
     */
    disabled: {
        type: Boolean,
        default: false,
    },
    /**
     * Fallback image URL to use if the main image fails to load.
     */
    fallbackImage: {
        type: String,
        default: '',
    },
    /**
     * Icon name to display inside the avatar.
     */
    icon: {
        type: String,
        default: '',
    },
    /**
     * Image URL to display as the avatar.
     */
    image: {
        type: String,
        default: '',
    },
    /**
     * Initials to display if no image or icon is provided.
     */
    initials: {
        type: String,
        default: '',
    },
    /**
     * If true, shows a loading spinner on the avatar.
     */
    loading: {
        type: Boolean,
        default: false,
    },
    /**
     * The size of the avatar. Can be 'm', 'l', 'xl', or 'xxl'.
     */
    size: {
        type: [String, Number] as PropType<'m' | 'l' | 'xl' | 'xxl'>,
        default: 'xxl',
    },
    /**
     * If true, shows an upload/add icon on the avatar.
     */
    upload: {
        type: Boolean,
        default: false,
    },
    /**
     * Icon name to display as a notification badge.
     */
    notificationIcon: {
        type: String,
        default: '',
    },
    /**
     * Sentiment for the notification badge. Can be 'critical', 'primary', or 'success'.
     */
    notificationSentiment: {
        type: String as PropType<'critical' | 'primary' | 'success'>,
        default: '',
    },
});

//
// Slots
//
const slots = useSlots();

//
// State
//
const avatarSpinner = ref(false);
const avatarUpload = ref(false);

//
// Composables
//
const { sizeClass, sizeStyle } = useSize(props, 'avatar');

//
// Computed
//
const computedShowImg = computed(() => {
    return props.image;
});

const computedShowAddIcon = computed(() => {
    return avatarUpload.value;
});

const computedShowSpinnerIcon = computed(() => {
    return !avatarUpload.value && avatarSpinner.value;
});

const computedShowIcon = computed(() => {
    return !props.image && !avatarUpload.value && !avatarSpinner.value && props.icon;
});

const computedShowInitials = computed(() => {
    return !props.image && !avatarUpload.value && !avatarSpinner.value && !props.icon && props.initials;
});

const computedAddIconStyle = computed(() => ({
    color: props.image ? 'var(--s-neutral-icon-inv-default)' : 'var(--s-primary-icon-hover)',
    position: 'absolute',
}));

const computedSpinnerIconStyle = computed(() => ({
    color: props.image ? 'var(--s-neutral-icon-inv-default)' : 'var(--s-primary-icon-active)',
    position: 'absolute',
}));

const computedClass = computed(() => [
    {
        'wtg-avatar--disabled': props.disabled,
        'wtg-avatar__border': computedShowInitials.value || computedShowIcon.value,
    },
    'wtg-avatar',
    sizeClass.value,
]);

const computedNotificationStyle = computed(() => {
    if (props.size === 'm') {
        return {
            width: 'var(--s-sizing-xs)',
            height: 'var(--s-sizing-xs)',
            left: 'calc(-1 * var(--s-sizing-xs)/2)',
        };
    }
    if (props.size === 'l') {
        return {
            width: 'var(--s-sizing-xs)',
            height: 'var(--s-sizing-xs)',
            left: 'calc(-1 * var(--s-sizing-xs)/2)',
        };
    }
    if (props.size === 'xl') {
        return {
            width: 'var(--s-sizing-s)',
            height: 'var(--s-sizing-s)',
            left: 'calc(-1 * var(--s-sizing-xs))',
        };
    }
    if (props.size === 'xxl') {
        return {
            width: 'var(--s-sizing-m)',
            height: 'var(--s-sizing-m)',
            left: 'calc(-1 * var(--s-sizing-xs))',
        };
    } else {
        return {
            width: 'var(--s-sizing-m)',
            height: 'var(--s-sizing-m)',
            left: 'calc(-1 * var(--s-sizing-xs))',
        };
    }
});

const computedNotificationClass = computed(() => {
    const classes = [
        'wtg-avatar__notification',
        {
            'wtg-avatar__notification--primary': props.notificationSentiment === 'primary',
            'wtg-avatar__notification--critical': props.notificationSentiment === 'critical',
            'wtg-avatar__notification--success': props.notificationSentiment === 'success',
        },
    ];

    return classes;
});

const computedStyles = computed(() => ({
    ...sizeStyle.value,
    background: `${props.color} !important`,
}));

const hasDefaultSlotContent = computed(() => {
    const slot =
        slots.default &&
        (typeof slots.default()[0].children !== 'string' || (slots.default()[0].children! as string).trim().length > 0);

    return slot;
});

//
// Watchers
//
watch(
    () => props.disabled,
    (newValue: any) => {
        if (newValue) {
            avatarUpload.value = false;
            avatarSpinner.value = false;
        }
    }
);

watch(
    () => props.upload,
    (newValue: any) => {
        avatarUpload.value = newValue;
    }
);

watch(
    () => props.loading,
    (newValue: any) => {
        avatarSpinner.value = newValue;
    }
);
</script>

<style lang="scss">
.wtg-avatar {
    align-items: center;
    position: relative;
    background: var(--s-primary-bg-weak-default);
    border-radius: var(--s-radius-xxl);
    color: var(--s-primary-txt-default);
    display: flex;
    flex-shrink: 0;
    height: var(--s-sizing-xxl);
    justify-content: center;
    width: var(--s-sizing-xxl);

    &__border {
        border: 1px solid var(--s-primary-border-weak-default);

        &:hover {
            background: 1px solid var(--s-primary-border-weak-hover);
        }
    }

    i {
        color: var(--s-primary-icon-default);
    }

    &:hover {
        background: var(--s-primary-bg-weak-hover);
        i {
            color: var(--s-primary-icon-hover) !important;
        }
        span {
            color: var(--s-primary-txt-hover) !important;
        }
    }

    img {
        background: lightgray 50% / cover no-repeat;
        border-radius: var(--s-radius-xxl);
        height: 100%;
        object-fit: cover;
        width: 100%;

        &:hover {
            opacity: 0.75;
        }
    }

    &__loader {
        animation: rotation infinite 3s linear;
    }

    &__default-slot {
        display: flex;
        flex: 1 1 auto;
        position: relative;
        align-items: center;
        overflow: hidden;
        justify-content: center;

        &:focus {
            outline: none;
        }
    }

    &__notification-wrapper {
        display: flex;
        position: relative;
    }

    &__notification {
        align-items: center;
        background: var(--s-neutral-bg-default);
        border-radius: 50%;
        color: var(--s-neutral-txt-default);
        display: flex;
        justify-content: center;
        position: absolute;
        z-index: 1;

        &--primary {
            .wtg-icon {
                color: var(--s-primary-icon-default);
            }
            background: var(--s-primary-bg-weak-default);
        }

        &--success {
            .wtg-icon {
                color: var(--s-success-icon-default);
            }
            background: var(--s-success-bg-weak-default);
        }

        &--critical {
            .wtg-icon {
                color: var(--s-error-icon-default);
            }
            background: var(--s-error-bg-weak-default);
        }
    }

    &--disabled {
        background: var(--s-neutral-bg-disabled);
        border-color: var(--s-neutral-border-disabled);
        color: var(--s-neutral-txt-disabled);
        pointer-events: none;

        i {
            color: var(--s-neutral-icon-disabled);
        }
    }

    &--m {
        height: var(--s-sizing-m);
        width: var(--s-sizing-m);
        border-radius: var(--s-radius-m);

        > .wtg-avatar__initials {
            font: var(--s-text-xs-default);
        }

        .wtg-avatar__notification-wrapper {
            .wtg-avatar__notification {
                .wtg-icon {
                    font-size: var(--s-sizing-s);
                    height: var(--s-sizing-s);
                    width: var(--s-sizing-s);
                }
            }
        }

        > .wtg-avatar__icon {
            font-size: 12px;
            height: var(--s-sizing-s);
            width: var(--s-sizing-s);
        }
    }

    &--l {
        height: var(--s-sizing-l);
        width: var(--s-sizing-l);

        .wtg-avatar__initials {
            font: var(--s-text-xs-default);
        }

        .wtg-avatar__notification-wrapper {
            .wtg-avatar__notification {
                .wtg-icon {
                    height: var(--s-sizing-m);
                    width: var(--s-sizing-m);
                    font-size: var(--s-sizing-m);
                }
            }
        }

        > .wtg-avatar-icon {
            font-size: 16px;
            height: var(--s-sizing-m);
            width: var(--s-sizing-m);
        }
    }

    &--xl {
        height: var(--s-sizing-xl);
        width: var(--s-sizing-xl);

        > .wtg-avatar__initials {
            font: var(--s-text-xs-default);
        }

        > .wtg-avatar-icon {
            height: var(--s-sizing-l);
            width: var(--s-sizing-l);
        }
    }

    &--xxl {
        height: var(--s-sizing-xxl);
        width: var(--s-sizing-xxl);

        > .wtg-avatar__initials {
            font: var(--s-text-xs-default);
        }

        > .wtg-avatar-icon {
            height: var(--s-sizing-l);
            width: var(--s-sizing-l);
        }
    }
}
</style>
