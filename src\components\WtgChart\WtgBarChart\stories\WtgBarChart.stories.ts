import { Meta, StoryObj } from '@storybook/vue3';
import { WtgBarChart } from '../..';

type Story = StoryObj<typeof WtgBarChart>;
const meta: Meta<typeof WtgBarChart> = {
    title: 'Data viz/Bar Chart',
    component: WtgBar<PERSON>hart,
    parameters: {
        docs: {
            description: {
                component:
                    'A bar chart is used when you want to show a distribution of data points or perform a comparison of metric values across different subgroups of your data. From a bar chart, we can see which groups are highest or most common, and how other groups compare against the others.',
            },
        },
    },
    render: (args) => ({
        components: { WtgBarChart },
        setup: () => ({ args }),
        template: `<WtgBarChart v-bind="args"/>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        data: {
            labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
            datasets: [
                {
                    label: 'My First Dataset',
                    data: [65, 59, 80, 81, 56, 55, 40],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.5)',
                },
                {
                    label: 'My Second Dataset',
                    data: [81, 56, 55, 40, 65, 59, 80],
                    borderColor: 'rgb(192, 192, 75)',
                    backgroundColor: 'rgba(192, 192, 75, 0.5)',
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
            },
        },
        loading: false,
    },
};
