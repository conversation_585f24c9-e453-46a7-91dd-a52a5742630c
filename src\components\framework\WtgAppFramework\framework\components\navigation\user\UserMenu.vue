<template>
    <UserInlineMenu v-if="isTabletOrMobile" @item-click="emit('item-click')" />
    <WtgPopover
        v-else
        close-on-content-click
        location="right bottom"
        nudge-right="var(--s-padding-xl)"
        min-width="312px"
        max-width="312px"
    >
        <template #activator="args">
            <WtgListItem
                role="menuitem"
                :aria-label="application.ariaLabels.userAccountPrefix + ' ' + name"
                aria-haspopup="menu"
                :class="!railActive ? '' : 'd-flex justify-center'"
                v-bind="args.props"
                :collapsed="railActive"
                trailing-icon="s-icon-caret-right"
            >
                <template #leading>
                    <WtgAvatar
                        v-if="currentUser.image.image"
                        :alt="currentUser.name"
                        :image="currentUser.image.image"
                        size="l"
                    />
                    <WtgIcon v-else>{{ currentUser.image.fallbackImage }}</WtgIcon>
                </template>
                {{ name }}
            </WtgListItem>
        </template>
        <WtgList>
            <div class="wtg-typography-title-small">
                {{ application.captions.myAccount }}
            </div>
            <WtgDivider />
            <WtgListItem v-if="currentUser.isImpersonated">
                <WtgCallout
                    :sentiment="WtgCalloutSentimentType.Critical"
                    :description="formatCaption('userImpersonation.impersonatingWarning')"
                >
                </WtgCallout>
            </WtgListItem>
            <WtgListItem class="nonClickable">
                <template #leading>
                    <WtgAvatar :image="currentUser.image.image" :icon="currentUser.image.fallbackImage" class="mr-2" />
                </template>
                <div>
                    <div :style="userAccountStyle">{{ name }}</div>
                    <div class="text-primary" :style="userAccountLinkStyle">
                        {{ currentUser.emailAddress }}
                    </div>
                    <WtgTag v-if="currentUser.orgCode" :label="currentUser.orgCode"></WtgTag>
                </div>
            </WtgListItem>
            <WtgDivider />
            <WtgListItem
                v-if="currentUser.onProfile"
                role="menuitem"
                :aria-label="application.captions.manageAccount"
                leading-icon="s-icon-user"
                @click="onProfileClick"
            >
                {{ application.captions.manageAccount }}
            </WtgListItem>
            <WtgListItem
                v-if="currentUser.onChangePassword"
                role="menuitem"
                :aria-label="application.captions.changePassword"
                leading-icon="s-icon-password"
                @click="onPasswordClick"
            >
                {{ application.captions.changePassword }}
            </WtgListItem>
            <WtgListItem
                v-if="currentUser.onChangeBranchDepartment"
                role="menuitem"
                :aria-label="application.captions.changeBranchDepartment"
                leading-icon="s-icon-building"
                @click="onChangeBranchClick"
            >
                {{ application.captions.changeBranchDepartment }}
            </WtgListItem>
            <WtgListItem
                v-if="currentUser.onImpersonateContactUser"
                role="menuitem"
                :aria-label="application.captions.impersonateContactUser"
                leading-icon="s-icon-user"
                @click="onImpersonateContactUserClick"
            >
                {{ application.captions.impersonateContactUser }}
            </WtgListItem>

            <WtgListItem
                role="menuitem"
                :aria-label="appearanceText"
                leading-icon="s-icon-toggle-light-dark"
                @click="toggleAppearance"
            >
                {{ appearanceText }}
            </WtgListItem>
            <WtgDivider />
            <div v-if="isSupplyBetaComponentsEnabled">
                <div>
                    <h3>SUPPLY Beta Components</h3>
                    <span>
                        Apply the SUPPLY Beta Components to this portal. This setting is temporary and will not be
                        saved.
                    </span>
                </div>
                <WtgSegmentedControl :model-value="frameworkVersion" mandatory dense>
                    <WtgButton @click="resetFrameworkVersion"> Reset </WtgButton>
                    <WtgButton :value="2000" @click="frameworkVersion = 2000"> 2.0 </WtgButton>
                    <WtgButton :value="FRAMEWORK_VERSION_BETA" @click="frameworkVersion = FRAMEWORK_VERSION_BETA">
                        Beta
                    </WtgButton>
                </WtgSegmentedControl>
                <WtgDivider class="mt-2 mb-1" />
            </div>
            <WtgListItem
                v-if="currentUser.onLogOff"
                role="menuitem"
                :aria-label="application.captions.logOff"
                leading-icon="s-icon-sign-out"
                @click="onLogOffClick"
            >
                {{ application.captions.logOff }}
            </WtgListItem>
        </WtgList>
    </WtgPopover>
</template>

<script setup lang="ts">
import { WtgFrameworkUser } from '@components/framework/types';
import WtgAvatar from '@components/WtgAvatar';
import WtgButton from '@components/WtgButton';
import WtgCallout, { WtgCalloutSentimentType } from '@components/WtgCallout';
import WtgTag from '@components/WtgTag';
import WtgDivider from '@components/WtgDivider';
import WtgIcon from '@components/WtgIcon';
import { WtgList, WtgListItem } from '@components/WtgList';
import WtgPopover from '@components/WtgPopover';
import WtgSegmentedControl from '@components/WtgSegmentedControl';
import { useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { useFeatureFlag, useFrameworkVersion } from '@composables/global';
import { useLocale } from '@composables/locale';
import { useTheme } from '@composables/theme';
import { computed, StyleValue } from 'vue';
import UserInlineMenu from './UserInlineMenu.vue';

//
// Emits
//
const emit = defineEmits<{
    'item-click': [];
}>();

//
// Composables
//
const { isTabletOrMobile } = useFramework();
const { darkOption, appearance } = useTheme();
const application = useApplication();
const { frameworkVersion, resetFrameworkVersion, FRAMEWORK_VERSION_BETA } = useFrameworkVersion();
const { formatCaption } = useLocale();
const { isSupplyBetaComponentsEnabled } = useFeatureFlag();

//
// Computed
//
const railActive = computed(() => {
    return isTabletOrMobile.value ? false : application.navDrawer.isRailActive;
});

const appearanceText = computed((): string => {
    switch (appearance.value) {
        case 'dark':
            return application.captions.mutedMode;
        case 'muted':
            return application.captions.lightMode;
        default:
            return application.captions.darkMode;
    }
});

const currentUser = computed((): WtgFrameworkUser => {
    return (
        application.user ?? {
            name: '',
            orgCode: '',
            emailAddress: '',
            image: { image: '', fallbackImage: '' },
        }
    );
});

const name = computed((): string => {
    return currentUser.value.name;
});

const userAccountStyle = computed((): StyleValue => {
    return {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        lineHeight: 'inherit',
    };
});
const userAccountLinkStyle = computed((): StyleValue => {
    return {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        lineHeight: 'inherit',
        textDecoration: 'underline',
        textUnderlineOffset: '3px',
    };
});

//
// Event Handlers
//
function onProfileClick(): void {
    if (currentUser.value.onProfile) {
        currentUser.value.onProfile('_self');
    }
}

function onPasswordClick(): void {
    if (currentUser.value.onChangePassword) {
        currentUser.value.onChangePassword('_self');
    }
}

function onChangeBranchClick(): void {
    if (currentUser.value.onChangeBranchDepartment) {
        currentUser.value.onChangeBranchDepartment('_self');
    }
}

function onImpersonateContactUserClick(): void {
    if (currentUser.value.onImpersonateContactUser) {
        currentUser.value.onImpersonateContactUser();
    }
}

function onLogOffClick(): void {
    if (currentUser.value.onLogOff) {
        currentUser.value.onLogOff();
    }
}

//
// Helpers
//
function toggleAppearance(): void {
    switch (appearance.value) {
        case 'light':
            appearance.value = 'dark';
            break;
        case 'dark':
            appearance.value = 'muted';
            break;
        case 'muted':
            appearance.value = 'light';
            break;
        default:
            appearance.value = darkOption ? 'dark' : 'light';
            break;
    }
}
</script>

<style lang="scss">
.wtg-usermenu-title {
    &.wtg-list-item:hover:before {
        cursor: default;
        background-color: unset;
    }

    .wtg-avatar {
        cursor: default;
    }
}
</style>
