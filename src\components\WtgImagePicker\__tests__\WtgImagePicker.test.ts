import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { nextTick } from 'vue';
import { WtgImagePicker } from '../';
import WtgUi from '../../../WtgUi';
import { defaultCaptionProvider } from '../../../language/captions';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

afterEach(() => {
    wtgUi.language.current = 'en-AU';
});

describe('WtgImagePicker', () => {
    test('its name is WtgImagePicker', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgImagePicker');
    });

    test('it renders a WtgImage component', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent({ name: 'WtgImage' }).exists()).toBeTruthy();
    });

    test('it passes its properties to the WtgImage component', () => {
        const wrapper = mountComponent({
            props: {
                alt: 'alt text',
                aspectRatio: '1/7',
                contentClass: 'content class',
                eager: true,
                gradient: 'my gradient',
                height: 300,
                lazySrc: 'my lazy src',
                maxHeight: 400,
                maxWidth: 410,
                minHeight: 200,
                minWidth: 210,
                sizes: 'my sizes',
                src: 'MyImg',
                srcset: 'MyImg MyImg2',
                transition: 'slideLeft',
                width: 310,
            },
        });
        const props = wrapper.findComponent({ name: 'WtgImage' }).props();
        expect(props.alt).toBe('alt text');
        expect(props.aspectRatio).toBe('1/7');
        expect(props.eager).toBe(true);
        expect(props.gradient).toBe('my gradient');
        expect(props.height).toBe(300);
        expect(props.lazySrc).toBe('my lazy src');
        expect(props.maxHeight).toBe(400);
        expect(props.maxWidth).toBe(410);
        expect(props.minHeight).toBe(200);
        expect(props.minWidth).toBe(210);
        expect(props.sizes).toBe('my sizes');
        expect(props.src).toBe('MyImg');
        expect(props.srcset).toBe('MyImg MyImg2');
        expect(props.transition).toBe('slideLeft');
        expect(props.width).toBe(310);
    });

    test('given no src it should display overlay card but not display clear icon', () => {
        const wrapper = mountComponent({
            props: {
                src: null,
            },
        });

        const card = wrapper.find('.wtg-image-picker__overlay-add');
        const icons = wrapper.findAllComponents({ name: 'WtgIcon' });
        expect(card.exists()).toBe(true);
        expect(icons.length).toBe(1);
        expect(icons.at(0)?.html()).toContain('s-icon-camera');
    });

    test('given no src and readonly it should not display overlay card', () => {
        const wrapper = mountComponent({
            props: {
                src: null,
                readonly: true,
            },
        });

        const card = wrapper.find('.wtg-image-picker__overlay-add');
        const icons = wrapper.findAllComponents({ name: 'WtgIcon' });
        expect(card.exists()).toBe(false);
        expect(icons.length).toBe(0);
    });

    test('given no src and readonly it should not trigger file selection when clicking on the image picker', async () => {
        const wrapper = mountComponent({
            props: {
                src: null,
                readonly: true,
            },
        });

        const fileInput = wrapper.vm.$refs.fileInputRef as HTMLElement;
        expect(fileInput).not.toBeTruthy();
    });

    test('given no src when clicking on the image picker it should trigger file selection', async () => {
        const wrapper = mountComponent({
            props: {
                src: null,
            },
        });

        const fileInput = wrapper.vm.$refs.fileInputRef as HTMLElement;
        jest.spyOn(fileInput, 'click');
        await wrapper.trigger('click');

        expect(fileInput.click).toBeCalled();
    });

    test('given src it should display image', async () => {
        const wrapper = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const image = wrapper.findComponent({ name: 'WtgImage' });
        expect(image.exists()).toBe(true);
        expect(image.isVisible()).toBe(true);
        expect(image.props().src).toBe(wrapper.props().src);
    });

    test('given src it should display overlay card and clear icon', () => {
        const wrapper = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const card = wrapper.find('.wtg-image-picker__overlay-add');
        const icons = wrapper.findAllComponents({ name: 'WtgIcon' });
        expect(card.exists()).toBe(true);
        expect(icons.length).toBe(2);
        expect(icons.at(0)?.html()).toContain('s-icon-camera');
        expect(icons.at(1)?.html()).toContain('s-icon-close');
    });

    test('given src and readonly it should not display overlay card', () => {
        const wrapper = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
                readonly: true,
            },
        });

        const card = wrapper.findComponent({ name: 'WtgCard' });
        const icons = wrapper.findAllComponents({ name: 'WtgIcon' });
        expect(card.exists()).toBe(false);
        expect(icons.length).toBe(0);
    });

    test('given src and readonly it should not trigger file selection when clicking on the image', async () => {
        const wrapper = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
                readonly: true,
            },
        });

        const fileInput = wrapper.vm.$refs.fileInputRef as HTMLElement;

        expect(fileInput).not.toBeTruthy();
    });

    test('given src when clicking in the image it should trigger file selection', async () => {
        const wrapper = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const fileInput = wrapper.vm.$refs.fileInputRef as HTMLElement;
        jest.spyOn(fileInput, 'click');
        await wrapper.trigger('click');

        expect(fileInput.click).toBeCalled();
    });

    test('when clicking in the clear icon it should trigger image-selected event with null image', async () => {
        const wrapper = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const icons = wrapper.findAllComponents({ name: 'WtgIcon' });
        expect(icons.length).toBe(2);
        expect(icons.at(0)?.html()).toContain('s-icon-camera');
        expect(icons.at(1)?.html()).toContain('s-icon-close');

        await icons.at(1)?.trigger('click');

        expect(wrapper.emitted('image-selected')!.length).toBe(1);
        expect(wrapper.emitted('image-selected')![0]![0]).toBeUndefined();
    });

    test('given non-image file selected it should show error to the user in the current language', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const file = createFile('notAnImage.txt', 'text/plain');

        expect(wrapper.vm.hasErrors).toBe(false);
        expect(wrapper.vm.errorText).toBe('');

        wrapper.vm.onFileSelected({ target: { files: [file] } });
        await nextTick();

        expect(wrapper.vm.hasErrors).toBe(true);
        expect(wrapper.vm.errorText).toBe(defaultCaptionProvider('en-AU', 'imagePicker.invalidFileError'));

        wtgUi.language.current = 'nl-NL';
        wrapper.vm.onFileSelected({ target: { files: [file] } });
        await nextTick();

        expect(wrapper.vm.errorText).toBe(defaultCaptionProvider('nl-NL', 'imagePicker.invalidFileError'));
    });

    test('given non-image file selected it should not trigger image-selected event', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const file = createFile('notAnImage.txt', 'text/plain');

        wrapper.vm.onFileSelected({ target: { files: [file] } });

        await nextTick();

        expect(wrapper.emitted('image-selected')).toBeUndefined();
    });

    test('given image file selected bigger than max file size it should show error to the user in the current language', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const file = { name: 'test.png', size: 10485761, type: 'image/png' };

        expect(wrapper.vm.hasErrors).toBe(false);
        expect(wrapper.vm.errorText).toBe('');

        wrapper.vm.onFileSelected({ target: { files: [file] } });
        await nextTick();

        expect(wrapper.vm.hasErrors).toBe(true);
        expect(wrapper.vm.errorText).toBe(defaultCaptionProvider('en-AU', 'imagePicker.maxFileSizeError'));

        wtgUi.language.current = 'nl-NL';
        wrapper.vm.onFileSelected({ target: { files: [file] } });
        await nextTick();

        expect(wrapper.vm.errorText).toBe(defaultCaptionProvider('nl-NL', 'imagePicker.maxFileSizeError'));
    });

    test('given image file selected bigger than max file size it should not trigger image-selected event', () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const file = { target: { name: 'test.png', size: 10485761, type: 'image/png' } };
        expect(wrapper.vm.hasErrors).toBe(false);
        expect(wrapper.vm.errorText).toBe('');

        wrapper.vm.onFileSelected(file);

        expect(wrapper.emitted('image-selected')).toBeUndefined();
    });

    test('given valid image file it should trigger image-selected event with image file', () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                src: 'https://www.test.com/image.png',
            },
        });

        const file = createFile();

        wrapper.vm.onFileSelected({ target: { files: [file] } });

        expect(wrapper.emitted('image-selected')!.length).toBe(1);
        expect(wrapper.emitted('image-selected')![0][0]).toBe(file);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    function createFile(name = 'test.png', type = 'image/png'): File {
        return new File([], name, { type });
    }

    function mountComponent({ props = {}, slots = {}, provide = {} } = {}) {
        return mount(WtgImagePicker, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
