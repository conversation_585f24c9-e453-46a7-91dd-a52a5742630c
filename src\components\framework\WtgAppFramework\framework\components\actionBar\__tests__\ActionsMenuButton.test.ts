import { WtgActionItemData } from '@components/WtgMenu';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import ActionsMenuButton from '../ActionsMenuButton.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('actions-menu-button', () => {
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is ActionsMenuButton', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('ActionsMenuButton');
    });

    describe('when actions are passed', () => {
        let actions: WtgActionItemData[] = [];
        let wrapper: VueWrapper;

        beforeEach(() => {
            actions = [{ caption: 'Default Action', click: jest.fn() }];
            const props = { actions };
            wrapper = mountComponent({ props });
        });

        test('it will render a menu containing the actions', async () => {
            const button = wrapper.findComponent({ name: 'WtgIconButton' });
            await button.trigger('click');

            const items = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(items.length).toBe(1);
            expect(items.at(0)!.text()).toBe('Default Action');
            await items.at(0)!.trigger('click');

            expect(actions[0].click).toHaveBeenCalledTimes(1);
        });
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(ActionsMenuButton, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
