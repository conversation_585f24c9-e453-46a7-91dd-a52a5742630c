import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgCardTitle } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgCardTitle', () => {
    test('it renders a <div> component', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('DIV');
    });

    test('it adds the wtg-card-title class to allow application styling to be added', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('wtg-card-title');
    });

    test('it renders as a VCardTitle to ensure we are backwards compatible with Vue2 code', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('v-card-title');
    });

    test('it passes the default slot to the <div>', () => {
        const wrapper = mountComponent();
        expect(wrapper.text()).toBe('Some Content');
    });

    function mountComponent({ propsData = {}, slots = { default: 'Some Content' } } = {}) {
        return mount(WtgCardTitle, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
