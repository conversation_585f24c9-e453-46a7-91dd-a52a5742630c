import { WtgFrameworkDocumentsMenuItem, WtgFrameworkTask } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import DocumentsAction from '../DocumentsAction.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('documents-action', () => {
    let task: WtgFrameworkTask;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        task = new WtgFrameworkTask();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is DocumentsAction', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('DocumentsAction');
    });

    describe('when given documents', () => {
        let menuItems: WtgFrameworkDocumentsMenuItem[];

        beforeEach(() => {
            menuItems = [
                { caption: 'Option 1', submenu: false, click: jest.fn() },
                { caption: 'Option 2', submenu: false, click: jest.fn() },
                {
                    caption: 'Submenu 1',
                    submenu: true,
                    loadDocumentsAsync: jest.fn().mockResolvedValue([
                        { caption: 'Option 1 1', submenu: false, click: jest.fn() },
                        { caption: 'Option 1 2', submenu: false, click: jest.fn() },
                    ]),
                },
            ];

            task.documents = {
                visible: false,
                caption: 'Documents',
            };
        });

        test('its does not render a popup menu when the action visible is false', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const menu = wrapper.findComponent({ name: 'WtgPopover' });
            expect(menu.exists()).toBe(false);
        });

        test('its does render a button when the action visible is true', () => {
            task.documents.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const menuButton = wrapper.findAllComponents({ name: 'WtgButton' }).at(0);
            expect(menuButton?.exists()).toBe(true);
        });

        test('it renders the button with aria-haspopup="menu" attributes', () => {
            task.documents.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.attributes('aria-haspopup')).toBe('menu');
        });

        test('it renders the button with aria-expanded="true" attribute when documents menu is open', async () => {
            task.documents.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            expect(button.attributes('aria-expanded')).toBe('true');
        });

        describe('on a large screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = false;
                task.documents.visible = true;
            });

            test('it display as a text button', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().variant).toBe('ghost');
                expect(button.text()).toBe('Documents');
            });

            test('it displays the button with a leading icon and a caption', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().leadingIcon).toBe('s-icon-documentation');
                expect(button.text()).toBe('Documents');
                expect(button.props().tooltip).toBe('Documents');
            });
        });

        describe('on a small screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = true;
                task.documents.visible = true;
            });

            test('it display as an icon', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgIcon' });
                expect(button.props().icon).toBe('s-icon-documentation');
            });
        });

        test('menu items are populated when button is clicked', async () => {
            task.documents = {
                visible: true,
                caption: 'Documents',
                loadDocumentsAsync: jest.fn().mockResolvedValue(menuItems),
            };

            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task: task } });

            expect(wrapper.vm.menuItems.length).toBe(0);

            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            expect(wrapper.vm.menuItems.length).toBe(3);
            expect(wrapper.vm.menuItems[0].caption).toBe('Option 1');
            expect(wrapper.vm.menuItems[1].caption).toBe('Option 2');
            expect(wrapper.vm.menuItems[2].caption).toBe('Submenu 1');
        });
    });

    describe('when the task property is passed', () => {
        test('when props is passed', () => {
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task: task } });
            expect(wrapper.vm.action).toStrictEqual(task?.documents);
        });

        test('when props is not passed', () => {
            const defaultValue = {
                visible: false,
                caption: 'Documents',
            };
            const wrapper: VueWrapper<any> = mountComponent();
            expect(wrapper.vm.action).toStrictEqual(defaultValue);
        });
    });

    describe('when popup menu item is clicked', () => {
        it('calls click if action.click is provided', async () => {
            const clickMock = jest.fn();
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task: task } });
            await wrapper.vm.onAction({ click: clickMock });
            expect(clickMock).toHaveBeenCalled();
        });

        it('loads documents async if action.loadDocumentsAsync is provided', async () => {
            const loadDocumentsAsyncMock = jest.fn().mockResolvedValue([{ label: 'doc1' }]);
            const actionObj = {
                loadDocumentsAsync: loadDocumentsAsyncMock,
                loading: false,
                actions: [],
            };
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task: task } });
            await wrapper.vm.onAction(actionObj);
            expect(loadDocumentsAsyncMock).toHaveBeenCalled();
            expect(actionObj.loading).toBe(false);
            expect(actionObj.actions).toEqual([{ label: 'doc1' }]);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(DocumentsAction, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
