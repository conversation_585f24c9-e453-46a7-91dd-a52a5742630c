import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgFileArea from './WtgFileArea.stories.ts';

<Meta of={WtgFileArea} />

<div className="component-header">
    <h1>File area</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td></td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

## API

<Canvas className="canvas-preview" of={WtgFileArea.Default} />
<Controls of={WtgFileArea.Default} sort={'alpha'} />

<footer>
    <hr />
    <p>
        💙 Have ideas, comments, or suggestions to improve this page? [Let us
        know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
    </p>
</footer>
