export const DropdownButtonWithIconsTemplate = `
<WtgRow>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgDropdownButton 
            v-bind="args"
            @click="action"
            @dropdown-click="dropdownAction">
                Outline
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
        </WtgDropdownButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            sentiment="primary"
            @click="action"
            @dropdown-click="dropdownAction">
                Fill Primary
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
            </template>
            </WtgDropdownButton>
    </WtgCol>
</WtgRow>`;
