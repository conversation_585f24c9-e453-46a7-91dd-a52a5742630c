export const DropdownButtonWithVariantsTemplate = `
<div style="flex: 1 1 100%; display: flex; flex-wrap: wrap; gap: 8px">
    <WtgDropdownButton 
        v-bind="args"
        @click="action"
        @dropdown-click="dropdownAction">
            Outline
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
    </WtgDropdownButton>
    <WtgDropdownButton 
        v-bind="args"
        variant="fill"
        sentiment="primary"
        @click="action"
        @dropdown-click="dropdownAction">
            Fill Primary
            <template #popover>
                <WtgList>
                    <WtgListItem type="subheader">Title</WtgListItem>
                    <WtgListItem type="divider" />
                    <WtgListItem>Item 1</WtgListItem>
                    <WtgListItem>Item 2</WtgListItem>
                    <WtgListItem>Item 3</WtgListItem>
                </WtgList>
            </template>
    </WtgDropdownButton>
</div>`;
