import WtgDocumentPreview from '@components/WtgDocumentPreview/WtgDocumentPreview.vue';
import { Meta, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgDocumentPreview>;
const meta: Meta<typeof WtgDocumentPreview> = {
    title: 'Utilities/Document Preview',
    component: WtgDocumentPreview,
    parameters: {
        docs: {
            description: {
                component:
                    'Document Preview is a utility component that is used to open and display various file types. Supported file types include BMP, GIF, JPG, JPEG, PDF, PNG.',
            },
        },
    },
    render: (args) => ({
        components: { WtgDocumentPreview },
        setup: () => ({ args }),
        template: '<WtgDocumentPreview v-bind="args"/>',
    }),
    decorators: [
        () => ({
            template: `
            <div style="display: flex; flex-wrap: wrap;">
                <story/>
            </div>
            `,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        src: 'https://www.wisetechglobal.com/media/stjg2w4m/wisetech-global-annual-report-2023.pdf',
        fileExtension: 'PDF',
    },
};

export const Image: Story = {
    args: {
        src: 'https://www.wisetechglobal.com/media/e5qibuwd/shareholder-services-banner.png',
        fileExtension: 'PNG',
    },
    render: (args) => ({
        components: { WtgDocumentPreview },
        setup: () => ({ args }),
        template: '<WtgDocumentPreview v-bind="args"/>',
    }),
};

export const NoPreviewSupport: Story = {
    args: {
        src: 'bad-url',
        fileExtension: 'TIF',
    },
    render: (args) => ({
        components: { WtgDocumentPreview },
        setup: () => ({ args }),
        template: '<WtgDocumentPreview v-bind="args"/>',
    }),
};
