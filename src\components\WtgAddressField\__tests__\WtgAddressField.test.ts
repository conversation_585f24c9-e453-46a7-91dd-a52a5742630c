import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import WtgPanel from '@components/WtgPanel';
import { WtgSearchField } from '@components/WtgSearchField';
import { SearchFieldItemProvider } from '@components/WtgSearchField/types';
import { DOMWrapper, enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import { AddressLookupSearchContent, WtgAddressField } from '../';
import WtgUi from '../../../WtgUi';
import AddressItemProvider from './AddressesItemProvider';
import addresses from './addresses';
import { usePendingCommits } from '@composables/input';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const domWrapper = new DOMWrapper(document.body);

describe('WtgAddressField', () => {
    let itemProvider: SearchFieldItemProvider;
    let selectedAddress: AddressLookupSearchContent;

    beforeEach(() => {
        window.focus = jest.fn();

        jest.useFakeTimers();
        itemProvider = new AddressItemProvider();
        selectedAddress = {
            guid: '1',
            companyGuid: '1',
            company: 'Big Corp',
            companyCode: 'BIG',
            code: '1LO',
            street: '111/22 Address line 1',
            streetAlt: 'Address line 2',
            city: 'Suburb',
            postcode: 'postcode',
            state: 'region',
            countryCode: 'GR',
        };
    });

    test('it renders a WtgSearchField', () => {
        const wrapper = mountComponent({
            props: {
                value: '1',
            },
        });
        expect(wrapper.findComponent(WtgSearchField)).toBeDefined();
    });

    test('it passes its props to the base WtgSearchField', () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
                displayOnly: true,
                flat: true,
                id: '111',
                label: 'My Label',
                loading: true,
                messages: 'message',
                placeholder: 'placeholder',
                promptable: true,
                readonly: true,
                required: true,
                restricted: true,
                sentiment: 'primary',
                searchProvider: {},
            },
        });
        const props = wrapper.findComponent({ name: 'WtgSearchField' }).props();
        expect(props.disabled).toBe(true);
        expect(props.displayOnly).toBe(true);
        expect(props.flat).toBe(true);
        expect(props.id).toBe('111');
        expect(props.label).toBe('My Label');
        expect(props.loading).toBe(true);
        expect(props.messages).toBe('message');
        expect(props.promptable).toBe(true);
        expect(props.placeholder).toBe('placeholder');
        expect(props.readonly).toBe(true);
        expect(props.required).toBe(true);
        expect(props.restricted).toBe(true);
        expect(props.sentiment).toBe('primary');
        expect(props.searchProvider).toEqual({});
    });

    test('it displays the selected address on the wtg-panel below', async () => {
        const wrapper = mountComponent({ props: { showSelectedAddress: true } });

        await flushPromises();
        const input = wrapper.find('input');

        input.setValue('a');
        input.trigger('input');
        jest.runAllTimers();
        await flushPromises();

        const listitems = new DOMWrapper(document.body).findAll('.wtg-address-search__item');
        expect(listitems.at(0)?.text()).toBe('Ant Tech (ANT) 1YES, 1 Yes stSunTown, 2017, NSW, AU');

        await listitems.at(0)?.trigger('click');
        await flushPromises();
        expect(input.element.value).toBe('Ant Tech');

        const selectedItemPanel = wrapper.findComponent(WtgPanel);
        expect(selectedItemPanel.html()).toContain('<span class="wtg-label text-md-strong">Ant Tech</span> (ANT)');
    });

    test('it tracks pending promise when there is change', async () => {
        jest.spyOn(Promise, 'allSettled');
        const { waitForPendingCommits } = usePendingCommits();

        const wrapper = mountComponent({ props: { showSelectedAddress: true } });

        (wrapper.vm as any).onUpdateModelValue('test');

        await waitForPendingCommits();
        expect(Promise.allSettled).toHaveBeenCalledTimes(1);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('it shows the AddressTitle component with the correct props when a value is selected', async () => {
        const wrapper = mountComponent({
            props: {
                addressOverrideValue: selectedAddress,
                editable: true,
                hideAddressDisplayModeCode: true,
                showSelectedAddress: true,
            },
        });

        await flushPromises();
        const addressTitle = wrapper.findComponent({ name: 'AddressTitle' });
        expect(addressTitle.exists()).toBe(true);
        expect(addressTitle.props('title')).toBe('(BIG)');
        expect(addressTitle.props('editable')).toBe(true);
    });

    test('it shows emtpy string as address title when title is empty', async () => {
        const wrapper = mountComponent({
            props: {
                addressOverrideValue: { ...selectedAddress, companyCode: '' },
                editable: true,
                hideAddressDisplayModeCode: true,
                showSelectedAddress: true,
            },
        });

        await flushPromises();
        const addressTitle = wrapper.findComponent({ name: 'AddressTitle' });
        expect(addressTitle.exists()).toBe(true);
        expect(addressTitle.props('title')).toBe('');
        expect(addressTitle.props('editable')).toBe(true);
    });

    test('it shows the WtgHyperlink component with the correct caption when there is no address selected', async () => {
        const wrapper = mountComponent({
            props: { modelValue: undefined, editable: true, showSelectedAddress: true },
        });

        const hyperLink = wrapper.findComponent({ name: 'WtgHyperlink' });
        expect(hyperLink.exists()).toBe(true);
        expect(hyperLink.text()).toBe('Enter manually');
    });

    test('When contact is passed, the contact should be shown.', async () => {
        const wrapper = mountComponent({
            props: {
                addressOverrideValue: { company: 'Test Company' },
                contact: { name: 'Alberto', email: '<EMAIL>', phone: '+*********', mobile: '+*********' },
                showSelectedAddress: true,
                popupContactSelected: true,
            },
        });

        const addressDisplayMode = wrapper.findComponent({ name: 'AddressDisplayMode' });
        expect(addressDisplayMode.props('contact')).toBe('Alberto, <EMAIL>, +*********, +*********');
    });

    test('If there is no contact name then it should display the associated contact of the address', async () => {
        const wrapper = mountComponent({
            props: {
                addressOverrideValue: {},
                contact: { name: '', email: '<EMAIL>', phone: '+*********', mobile: '+*********' },
            },
        });

        (wrapper.vm as any).selectAddressItemAsync('1');
        await nextTick();

        const contactLines = (wrapper.vm as any).contactLines;
        expect(contactLines).toBe('<EMAIL>, 12345678, 12345678');
    });

    test('It will default to undefined value if there is no item found in RealAddressOnly mode', async () => {
        const wrapper = mountComponent({ props: { modelValue: undefined, returnObject: true } });

        await (wrapper.vm as any).selectAddressItemAsync('TEST');
        expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        expect((wrapper.emitted()['update:modelValue'] as any)![0][0]).toBe(undefined);
    });

    test('It will default to a value if there is no item found in RealAddressAndFreeText mode', async () => {
        const wrapper = mountComponent({
            props: { modelValue: undefined, allowFreeTextAddressEntry: true, returnObject: true },
        });

        await (wrapper.vm as any).selectAddressItemAsync('TEST');
        expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        expect((wrapper.emitted()['update:modelValue'] as any)![0][0]).toStrictEqual({
            company: 'TEST',
            companyGuid: undefined,
            guid: undefined,
            isAddressOverriden: true,
        });
    });

    test('It will update internal value when the selected address is found', async () => {
        const wrapper = mountComponent({
            props: { modelValue: undefined, allowFreeTextAddressEntry: true, returnObject: true },
        });

        await (wrapper.vm as any).selectAddressItemAsync('1');
        expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        expect((wrapper.emitted()['update:modelValue'] as any)![0][0]).toStrictEqual({
            city: 'Alexandria',
            code: '1LO',
            company: 'WiseTech Global',
            companyCode: 'WTG',
            companyGuid: '1',
            countryCode: 'AU',
            email: '<EMAIL>',
            guid: '1',
            isActive: true,
            mobile: '12345678',
            phone: '12345678',
            postcode: '2015',
            state: 'NSW',
            street: "Unit 3A, 72 O'Riordan Street",
        });
    });

    it.each([
        [true, true],
        [false, false],
        [undefined, false],
    ])(
        'it shows the WtgSearchField component with the correct allowFreeTextEntry prop set',
        (allowFreeTextAddressEntry, expectedValue) => {
            const wrapper = mountComponent({
                props: { allowFreeTextAddressEntry },
            });

            const searchField = wrapper.findComponent({ name: 'WtgSearchField' });
            expect(searchField.props('allowFreeTextEntry')).toBe(expectedValue);
        }
    );

    test('When searchByAddress is true, overrideSearchResult and placeholder are set', async () => {
        const wrapper = mountComponent({
            props: {
                searchByAddress: true,
            },
        });

        const searchField = wrapper.findComponent({ name: 'WtgSearchField' });
        expect(searchField.props('overrideSearchResult')).toBe(true);
        expect(searchField.props('placeholder')).toBe('Search for address');
    });

    test.each([true, false])(
        'when allowFreeTextAddressEntry=%s, it displays the search results with the correct active item and no duplicates',
        async (allowFreeTextAddressEntry) => {
            mountComponent({
                props: {
                    allowFreeTextAddressEntry,
                    modelValue: '3',
                },
            });
            await clickOnInputAsync();
            const searchResults = await findSearchResultItems();
            const demoTechIndex = searchResults.findIndex((result) => result.text().includes('Demo Tech'));
            expect(demoTechIndex).toBeGreaterThan(-1);
            searchResults.forEach((result, index) => {
                if (index === demoTechIndex) {
                    expect(result.text()).toContain('Demo Tech');
                    expect(result.classes()).toContain('wtg-list-item--active');
                } else {
                    expect(result.text()).not.toContain('Demo Tech');
                    expect(result.classes()).not.toContain('wtg-list-item--active');
                }
            });
        }
    );

    test(`it shows no address seleted label when internal value's company is empty`, async () => {
        const wrapper = mountComponent({
            props: {
                addressOverrideValue: { company: '' },
                showSelectedAddress: true,
            },
        });

        await flushPromises();

        const panel = wrapper.findComponent(WtgPanel);
        expect(panel.exists()).toBe(true);
        expect(panel.text().toLowerCase()).toContain('no address selected');
    });

    test('it has a (deprecated) showPrompter property as an alias of promptable property', async () => {
        const wrapper = mountComponent({ props: { promptable: true, showPrompter: true } });
        const searchField = wrapper.findComponent({ name: 'WtgSearchField' });

        expect(searchField.props('promptable')).toBe(true);

        await wrapper.setProps({ promptable: undefined, showPrompter: true });
        expect(searchField.props('promptable')).toBe(true);

        await wrapper.setProps({ promptable: undefined, showPrompter: undefined });
        expect(searchField.props('promptable')).toBe(false);
    });

    test('it should bind the company name to modelValue if there is an addressOverrideValue', async () => {
        const wrapper = mountComponent({
            props: {
                addressOverrideValue: addresses[0],
            },
        });

        const searchField = wrapper.findComponent(WtgSearchField);
        expect(searchField.props('modelValue')).toBe(addresses[0].company);
    });

    function mountComponent({ props = {}, provide = {} } = {}) {
        return mount(WtgAddressField, {
            wtgUi,
            propsData: {
                itemProvider,
                ...props,
            },
            global: {
                plugins: [wtgUi],
                provide,
            },
            attachTo: document.body,
        });
    }
});

async function clickOnInputAsync() {
    const input = domWrapper.find('input');
    input.trigger('click');
    await flushPromises();
}

async function findSearchResultItems() {
    return domWrapper.findAll('[data-testid="search-results-item"]');
}
