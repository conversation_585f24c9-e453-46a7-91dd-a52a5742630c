import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgDisplayField from './WtgDisplayField.stories.ts';
import displayFieldAnatomy from '../../../assets/WtgDisplayField/displayfield-anatomy.png';

<Meta of={WtgDisplayField} />

<div className="component-header">
    <h1>Display field</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=2182-330&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    
The display field presents a static label and value pair. It provides a clean, read-only format ideal for summarizing information. Commonly used in review screens, summaries, or read-only forms where editing is not required.

</p>

## API

<Canvas className="canvas-preview" of={WtgDisplayField.Default} />
<Controls of={WtgDisplayField.Default} sort={'alpha'} />

## Anatomy

<div className="d-flex flex-column align-center">
    <img srcSet={`${displayFieldAnatomy} 3x`} alt="Anatomy diagram of the" />
</div>

<ol className="anatomy-list">
    <li>
        <strong>Label:</strong> Describes the value shown.
    </li>
    <li>
        <strong>Value:</strong> The content being displayed.
    </li>

</ol>

## How to use

### ✅ Do

-   Use for displaying non-editable information.
-   Ensure labels are clear, concise, and context-relevant.
-   Group related display fields together with consistent spacing.

### ❌ Don't

-   Switch up styles between label and values.
-   Use display field when inline editing is needed, use input or select instead.
-   Overload with long-form text - keep values scannable.

## Behavior

-   Display fields are non-interactive
-   Responsive layout is inherited from container context
-   Text will wrap or truncate based on parent width constraints

## Variants

-   Horizontal or stacked, horizontal must be intentionally placed as the 'value' carries a different text-style to the 'label'.

## Content

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview) and [field content best practices.](/docs/components-text-field--docs#content-guidelines)

## Related components

-   [Select field](/docs/components-select-field--docs)
-   [Unit field](/docs/components-unit-field--docs)
-   [Text field](/docs/components-text-field--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
