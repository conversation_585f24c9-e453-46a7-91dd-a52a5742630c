import WtgExpander from '@components/WtgExpander/WtgExpander.vue';
import WtgExpanderPanel from '@components/WtgExpander/WtgExpanderPanel.vue';
import WtgExpanderPanelHeader from '@components/WtgExpander/WtgExpanderPanelHeader.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { VExpansionPanelTitle } from 'vuetify/components/VExpansionPanel';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgExpanderPanelHeader', () => {
    test('it renders a VExpansionPanelTitle component', () => {
        const wrapper = mountComponent();
        const tabs = wrapper.findComponent(VExpansionPanelTitle);
        expect(tabs.exists()).toBe(true);
    });

    test('it renders the header content if a title or description prop is provided', () => {
        const wrapper = mountComponent({
            props: {
                title: 'Test Title',
                description: 'Test Description',
            },
        });
        const header = wrapper.find('.wtg-expander-header__title');
        expect(header.exists()).toBe(true);
        expect(header.text()).toBe('Test Title');
        const description = wrapper.find('.wtg-expander-header__description');
        expect(description.exists()).toBe(true);
        expect(description.text()).toBe('Test Description');
    });

    test('it renders the default slot content when a title or description is not provided', () => {
        const wrapper = mountComponent();
        const header = wrapper.find('.wtg-expander-header__slot');
        expect(header.exists()).toBe(true);
        expect(header.text()).toBe('ONE');
    });

    function mountComponent({ props = {} } = {}) {
        const component = {
            components: { WtgExpander, WtgExpanderPanel, WtgExpanderPanelHeader },
            template: `
                <wtg-expander v-model="panel">
                    <wtg-expander-panel>
                        <wtg-expander-panel-header :title="title" :description="description">ONE</wtg-expander-panel-header>
                    </wtg-expander-panel>
                </wtg-expander>
            `,
            data: () => ({
                panel: 1,
            }),
            props: {
                title: { type: String, default: null },
                description: { type: String, default: null },
            },
        };

        const wrapper = mount(component, {
            props,
            global: {
                plugins: [wtgUi],
            },
        });

        const headerWrapper = wrapper.findComponent({ name: 'WtgExpanderPanelHeader' });
        return headerWrapper;
    }
});
