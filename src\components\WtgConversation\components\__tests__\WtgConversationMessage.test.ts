import WtgConversationMessage from '@components/WtgConversation/components/WtgConversationMessage.vue';
import '@testing-library/jest-dom';
import { VueWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../../WtgUi';
import { ConversationMessage, ConversationMessageType } from '../../types';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();
const message = {
    id: 'pk-1',
    body: 'message 1',
    score: 0,
    loadingLikeScore: false,
    loadingDislikeScore: false,
} as ConversationMessage;

describe('WtgConversationMessage', () => {
    test('its name is WtgConversationMessage', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('WtgConversationMessage');
    });

    test('displays the correct number of likes when score is positive', () => {
        const wrapper = mountComponent({
            propsData: {
                message: {
                    id: 'pk-1',
                    score: 4,
                },
            },
        });
        const buttons = wrapper.findAllComponents({ name: 'wtg-button' });
        const like = buttons.at(0);
        const dislike = buttons.at(1);

        expect(like?.text()).toBe('4');
        expect(dislike?.text()).toBe('0');
    });

    test('displays the correct number of dislikes when score is negative', () => {
        const wrapper = mountComponent({
            propsData: {
                message: {
                    id: 'pk-1',
                    score: -3,
                },
            },
        });
        const buttons = wrapper.findAllComponents({ name: 'wtg-button' });
        const like = buttons.at(0);
        const dislike = buttons.at(1);

        expect(like?.text()).toBe('0');
        expect(dislike?.text()).toBe('3');
    });

    test('display to the right when message is from self', () => {
        const wrapper = mountComponent({ propsData: { message: { isFromSelf: true } } });
        const messageBox = wrapper.find('.message-box');
        expect(messageBox.element).toHaveClass('ml-auto');
    });

    test('display to the left when message is not from self', () => {
        const wrapper = mountComponent({ propsData: { message: { isFromSelf: false } } });
        const messageBox = wrapper.find('.message-box');
        expect(messageBox.element).toHaveClass('mr-auto');
    });

    test('display proper "staff" type theme color', () => {
        const wrapper = mountComponent({ propsData: { message: { type: ConversationMessageType.staff } } });
        const messageBox = wrapper.find('.message-box');
        expect(messageBox.element).toHaveClass('bg-primary');
        expect(messageBox.element).toHaveClass('white--text');
    });

    test('display proper "system" type theme color', () => {
        const wrapper = mountComponent({ propsData: { message: { type: ConversationMessageType.system } } });
        const messageBox = wrapper.find('.message-box');
        expect(messageBox.element).toHaveClass('bg-accent');
        expect(messageBox.element).toHaveClass('white--text');
    });

    test('display proper "user" type theme color', () => {
        const wrapper = mountComponent({ propsData: { message: { type: ConversationMessageType.user } } });
        const messageBox = wrapper.find('.message-box');
        expect(messageBox.element).toHaveClass('bg-secondary');
        expect(messageBox.element).toHaveClass('white--text');
    });

    test('display proper "user" type theme color when provided type is invalid', () => {
        const wrapper = mountComponent({ propsData: { message: { type: 'invalid' } } });
        const messageBox = wrapper.find('.message-box');
        expect(messageBox.element).toHaveClass('bg-secondary');
    });

    describe('when like button is clicked', () => {
        let wrapper: VueWrapper<any>,
            likeButton: VueWrapper<any> | undefined,
            dislikeButton: VueWrapper<any> | undefined;

        beforeEach(async () => {
            wrapper = mountComponent();
            const buttons = wrapper.findAllComponents({ name: 'wtg-button' });
            likeButton = buttons.at(0);
            dislikeButton = buttons.at(1);

            likeButton?.trigger('click');
            await nextTick();
        });

        test('it emits like-click event', () => {
            const emitted: any = wrapper.emitted();
            expect(emitted['like-click']!.length).toBe(1);
            expect(emitted['like-click']![0]![0]).toStrictEqual(message);
        });

        describe('while loading score', () => {
            let likeButtonIcon: VueWrapper<any> | undefined;

            beforeEach(() => {
                wrapper.vm.message.loadingLikeScore = true;
                likeButtonIcon = wrapper.findAllComponents({ name: 'wtg-icon' }).at(0);
            });

            test('sets pointer-events to none for both like and dislike buttons', () => {
                expect((likeButton?.element as HTMLElement).style.pointerEvents).toBe('none');
                expect((dislikeButton?.element as HTMLElement).style.pointerEvents).toBe('none');
            });

            test('hides thumbs up icon', () => {
                expect(likeButtonIcon?.element).not.toHaveClass('s-icon-thumbs-up');
            });

            test('shows loading icon', () => {
                expect(likeButtonIcon?.element).toHaveClass('s-icon-loading');
            });

            describe('once score is done loading', () => {
                beforeEach(() => {
                    wrapper.vm.message.loadingLikeScore = false;
                });

                test('sets pointer-events to auto', () => {
                    expect((likeButton?.element as HTMLElement).style.pointerEvents).toBe('auto');
                });

                test('shows thumbs up icon', () => {
                    expect(likeButtonIcon?.element).toHaveClass('s-icon-thumbs-up');
                });

                test('hides loading icon', () => {
                    expect(likeButtonIcon?.element).not.toHaveClass('s-icon-loading');
                });
            });
        });
    });

    describe('when dislike button is clicked', () => {
        let wrapper: VueWrapper<any>, likeButton: VueWrapper<any>, dislikeButton: VueWrapper<any>;

        beforeEach(async () => {
            wrapper = mountComponent();
            const buttons = wrapper.findAllComponents({ name: 'wtg-button' });
            likeButton = buttons.at(0)!;
            dislikeButton = buttons.at(1)!;

            dislikeButton?.trigger('click');
            await nextTick();
        });

        test('it emits dislike-click event', () => {
            const emitted: any = wrapper.emitted();
            expect(emitted['dislike-click']!.length).toBe(1);
            expect(emitted['dislike-click']![0]![0]).toStrictEqual(message);
        });

        describe('while loading score', () => {
            let dislikeButtonIcon: VueWrapper<any> | undefined;

            beforeEach(() => {
                wrapper.vm.message.loadingDislikeScore = true;
                dislikeButtonIcon = wrapper.findAllComponents({ name: 'wtg-icon' }).at(1);
            });

            test('sets pointer-events to none for both like and dislike buttons', () => {
                expect((likeButton?.element as HTMLElement).style.pointerEvents).toBe('none');
                expect((dislikeButton?.element as HTMLElement).style.pointerEvents).toBe('none');
            });

            test('hides thumbs down icon', () => {
                expect(dislikeButtonIcon?.element).not.toHaveClass('s-icon-thumbs-down');
            });

            test('shows loading icon', () => {
                expect(dislikeButtonIcon?.element).toHaveClass('s-icon-loading');
            });

            describe('once score is done loading', () => {
                beforeEach(() => {
                    wrapper.vm.message.loadingDislikeScore = false;
                });

                test('sets pointer-events to auto', () => {
                    expect((dislikeButton?.element as HTMLElement).style.pointerEvents).toBe('auto');
                });

                test('shows thumbs down icon', () => {
                    expect(dislikeButtonIcon?.element).toHaveClass('s-icon-thumbs-down');
                });

                test('hides loading icon', () => {
                    expect(dislikeButtonIcon?.element).not.toHaveClass('s-icon-loading');
                });
            });
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgConversationMessage, {
            propsData: {
                message,
                ...propsData,
            },
            global: {
                plugins: [wtgUi],
            },
            slots,
        });
    }
});
