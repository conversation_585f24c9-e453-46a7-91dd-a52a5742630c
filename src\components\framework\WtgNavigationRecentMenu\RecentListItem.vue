<template>
    <WtgListGroup v-if="item.actions?.length" role="menuitem" aria-haspopup="menu" :aria-label="item.caption">
        <template #activator="{ isOpen }">
            <WtgListItem
                role="menuitem"
                :aria-label="item.caption"
                :aria-haspopup="item.actions ? (item.actions.length > 0 ? 'menu' : 'false') : 'false'"
                style="text-decoration: none; color: unset"
            >
                <template #leading>
                    <WtgIconButton
                        v-if="item.favorite"
                        icon="s-icon-star-filled"
                        color="amber"
                        variant="ghost"
                        :aria-label="application.captions.toggleFavorite"
                        :tooltip="application.captions.toggleFavorite"
                        size="s"
                        icon-size="s"
                        @click.stop="item.onRemoveFromFavorites"
                    />
                    <WtgIconButton
                        v-else
                        icon="s-icon-star-empty"
                        variant="ghost"
                        :aria-label="application.captions.toggleFavorite"
                        :tooltip="application.captions.toggleFavorite"
                        size="s"
                        icon-size="s"
                        @click.stop="item.onAddToFavorites"
                    />
                </template>
                {{ item.caption }}
                <template #trailing>
                    <WtgIcon v-if="item.actions && item.actions.length > 0">
                        {{ isOpen ? 's-icon-caret-up' : 's-icon-caret-down' }}
                    </WtgIcon>
                </template>
            </WtgListItem>
        </template>
        <WtgListItem
            v-for="action in item.actions || []"
            :key="action.id"
            :href="action.href"
            role="menuitem"
            :aria-label="action.caption"
            @click="$emit('item-click', item)"
        >
            {{ action.caption }}
        </WtgListItem>
    </WtgListGroup>
    <WtgListItem
        v-else
        :href="item.href"
        role="menuitem"
        :aria-label="item.caption"
        :aria-haspopup="item.actions ? (item.actions.length > 0 ? 'menu' : 'false') : 'false'"
        style="text-decoration: none; color: unset"
        @click="$emit('item-click', item)"
    >
        <template #leading>
            <WtgIconButton
                v-if="item.favorite"
                icon="s-icon-star-filled"
                color="amber"
                variant="ghost"
                :aria-label="application.captions.toggleFavorite"
                :tooltip="application.captions.toggleFavorite"
                size="s"
                icon-size="s"
                @click.stop.prevent="item.onRemoveFromFavorites"
            />
            <WtgIconButton
                v-else
                icon="s-icon-star-empty"
                variant="ghost"
                :aria-label="application.captions.toggleFavorite"
                :tooltip="application.captions.toggleFavorite"
                size="s"
                icon-size="s"
                @click.stop.prevent="item.onAddToFavorites"
            />
        </template>
        {{ item.caption }}
    </WtgListItem>
</template>

<script setup lang="ts">
import WtgIcon from '@components/WtgIcon';
import WtgIconButton from '@components/WtgIconButton';
import { WtgListGroup, WtgListItem } from '@components/WtgList';
import { WtgFrameworkRecentItem } from '@components/framework/types';
import { useApplication } from '@composables/application';
import { PropType } from 'vue';

const application = useApplication();

defineProps({
    item: {
        type: Object as PropType<WtgFrameworkRecentItem>,
        required: true,
    },
});

defineEmits<{
    'item-click': [item: WtgFrameworkRecentItem];
}>();
</script>
