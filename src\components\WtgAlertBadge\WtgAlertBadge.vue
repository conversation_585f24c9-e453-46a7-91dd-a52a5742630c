<template>
    <div class="wtg-alert-badge">
        <div class="wtg-badge__wrapper">
            <slot />
            <div v-if="modelValue" :class="computedClass">
                <WtgIcon v-if="computedIcon">{{ computedIcon }}</WtgIcon>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { PropType, computed } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * Controls the visibility of the alert badge.
     * If true, the badge is shown; if false, it is hidden.
     */
    modelValue: {
        type: Boolean,
        default: true,
    },
    /**
     * The visual style of the badge.
     * Options are 'info', 'success', 'warning', or 'error'.
     */
    variant: {
        type: String as PropType<'info' | 'success' | 'warning' | 'error'>,
        default: undefined,
    },
});

//
// Computed
//
const computedClass = computed(() => [
    {
        'wtg-alert-badge--error': props.variant === 'error',
        'wtg-alert-badge--warning': props.variant === 'warning',
        'wtg-alert-badge--success': props.variant === 'success',
        'wtg-alert-badge--info': props.variant === 'info',
    },
    'wtg-badge__content',
]);

const computedIcon = computed(() => {
    switch (props.variant) {
        case 'error':
            return 's-icon-status-critical-filled';
        case 'warning':
            return 's-icon-status-warning-filled';
        case 'success':
            return 's-icon-status-success-filled';
        case 'info':
            return 's-icon-info-circle-filled';
    }

    return undefined;
});
</script>

<style lang="scss">
.wtg-alert-badge {
    display: inline-flex;

    .wtg-badge__wrapper {
        display: flex;
        position: relative;
    }

    .wtg-badge__content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: var(--s-padding-xs);
        border-radius: var(--s-radius-xxl);
        position: absolute;
        float: right;
        z-index: 1;
        right: calc(-1 * var(--s-sizing-m) / 2);
        top: calc(-1 * var(--s-sizing-m) / 2);
        height: var(--s-sizing-m);
        width: var(--s-sizing-m);

        .wtg-icon {
            height: var(--s-sizing-m);
            width: var(--s-sizing-m);
            font-size: var(--s-sizing-m);
        }
    }

    .wtg-alert-badge--error {
        color: var(--s-error-icon-inv-default);
        background: var(--s-error-bg-default);
    }

    .wtg-alert-badge--warning {
        color: var(--s-warning-icon-default);
        background: var(--s-warning-bg-default);
    }

    .wtg-alert-badge--success {
        color: var(--s-success-icon-inv-default);
        background: var(--s-success-bg-default);
    }

    .wtg-alert-badge--info {
        color: var(--s-info-icon-inv-default);
        background: var(--s-info-bg-default);
    }
}
</style>
