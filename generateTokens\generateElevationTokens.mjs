import * as utils from './utils.mjs';

export default function generateElevationTokens() {
    const coreElevationTokensPromise = getCoreElevationNodes().then((nodes) => {
        const elevationTokens = {};
        for (const [, node] of Object.entries(nodes)) {
            const elevationToken = node.document.effects
                .filter((effect) => effect.type === 'INNER_SHADOW' || effect.type === 'DROP_SHADOW')
                .map(figmaEffectToBoxShadowCssProp)
                .join(', ');
            const elevationTokenKey = utils.formatName(
                node.document.name.replace('.', '-').replace('style', 'elevation')
            );
            elevationTokens[elevationTokenKey] = elevationToken;
        }
        return elevationTokens;
    });
    coreElevationTokensPromise.then((coreElevationTokens) => {
        utils.writeTokens(coreElevationTokens, 'supply-elevation.css', ':root');
    });
}

async function getCoreElevationNodes() {
    return utils
        .fetchFromFigma('styles')
        .then((json) => json.meta.styles)
        .then((styles) => {
            const elevationNodeIds = styles
                .filter((style) => style.style_type === 'EFFECT')
                .map((style) => style.node_id);
            return utils.fetchFromFigma(`nodes?ids=${elevationNodeIds.join(',')}`);
        })
        .then((json) => json.nodes);
}

function figmaEffectToBoxShadowCssProp(effect) {
    const color = utils.figmaColorToCssColor(effect.color);
    return `${effect.type === 'INNER_SHADOW' ? 'inset ' : ''}${effect.offset.x}px ${effect.offset.y}px ${
        effect.radius ?? 0
    }px ${effect.spread ?? 0}px ${color}`;
}
