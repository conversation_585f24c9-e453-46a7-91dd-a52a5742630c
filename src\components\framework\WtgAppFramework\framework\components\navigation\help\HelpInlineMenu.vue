<template>
    <WtgListGroup role="menuitem" :aria-label="application.captions.help" aria-haspopup="menu">
        <template #activator="{ isOpen }">
            <WtgListItem leading-icon="s-icon-help">
                {{ application.captions.help }}
                <template #trailing>
                    <WtgIcon>
                        {{ isOpen ? 's-icon-caret-up' : 's-icon-caret-down' }}
                    </WtgIcon>
                </template>
            </WtgListItem>
        </template>
        <WtgListItem
            v-if="hasPageHelp"
            role="menuitem"
            :aria-label="application.captions.pageHelp"
            @click="onPageHelpClick"
        >
            {{ application.captions.pageHelp }}
        </WtgListItem>
        <WtgListItem
            v-for="item in helpItems"
            :key="item.title"
            :href="item.link"
            role="menuitem"
            :aria-label="item.title"
            target="_blank"
            @click="onItemClick($event, item)"
        >
            {{ item.title }}
        </WtgListItem>
        <WtgListItem role="menuitem" :aria-label="application.captions.about" @click="onAboutClick">
            {{ application.captions.about }}
        </WtgListItem>
    </WtgListGroup>
</template>

<script setup lang="ts">
import WtgIcon from '@components/WtgIcon';
import { WtgListGroup, WtgListItem } from '@components/WtgList';
import { WtgFrameworkHelpItem } from '@components/framework/types';
import { useApplication } from '@composables/application';
import { computed } from 'vue';

const application = useApplication();

const emit = defineEmits<{
    'item-click': [];
}>();

const hasPageHelp = computed((): boolean => {
    return !!application.pageHelp;
});

const helpItems = computed((): WtgFrameworkHelpItem[] => {
    return application.helpItems;
});

function onItemClick(event: UIEvent, item: WtgFrameworkHelpItem): void {
    if (item.onClick) {
        item.onClick(event);
    }
}

function onPageHelpClick(): void {
    emit('item-click');
    application.openPageHelp();
}

function onAboutClick(): void {
    emit('item-click');
    application.dialogs.about.open();
}
</script>
