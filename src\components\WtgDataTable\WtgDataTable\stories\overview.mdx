import * as WtgDataTable from './WtgDataTable.stories.ts';

import { Canvas, ColorItem, ColorPalette, Description, Meta, Story, Title } from '@storybook/blocks';

<Meta title="Components/Data Table/Variants/Data Table" />

<div className="component-header">
    <h1>Data Table</h1>
</div>

## Overview

This section provides a collection of Data Table examples.

## Default

The data table component is used for displaying tabular data. Features include sorting, searching, pagination, grouping, and row selection. This simple data table presumes that the entire data set is available locally.

<div class="docs-page">
    <Canvas of={WtgDataTable.Default} />
</div>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
