<template>
    <WtgPopover
        v-if="notifications.length > 0"
        transition="slide-y-transition"
        left
        min-width="250"
        offset-y
        close-delay="500"
    >
        <template #activator="{ props }">
            <WtgNotification v-model="showBadge" dot bordered :color="badgeColor" overlap>
                <WtgIconButton aria-haspopup="true" icon="$bell" v-bind="props" />
            </WtgNotification>
        </template>
        <NotificationsList :notifications="notifications" />
    </WtgPopover>
</template>

<script lang="ts">
import { WtgFrameworkNotification, WtgFrameworkNotificationType } from '@components/framework/types';
import { WtgBtnVariantType } from '@components/WtgButton';
import WtgIconButton from '@components/WtgIconButton';
import WtgNotification from '@components/WtgNotification';
import WtgPopover from '@components/WtgPopover';
import { useCurrentTask } from '@composables/application';
import { computed, defineComponent } from 'vue';
import NotificationsList from './NotificationsList.vue';

export default defineComponent({
    name: 'NotificationsButton',
    components: {
        NotificationsList,
        WtgNotification,
        WtgIconButton,
        WtgPopover,
    },
    setup() {
        const task = useCurrentTask();

        const notifications = computed((): WtgFrameworkNotification[] => {
            return task.value?.notifications ?? [];
        });

        const highestSeverity = computed((): WtgFrameworkNotificationType => {
            let type = WtgFrameworkNotificationType.Information;
            for (const notification of notifications.value) {
                if (notification.type > type) {
                    type = notification.type;
                }
            }
            return type;
        });

        const badgeColor = computed((): string => {
            let color = '';
            switch (highestSeverity.value) {
                case WtgFrameworkNotificationType.Information:
                    color = 'info';
                    break;
                case WtgFrameworkNotificationType.Warning:
                    color = 'warning';
                    break;
                case WtgFrameworkNotificationType.Error:
                    color = 'error';
                    break;
            }
            return color;
        });

        const showBadge = computed((): boolean => {
            return notifications.value.length > 0;
        });

        return {
            badgeColor,
            highestSeverity,
            notifications,
            showBadge,
            WtgBtnVariantType,
        };
    },
});
</script>
