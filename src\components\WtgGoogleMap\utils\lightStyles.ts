export const lightStyles = [
    {
        featureType: 'water',
        elementType: 'geometry',
        stylers: [{ color: '#cbd7d4' }],
    },
    {
        featureType: 'landscape',
        elementType: 'all',
        stylers: [{ color: '#ffffff' }],
    },
    {
        elementType: 'labels.text.stroke',
        stylers: [{ color: '#f0f3f2' }],
    },
    {
        elementType: 'labels.text.fill',
        stylers: [{ color: '#262626' }],
    },
    {
        featureType: 'administrative.country',
        elementType: 'labels',
        stylers: [{ visibility: 'on' }],
    },
    {
        featureType: 'administrative.locality',
        elementType: 'labels',
        stylers: [{ visibility: 'on' }],
    },
    {
        featureType: 'administrative',
        elementType: 'geometry',
        stylers: [{ visibility: 'on' }],
    },
    {
        featureType: 'landscape',
        elementType: 'labels',
        stylers: [{ visibility: 'off' }],
    },
    {
        featureType: 'poi',
        stylers: [{ visibility: 'off' }],
    },
    {
        featureType: 'road',
        stylers: [{ visibility: 'off' }],
    },
    {
        featureType: 'transit',
        stylers: [{ visibility: 'off' }],
    },
];
