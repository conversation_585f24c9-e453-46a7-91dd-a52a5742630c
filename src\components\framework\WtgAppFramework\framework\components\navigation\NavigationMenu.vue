<template>
    <div>
        <WtgList role="menu">
            <template v-for="menuItem in menuItems">
                <WtgListGroup
                    v-if="menuItem.action === WtgFrameworkMenuItemType.Menu"
                    :key="menuItem.id"
                    v-model="menuItem.active"
                    role="menuitem"
                    aria-haspopup="menu"
                    :aria-label="menuItem.caption"
                    @click.stop="onMenuGroupClick(menuItem, $event)"
                >
                    <template #activator="{ isOpen }">
                        <WtgListItem :class="!railActive ? '' : 'd-flex justify-center'" :collapsed="railActive">
                            <template #leading>
                                <WtgIcon>{{ menuItem.icon }}</WtgIcon>
                            </template>
                            {{ menuItem.caption }}
                            <template #trailing>
                                <WtgIcon>
                                    {{ isOpen ? 's-icon-caret-up' : 's-icon-caret-down' }}
                                </WtgIcon>
                            </template>
                        </WtgListItem>
                    </template>
                    <template v-for="childMenuItem in menuItem.items">
                        <WtgListGroup
                            v-if="childMenuItem.action === WtgFrameworkMenuItemType.Menu"
                            v-show="!railActive"
                            :key="childMenuItem.id"
                            v-model="childMenuItem.active"
                            role="menuitem"
                            aria-haspopup="menu"
                            :aria-label="childMenuItem.caption"
                        >
                            <template #activator="{ isOpen }">
                                <WtgListItem>
                                    <template #leading>
                                        <WtgIcon>
                                            {{ isOpen ? 's-icon-caret-up' : 's-icon-caret-down' }}
                                        </WtgIcon>
                                    </template>
                                    {{ childMenuItem.caption }}
                                </WtgListItem>
                            </template>
                            <WtgListItem
                                v-for="item in childMenuItem.items"
                                :key="item.id"
                                :active="item.active"
                                :href="item.href"
                                class="ml-5"
                                :class="item.active ? 'list-item--active' : ''"
                                role="menuitem"
                                :aria-label="item.caption"
                                :collapsed="railActive"
                                @click.stop="onMenuItemClick(item)"
                            >
                                {{ item.caption }}
                            </WtgListItem>
                        </WtgListGroup>
                        <WtgListItem
                            v-else-if="childMenuItem.action === WtgFrameworkMenuItemType.Link && !railActive"
                            v-show="!railActive"
                            :key="'levelItem1' + childMenuItem.id"
                            :active="childMenuItem.active"
                            :href="childMenuItem.href"
                            :to="childMenuItem.to"
                            :class="childMenuItem.active ? 'list-item--active' : ''"
                            role="menuitem"
                            :aria-label="childMenuItem.caption"
                            @click.stop="onMenuItemClick(childMenuItem)"
                        >
                            {{ childMenuItem.caption }}
                        </WtgListItem>
                    </template>
                </WtgListGroup>
                <WtgListItem
                    v-else-if="menuItem.action === WtgFrameworkMenuItemType.Link"
                    :key="'lvl1-' + menuItem.id"
                    :active="menuItem.active"
                    :href="menuItem.href"
                    :to="menuItem.to"
                    :class="!railActive ? '' : 'd-flex justify-center'"
                    role="menuitem"
                    :aria-label="menuItem.caption"
                    :collapsed="railActive"
                    @click.stop="onMenuItemClick(menuItem)"
                >
                    <template #leading>
                        <WtgIcon>{{ menuItem.icon }}</WtgIcon>
                    </template>
                    {{ menuItem.caption }}
                </WtgListItem>
            </template>
        </WtgList>
    </div>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { WtgList, WtgListGroup, WtgListItem } from '@components/WtgList';
import { WtgFrameworkMenuItem, WtgFrameworkMenuItemType } from '@components/framework/types';
import { useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { computed, ref, Ref, watch } from 'vue';

const application = useApplication();
const { isTabletOrMobile } = useFramework();

const menuItems = computed((): WtgFrameworkMenuItem[] => {
    return application.menu ?? [];
});

const emit = defineEmits<{
    'group-click': [target: HTMLElement];
    'item-click': [];
}>();

const open: Ref<string[]> = ref([]);
const openState: Ref<string[]> = ref([]);

const railActive = computed(() => {
    return isTabletOrMobile.value ? false : application.navDrawer.isRailActive;
});

watch(
    () => railActive.value,
    () => {
        if (railActive.value) {
            openState.value = open.value;
            open.value = [];
        } else {
            open.value = openState.value;
        }
    }
);

function onMenuGroupClick(item: WtgFrameworkMenuItem, e: UIEvent): void {
    openState.value.push(open.value[0]);
    emit('group-click', e.currentTarget as HTMLElement);
}

function onMenuItemClick(item: WtgFrameworkMenuItem): void {
    if (item.onClick) {
        item.onClick();
    }
    if (isTabletOrMobile.value) {
        emit('item-click');
    }
}
</script>
