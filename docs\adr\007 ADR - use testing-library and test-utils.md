# Use @testing-library/vue and @vue/test-utils for Testing

## Status
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context



## Decision

## Status
- [ ] Proposed
- [x] Accepted
- [ ] Rejected
- [ ] Deprecated
- [ ] Superseded


1. Black box testing: @vue/test-utils is a set of utility functions aimed to simplify testing Vue. js components. It provides some methods to mount and interact with Vue components in an isolated manner.

2. White box testing: @testing-library/vue is a Vue testing library focused on testing components without relying on implementation details.

See also [testing strategy from the BorderWise team](https://devops.wisetechglobal.com/wtg/BorderWise/_wiki/wikis/BorderWise.wiki/3347/Testing)

## Consequences

Pros @testing-library/vue:

@testing-library/vue is part of the Testing Library family, which focuses on testing components in a way that simulates how a user interacts with them, rather than testing implementation details. It encourages writing tests that are more user-centric and maintainable.

1. Focus on User Interaction: Tests are written to simulate real user behavior, such as clicking buttons, entering text, etc., rather than focusing on the internal implementation of a component. This leads to tests that are more aligned with user experience.
2. Minimalist Approach: The library promotes testing components by their visible output and behavior. This makes tests easier to write, read, and maintain.
3. Accessible Queries: It provides utility functions for querying elements based on accessibility standards (e.g., getByText, getByRole, getByLabelText). This encourages the creation of accessible web applications and helps ensure that your components are usable by screen readers and other assistive technologies.
4. Simplicity and Flexibility: @testing-library/vue uses plain JavaScript and doesn’t impose a specific testing framework, making it easy to integrate with tools like Jest, Mocha, or even Karma.
5. Focus on Behavior Over Structure: It abstracts away the details of the component's internal implementation, allowing tests to focus on the external behavior of the components, which is less likely to break with refactoring.
6. Improved Test Maintainability: By focusing on testing user interactions and outcomes, tests are less coupled to component structure, which makes them more resilient to refactors.

Pros @testing-library/vue:

@vue/test-utils is the official unit testing utility library for Vue.js. It provides a more component-centric testing approach, offering utilities to mount, interact with, and assert on Vue components.

1. Vue-Specific Integration: @vue/test-utils is specifically designed for Vue components, making it easier to interact with Vue-specific features like reactivity, directives, and lifecycle hooks.
2. Mounting Components: The library allows you to mount Vue components in isolation, providing methods like mount() and shallowMount(). These methods allow you to render components with or without their child components, making it easier to test the component in isolation.
3. Access to Component Instances: Provides direct access to the component instance and its internal methods, making it great for testing internal logic, computed properties, data, and events.
4. Support for Vue-Specific Features: Since it is built specifically for Vue, @vue/test-utils integrates seamlessly with Vue's reactivity system, and it supports testing Vue's built-in features such as props, events, computed properties, and watchers.
5. Component Interaction: You can simulate user interactions like clicking events, typing in inputs, or emitting events. This makes it useful for unit tests where you want to verify how the component behaves under specific conditions.
6. Full Control Over the Component: @vue/test-utils provides more control over the component, including the ability to inspect or trigger Vue-specific behavior. This makes it ideal for more fine-grained testing of a component’s behavior.

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.
