export const DropdownIconButtonSandboxTemplate = `
<WtgRow>
    <WtgCol style="max-width: fit-content; gap: 30px; margin-right: 20px" class="d-flex flex-column">
        <WtgDropdownIconButton 
            v-bind="args"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            variant="ghost"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            disabled="true"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            loading="true"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 30px; margin-right: 20px" class="d-flex flex-column">
        <WtgDropdownIconButton 
            v-bind="args"
            loading="true"
            sentiment="primary"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="primary"
            loading="true"
            variant="fill"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="primary"
            loading="true"
            variant="ghost"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 30px; margin-right: 20px" class="d-flex flex-column">
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="primary"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="success"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="critical"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 30px; margin-right: 20px" class="d-flex flex-column">
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="primary"
            variant="ghost"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="success"
            variant="ghost"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="critical"
            variant="ghost"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 30px; margin-right: 20px" class="d-flex flex-column">
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="primary"
            variant="fill"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="success"
            variant="fill"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            sentiment="critical"
            variant="fill"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
    </WtgCol>
    <WtgCol>
        <WtgDropdownIconButton 
            v-bind="args"
            aria-label="Aria Name"
            size="xs"
            icon-size="xs"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            aria-label="Aria Name"
            size="s"
            icon-size="s"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            aria-label="Aria Name"
            size="m"
            icon-size="m"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            aria-label="Aria Name"
            size="l"
            icon-size="l"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            aria-label="Aria Name"
            size="xl"
            icon-size="xl"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
        <WtgDropdownIconButton 
            v-bind="args"
            aria-label="Aria Name"
            size="xxl"
            icon-size="xxl"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>
    </WtgCol>
</WtgRow>`;
