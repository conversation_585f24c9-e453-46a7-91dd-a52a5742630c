import WtgTifViewer from '@components/WtgDocumentPreview/WtgTifViewer.vue';
import { VueWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import 'jest-canvas-mock';
import fetchMock from 'jest-fetch-mock';
import UTIF from 'utif';
import { nextTick } from 'vue';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

fetchMock.enableMocks();

describe('WtgTifViewer', () => {
    afterEach(() => {
        fetchMock.resetMocks();
    });

    test('its name is WtgTifViewer', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgTifViewer');
    });

    describe('view model', () => {
        describe('has valid src', () => {
            test('should fetch src', () => {
                mountComponent({ props: { src: 'eDocSrc' } });
                expect(fetch).toHaveBeenCalledWith('eDocSrc');
            });

            test('when fetch fails then should emit error event', async () => {
                fetchMock.mockRejectOnce(jest.fn());
                const wrapper = mountComponent({ props: { src: 'x' } });

                await nextTick();
                expect(wrapper.emitted('error')?.length).toBe(1);
            });

            test('when fetch succeeds then should emit load event', async () => {
                fetchMock.mockResponseOnce('', { status: 300 });
                const wrapper = mountComponent({ props: { src: 'x' } });

                await nextTick();
                expect(wrapper.emitted('load')?.length).toBe(1);
            });

            describe('when fetch succeeds', () => {
                test('should populate buffer', async () => {
                    const data = new Int32Array([1, 3, 0, 2]);
                    fetchMock.mockResponse(data.buffer as any as string, { status: 200 });

                    const wrapper: VueWrapper<any> = mountComponent({ props: { src: 'x' } });
                    await waitForResponse();

                    const buffer = wrapper.vm['buffer'];
                    const array = new Int32Array(buffer);
                    expect(array.length).toBe(4);
                    expect(array[0]).toBe(1);
                    expect(array[1]).toBe(3);
                    expect(array[2]).toBe(0);
                    expect(array[3]).toBe(2);
                });

                test('should populate ifds array', async () => {
                    const data = new Int32Array([1, 3, 0, 2]);
                    fetchMock.mockResponse(data.buffer as any as string, { status: 200 });

                    const ifds = [{ id: 'someifd' }];
                    const spyUtifDecode = jest.spyOn(UTIF, 'decode').mockReturnValue(ifds as any);

                    const wrapper: VueWrapper<any> = mountComponent({ props: { src: 'x' } });
                    await waitForResponse();

                    expect(spyUtifDecode).toHaveBeenCalled();
                    expect(wrapper.vm['ifds']).toEqual(ifds);

                    const bufferToDecode = spyUtifDecode.mock.calls[0][0];
                    const array = new Int32Array(bufferToDecode);
                    expect(array.length).toBe(4);
                    expect(array[0]).toBe(1);
                    expect(array[1]).toBe(3);
                    expect(array[2]).toBe(0);
                    expect(array[3]).toBe(2);
                });

                describe('when single page tif', () => {
                    let wrapper: VueWrapper<any>;
                    beforeEach(async () => {
                        const data = new Int32Array([1, 3, 0, 2]);
                        fetchMock.mockResponse(data.buffer as any as string, { status: 200 });

                        const ifds = [{ id: 'someifd' }];
                        jest.spyOn(UTIF, 'decode').mockReturnValue(ifds as any);
                        jest.spyOn(UTIF, 'decodeImage').mockImplementation((_, page) => {
                            Object.assign(page, {
                                width: 4,
                                height: 4,
                            });
                        });
                        jest.spyOn(UTIF, 'toRGBA8').mockReturnValue(new Uint8Array());

                        wrapper = mountComponent({ props: { src: 'x' } });
                        await waitForResponse();
                    });

                    test('should render without carousel', async () => {
                        const wtgTifPages = wrapper.findAllComponents({ name: 'wtg-tif-page' });
                        const wtgCarousel = wrapper.findComponent({ name: 'v-carousel' });
                        expect(wtgTifPages.length).toBe(1);

                        const wtgTifPage = wtgTifPages.at(0)!;
                        expect(wtgTifPage.props().buffer).toBe(wrapper.vm.buffer);
                        expect(wtgTifPage.props().ifd).toBe(wrapper.vm.ifds[0]);
                        expect(wtgCarousel.exists()).toBe(false);
                    });

                    test('should handle single page tif error', async () => {
                        const wtgTifPage = wrapper.findComponent({ name: 'wtg-tif-page' });
                        wtgTifPage.vm.$emit('error');
                        expect(wrapper.emitted('error')?.length).toBe(1);
                    });

                    test('should handle single page tif decode error', async () => {
                        const wtgTifPage = wrapper.findComponent({ name: 'wtg-tif-page' });
                        wtgTifPage.vm.$emit('tif-decode-error');
                        expect(wrapper.emitted('tif-decode-error')?.length).toBe(1);
                    });
                });

                describe('when multi page tif', () => {
                    let wrapper: VueWrapper<any>;
                    beforeEach(async () => {
                        const data = new Int32Array([1, 3, 0, 2]);
                        fetchMock.mockResponse(data.buffer as any as string, { status: 200 });

                        const ifds = [{ id: 'someifd' }, { id: 'ifd2' }, { id: 'ifd3' }];
                        jest.spyOn(UTIF, 'decode').mockReturnValue(ifds as any);
                        jest.spyOn(UTIF, 'decodeImage').mockImplementation((_, page) => {
                            Object.assign(page, {
                                width: 4,
                                height: 4,
                            });
                        });
                        jest.spyOn(UTIF, 'toRGBA8').mockReturnValue(new Uint8Array());

                        wrapper = mountComponent({ props: { src: 'x' } });
                        await waitForResponse();
                    });

                    test('should render one carousel item per page', () => {
                        const carouselItems = wrapper.findAllComponents({
                            name: 'v-carousel-item',
                        });
                        expect(carouselItems.length).toBe(3);
                    });

                    test('should render only first carousel item page initially', () => {
                        const carouselItems = wrapper.findAllComponents({
                            name: 'v-carousel-item',
                        });
                        expect(carouselItems.at(0)!.findComponent({ name: 'wtg-tif-page' }).exists()).toBe(true);
                        expect(carouselItems.at(1)!.findComponent({ name: 'wtg-tif-page' }).exists()).toBe(false);
                        expect(carouselItems.at(2)!.findComponent({ name: 'wtg-tif-page' }).exists()).toBe(false);
                    });

                    test('should render first page correctly', () => {
                        const carouselItems = wrapper.findAllComponents({
                            name: 'v-carousel-item',
                        });
                        const wtgTifPage = carouselItems.at(0)!.findComponent({ name: 'wtg-tif-page' });
                        expect(wtgTifPage.props().buffer).toBe(wrapper.vm.buffer);
                        expect(wtgTifPage.props().ifd).toBe(wrapper.vm.ifds[0]);
                    });

                    test('when changing page then should render new carousel item page', async () => {
                        const carouselItems = wrapper.findAllComponents({
                            name: 'v-carousel-item',
                        });
                        wrapper.findComponent({ name: 'v-carousel' }).vm.$emit('update:modelValue', 1);
                        await waitForResponse();

                        expect(carouselItems.at(0)!.findComponent({ name: 'wtg-tif-page' }).exists()).toBe(false);
                        expect(carouselItems.at(1)!.findComponent({ name: 'wtg-tif-page' }).exists()).toBe(true);
                        expect(carouselItems.at(2)!.findComponent({ name: 'wtg-tif-page' }).exists()).toBe(false);
                    });

                    test('should handle multi page tif error', async () => {
                        const carouselItems = wrapper.findAllComponents({
                            name: 'v-carousel-item',
                        });
                        const wtgTifPage1 = carouselItems.at(0)!.findComponent({ name: 'wtg-tif-page' });
                        wtgTifPage1.vm.$emit('error');
                        expect(wrapper.emitted('error')?.length).toBe(1);

                        wrapper.findComponent({ name: 'v-carousel' }).vm.$emit('update:modelValue', 1);
                        await waitForResponse();
                        const wtgTifPage2 = carouselItems.at(1)!.findComponent({ name: 'wtg-tif-page' });
                        wtgTifPage2.vm.$emit('error');
                        expect(wrapper.emitted('error')?.length).toBe(2);
                    });

                    test('should handle multi page tif decode error', async () => {
                        const carouselItems = wrapper.findAllComponents({
                            name: 'v-carousel-item',
                        });
                        const wtgTifPage1 = carouselItems.at(0)!.findComponent({ name: 'wtg-tif-page' });
                        wtgTifPage1.vm.$emit('tif-decode-error');
                        expect(wrapper.emitted('tif-decode-error')?.length).toBe(1);

                        wrapper.findComponent({ name: 'v-carousel' }).vm.$emit('update:modelValue', 1);
                        await waitForResponse();
                        const wtgTifPage2 = carouselItems.at(1)!.findComponent({ name: 'wtg-tif-page' });
                        wtgTifPage2.vm.$emit('tif-decode-error');
                        expect(wrapper.emitted('tif-decode-error')?.length).toBe(2);
                    });
                });
            });
        });

        describe('has empty src', () => {
            test('should not fetch anything', async () => {
                mountComponent({ props: { src: '' } });
                expect(fetch).not.toHaveBeenCalled();
            });

            test('should clear previously loaded data', async () => {
                fetchMock.mockResponse('', { status: 200 });

                const wrapper: VueWrapper<any> = mountComponent({ props: { src: 'dud' } });
                await waitForResponse();
                expect(wrapper.vm['buffer']).not.toBeUndefined();
                expect(wrapper.vm['ifds']).not.toEqual([]);

                await wrapper.setProps({ src: '' });
                expect(wrapper.vm['buffer']).toBeUndefined();
                expect(wrapper.vm['ifds']).toEqual([]);
            });

            test('should not display anything', async () => {
                const wrapper = mountComponent({ props: { src: '' } });
                await waitForResponse();

                const wtgTifPage = wrapper.findComponent({ name: 'wtg-wtg-tif-page' });
                expect(wtgTifPage.exists()).toBe(false);
            });
        });
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgTifViewer, {
            slots,
            props,
            global: {
                plugins: [wtgUi],
            },
        });
    }

    async function waitForResponse() {
        await nextTick();
        await nextTick();
    }
});
