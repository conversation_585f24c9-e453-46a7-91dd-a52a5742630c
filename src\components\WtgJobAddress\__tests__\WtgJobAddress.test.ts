import WtgAddress<PERSON>ield from '@components/WtgAddressField';
import WtgButton from '@components/WtgButton';
import WtgDialog from '@components/WtgDialog';
import { JobAddressEditMode, WtgJobAddress } from '@components/WtgJobAddress';
import WtgPanel from '@components/WtgPanel';
import WtgSearchField from '@components/WtgSearchField';
import WtgTextField from '@components/WtgTextField';
import { useWtgUi } from '@composables/global';
import { flushPromises, mount, VueWrapper } from '@vue/test-utils';
import { nextTick } from 'vue';
import AddressEditor from '../components/AddressEditor/AddressEditor.vue';
import AddressTitle from '../components/AddressTitle/AddressTitle.vue';
import { dataProvider, db } from './WtgJobAddressDataProvider';
import { usePendingCommits } from '@composables/input';

const wtgUi = useWtgUi();
jest.useFakeTimers();

describe('WtgJobAddress', () => {
    let el: HTMLElement;

    beforeEach(async () => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('it renders the component', () => {
        const wrapper = mountComponent();
        expect(wrapper.exists()).toBe(true);
    });

    test('it renders the panel', () => {
        const wrapper = mountComponent();
        const panel = wrapper.findComponent(WtgPanel);
        expect(panel.exists()).toBe(true);
    });

    test('it displays no address selected label when address is selected', () => {
        const wrapper = mountComponent();
        const labelsList = wrapper.findAllComponents({ name: 'WtgLabel' });
        expect(labelsList.at(0)!.text()).toBe('No address selected');
    });

    test('company name is set', () => {
        const wrapper = mountComponent({
            propsData: {
                modelValue: {
                    address: db.addresses[0],
                },
            },
        });

        const labelsList = wrapper.findAllComponents({ name: 'WtgLabel' });
        expect(labelsList.at(0)!.text()).toBe('(BIG)');
        expect(labelsList.at(1)!.text()).toBe('Big Corp');
    });

    test('it forwards showAdditionalInformation props to WtgAddressField', () => {
        const wrapper = mountComponent({
            propsData: {
                showAdditionalInformation: true,
            },
        });

        const addressField = wrapper.findComponent({ name: 'WtgAddressField' });

        expect(addressField.props('showAdditionalInformation')).toBe(true);
    });

    test('When address is changed in WtgAddressField the contact prop should become empty.', async () => {
        const wrapper = mountComponent({
            propsData: { modelValue: { address: db.addresses[0], contact: db.contacts[0] } },
        });
        const addressField = wrapper.findComponent(WtgAddressField);
        expect(addressField.props('contact')).toEqual(db.contacts[0]);

        addressField.vm.$emit('update:modelValue', db.addresses[1]);

        await nextTick();

        expect(addressField.props('contact')).toBeUndefined();
    });

    test('When address is changed in AddressEditor contact should be cleared.', async () => {
        const wrapper = mountComponent({
            propsData: { modelValue: { address: db.addresses[0], contact: db.contacts[0] } },
        });

        const addressField = wrapper.findAllComponents(WtgAddressField).at(0);
        addressField?.vm.$emit('edit');
        await nextTick();

        const editorAddressField = wrapper.findAllComponents(WtgAddressField).at(1);
        editorAddressField?.vm.$emit('update:modelValue', db.addresses[1]);
        await nextTick();

        expect((wrapper.vm as any).popupContact).toEqual({});
        expect((wrapper.vm as any).popupContactSelected).toBe(false);
    });

    test('When address is changed in WtgAddressField the contact in dialog should become empty and address should be updated.', async () => {
        const wrapper = mountComponent({
            propsData: { modelValue: { address: db.addresses[0], contact: db.contacts[0] } },
        });
        const addressField = wrapper.findComponent(WtgAddressField);
        expect(addressField.props('contact')).toEqual(db.contacts[0]);

        addressField.vm.$emit('update:modelValue', db.addresses[1]);
        addressField.vm.$emit('edit');
        await nextTick();

        const addressEditor = wrapper.getComponent(WtgDialog).findComponent(AddressEditor);
        expect(addressEditor.props('popupAddress')).toEqual(db.addresses[1]);
        expect(addressEditor.props('popupContact')).toEqual({});
    });

    test('When address is changed in dialog, and apply button is clicked, the address in WtgAddressField should change.', async () => {
        const wrapper = mountComponent({
            propsData: { modelValue: { address: db.addresses[0], contact: db.contacts[0] } },
        });
        const addressField = wrapper.findComponent(WtgAddressField);
        addressField.vm.$emit('edit');
        await nextTick();
        const dialog = wrapper.getComponent(WtgDialog);
        const addressEditor = dialog.findComponent(AddressEditor);

        addressEditor.vm.$emit('address-changed', db.addresses[1]);
        addressEditor.vm.$emit('contact-changed', db.contacts[1]);
        await nextTick();

        const applyBtn = dialog.findAllComponents(WtgButton).at(3);
        applyBtn?.vm.$emit('click');
        await nextTick();
        await nextTick();

        expect(addressField.props('addressOverrideValue')).toEqual(db.addresses[1]);
        expect(addressField.props('contact')).toEqual(db.contacts[1]);
    });

    test('When address is changed in dialog, and cancel button is clicked, the address in WtgAddressField should NOT change.', async () => {
        const wrapper = mountComponent({
            propsData: { modelValue: { address: db.addresses[0], contact: db.contacts[0] } },
        });
        const addressField = wrapper.findComponent(WtgAddressField);
        addressField.vm.$emit('edit');
        await nextTick();
        const dialog = wrapper.getComponent(WtgDialog);
        const addressEditor = dialog.findComponent(AddressEditor);

        addressEditor.vm.$emit('address-changed', db.addresses[1]);
        addressEditor.vm.$emit('contact-changed', db.contacts[1]);
        await nextTick();
        const cancelBtn = dialog.findAllComponents(WtgButton).at(1);
        cancelBtn?.vm.$emit('click');
        await nextTick();

        expect(addressField.props('addressOverrideValue')).toEqual(db.addresses[0]);
        expect(addressField.props('contact')).toEqual(db.contacts[0]);
    });

    test('it passes the api key down to the AddressEditor component', async () => {
        const wrapper = mountComponent({
            propsData: {
                apiKey: 'test',
            },
        });

        (wrapper.vm as any).showDialog = true;

        await flushPromises();

        expect(wrapper.getComponent({ name: 'AddressEditor' }).props('apiKey')).toBe('test');
    });

    test('it applies attributes to the AddressField component', async () => {
        const wrapper = mountComponent({
            propsData: {
                apiKey: 'test',
            },
        });

        (wrapper.vm as any).showDialog = true;

        await flushPromises();

        expect(wrapper.getComponent({ name: 'AddressEditor' }).props('apiKey')).toBe('test');
    });

    describe('dialog', () => {
        let wrapper: VueWrapper<any>;
        let dialog: VueWrapper<any>;
        let textFields: VueWrapper<any>[];
        let searchControls: VueWrapper<any>[];

        describe('when real-address-only', () => {
            beforeEach(async () => {
                wrapper = mountComponent({
                    propsData: {
                        readonly: false,
                        addressEditMode: 'real-address-only',
                        modelValue: {
                            address: db.addresses[0],
                            contact: db.contacts[0],
                        },
                        dataProvider,
                    },
                });
                wrapper.findComponent(WtgAddressField).vm.$emit('edit');
                await nextTick();

                dialog = wrapper.findComponent(WtgDialog);
                textFields = dialog.findAllComponents(WtgTextField);
                searchControls = dialog.findAllComponents(WtgSearchField);
                jest.spyOn(dataProvider, 'setActiveCompany');
            });

            test('text fields are populated', () => {
                const textFieldValues = textFields.map((x: any) => x.props('modelValue'));
                expect(textFieldValues).toEqual([
                    'Big Corp',
                    'Address line 2',
                    'Suburb',
                    'postcode',
                    'region',
                    '<EMAIL>',
                    '+61444444490',
                    '+**********',
                ]);
            });

            test('The correct name of the contact is selected', () => {
                const contactField = searchControls.at(2)!;
                expect(contactField?.props('modelValue')).toBe('Alberto');
            });

            test('correct company is selected', () => {
                const companyField = textFields.at(0);
                expect(companyField?.props('modelValue')).toBe('Big Corp');
            });

            test('the correct country is selected', () => {
                const countryField = searchControls.at(1);
                expect(countryField?.props('modelValue')).toBe('GR');
            });

            describe('when clear icon is clicked', () => {
                beforeEach(async () => {
                    const buttons = dialog.findAllComponents(WtgButton);
                    buttons.at(1)!.trigger('click');
                    await nextTick();
                });

                test('setActiveCompany is called', async () => {
                    expect(dataProvider.setActiveCompany).toHaveBeenCalledWith(undefined);
                });

                test('no company is selected', async () => {
                    expect(dataProvider.setActiveCompany).toHaveBeenCalledWith(undefined);

                    const companyField = searchControls.at(0)!;
                    expect(companyField.props('modelValue')).toEqual('');
                });

                test('no address is selected', () => {
                    expect(wrapper.vm.selectedAddress).toBeUndefined();
                });

                test('no contact is selected', async () => {
                    expect(wrapper.vm.selectedContact).toBeUndefined();
                });

                test('text fields are all empty', () => {
                    const allFieldsEmpty = textFields
                        .map((x: any) => x.props('modelValue'))
                        .every((x: string) => x === undefined);
                    expect(allFieldsEmpty).toBe(true);
                });
            });

            test('address is not overriden', () => {
                const addressTitles = dialog.findAllComponents({ name: 'AddressTitle' });
                expect(addressTitles.at(0)!.props('isCardAddressOverriden')).toBe(false);
            });

            test('address override icon is NOT visible', () => {
                const iconWrappers = dialog.findAllComponents({ name: 'WtgIcon' });
                expect(iconWrappers.at(0)!.html()).not.toContain('s-icon-status-critical');
            });
        });

        describe('when real-address-free-text', () => {
            describe('when address and contact are selected', () => {
                beforeEach(() => {
                    wrapper = mountComponent({
                        propsData: {
                            readonly: false,
                            addressEditMode: 'real-address-free-text',
                            modelValue: {
                                address: db.addresses[0],
                                contact: db.contacts[0],
                                isAddressOverriden: false,
                            },
                            dataProvider,
                        },
                    });
                });

                test('address is not overriden if no values are changed', () => {
                    const addressTitle = wrapper.findComponent(AddressTitle);
                    expect(addressTitle?.props('isCardAddressOverriden')).toBe(false);
                });

                describe('when dialog is opened', () => {
                    beforeEach(async () => {
                        wrapper.findComponent(WtgAddressField).vm.$emit('edit');
                        await nextTick();

                        dialog = wrapper.findComponent(WtgDialog);
                        textFields = dialog.findAllComponents(WtgTextField);
                        searchControls = dialog.findAllComponents(WtgSearchField);
                    });

                    const assertAddressOverrideDescription = (wrapper: VueWrapper<any>, expectedChanges: string) => {
                        expect(wrapper.vm.popupAddressOverrideDescription).toContain(
                            `This is a free-text overridden address due to the following changes: ${expectedChanges}`
                        );
                    };

                    test('address is not overriden if no values are changed', () => {
                        const addressTitle = wrapper.findComponent(AddressTitle);
                        expect(addressTitle?.props('isCardAddressOverriden')).toBe(false);
                    });

                    test('if Organization name value is changed', async () => {
                        const companyField = textFields.at(0);
                        const input = companyField!.find('input');
                        await input.setValue(`${input.element.value}-changed`);
                        await input.trigger('blur');

                        await nextTick();

                        const addressTitle = dialog.findComponent(AddressTitle);
                        expect(addressTitle?.props('isCardAddressOverriden')).toBe(true);
                        expect(wrapper.vm.popupAddressOverrideDescription).toContain('This is a free-text address');
                    });

                    describe('address is overriden', () => {
                        [
                            {
                                message: 'if Address line 1 value is changed',
                                elementsAt: [0],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>Address line 1</strong> changed from '111/22 Address line 1' to '111/22 Address line 1-0'</li></ul>",
                            },
                            {
                                message: 'if Name value is changed',
                                elementsAt: [2],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>Fullname</strong> changed from 'Alberto' to 'Alberto-2'</li></ul>",
                            },
                        ].forEach((testCase) => {
                            test(testCase.message, async () => {
                                await nextTick();

                                await Promise.all(
                                    testCase.elementsAt.map(async (at) => {
                                        const input = searchControls.at(at)!.find('input');
                                        await input.setValue(`${input.element.value}-${at}`);
                                        await input.trigger('blur');
                                    })
                                );

                                await nextTick();

                                const addressTitle = dialog.findComponent(AddressTitle);
                                expect(addressTitle?.props('isCardAddressOverriden')).toBe(true);
                                assertAddressOverrideDescription(wrapper, testCase.expected);
                            });
                        });
                        [
                            {
                                message: 'if Address line 2 value is changed',
                                elementsAt: [1],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>Address line 2</strong> changed from 'Address line 2' to 'Address line 2-1'</li></ul>",
                            },
                            {
                                message: 'if Suburb value is changed',
                                elementsAt: [2],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>City</strong> changed from 'Suburb' to 'Suburb-2'</li></ul>",
                            },
                            {
                                message: 'if Postal/Zip code value is changed',
                                elementsAt: [3],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>Postal/Zip code</strong> changed from 'postcode' to 'postcode-3'</li></ul",
                            },
                            {
                                message: 'if Region value is changed',
                                elementsAt: [4],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>State/Province/Region</strong> changed from 'region' to 'region-4'</li></ul>",
                            },
                            {
                                message: 'if Email value is changed',
                                elementsAt: [5],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>Email</strong> changed from '<EMAIL>' to '<EMAIL>-5'</li></ul>",
                            },
                            {
                                message: 'if Phone 1 value is changed',
                                elementsAt: [6],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>Phone 1</strong> changed from '+61444444490' to '+61444444490-6'</li></ul>",
                            },
                            {
                                message: 'if Phone 2 value is changed',
                                elementsAt: [7],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>Phone 2</strong> changed from '+**********' to '+**********-7'</li></ul>",
                            },
                            {
                                message: 'if multiple values are changed',
                                elementsAt: [1, 7],
                                expected:
                                    "<ul style=\"padding-left: 15px;\"><li><strong>Address line 2</strong> changed from 'Address line 2' to 'Address line 2-1'</li><li><strong>Phone 2</strong> changed from '+**********' to '+**********-7'</li></ul>",
                            },
                        ].forEach((testCase) => {
                            test(testCase.message, async () => {
                                wrapper.vm.popupContactSelected = true;
                                await nextTick();

                                testCase.elementsAt.forEach(async (at) => {
                                    const input = textFields.at(at)!.find('input');

                                    await input.setValue(`${input.element.value}-${at}`);
                                    await input.trigger('blur');
                                });

                                await nextTick();

                                const addressTitle = dialog.findComponent(AddressTitle);
                                expect(addressTitle?.props('isCardAddressOverriden')).toBe(true);
                                assertAddressOverrideDescription(wrapper, testCase.expected);
                            });
                        });

                        describe('when address changes are applied to WtgAddressField', () => {
                            beforeEach(async () => {
                                searchControls.at(0)!.vm.$emit('update:modelValue', 'new street value');
                                await nextTick();
                                const applyButton = dialog.findAllComponents(WtgButton).at(3);
                                applyButton!.vm.$emit('click');
                            });

                            test('address override is applied to WtgAddressField', () => {
                                const addressField = wrapper.findComponent(WtgAddressField);
                                expect(addressField.props('isAddressOverriden')).toBe(true);
                            });

                            test('new street value is updated', () => {
                                const addressField = wrapper.findComponent(WtgAddressField);
                                expect(addressField.props('addressOverrideValue')).toEqual({
                                    additionalInfo: 'Additional information',
                                    city: 'Suburb',
                                    code: '1LO',
                                    company: 'Big Corp',
                                    companyCode: 'BIG',
                                    companyGuid: '1',
                                    countryCode: 'GR',
                                    email: '<EMAIL>',
                                    guid: '1',
                                    isAddressOverriden: true,
                                    mobile: '+**********',
                                    phone: '+**********',
                                    postcode: 'postcode',
                                    state: 'region',
                                    street: 'new street value',
                                    streetAlt: 'Address line 2',
                                });
                                expect(addressField.props('addressOverrideDescription')).toEqual(
                                    "This is a free-text overridden address due to the following changes: <ul style=\"padding-left: 15px;\"><li><strong>Address line 1</strong> changed from '111/22 Address line 1' to 'new street value'</li></ul>"
                                );
                            });

                            test('dialog is closed', async () => {
                                jest.runAllTimers();
                                await nextTick();
                                const dialog = wrapper.findComponent(WtgDialog);
                                expect(dialog.props('modelValue')).toBe(false);
                            });
                        });

                        describe('when contact changes are applied to WtgAddressField', () => {
                            beforeEach(async () => {
                                textFields.at(7)!.vm.$emit('update:modelValue', '+6141234568');
                                const applyButton = dialog.findAllComponents(WtgButton).at(3);
                                applyButton!.vm.$emit('click');
                            });

                            test('new mobile number is updated', () => {
                                const addressField = wrapper.findComponent(WtgAddressField);

                                expect(addressField.props('contact')).toEqual(
                                    expect.objectContaining({
                                        email: '<EMAIL>',
                                        guid: '1',
                                        mobile: '+6141234568',
                                        name: 'Alberto',
                                        phone: '+61444444490',
                                    })
                                );
                                expect(addressField.props('addressOverrideDescription')).toEqual(
                                    "This is a free-text overridden address due to the following changes: <ul style=\"padding-left: 15px;\"><li><strong>Phone 2</strong> changed from '+**********' to '+6141234568'</li></ul>"
                                );
                            });

                            test('dialog is closed', async () => {
                                jest.runAllTimers();
                                await nextTick();
                                const dialog = wrapper.findComponent(WtgDialog);
                                expect(dialog.props('modelValue')).toBe(false);
                            });
                        });

                        describe('when save is clicked without making changes', () => {
                            beforeEach(async () => {
                                dialog.findAllComponents(WtgButton).at(2)!.vm.$emit('click');
                                await nextTick();
                            });

                            test('address is not overriden if no values are changed', () => {
                                expect(wrapper.vm.isPopupAddressOverriden).toBe(false);
                            });
                        });

                        describe('when cancel is clicked', () => {
                            beforeEach(async () => {
                                dialog.findAllComponents(WtgButton).at(0)!.vm.$emit('click');
                                await nextTick();
                            });

                            test('dialog is closed', () => {
                                const dialog = wrapper.findComponent(WtgDialog);
                                expect(dialog.props('modelValue')).toBe(false);
                            });

                            test('address is not overriden if no values are changed', () => {
                                const addressField = wrapper.findComponent(WtgAddressField);
                                expect(addressField.props('isAddressOverriden')).toBe(false);
                            });
                        });
                    });
                });
            });

            describe('when address with override is selected', () => {
                beforeEach(() => {
                    wrapper = mountComponent({
                        propsData: {
                            readonly: false,
                            addressEditMode: 'real-address-free-text',
                            modelValue: {
                                address: db.addresses[0],
                                isAddressOverriden: true,
                            },
                            dataProvider,
                        },
                    });
                });

                test('address is overriden', () => {
                    expect(wrapper.findComponent(AddressTitle).props('isCardAddressOverriden')).toBe(true);
                });
            });

            test('when address with overridden company name is selected, company name is passed as value to WtgSearchControl', async () => {
                wrapper = mountComponent({
                    propsData: {
                        readonly: false,
                        addressEditMode: 'real-address-free-text',
                        modelValue: {
                            address: { company: 'Not a real company' },
                            isAddressOverriden: true,
                        },
                        dataProvider,
                    },
                });
                wrapper.findComponent(WtgAddressField).vm.$emit('edit');
                await nextTick();

                dialog = wrapper.findComponent(WtgDialog);
                const searchControl = dialog.findComponent(WtgSearchField);
                expect(searchControl.props('modelValue')).toBe('Not a real company');
            });

            test('when address without overridden company name is selected, company guid is passed as value to WtgSearchControl', async () => {
                wrapper = mountComponent({
                    propsData: {
                        readonly: false,
                        addressEditMode: 'real-address-free-text',
                        modelValue: {
                            address: db.addresses[0],
                            isAddressOverriden: true,
                        },
                        dataProvider,
                    },
                });
                wrapper.findComponent(WtgAddressField).vm.$emit('edit');
                await nextTick();
                const searchControl = wrapper.findComponent(WtgSearchField);
                expect(searchControl.props('modelValue')).toBe('Big Corp');
            });

            describe('when address and contact are NOT selected', () => {
                beforeEach(async () => {
                    wrapper = mountComponent({
                        propsData: {
                            readonly: false,
                            addressEditMode: 'real-address-free-text',
                            modelValue: {
                                address: undefined,
                                contact: undefined,
                            },
                            dataProvider,
                        },
                    });
                    wrapper.findComponent(WtgAddressField).vm.$emit('edit');
                    await nextTick();

                    dialog = wrapper.findComponent(WtgDialog);
                    textFields = dialog.findAllComponents(WtgTextField);
                });

                test('address is not overriden if no values are changed', () => {
                    const addressField = wrapper.findComponent(WtgAddressField);
                    expect(addressField.props('isAddressOverriden')).toBe(false);
                });

                describe('address is overriden', () => {
                    [
                        {
                            message: 'if street value is changed',
                            elementsAt: [0],
                        },
                        {
                            message: 'if Address line 2 value is changed',
                            elementsAt: [1],
                        },
                        {
                            message: 'if city value is changed',
                            elementsAt: [2],
                        },
                        {
                            message: 'if state value is changed',
                            elementsAt: [3],
                        },
                        {
                            message: 'if postcode value is changed',
                            elementsAt: [4],
                        },
                        {
                            message: 'if phone value is changed',
                            elementsAt: [5],
                        },
                        {
                            message: 'if mobile value is changed',
                            elementsAt: [6],
                        },
                        {
                            message: 'if email value is changed',
                            elementsAt: [7],
                        },
                        {
                            message: 'if multiple values are changed',
                            elementsAt: [1, 7],
                        },
                    ].forEach((testCase) => {
                        test(testCase.message, async () => {
                            testCase.elementsAt.forEach(async (at) => {
                                const input = textFields.at(at)!.find('input');

                                await input.setValue(at);
                                await input.trigger('blur');
                            });

                            expect(wrapper.vm.isPopupAddressOverriden).toBe(true);
                            expect(wrapper.vm.popupAddressOverrideDescription).toBe('This is a free-text address');
                        });
                    });
                });
            });

            describe('when changing contact details', () => {
                let dialog: VueWrapper<any>;
                let addressEditor: VueWrapper<any>;
                let emailField: VueWrapper<any> | undefined;

                beforeEach(async () => {
                    wrapper = mountComponent({
                        propsData: {
                            readonly: false,
                            addressEditMode: 'real-address-free-text',
                            modelValue: {
                                address: db.addresses[0],
                                contact: db.contacts[0],
                                isAddressOverriden: true,
                            },
                            dataProvider,
                        },
                    });
                    wrapper.vm.showDialog = true;
                    await nextTick();

                    dialog = wrapper.findComponent(WtgDialog);
                    addressEditor = dialog.findComponent(AddressEditor);
                    emailField = addressEditor.findAllComponents(WtgTextField).at(5);
                });

                test('if contact name is removed, contact details should remain', async () => {
                    const contactField = addressEditor.findAllComponents(WtgSearchField).at(2);
                    const phoneField = addressEditor.findAllComponents(WtgTextField).at(6);
                    const mobileField = addressEditor.findAllComponents(WtgTextField).at(7);

                    expect(contactField?.props('modelValue')).toEqual(db.contacts[0].name);
                    expect(emailField?.props('modelValue')).toEqual(db.contacts[0].email);
                    expect(phoneField?.props('modelValue')).toEqual(db.contacts[0].phone);
                    expect(mobileField?.props('modelValue')).toEqual(db.contacts[0].mobile);

                    contactField?.vm.$emit('update:modelValue', '');
                    await nextTick();

                    expect(addressEditor.emitted('contact-changed')).toBeTruthy();
                    expect(emailField?.props('modelValue')).toEqual(db.contacts[0].email);
                    expect(phoneField?.props('modelValue')).toEqual(db.contacts[0].phone);
                    expect(mobileField?.props('modelValue')).toEqual(db.contacts[0].mobile);
                });

                test('if contact name is selected, update details from selected contact', async () => {
                    emailField?.vm.$emit('update:modelValue', 'new email');

                    expect(addressEditor.emitted('contact-changed')).toBeTruthy();
                    expect(wrapper.vm.popupAddress.email).toBe(db.addresses[0].email);
                    expect(wrapper.vm.popupContact.email).toBe('new email');
                });

                test('if no contact name is selected, update details from selected address', async () => {
                    wrapper.vm.popupContact = { ...db.contacts[4] };
                    await nextTick();

                    emailField?.vm.$emit('update:modelValue', 'new email');

                    expect(addressEditor.emitted('contact-changed')).toBeTruthy();
                    expect(wrapper.vm.popupAddress.email).toBe('new email');
                    expect(wrapper.vm.popupContact.email).toBe(db.contacts[4].email);
                });
            });
        });
    });

    test('AddressField to allow free text entry or not based on the mode', () => {
        const wrapper = mountComponent({
            propsData: {
                readonly: false,
                addressEditMode: JobAddressEditMode.RealAddressAndFreeText,
                modelValue: {
                    address: undefined,
                    contact: undefined,
                },
                dataProvider,
            },
        });

        const addressField = wrapper.findComponent(WtgAddressField);
        expect(addressField.props('allowFreeTextAddressEntry')).toBe(true);
    });

    test('onPopupApply will sync back the internal value as undefined if popUpAddress is empty', async () => {
        const wrapper = mountComponent({
            propsData: {
                readonly: false,
                addressEditMode: JobAddressEditMode.RealAddressAndFreeText,
                modelValue: {
                    address: undefined,
                    contact: undefined,
                },
                dataProvider,
            },
        });

        await (wrapper.vm as any).onPopupApply();
        expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        expect((wrapper.emitted()['update:modelValue'] as any)![0][0]).toBe(undefined);
    });

    test('onPopupApply will sync back the internal value as the popUpAddress data if popUpAddress has data', async () => {
        const wrapper = mountComponent({
            propsData: {
                readonly: false,
                addressEditMode: JobAddressEditMode.RealAddressAndFreeText,
                modelValue: { address: db.addresses[0], contact: db.contacts[0] },
                dataProvider,
            },
        });

        const addressField = wrapper.findComponent(WtgAddressField);
        expect(addressField.props('contact')).toEqual(db.contacts[0]);

        addressField.vm.$emit('update:modelValue', db.addresses[1]);
        addressField.vm.$emit('edit');
        await nextTick();

        await (wrapper.vm as any).onPopupApply();
        expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        expect((wrapper.emitted()['update:modelValue'] as any)![0][0]).toStrictEqual({
            address: {
                additionalInfo: 'Additional Information',
                city: 'SunTown',
                code: '1YES',
                company: 'Ant Tech',
                companyCode: 'ANT',
                companyGuid: '2',
                countryCode: 'AU',
                guid: '2',
                postcode: '2017',
                state: 'NSW',
                street: '1 Yes st',
            },
            contact: undefined,
            isAddressOverriden: false,
        });
    });

    describe('when there are errors in the input and the Apply button is clicked', () => {
        let wrapper: VueWrapper<any>;
        let dialog: VueWrapper<any>;

        beforeEach(async () => {
            jest.useFakeTimers();
            const addressWithError = {
                guid: '1',
                companyGuid: '1',
                company: 'Big Corp',
                companyCode: 'BIG',
                code: '1LO',
                street: '',
                streetAlt: 'Address line 2',
                city: 'Suburb',
                postcode: 'postcode',
                state: 'region',
                countryCode: 'GR',
                phone: '+**********',
                mobile: '+**********',
                email: '<EMAIL>',
                additionalInfo: 'Additional information',
            };
            wrapper = mountComponent({
                propsData: {
                    readonly: false,
                    addressEditMode: 'real-address-only',
                    modelValue: {
                        address: db.addresses[0],
                        contact: db.contacts[0],
                    },
                    dataProvider,
                },
            });
            wrapper.findComponent(WtgAddressField).vm.$emit('edit');
            await nextTick();

            dialog = wrapper.findComponent(WtgDialog);
            const applyButton = dialog.findAllComponents({ name: 'WtgButton' }).at(3);

            const addressEditor = dialog.findComponent(AddressEditor);

            addressEditor.vm.$emit('address-changed', addressWithError);
            await nextTick();

            applyButton?.vm.$emit('click');
            await nextTick();
            await wrapper.setProps({
                validationStates: [
                    {
                        alertLevel: 4,
                        targetKey: undefined,
                        targetProperty: 'E2_Address1',
                        messages: 'Address Line 1 is required',
                        error: true,
                        warning: false,
                    },
                ],
            });
        });

        test('the dialog should not close', () => {
            jest.runAllTimers();
            expect(dialog.props('modelValue')).toBe(true);
        });
    });

    test('when cancelling the address should be set back to the prevous valid value', async () => {
        const wrapper = mountComponent({
            propsData: {
                readonly: false,
                addressEditMode: 'real-address-only',
                modelValue: {
                    address: db.addresses[0],
                    contact: db.contacts[0],
                },
                dataProvider,
            },
        });
        wrapper.findComponent(WtgAddressField).vm.$emit('edit');
        await nextTick();

        const dialog = wrapper.findComponent(WtgDialog);
        const addressEditor = dialog.findComponent(AddressEditor);
        addressEditor.vm.$emit('address-changed', db.addresses[1]);
        await nextTick();

        const applyButton = dialog.findAllComponents({ name: 'WtgButton' }).at(3);
        applyButton?.vm.$emit('click');
        await nextTick();
        await nextTick();
        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);

        const addressField = wrapper.findComponent(WtgAddressField);
        expect(addressField.props('addressOverrideValue')).toEqual(db.addresses[1]);

        const cancelButton = dialog.findAllComponents({ name: 'WtgButton' }).at(2);
        cancelButton?.vm.$emit('click');
        await nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(2);
        expect(addressField.props('addressOverrideValue')).toEqual(db.addresses[0]);
    });

    test('it propagates the hide-map-location property.', async () => {
        const wrapper = mountComponent({
            propsData: {
                modelValue: { address: db.addresses[0], contact: db.contacts[0] },
                hideMapLocation: true,
                hideCountry: true,
                hideStreetDetails: true,
            },
        });
        const addressField = wrapper.findComponent(WtgAddressField);
        expect(addressField.props('contact')).toEqual(db.contacts[0]);

        addressField.vm.$emit('update:modelValue', db.addresses[1]);
        addressField.vm.$emit('edit');
        await nextTick();

        const addressEditor = wrapper.getComponent(WtgDialog).findComponent(AddressEditor);
        expect(addressEditor.props('hideMapLocation')).toBe(true);
        expect(addressEditor.props('hideCountry')).toBe(true);
        expect(addressEditor.props('hideStreetDetails')).toBe(true);

        wrapper.setProps({ hideMapLocation: false, hideCountry: false, hideStreetDetails: false });
        await nextTick();
        expect(addressEditor.props('hideMapLocation')).toBe(false);
        expect(addressEditor.props('hideCountry')).toBe(false);
        expect(addressEditor.props('hideStreetDetails')).toBe(false);
    });

    test('it has a (deprecated) title property as an alias of label property', async () => {
        const wrapper = mountComponent({ propsData: { title: 'Custom Title' } });

        const label = wrapper.get('label');
        expect(label.text()).toBe('Custom Title');

        wrapper.findComponent(WtgAddressField).vm.$emit('edit');
        await nextTick();

        const dialog = wrapper.findComponent(WtgDialog);
        const addressTitle = dialog.findComponent(AddressTitle);
        expect(addressTitle.props('title')).toBe('Custom Title');
    });

    test('it does not have a single root component, therefor it needs to pass its attributes explicitly to the adress field component', () => {
        const wrapper = mountComponent({ attrs: { ['design-control-id']: 'some id' } });

        const addressField = wrapper.findComponent(WtgAddressField);
        expect(addressField.attributes('design-control-id')).toBe('some id');
    });

    test('When address is changed in dialog, and apply button is clicked, Promise.allSettled is called to settle all tracked pending promises', async () => {
        jest.spyOn(Promise, 'allSettled');
        const { trackPendingCommit } = usePendingCommits();
        const pendingPromise = new Promise<void>((resolve) => setTimeout(resolve, 100));

        trackPendingCommit(pendingPromise);
        const wrapper = mountComponent({
            propsData: { modelValue: { address: db.addresses[0], contact: db.contacts[0] } },
        });
        const addressField = wrapper.findComponent(WtgAddressField);
        addressField.vm.$emit('edit');
        await nextTick();
        const dialog = wrapper.getComponent(WtgDialog);
        const addressEditor = dialog.findComponent(AddressEditor);

        addressEditor.vm.$emit('address-changed', db.addresses[1]);
        addressEditor.vm.$emit('contact-changed', db.contacts[1]);

        const applyBtn = dialog.findAllComponents(WtgButton).at(3);
        applyBtn?.vm.$emit('click');
        expect(Promise.allSettled).toHaveBeenCalledTimes(1);
        expect(Promise.allSettled).toHaveBeenCalledWith([pendingPromise]);
    });

    function mountComponent({ attrs = {}, propsData = {} } = {}) {
        return mount<any>(WtgJobAddress, {
            attrs,
            propsData: {
                dataProvider,
                ...propsData,
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
