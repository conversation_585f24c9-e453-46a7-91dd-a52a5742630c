import calloutAnatomy from '../../../assets/WtgCallout/callout-anatomy.png';
import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import infoToast from '../../../assets/WtgToast/sentiment-info.png';
import WtgToast from '../../WtgToast/stories/WtgToast.stories';
import * as WtgCallout from './WtgCallout.stories.ts';

import calloutBehaviorExample from '../../../assets/WtgCallout/callout-behavior-example.png';
import calloutBehaviorExample2 from '../../../assets/WtgCallout/callout-behavior-example2.png';
import calloutDoExample from '../../../assets/WtgCallout/callout-do-example.png';
import calloutDoExample2 from '../../../assets/WtgCallout/callout-do-example2.png';
import calloutDoExample3 from '../../../assets/WtgCallout/callout-do-example3.png';
import calloutDontExample from '../../../assets/WtgCallout/callout-dont-example.png';
import calloutDontExample2 from '../../../assets/WtgCallout/callout-dont-example2.png';
import calloutDontExample3 from '../../../assets/WtgCallout/callout-dont-example3.png';

<Meta of={WtgCallout} />

<div className="component-header">
    <h1>Callout</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-3704&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    Callouts are alerts that highlight important contextual information within an interface, sitting inline with the
    content.
</p>

## API

<Canvas className="canvas-preview" of={WtgCallout.Default} />
<Controls of={WtgCallout.Default} sort={'alpha'} exclude={['default']} />

## Anatomy

<div className="d-flex flex-column align-center">
    <img srcSet={`${calloutAnatomy} 3x`} alt="anatomy of callout component" />
</div>

<ol className="anatomy-list">
    <li>
        <strong>Icon:</strong> Icon type is linked to sentiment, see [sentiments](#sentiments) below.
    </li>
    <li>
        <strong>Title (optional):</strong> Title clearly indicating the purpose of the message.
    </li>
    <li>
         <strong>Action (optional):</strong> A link that enables users to act on the callout content.
    </li>
    <li>
    <strong>Description:</strong> Describes the issue or information.

    </li>
    <li>
        <strong>Dismiss (optional):</strong> Removes the callout from the page.
    </li>

</ol>

## How to use

### Summary table

Callouts are part of the alerts family. Use the summary table below to help decide which alert component or pattern to use.

<table width="100%" className="component-summary-table">
    <thead>
        <tr>
            <th>Component/Pattern</th>
            <th>Priority</th>
            <th>Best used for</th>
            <th>Available sentiments</th>
        </tr>
    </thead>
    <tbody>
        <tr>
        <td>
                <p>[Callout](/docs/components-callout--docs)</p> 
            </td>
            <td>
                <p>Low</p>
            </td>
            <td>
                <p>Highlighting important contextual information on a page or specific section of a page. </p>
            </td>
            <td>
                <p>Success, info, warning, critical</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>[Toast](/docs/components-toast--docs)</p>
            </td>
            <td>
                <p>Low-medium</p>
            </td>
            <td>
                <p>Providing feedback after a user action and enables users to act on the toast content, if applicable.</p>
            </td>
            <td>
                <p>Success, info, warning, critical</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>[Field validation](/docs/components-text-field--docs)</p>
            </td>
            <td>
                <p>Medium-high</p>
            </td>
            <td>
                <p>Indicating errors or warnings on specific field/s along with actionable feedback. </p>
            </td>
            <td>
                <p>Success, warning, critical</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>[Alert modal](/docs/components-alert-modal--docs)</p>
            </td>
            <td>
                <p>High</p>
            </td>
            <td>
                <p>Providing important information that blocks a user from proceeding until a decision has been made.</p>
            </td>
            <td>
                <p>Success, warning, critical</p>
            </td>

        </tr>
    </tbody>

</table>

### Do's and dont's

<div className="do-dont-pair">
    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-do">
            <img src={calloutDoExample} alt="example alt text" />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ✔️ <strong>Do</strong>
            </p>
            <p>Use callouts to provide lower priority alerts and information relevant to the surrounding context.</p>
        </div>
    </div>
    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-dont">
            <img src={calloutDontExample} alt="example alt text" />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ❌ <strong>Don't</strong>
            </p>
            <p>Use callouts for high urgency alerts that require immediate user attention. Instead, consider using an [alert modal](/docs/components-alert-modal--docs). </p>
        </div>
    </div>

    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-do">
            <img src={calloutDoExample2} alt="example alt text" />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ✔️ <strong>Do</strong>
            </p>
            <p>Use callouts sparingly to ensure they remain effective and impactful.</p>
        </div>
    </div>
    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-dont">
            <img src={calloutDontExample2} alt="example alt text" />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ❌ <strong>Don't</strong>
            </p>
            <p>Use multiple callouts in a single panel that could overwhelm a user.</p>
        </div>

    </div>

    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-do">
            <img src={calloutDoExample3} alt="example alt text" />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ✔️ <strong>Do</strong>
            </p>
            <p>Use callouts inside content panels.</p>
        </div>
    </div>
    <div className="do-dont-panel">
        <div className="do-dont-example do-dont-example-dont">
            <img src={calloutDontExample3} alt="example alt text" />
        </div>
        <div className="do-dont-content">
            <p className="do-dont-content-header">
                ❌ <strong>Don't</strong>
            </p>
            <p>Use callouts outside of content panels.</p>
        </div>

    </div>

</div>

## Behavior

### Callout placement and sizing

-   Callouts sit inline with content and should always be contained within a panel.
-   Callouts, by default, fill the width of their container. However, this can be adjusted to "hug" width (based on content length) or a fixed width if spanning the entire container isn't appropriate for the context.
-   Their height is based on the content length.

### Display behavior

{' '}

<strong>Persistent:</strong> Callouts that are a permanent fixture on the interface with no option to dismiss.
<img src={calloutBehaviorExample} width="400px" height="428px" />
<strong>Dynamic:</strong> Callouts that exist temporarily to indicate an issue or warning on an interface. Once
resolved, the callout will disappear.
<img src={calloutBehaviorExample2} width="434" height="687px" />
<strong>Dismissible:</strong> Callouts with a dismiss action allow users to remove them from the interface. Once dismissed,
the callout won’t return unless the page is reloaded, so use with caution. Do not use dismissible callouts for information
that users need to reference readily.

## Variants

Callouts have two variants to choose from, default and inline, providing two levels of visual hierarchy to work with. Both variants have success, info, warning, and critical sentiments.

### Default vs inline

-   The inline variant is a simplified, less visually prominent version of the default callout.
-   The inline variant only has a description, no title.
-   The inline variant does not have the option to be dismissed.
-   Use the inline variant when space is limited, and/or when a more subtle approach is appropriate, e.g., using the inline callout variant in a complex form where important information needs to be highlighted but without disrupting the overall flow.

<div className="d-inline-flex mt-6">
    <Story of={WtgCallout.Sentiments} sourceState={'none'} />
    <Story of={WtgCallout.Inline} sourceState={'none'} />
</div>

## Sentiments

Both default and inline variants have success, info, warning, and critical sentiments.

### Success

<div className="sentiment-row">
    Success callouts provide positive messages, indicate a successful outcome or completion, or offer reassurance.
    <Story of={WtgCallout.Success} />
</div>

### Info

<div className="sentiment-row">
    Info callouts highlight neutral, yet important information that could help or impact the user.
    <Story of={WtgCallout.Info} />
</div>

### Warning

<div className="sentiment-row">
    Warning callouts indicate potential issues that warrant caution but are not critical. It might not be a blocker, but
    it might lead to an undesirable outcome. Currently, within CargoWise, warning callouts are also used to list
    validation errors in an [error dialog](/docs/framework-dialogs-error-dialog--docs) on 'Save' or 'Submit'.
    <Story of={WtgCallout.Warning} />
</div>

### Critical

<div className="sentiment-row">
    <div>
    Critical callouts alert users to serious issues that could lead to failures or major problems if the user is not
    aware of the information in the callout. The action cannot be completed because of a user or system error, and the
    user will be blocked from proceeding until the error has been resolved. Currently, within CargoWise, critical callouts are also used
    to list validation errors in an [error dialog](/docs/framework-dialogs-error-dialog--docs) on 'Save' or 'Submit'.

    ⚠️ Keep in mind that callouts are fundamentally lower-priority alerts. For critical actions requiring the user's full attention and preventing them from moving forward, consider using an [alert modal](/docs/components-alert-modal--docs) instead.
    </div>

    <Story of={WtgCallout.Critical} />

</div>

## Content guidelines

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview).

### Title

-   Titles should be short and descriptive, optimized for scanning.
-   If titles need to be longer, always wrap text and never truncate.
-   Never end a title with a period.

### Description

-   Description text should be clear, concise, and informative.
-   Descriptions should complement the title but avoid repeating information already contained in the title.
-   There are no strict line or character limits, but consider the above guidance and don’t overwhelm users with too much written content inside a callout.
-   Always wrap text and never truncate.

### Action

-   Callouts support one optional action in the form of a link.
-   Use brief, active verbs e.g. “View” or “Undo”.
-   Ideally, follow with a noun to provide further clarity on the action to be done, e.g. "View details".

## Related components

-   [Toast](/docs/components-toast--docs)
-   [Text field](/docs/components-text-field--docs)
-   [Alert modal](/docs/components-alert-modal--docs)
-   [Error dialog](/docs/framework-dialogs-error-dialog--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
