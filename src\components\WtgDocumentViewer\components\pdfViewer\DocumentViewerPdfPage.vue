<template>
    <div
        ref="pageRef"
        :class="{
            'document-page': true,
            selected: isSelected,
            'page-preview': pagePreviewMode,
        }"
        :data-page="pageNumber"
        :data-preview-mode="pagePreviewMode ? 'true' : 'false'"
        @click="handlePageClick"
    >
        <canvas ref="pdfCanvas"></canvas>
        <slot :pdf-page-data="pdfPageData"></slot>
        <div v-if="pagePreviewMode">
            <WtgLabel color="var(--s-neutral-txt-disabled)" align="center" display="block" typography="text-sm-default">
                {{ pageNumber }}
            </WtgLabel>
        </div>
    </div>
</template>

<script setup lang="ts">
import { PdfPageData } from '@components/WtgDocumentViewer/types';
import WtgLabel from '@components/WtgLabel';
import { type PDFDocumentProxy, type PDFPageProxy } from 'pdfjs-dist';
import { computed, onMounted, PropType, ref, toRaw, watch } from 'vue';

const props = defineProps({
    pageNumber: { type: Number, default: 1 },
    pdfDocument: { type: Object as PropType<PDFDocumentProxy>, default: null },
    pdfScale: { type: Number, default: 1 },
    pdfRotation: { type: Number, default: 0 },
    pagePreviewMode: { type: Boolean, default: false },
    activePage: { type: Number, default: 1 },
});

const emit = defineEmits<{
    'active-page-updated': [number];
}>();

const page = ref<PDFPageProxy | null>(null);
const pageWrapWidth = ref(0);
const pageWrapHeight = ref(0);
const renderTask = ref<ReturnType<PDFPageProxy['render']> | null>(null);
const pdfCanvas = ref<HTMLCanvasElement | null>(null);
const pageRef = ref<HTMLDivElement | null>(null);

const isSelected = computed(() => props.activePage === props.pageNumber && props.pagePreviewMode);

const pdfPageData = computed<PdfPageData>(() => ({
    pageWidth: pageWrapWidth.value,
    pageHeight: pageWrapHeight.value,
    pageNumber: props.pageNumber,
}));

watch(
    () => props.pdfScale,
    () => {
        renderPage();
    }
);

watch(
    () => props.pdfRotation,
    () => {
        renderPage();
    }
);

const getPageDimensions = () => {
    if (!pageRef.value) return;
    pageWrapWidth.value = pageRef.value.clientWidth;
    pageWrapHeight.value = pageRef.value.clientHeight;
};

const renderPage = async () => {
    if (props.pdfDocument) {
        page.value = await toRaw(props.pdfDocument).getPage(props.pageNumber);
        const context = pdfCanvas.value ? pdfCanvas.value.getContext('2d') : null;

        if (page.value && context) {
            const viewport = page.value.getViewport({ scale: props.pdfScale, rotation: props.pdfRotation });
            const outputScale = window.devicePixelRatio || 1;

            // Set the canvas width and height with scaling for high DPI
            pdfCanvas.value!.width = Math.floor(viewport.width * outputScale);
            pdfCanvas.value!.height = Math.floor(viewport.height * outputScale);

            // Set the width and height to the viewport size
            pdfCanvas.value!.style.width = `${viewport.width}px`;
            pdfCanvas.value!.style.height = `${viewport.height}px`;

            const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : [1, 0, 0, 1, 0, 0];

            const renderContext = {
                canvasContext: context,
                viewport: viewport,
                transform: transform,
            };

            if (renderTask.value) {
                toRaw(renderTask.value).cancel();
            }

            renderTask.value = await toRaw(page.value).render(renderContext);
            await toRaw(renderTask.value).promise.catch((reason: { message: any }) => {
                console.error('Render error:', reason);
            });

            getPageDimensions();
        }
    }
};

const handlePageClick = () => {
    if (!props.pagePreviewMode) return;
    emit('active-page-updated', props.pageNumber);
};

onMounted(() => {
    renderPage();
});
</script>

<style scoped lang="scss">
.document-page {
    position: relative;
    margin: 0 auto 12px auto;
    width: fit-content;

    &.page-preview {
        &:hover {
            cursor: pointer;
        }

        &.selected canvas {
            border: 1px solid var(--s-primary-border-default);
        }
    }

    canvas {
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    }
}
</style>
