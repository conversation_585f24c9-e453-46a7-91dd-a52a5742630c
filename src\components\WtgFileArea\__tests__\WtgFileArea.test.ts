import WtgPanel from '@components/WtgPanel';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgFileArea from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgFileArea', () => {
    test('its name is WtgFileArea', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgFileArea');
    });

    test('it renders a Div to act as the area', () => {
        const area = mountComponent().find('div');
        expect(area.element.tagName.toLowerCase()).toBe('div');
    });

    test('it renders an input type file inside Div to accept file selection', () => {
        const area = mountComponent().find('div');
        expect(area.element.innerHTML).toContain(
            '<input aria-label="Select files" type="file" class="d-none" accept="">'
        );
    });

    test('it passes the multiple property as an attribute on the input', async () => {
        const props: any = {
            multiple: true,
        };
        const wrapper = mountComponent({
            props,
        });
        const inputWrapper = wrapper.find('input');
        expect(inputWrapper.attributes('multiple')).toBeDefined;
        await wrapper.setProps({ multiple: false });
        expect(inputWrapper.attributes('multiple')).toBeUndefined;
    });

    test('it passes the accept property as an attribute on the input', async () => {
        const props: any = {
            accept: '.jpeg, .jpg',
        };
        const wrapper = mountComponent({
            props,
        });
        const inputWrapper = wrapper.find('input');
        expect(inputWrapper.attributes('accept')).toBe('.jpeg, .jpg');
        await wrapper.setProps({ accept: '' });
        expect(inputWrapper.attributes('accept')).toBe('');
    });

    test('it has drag or drop event handlers if disableDrag is false', async () => {
        const wrapper: VueWrapper<any> = mountComponent();
        await wrapper.trigger('dragenter');
        expect(wrapper.vm['dragOverArea']).toBe(true);
    });

    test('it has no drag or drop event handlers if disableDrag is true', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                disableDrag: true,
            },
        });
        await wrapper.trigger('dragenter');
        expect(wrapper.vm['dragOverArea']).toBe(false);
    });

    test('it sets the dragOverArea state when the drag handlers are called', async () => {
        const wrapper: VueWrapper<any> = mountComponent();
        await wrapper.trigger('dragenter', {});
        expect(wrapper.vm['dragOverArea']).toBe(true);
        await wrapper.trigger('dragleave', {});
        expect(wrapper.vm['dragOverArea']).toBe(false);
    });

    test('it has no drag or drop event handlers if disableDrag is toggled', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                disableDrag: false,
            },
        });
        await wrapper.setProps({ disableDrag: true });
        await wrapper.trigger('dragenter');
        expect(wrapper.vm['dragOverArea']).toBe(false);
        await wrapper.setProps({ disableDrag: false });
        await wrapper.trigger('dragenter');
        expect(wrapper.vm['dragOverArea']).toBe(true);
    });

    test('it should emit an input event when input changes', async () => {
        const wrapper = mountComponent();
        const fileInput = wrapper.find('input') as any;
        const returnFile = new File([], 'test.png', { type: 'image/png' });
        Object.defineProperty(fileInput.element, 'files', {
            get: () => [returnFile],
        });
        await fileInput.trigger('change');
        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
    });

    test('it should not emit an input event when input changes but no files are available (user hits ESC)', async () => {
        const wrapper = mountComponent();
        const fileInput = wrapper.find('input') as any;
        Object.defineProperty(fileInput.element, 'files', {
            get: () => [],
        });
        await fileInput.trigger('change');
        expect(wrapper.emitted()['update:modelValue']).toBeUndefined();
    });

    test('it should emit an input event only ONCE when input changes', async () => {
        const wrapper = mountComponent();
        await wrapper.setProps({
            disableDrag: true,
        });

        const fileInput = wrapper.find('input') as any;
        const returnFile = new File([], 'test.png', { type: 'image/png' });
        Object.defineProperty(fileInput.element, 'files', {
            get: () => [returnFile],
        });
        await fileInput.trigger('change');
        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
    });

    test('it should emit an input event when files are dropped', async () => {
        const wrapper = mountComponent();
        const returnFile = new File([], 'test.png', { type: 'image/png' });
        const mockEvent = {
            dataTransfer: {
                files: [returnFile],
            },
        };
        await wrapper.trigger('drop', mockEvent);
        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
    });

    test('it should NOT emit an input event when something that is NOT a file is dropped, for example just some text', async () => {
        const wrapper = mountComponent();
        const mockEvent = {
            dataTransfer: {
                items: [{ kind: 'string', type: 'text/plain' }],
                types: ['text/plain'],
            },
        };
        await wrapper.trigger('drop', mockEvent);
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
    });

    test('it should render a scoped slot if passed', async () => {
        const wrapper = mountComponent({
            slots: {
                default: '<wtg-panel>Drop or Click Here</wtg-panel>',
            },
        });
        const panelWrapper = wrapper.findComponent(WtgPanel);
        expect(panelWrapper.text()).toBe('Drop or Click Here');
    });

    test('it should accept the click from the scoped slot if passed', async () => {
        const wrapper = mountComponent({
            slots: {
                default: '<wtg-panel v-on="on">Drop or Click Here</wtg-panel>',
            },
        });
        const fileInput = wrapper.vm.$refs.input as HTMLElement;
        const slotPanel = wrapper.findComponent(WtgPanel);
        jest.spyOn(fileInput, 'click');
        await slotPanel.trigger('click');
        expect(fileInput.click).toBeCalled();
    });

    test('it should reset the input value prior to displaying the picker dialog', async () => {
        const wrapper = mountComponent({
            slots: {
                default: '<wtg-panel v-on="on">Drop or Click Here</wtg-panel>',
            },
        });
        const fileInput = wrapper.vm.$refs.input as HTMLInputElement;
        fileInput.type = 'text';
        fileInput.value = 'abc';
        const slotPanel = wrapper.findComponent(WtgPanel);
        await slotPanel.trigger('click');
        expect(fileInput.value).toBe('');
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgFileArea, {
            props,
            slots,
            global: {
                components: {
                    WtgPanel,
                },
                plugins: [wtgUi],
            },
        });
    }
});
