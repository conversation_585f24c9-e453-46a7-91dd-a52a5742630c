import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { segmentedControlContextKey } from '@composables/segmentedControl';
import { enableAutoUnmount, mount, type VueWrapper } from '@vue/test-utils';
import { WtgButton } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgButton', () => {
    test('it renders a <button> tag', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('BUTTON');
    });

    test('it renders an <a> tag when the href property is set', () => {
        const wrapper = mountComponent({
            props: {
                href: 'some href',
            },
        });
        expect(wrapper.element.tagName).toBe('A');
    });

    test('it does not render an <a> tag when the href property is set and the control is disabled as <a> tag does not support the disabled attribute', () => {
        const wrapper = mountComponent({
            props: {
                href: 'some href',
                disabled: true,
            },
        });
        expect(wrapper.element.tagName).toBe('BUTTON');
    });

    test('it renders a <router-link> tag when the to property is set', () => {
        const wrapper = mountComponent({
            props: {
                to: '/dashboard',
            },
        });
        expect(wrapper.element.tagName).toBe('ROUTER-LINK');
    });

    test('it does not render an <router-link> tag when the to property is set and the control is disabled as <router-link> tag does not support the disabled attribute', () => {
        const wrapper = mountComponent({
            props: {
                to: '/dashboard',
                disabled: true,
            },
        });
        expect(wrapper.element.tagName).toBe('BUTTON');
    });

    test('it emits a click event when the <button> is clicked', async () => {
        const wrapper = mountComponent();
        const button = wrapper.find('button');
        expect(wrapper.emitted('click')).toBeUndefined();
        await button.trigger('click');
        expect(wrapper.emitted('click')!.length).toBe(1);
        expect(wrapper.emitted('click')![0][0]).toBeInstanceOf(MouseEvent);
    });

    test('it passes the default slot to the <button>', () => {
        const wrapper = mountComponent();
        expect(wrapper.text()).toBe('My Button');
    });

    test('it applies the wtg-button-fill class when variant is fill', () => {
        const wrapper = mountComponent({
            props: {
                variant: 'fill',
            },
        });
        expect(wrapper.classes()).toEqual(['wtg-button--fill', 'wtg-button']);
    });

    test('it applies the wtg-button-fill-available class when fill is undefined', () => {
        const wrapper = mountComponent({
            props: {
                fill: true,
            },
        });
        expect(wrapper.classes()).toContain('wtg-button--fill-available');
    });

    test('it applies the wtg-button-default class when variant is undefined', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toEqual(['wtg-button--default', 'wtg-button']);
    });

    test('it applies the wtg-button-ghost class when variant is ghost', () => {
        const wrapper = mountComponent({
            props: {
                variant: 'ghost',
            },
        });
        expect(wrapper.classes()).toContain('wtg-button--ghost');
    });

    test('it applies the wtg-button--active class when active prop is set', () => {
        const wrapper = mountComponent({
            props: {
                active: true,
            },
        });
        expect(wrapper.classes()).toContain('wtg-button--active');
    });

    test('it defaults type to button for the button element', () => {
        const wrapper = mountComponent();
        expect((wrapper.element as any).type).toBe('button');
    });

    test('it passes type to the button element', () => {
        const wrapper = mountComponent({
            props: {
                type: 'submit',
            },
        });
        expect((wrapper.element as any).type).toBe('submit');
    });

    test('it does not pass the type to the a element', () => {
        const wrapper = mountComponent({
            props: {
                href: 'some href',
                type: 'submit',
            },
        });
        expect((wrapper.element as any).type).toBe('');
    });

    test('it passes disabled to the button element', () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });
        expect(wrapper.element.disabled).toBe(true);
    });

    test('it ensures correct disabled appearance by not adding sentiments or color classes when disabled', async () => {
        const wrapper = mountComponent();

        await wrapper.setProps({ sentiment: 'success', disabled: false });
        expect(wrapper.classes()).toContain('wtg-button--success');
        await wrapper.setProps({ sentiment: 'success', disabled: true });
        expect(wrapper.classes()).not.toContain('wtg-button--success');

        await wrapper.setProps({ sentiment: 'critical', disabled: false });
        expect(wrapper.classes()).toContain('wtg-button--critical');
        await wrapper.setProps({ sentiment: 'critical', disabled: true });
        expect(wrapper.classes()).not.toContain('wtg-button--critical');

        await wrapper.setProps({ sentiment: 'primary', disabled: false });
        expect(wrapper.classes()).toContain('wtg-button--primary');
        await wrapper.setProps({ sentiment: 'primary', disabled: true });
        expect(wrapper.classes()).not.toContain('wtg-button--primary');

        await wrapper.setProps({ sentiment: undefined });
        await wrapper.setProps({ color: 'red', disabled: false });
        expect(wrapper.classes()).toContain('wtg-button--color');
        await wrapper.setProps({ color: 'red', disabled: true });
        expect(wrapper.classes()).not.toContain('wtg-button--color');
    });

    test('it renders the box shadow', () => {
        const wrapper = mountComponent();
        expect(wrapper.findAll('div').at(0)!.classes()).toContain('wtg-button__shadow');
    });

    describe('when loading is set to true', () => {
        let wrapper: VueWrapper;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    loading: true,
                },
            });
        });

        test('it applies the wtg-button-loading class to the button element', () => {
            expect(wrapper.classes()).toContain('wtg-button--loading');
        });

        test('it renders a WtgLoader', () => {
            expect(wrapper.findComponent({ name: 'WtgLoader' }).exists()).toBe(true);
        });
    });

    describe('when sentiment is set', () => {
        test('it applies the wtg-button-success class if sentiment is success', () => {
            const wrapper = mountComponent({
                props: {
                    sentiment: 'success',
                },
            });
            expect(wrapper.classes()).toContain('wtg-button--success');
        });

        test('it applies the wtg-button-critical class if sentiment is critical', () => {
            const wrapper = mountComponent({
                props: {
                    sentiment: 'critical',
                },
            });
            expect(wrapper.classes()).toContain('wtg-button--critical');
        });
    });

    describe('when color is set', () => {
        test('it applies the wtg-button-color class', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('wtg-button--color');
        });

        test('it sets background color and border color to color prop when variant is fill', () => {
            const wrapper = mountComponent({
                props: {
                    variant: 'fill',
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('bg-red');
        });

        test('it sets text color to color prop when variant is ghost', () => {
            const wrapper = mountComponent({
                props: {
                    variant: 'ghost',
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('text-red');
        });

        test('it sets text color and border color to color prop when variant is undefined', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('text-red');
            expect((wrapper.vm as any).computedStyle.borderColor).toBe('currentColor');
        });

        test('it sets the element style from vars', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'var(--s-error-txt-default)',
                },
            });
            expect((wrapper.vm as any).computedStyle.color).toBe('var(--s-error-txt-default)');
        });
    });

    test('it has tooltip capability mixed in', () => {
        const wrapper = mountComponent({
            props: { tooltip: 'Some tooltip' },
        });

        const vm: any = wrapper.vm;
        expect(vm.tooltipDirective.content).toBe('Some tooltip');
        expect(vm.tooltipDirective.placement).toBe('bottom');
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('it toggles a button', async () => {
        const mockOnToggle = jest.fn();
        const mockValue = 'mock-value';

        const wrapper = mountComponent({
            props: {
                value: mockValue,
            },
            provide: {
                [segmentedControlContextKey as symbol]: {
                    value: {
                        onToggle: mockOnToggle,
                    },
                },
            },
        });

        const button = wrapper.find('button');
        await button.trigger('click');

        expect(mockOnToggle).toHaveBeenCalledWith(mockValue, true);
    });

    function mountComponent({ props = {}, provide = {} } = {}) {
        return mount(WtgButton, {
            props,
            slots: {
                default: 'My Button',
            },
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
