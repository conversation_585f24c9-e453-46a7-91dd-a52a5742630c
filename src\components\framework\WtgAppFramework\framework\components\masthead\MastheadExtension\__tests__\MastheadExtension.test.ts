import { WtgFramework, WtgFrameworkTask, WtgFrameworkTaskEntityStatusDisplayMode } from '@components/framework/types';
import WtgStatus from '@components/WtgStatus';
import { setApplication, tabsInfoInjectionKey } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, nextTick, reactive, ref } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import MastheadExtension from '../MastheadExtension.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('masthead-extension', () => {
    let el: HTMLElement;
    let application: WtgFramework;
    const tabsInfo = ref();

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.currentTask = new WtgFrameworkTask();

        application.currentTask = {
            entityName: 'ABC123',
            showBackButton: true,
            cancelAction: {
                caption: 'Close',
                label: 'Cancel',
                visible: true,
                onInvoke: jest.fn(),
            },
            showEDocsAction: {
                visible: false,
                onInvoke: jest.fn(),
            } as any,
            documents: {
                visible: false,
            } as any,
            showLogsAction: {
                visible: false,
            } as any,
            showMessagesAction: {
                visible: false,
            } as any,
            showNotesAction: {
                visible: false,
            } as any,
            showWorkflowActions: {
                visible: false,
            } as any,
            genericActions: [],
            others: {
                visible: false,
            } as any,
        } as any;
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is MastheadExtension', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('MastheadExtension');
    });

    describe('when rendering a task', () => {
        describe('given a task title', () => {
            beforeEach(() => {
                application.currentTask!.showTaskTitle = true;
            });

            afterEach(() => {
                application.menu = [];
            });

            test('it will render a button and entity caption if requested & required', async () => {
                const wrapper = await mountComponentAsync();
                let cancelButton = wrapper.findComponent({ name: 'WtgIconButton' });
                let caption = wrapper.findComponent({ name: 'WtgLabel' });
                expect(cancelButton.exists()).toBe(true);
                expect(cancelButton.props().icon).toContain('s-icon-arrow-left');
                expect(caption.exists()).toBe(true);
                expect(caption.text()).toBe('ABC123');
                application.currentTask!.showTaskTitle = false;
                await nextTick();

                cancelButton = wrapper.findComponent({ name: 'WtgIconButton' });
                caption = wrapper.findComponent({ name: 'WtgLabel' });
                expect(cancelButton.exists()).toBe(false);
                expect(caption.exists()).toBe(false);

                application.currentTask!.showTaskTitle = true;
                application.menu.push({ id: 'HomeItem', caption: 'Home Item', home: true, active: true } as any);
                await nextTick();

                cancelButton = wrapper.findComponent({ name: 'WtgIconButton' });
                caption = wrapper.findComponent({ name: 'WtgLabel' });
                expect(cancelButton.exists()).toBe(false);
                expect(caption.exists()).toBe(false);
            });

            test('it renders back button with aria-label attribute', async () => {
                const wrapper = await mountComponentAsync();
                const backButton = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(backButton.attributes('aria-label')).toBe('Back');
            });

            test('it will call the cancel action onInvoke when clicked', async () => {
                const wrapper = await mountComponentAsync();
                const cancelButton = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(cancelButton.exists()).toBe(true);
                await cancelButton.trigger('click');

                expect(application.currentTask!.cancelAction.onInvoke).toHaveBeenCalledTimes(1);
            });

            test('it will hide the back button when showBackButton is false', async () => {
                application.currentTask!.showBackButton = false;
                const wrapper = await mountComponentAsync();
                const backButton = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(backButton.exists()).toBe(false);
            });

            test('it does not render a back button when application hideBackButton is true', async () => {
                application.hideBackButton = true;
                const wrapper = await mountComponentAsync();
                const backButton = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(backButton.exists()).toBe(false);
            });
        });

        describe('given the current task has visible actions', () => {
            beforeEach(() => {
                application.currentTask!.showEDocsAction.visible = true;
            });

            test('it will render the task actions component', async () => {
                const wrapper = await mountComponentAsync();
                let entityActions = wrapper.findComponent({ name: 'EntityActions' });
                expect(entityActions.exists()).toBe(true);
                application.currentTask!.showEDocsAction.visible = false;
                await nextTick();

                entityActions = wrapper.findComponent({ name: 'EntityActions' });
                expect(entityActions.exists()).toBe(false);
            });
        });

        describe('given the current task has generic actions', () => {
            beforeEach(() => {
                application.currentTask!.genericActions = [
                    {
                        id: 'someguid1',
                        caption: 'Task Action 1',
                        placement: 'taskactions',
                        onInvoke: jest.fn(),
                    },
                ];
            });

            test('it will render the task actions component', async () => {
                const wrapper = await mountComponentAsync();
                let entityActions = wrapper.findComponent({ name: 'EntityActions' });
                expect(entityActions.exists()).toBe(true);

                application.currentTask!.genericActions = [];
                await nextTick();

                entityActions = wrapper.findComponent({ name: 'EntityActions' });
                expect(entityActions.exists()).toBe(false);
            });
        });

        describe('given the application has tab info', () => {
            beforeEach(() => {
                tabsInfo.value = {
                    tabs: [
                        {
                            caption: 'Tab 1',
                        },
                        {
                            caption: 'Tab 2',
                        },
                    ],
                    current: 0,
                    visible: false,
                };
            });

            test('it will render the tabs', async () => {
                const wrapper = await mountComponentAsync();
                let applicationTabs = wrapper.findAllComponents({ name: 'WtgTab' });
                expect(applicationTabs.length).toBe(2);
                expect(applicationTabs.at(0)?.text()).toBe('Tab 1');
                expect(applicationTabs.at(1)?.text()).toBe('Tab 2');
                tabsInfo.value.tabs = [];
                await nextTick();

                applicationTabs = wrapper.findAllComponents({ name: 'WtgTab' });
                expect(applicationTabs.length).toBe(0);
            });
        });

        describe('given the current task has currentStatus', () => {
            beforeEach(() => {
                application.currentTask!.currentStatus = {
                    code: 'TEST',
                    label: 'Test Label',
                    sentiment: 'info',
                    variant: 'fill',
                };

                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Hidden;

                application.currentTask!.showTaskTitle = true;
            });

            test('it will not render the task current status when status label is falsy', async () => {
                application.currentTask!.currentStatus!.label = '';
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.exists()).toBe(false);
            });

            test('it will render the task current status by status component', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.exists()).toBe(true);
                expect(statusWrapper.vm.$props.label).toBe('Test Label');
                expect(statusWrapper.vm.$props.sentiment).toBe('info');
                expect(statusWrapper.vm.$props.variant).toBe('fill');
            });

            test('it will use info as default sentiment', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
                application.currentTask!.currentStatus!.sentiment = '';
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.vm.$props.sentiment).toBe('info');
            });

            test('it will use info as default sentiment for items', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
                application.currentTask!.statusItems = [
                    { code: 'Test', label: 'Test description', sentiment: undefined, variant: 'fill' },
                ];
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.vm.$props.items![0].sentiment).toBe('info');
            });

            test('it will use fill as default variant', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
                application.currentTask!.currentStatus!.variant = '';
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.vm.$props.variant).toBe('fill');
            });

            test('it will use fill as default variant for items', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
                application.currentTask!.statusItems = [
                    { code: 'Test', label: 'Test description', sentiment: 'critical', variant: undefined },
                ];
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.vm.$props.variant).toBe('fill');
            });

            test('it will not render the task current status when currentStatus is undefined', async () => {
                const wrapper = await mountComponentAsync();
                application.currentTask!.currentStatus = undefined;
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.exists()).toBe(false);
            });

            test('it will not render the task current status when status display mode is Hidden', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Hidden;
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.exists()).toBe(false);
            });

            test('it will render the task current status as editable when status display mode is Editable', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Editable;
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.exists()).toBe(true);
                expect(statusWrapper.vm.$props.editable).toBe(true);
            });

            test('it will render the task current status as ReadOnly when status display mode is ReadOnly', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
                const wrapper = await mountComponentAsync();
                const statusWrapper = wrapper.findComponent(WtgStatus);
                expect(statusWrapper.exists()).toBe(true);
                expect(statusWrapper.vm.$props.editable).toBe(false);
            });
        });

        describe('given the current task has others', () => {
            beforeEach(() => {
                application.currentTask!.others = {
                    visible: true,
                    hasNext: true,
                    hasPrevious: true,
                    indexCountText: '2 of 3',
                    onNext: jest.fn(),
                    onPrevious: jest.fn(),
                };
            });

            test('it will render the entity navigation', async () => {
                const wrapper = await mountComponentAsync();
                const entityNavigation = wrapper.findComponent({ name: 'EntityNavigation' });
                expect(entityNavigation.exists()).toBe(true);
            });

            test('it will not render the entity navigation when visible is false', async () => {
                application.currentTask!.others.visible = false;
                const wrapper = await mountComponentAsync();
                const entityNavigation = wrapper.findComponent({ name: 'EntityNavigation' });
                expect(entityNavigation.exists()).toBe(false);
            });
        });
    });

    test('it does not render the title actions when mobile framework is active and on mobile screen size', async () => {
        application.currentTask!.showTaskTitle = true;
        const wrapper = await mountComponentAsync({ props: { showCloseButton: true } });
        expect(wrapper.find("[data-testid='title-actions']").exists()).toBe(true);
        wtgUi.framework = 'mobile';
        wtgUi.breakpoint.smAndUp = false;
        await nextTick();
        expect(wrapper.find("[data-testid='title-actions']").exists()).toBe(false);
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(MastheadExtension) } } = {}) {
        const wrapper = mount(VApp, {
            provide: {
                [tabsInfoInjectionKey]: tabsInfo,
            },
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(MastheadExtension);
    }
});
