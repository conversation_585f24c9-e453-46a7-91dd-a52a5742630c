export interface Items {
    favorites: WtgRecentItem[];
    recents: WtgRecentItem[];
}

export interface WtgRecentItem {
    id: string;
    href?: string;
    caption: string;
    favorite: boolean;
    actions?: WtgRecentItemAction[];
    onAddToFavorites: () => void;
    onRemoveFromFavorites: () => void;
}

export interface WtgRecentItemAction {
    id: string;
    caption: string;
    href: string;
}
