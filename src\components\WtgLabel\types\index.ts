export const createTypographyClasses = () => [
    {
        text: 'display-lg-default',
        value: 'text-display-lg-default',
    },
    {
        text: 'display-lg-default-num',
        value: 'text-display-lg-default-num',
    },
    {
        text: 'display-md-default',
        value: 'text-display-md-default',
    },
    {
        text: 'display-md-default-num',
        value: 'text-display-md-default-num',
    },
    {
        text: 'display-sm-default',
        value: 'text-display-sm-default',
    },
    {
        text: 'display-sm-default-num',
        value: 'text-display-sm-default-num',
    },
    {
        text: 'title-lg-default',
        value: 'text-title-lg-default',
    },
    {
        text: 'title-lg-default-num',
        value: 'text-title-lg-default-num',
    },
    {
        text: 'title-md-default',
        value: 'text-title-md-default',
    },
    {
        text: 'title-md-default-num',
        value: 'text-title-md-default-num',
    },
    {
        text: 'title-sm-default',
        value: 'text-title-sm-default',
    },
    {
        text: 'title-sm-default-num',
        value: 'text-title-sm-default-num',
    },
    {
        text: 'text-md-default',
        value: 'text-md-default',
    },
    {
        text: 'text-md-default-link',
        value: 'text-md-default-link',
    },
    {
        text: 'text-md-default-num',
        value: 'text-md-default-num',
    },
    {
        text: 'text-md-default-num-link',
        value: 'text-md-default-num-link',
    },
    {
        text: 'text-md-strong',
        value: 'text-md-strong',
    },
    {
        text: 'text-md-strong-link',
        value: 'text-md-strong-link',
    },
    {
        text: 'text-md-strong-num',
        value: 'text-md-strong-num',
    },
    {
        text: 'text-md-strong-num-link',
        value: 'text-md-strong-num-link',
    },
    {
        text: 'text-sm-default',
        value: 'text-sm-default',
    },
    {
        text: 'text-sm-default-link',
        value: 'text-sm-default-link',
    },
    {
        text: 'text-sm-default-num',
        value: 'text-sm-default-num',
    },
    {
        text: 'text-sm-default-num-link',
        value: 'text-sm-default-num-link',
    },
    {
        text: 'text-sm-strong',
        value: 'text-sm-strong',
    },
    {
        text: 'text-sm-strong-link',
        value: 'text-sm-strong-link',
    },
    {
        text: 'text-sm-strong-num',
        value: 'text-sm-strong-num',
    },
    {
        text: 'text-sm-strong-num-link',
        value: 'text-sm-strong-num-link',
    },
    {
        text: 'text-xs-default',
        value: 'text-xs-default',
    },
    {
        text: 'text-xs-default-link',
        value: 'text-xs-default-link',
    },
    {
        text: 'text-xs-default-num',
        value: 'text-xs-default-num',
    },
    {
        text: 'text-xs-default-num-link',
        value: 'text-xs-default-num-link',
    },
    {
        text: 'text-xs-strong',
        value: 'text-xs-strong',
    },
    {
        text: 'text-xs-strong-link',
        value: 'text-xs-strong-link',
    },
    {
        text: 'text-xs-strong-num',
        value: 'text-xs-strong-num',
    },
    {
        text: 'text-xs-strong-num-link',
        value: 'text-xs-strong-num-link',
    },
];
