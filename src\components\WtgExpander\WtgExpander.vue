<template>
    <VExpansionPanels
        v-model="internalValue"
        :disabled="disabled"
        :class="computedClass"
        flat
        tile
        @update:model-value="onUpdateModelValue"
    >
        <slot />
    </VExpansionPanels>
</template>

<script setup lang="ts">
import { useDisplay } from '@composables/display';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { computed, PropType, ref, watchEffect } from 'vue';
import { VExpansionPanels } from 'vuetify/components/VExpansionPanel';

//
// Properties
//
const props = defineProps({
    /**
     * Disables the expander, making it unclickable and visually indicating its disabled state.
     */
    disabled: {
        type: Boolean,
        default: false,
    },

    /**
     * If true, the expander will use a mobile-friendly layout.
     * If undefined, the layout will adapt based on the screen size.
     */
    mobile: {
        type: Boolean as PropType<boolean | undefined>,
        default: undefined,
    },

    /**
     * The current value of the expander, used for two-way binding.
     * Can be a single value or an array of values for multiple expanded panels.
     */
    modelValue: {
        type: [Number, String, Array] as PropType<string | string[] | number | number[]>,
        default: undefined,
    },

    ...makeLayoutGridColumnProps(),

    /**
     * The value of the expander.
     * @deprecated Use `modelValue` instead.
     */
    value: {
        type: [String, Number, Array] as PropType<string | string[] | number | number[]>,
        default: undefined,
    },
});

//
// Emits
//
const emits = defineEmits<{
    input: [value: string | string[] | number | number[]];
    'model-compat:input': [value: string | string[] | number | number[]];
    'update:modelValue': [value: string | string[] | number | number[]];
}>();

//
// State
//
const internalValue = ref<string | string[] | number | number[]>('');

//
// Composables
//
const { onMobile } = useDisplay();

useLayoutGridColumn(props);

//
// Computed
//
const computedClass = computed(() => [
    'wtg-expander',
    {
        'wtg-expander--mobile': props.mobile !== undefined ? props.mobile : onMobile.value,
    },
]);

//
// Watchers
//
watchEffect(() => {
    internalValue.value = props.modelValue ?? props.value ?? '';
});

//
// Event Handlers
//
function onUpdateModelValue(value: any) {
    emits('input', value);
    emits('model-compat:input', value);
    emits('update:modelValue', value);
}
</script>

<style lang="scss">
.wtg-expander {
    border-radius: var(--s-radius-m);

    .v-expansion-panel-title__overlay {
        opacity: 0 !important;
    }

    .wtg-expander-panel {
        border-radius: var(--s-radius-m);
        border: 1px solid var(--s-neutral-border-weak-default);
        box-shadow: var(--s-elevation-100);
        box-shadow: none;
    }

    &--mobile {
        border-radius: 0px;

        .wtg-expander-panel {
            border-radius: 0px;
            border: none;
            border-top: 1px solid var(--s-neutral-border-weak-default);
            border-bottom: 1px solid var(--s-neutral-border-weak-default);
            box-shadow: none;
        }
    }
}
</style>
