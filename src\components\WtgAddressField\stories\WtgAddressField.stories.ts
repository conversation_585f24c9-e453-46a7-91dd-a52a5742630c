import WtgCol from '@components/WtgCol';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgRow from '@components/WtgRow';
import { inputArgTypes } from '@composables/input';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { VNodeProps } from 'vue';
import WtgAddressField from '..';
import AddressesItemProvider from '../__tests__/AddressesItemProvider';

type Story = StoryObj<typeof WtgAddressField>;
const meta: Meta<typeof WtgAddressField> = {
    title: 'Components/Address Field',
    component: WtgAddressField,
    parameters: {
        docs: {
            description: {
                component:
                    'The Address Field is a TextField that allows you to search for addresses from the related page, entity, datagrid or portal',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=715-50148&mode=design&t=8skww1YZjQmpwYkT-0',
        },
        layout: 'centered',
        controls: {
            sort: 'alpha',
            exclude: ['loading'],
        },
    },
    render: (args) => ({
        components: { WtgAddressField },
        setup: () => ({ args }),
        methods: { changeAction: action('change') },
        template: `<WtgAddressField
                        v-bind="args"
                        @change="changeAction">
                    </WtgAddressField>`,
    }),
    argTypes: { ...inputArgTypes },
    decorators: [
        () => ({
            template: `
                <div style="width: 337px">
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

const addressProvider = new AddressesItemProvider();

export const Default: Story = {
    args: {
        itemProvider: addressProvider,
        label: 'Organisation Address',
        showSelectedAddress: true,
    },
};

export const Info: Story = {
    args: {
        itemProvider: addressProvider,
        label: 'Organisation Address',
        showSelectedAddress: true,
        info: 'Some helpful information',
    },
};

export const Sentiments: Story = {
    args: {
        itemProvider: addressProvider,
        showSelectedAddress: true,
    },
    render: (args) => ({
        components: { WtgAddressField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
        <wtg-row>
        <wtg-col style="max-width: fit-content; gap: 8px;" class="d-flex flex-column col-md-3">
            <wtg-layout-grid>
                <WtgAddressField
                    label="Default"
                    v-bind="args"
                    @update:model-value="updateModel"
                    @input="inputAction"
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgAddressField>
                <WtgAddressField
                    label="Success"
                    sentiment="success"
                    messages="Sample success message"
                    v-bind="args"
                    @update:model-value="updateModel"
                    @input="inputAction"
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgAddressField>
                <WtgAddressField
                    label="Warning"
                    sentiment="warning"
                    messages="Sample warning message"
                    v-bind="args"
                    @update:model-value="updateModel"
                    @input="inputAction"
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgAddressField>
                <WtgAddressField
                    label="Error"
                    sentiment="critical"
                    messages="Sample error message"
                    v-bind="args"
                    @update:model-value="updateModel"
                    @input="inputAction"
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgAddressField>
                <WtgAddressField
                    label="Info"
                    sentiment="info"
                    messages="Sample info message"
                    v-bind="args"
                    @update:model-value="updateModel"
                    @input="inputAction"
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgAddressField>
            </wtg-layout-grid>
        </wtg-col>
    </wtg-row>`,
    }),
};

export const ReadOnly: Story = {
    args: {
        itemProvider: addressProvider,
        label: 'Read only',
        readonly: true,
        showSelectedAddress: true,
    },
    render: (args) => ({
        components: { WtgAddressField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
        <WtgAddressField
                    v-bind="args"
                    @update:model-value="updateModel"
                    @input="inputAction"
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgAddressField>`,
    }),
};

export const Disabled: Story = {
    args: {
        itemProvider: addressProvider,
        label: 'Disabled',
        disabled: true,
        showSelectedAddress: true,
    },
    render: (args) => ({
        components: { WtgAddressField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
        <WtgAddressField v-bind="args"
                    @update:model-value="updateModel"
                    @input="inputAction"
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgAddressField>`,
    }),
};

export const Sandbox: StoryObj = {
    args: {
        itemProvider: addressProvider,
        label: 'Open by default',
        numClick: 0,
        showSelectedAddress: true,
    },
    render: (args) => ({
        components: { WtgAddressField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => {
            (args as VNodeProps).onVnodeMounted = () => {
                if ((args as StoryObj & { numClick: number }).numClick === 0) {
                    (document.getElementsByClassName('wtg-input').item(0) as HTMLElement).click();
                    ++(args as StoryObj & { numClick: number }).numClick;
                }
            };
            return {
                args,
            };
        },
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
        <WtgAddressField v-bind="args"
                    @update:model-value="updateModel"
                    @input="inputAction"
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgAddressField>`,
    }),
};
