import WtgApp from '@components/WtgApp';
import WtgBreadcrumbs from '@components/WtgBreadcrumbs';
import WtgIconButton from '@components/WtgIconButton';
import WtgList, { WtgListItem } from '@components/WtgList';
import WtgMain from '@components/WtgMain';
import WtgMasthead from '@components/WtgMasthead';
import WtgNavigation from '@components/WtgNavigation';
import WtgSpacer from '@components/WtgSpacer';
import WtgThemeProvider from '@components/WtgThemeProvider';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { computed, ref } from 'vue';

type Story = StoryObj<typeof WtgApp>;
const meta: Meta<typeof WtgApp> = {
    title: 'Utilities/App',
    component: WtgApp,
    parameters: {
        docs: {
            description: {
                component:
                    'App is the outermost component of an application. It enables the layout system that allows you to easily create complex website designs.',
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: { WtgApp, WtgNavigation },
        setup: () => ({ args }),
        methods: {
            changeAction: action('change'),
        },
        template: '<WtgApp><wtg-navigation v-bind="args"/></WtgApp>',
    }),
    decorators: [
        () => ({
            components: { WtgApp },
            template: '<WtgApp class="content-embedded-app"><story /></WtgApp>',
        }),
    ],
};

export default meta;

const menuItems = [
    {
        icon: 's-icon-home',
        to: '/home',
        caption: 'Home',
    },
    {
        icon: 's-icon-package',
        to: '/orders',
        caption: 'Orders',
    },
    {
        icon: 's-icon-supplier-bookings',
        to: '/Supplier Bookings',
        caption: 'Supplier Bookings',
    },
    {
        icon: 's-icon-person-delivery-a',
        to: 'containers',
        caption: 'Containers',
    },
];

const breadcrumbItems = computed(() => {
    return [{ caption: 'Portal name', link: 'javascript:void(0);' }];
});

const navigationContentTemplate = `<WtgList aria-label="navigation">
            <WtgListItem
                v-for="menuItem in menuItems"
                :key="menuItem.to"
                :leading-icon="menuItem.icon"
                :to="menuItem.to"
            >
                {{ menuItem.caption }}
            </WtgListItem>
        </WtgList>`;

const MastheadTemplate = `<WtgNavigation v-model="navDrawer.visible">
        ${navigationContentTemplate}
        </WtgNavigation>
        <WtgMasthead>
            <template #prepend>
                <WtgIconButton
                    v-if="isTabletOrMobile"
                    aria-label="Toggle Navigation Drawer"
                    variant="ghost"
                    icon="s-icon-menu-hamburger"
                    @click="navDrawer.visible = true"
                />
            </template>
            <WtgBreadcrumbs :items="breadcrumbItems" />
            <WtgSpacer />
            <template #append>
                <WtgIconButton aria-label="Favorites" icon="s-icon-star-empty"></WtgIconButton>
                <WtgIconButton aria-label="Search" icon="s-icon-search"></WtgIconButton>
                <WtgIconButton aria-label="More" icon="s-icon-menu-kebab"></WtgIconButton>
            </template>
        </WtgMasthead>`;

const MobileMastheadTemplate = `<WtgNavigation v-model="navDrawerVisible" location="right">
        ${navigationContentTemplate}
        </WtgNavigation>
        <WtgMasthead>
            <span class="text-sm-default pl-4"> Portal name </span>
            <WtgSpacer />
            <template #append>
                <WtgIconButton aria-label="Notifications" icon="s-icon-bell" variant="ghost"></WtgIconButton>
                <WtgIconButton aria-label="Search" icon="s-icon-search" variant="ghost"></WtgIconButton>
                <WtgIconButton aria-label="Navigation" icon="s-icon-menu-hamburger" variant="ghost" @click="navDrawerVisible = true"></WtgIconButton>
            </template>
        </WtgMasthead>`;

export const Default: Story = {
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
        docs: {
            source: {
                code: `<template>
    <WtgApp>
      ${MastheadTemplate}
        <WtgMain>
            <router-view />
        </WtgMain>
    </WtgApp>
</template>

<script setup lang="ts">
import { WtgApp, WtgMain, WtgBreadcrumbs, WtgIconButton, WtgMasthead, WtgList, WtgListItem, WtgNavigation, WtgSpacer, useFramework } from '@wtg/wtg-components';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const breadcrumbItems = computed(() => {
    const items = [{ caption: 'Sample Portal', link: route.name ? '/' : undefined }];
    if (route.name) {
        items.push({ caption: route.name as string, link: route.path });
    }
    return items;
});

const menuItems = [
    {
        icon: 's-icon-home',
        to: '/home',
        caption: 'Home',
    },
    {
        icon: 's-icon-package',
        to: '/orders',
        caption: 'Orders',
    },
    {
        icon: 's-icon-supplier-bookings',
        to: '/Supplier Bookings',
        caption: 'Supplier Bookings',
    },
    {
        icon: 's-icon-person-delivery-a',
        to: 'containers',
        caption: 'Containers',
    },
];

const { isTabletOrMobile } = useFramework();

const navDrawer = ref({
    isRailActive: false,
    visible: undefined as boolean | undefined,
});

</script>`,
            },
        },
        layout: 'fullscreen',
    },
    tags: ['!dev'],
    render: (args) => ({
        components: {
            WtgApp,
            WtgMain,
            WtgBreadcrumbs,
            WtgIconButton,
            WtgList,
            WtgListItem,
            WtgMasthead,
            WtgNavigation,
            WtgSpacer,
        },
        setup: () => {
            const navDrawer = ref({
                isRailActive: false,
                visible: undefined as boolean | undefined,
            });

            return {
                args,
                navDrawer,
                menuItems,
                breadcrumbItems,
            };
        },
        template: MastheadTemplate,
    }),
};

export const Mobile: Story = {
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
        docs: {
            source: {
                code: `<template>
    <WtgApp>
      ${MobileMastheadTemplate}
        <WtgMain>
            <router-view />
        </WtgMain>
    </WtgApp>
</template>

<script setup lang="ts">
import { WtgApp, WtgMain, WtgIconButton, WtgMasthead, WtgList, WtgListItem, WtgNavigation, WtgSpacer } from '@wtg/wtg-components';
import { computed, ref } from 'vue';

const navDrawerVisible = ref(false);

const menuItems = [
    {
        icon: 's-icon-home',
        to: '/home',
        caption: 'Home',
    },
    {
        icon: 's-icon-package',
        to: '/orders',
        caption: 'Orders',
    },
    {
        icon: 's-icon-supplier-bookings',
        to: '/Supplier Bookings',
        caption: 'Supplier Bookings',
    },
    {
        icon: 's-icon-person-delivery-a',
        to: 'containers',
        caption: 'Containers',
    },
];

</script>`,
            },
        },
        layout: 'fullscreen',
    },
    tags: ['!dev'],
    render: (args) => ({
        components: {
            WtgApp,
            WtgMain,
            WtgIconButton,
            WtgList,
            WtgListItem,
            WtgMasthead,
            WtgNavigation,
            WtgSpacer,
            WtgThemeProvider,
        },
        setup: () => {
            const navDrawerVisible = ref(false);

            return {
                args,
                navDrawerVisible,
                menuItems,
            };
        },
        template: `<WtgThemeProvider framework="mobile">${MobileMastheadTemplate}</WtgThemeProvider>`,
    }),
};
