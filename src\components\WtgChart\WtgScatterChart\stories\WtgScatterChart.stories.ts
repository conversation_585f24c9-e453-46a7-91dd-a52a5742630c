import { <PERSON>a, StoryObj } from '@storybook/vue3';
import { WtgScatterChart } from '../..';

type Story = StoryObj<typeof WtgScatterChart>;
const meta: Meta<typeof WtgScatterChart> = {
    title: 'Data viz/Scatter Chart',
    component: WtgScatterChart,
    parameters: {
        docs: {
            description: {
                component:
                    'Scatter charts are based on basic line charts with the x axis changed to a linear axis. To use a scatter chart, data must be passed as objects containing X and Y properties.',
            },
        },
    },
    render: (args) => ({
        components: { WtgScatterChart },
        setup: () => ({ args }),
        template: `<wtg-scatter-chart v-bind="args"/>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        data: {
            datasets: [
                {
                    label: 'Dataset 1',
                    data: [
                        {
                            x: -10,
                            y: 0,
                        },
                        {
                            x: -4,
                            y: 9,
                        },
                        {
                            x: 10,
                            y: 3,
                        },
                        {
                            x: 0.5,
                            y: 5.5,
                        },
                    ],
                    backgroundColor: '#F4433680',
                },
                {
                    label: 'Dataset 2',
                    data: [
                        {
                            x: -5,
                            y: 5,
                        },
                        {
                            x: 4,
                            y: -3,
                        },
                        {
                            x: 6,
                            y: 2,
                        },
                        {
                            x: 0.5,
                            y: 9.5,
                        },
                    ],
                    backgroundColor: '#4CAF5080',
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
            },
        },
        loading: false,
    },
};
