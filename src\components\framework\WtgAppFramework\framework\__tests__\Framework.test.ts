import { WtgFramework } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../WtgUi';
import Framework from '../Framework.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

window.URL.createObjectURL = jest.fn().mockReturnValue('dummyBlob');

describe('framework', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.hideAppBar = false;
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is Framework', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('Framework');
    });

    test('it renders the masthead', async () => {
        const wrapper = await mountComponentAsync();
        const masthead = wrapper.findComponent({ name: 'Masthead' });
        expect(masthead.exists()).toBe(true);
    });

    test('it displays a fixed progress bar when the loading property is set to true', async () => {
        const wrapper = await mountComponentAsync();

        const progress = wrapper.findComponent({ name: 'VProgressLinear' });

        application.loading = true;
        await nextTick();
        expect(progress.isVisible()).toBe(true);

        application.loading = false;
        await nextTick();
        expect(progress.isVisible()).toBe(false);
    });

    test('it ensures the progress bar displays on top of all other framework content', async () => {
        const wrapper = await mountComponentAsync();

        const progress = wrapper.findComponent({ name: 'VProgressLinear' });
        const computedStyle = window.getComputedStyle(progress.element);
        expect(computedStyle.zIndex).toBe('2000');
    });

    test('it displays the progress bar below the masthead on mobile', async () => {
        wtgUi.breakpoint.smAndUp = false;
        const wrapper = await mountComponentAsync();
        application.loading = true;
        await nextTick();

        const progressBar = wrapper.findComponent({ name: 'VProgressLinear' });
        expect(window.getComputedStyle(progressBar.element).top).toBe('62px');
    });

    test('it displays the progress bar at the top of the masthead on desktop', async () => {
        wtgUi.breakpoint.smAndUp = true;
        const wrapper = await mountComponentAsync();
        application.loading = true;
        await nextTick();

        const progressBar = wrapper.findComponent({ name: 'VProgressLinear' });
        expect(window.getComputedStyle(progressBar.element).top).toBe('0px');
    });

    test('it displays the progress bar at the top of the masthead on mobile when the app bar is hidden', async () => {
        wtgUi.breakpoint.smAndUp = false;
        const wrapper = await mountComponentAsync();
        application.loading = true;
        application.hideAppBar = true;
        await nextTick();

        const progressBar = wrapper.findComponent({ name: 'VProgressLinear' });
        expect(window.getComputedStyle(progressBar.element).top).toBe('0px');
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(Framework) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(Framework);
    }
});
