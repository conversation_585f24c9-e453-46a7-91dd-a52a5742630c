import { WtgTabsWindow } from '@components/WtgTabs';
import WtgTabs from '@components/WtgTabs/WtgTabs.vue';
import { useAppLevelTabs } from '@composables/application';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { computed, defineComponent, h, onBeforeUnmount, onMounted, provide, reactive, VNode, watch } from 'vue';

export default defineComponent({
    name: 'WtgAppFrameworkTabs',
    props: {
        app: {
            type: Boolean,
            default: false,
        },
        backgroundColor: {
            type: String,
            default: undefined,
        },
        color: {
            type: String,
            default: undefined,
        },
        fitToHeight: {
            type: Boolean,
            default: false,
        },
        grow: {
            type: Boolean,
            default: false,
        },
        right: {
            type: Boolean,
            default: false,
        },
        value: {
            type: Number,
            default: 0,
        },
        vertical: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    emits: ['change'],
    setup(props, { emit, slots }) {
        useLayoutGridColumn(props);
        const tabInfo = reactive({
            current: props.value,
            tabs: [],
            visible: props.app,
        });

        const appLevelTabs = useAppLevelTabs();

        provide('appFrameworkTabsProvide', { tabs: tabInfo.tabs });

        onMounted(() => {
            if (appLevelTabs && props.app) {
                appLevelTabs.value = tabInfo;
            }
        });

        onBeforeUnmount(() => {
            if (appLevelTabs && props.app) {
                appLevelTabs.value = undefined;
            }
        });

        watch(
            () => props.app,
            () => {
                tabInfo.visible = props.app;
            }
        );

        watch(
            () => tabInfo.current,
            () => {
                emit('change', tabInfo.current);
            }
        );

        watch(
            () => props.value,
            () => {
                tabInfo.current = props.value;
            },
            { immediate: true }
        );

        const appFrameworkTabsClasses = computed(() => {
            const classes: string[] = ['wtg-application-tabs'];
            if (props.fitToHeight) {
                classes.push('wtg-fit-to-height');
            }
            return classes;
        });

        return () => {
            const tabs: VNode[] = [];
            const items: VNode[] = [];

            if (slots.default) {
                const children = slots.default();
                children.forEach((child) => {
                    if (child.props?.['data-tab']) {
                        tabs.push(child);
                    } else {
                        items.push(child);
                    }
                });
            }

            const tabsContainer = h(
                WtgTabs,
                {
                    alignTabs: props.right ? 'end' : 'start',
                    modelValue: tabInfo.current,
                    ['onUpdate:modelValue']: (value: number | string): void => {
                        tabInfo.current = value as number;
                    },
                    class: {
                        'd-none': props.app,
                    },
                },
                { default: () => tabs }
            );

            const itemsContainer = h(
                WtgTabsWindow,
                {
                    modelValue: tabInfo.current,
                    ['onUpdate:modelValue']: (value: number | string): void => {
                        tabInfo.current = value as number;
                    },
                },
                {
                    default: () => items,
                }
            );

            return h('div', { class: appFrameworkTabsClasses.value }, [tabsContainer, itemsContainer]);
        };
    },
});
