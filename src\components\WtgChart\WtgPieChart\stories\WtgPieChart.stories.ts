import { Meta, StoryObj } from '@storybook/vue3';
import { Wtg<PERSON>ie<PERSON><PERSON> } from '../..';

type Story = StoryObj<typeof WtgPieChart>;
const meta: Meta<typeof WtgPieChart> = {
    title: 'Data viz/Pie Chart',
    component: Wtg<PERSON>ie<PERSON>hart,
    parameters: {
        docs: {
            description: {
                component:
                    "A pie chart shows how some total amount is divided among distinct categories as a circle (the namesake pie) divided into radial slices. Each category is associated with a single slice whose size corresponds with the category's proportion of the total.",
            },
        },
    },
    render: (args) => ({
        components: { WtgPieChart },
        setup: () => ({ args }),
        template: `<wtg-pie-chart v-bind="args"/>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        data: {
            labels: ['Red', 'Orange', 'Yellow', 'Green', 'Blue'],
            datasets: [
                {
                    label: 'Dataset 1',
                    data: [5, 7, 12, 4, 8],
                    backgroundColor: ['#F4433680', '#FF980080', '#FFEB3B80', '#4CAF5080', '#03A9F480'],
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                },
            },
        },
        loading: false,
    },
};
