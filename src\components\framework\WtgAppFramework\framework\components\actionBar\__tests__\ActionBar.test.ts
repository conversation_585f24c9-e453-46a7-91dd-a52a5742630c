import { WtgFramework, WtgFrameworkTask } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../WtgUi';
import ActionBar from '../ActionBar.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('action-bar', () => {
    let el: HTMLElement;
    let application: WtgFramework;
    let task: WtgFrameworkTask;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        task = {
            showFooter: true,
            cancelAction: {
                visible: false,
                caption: 'Cancel',
                onInvoke: jest.fn(),
            },
            saveAction: {
                visible: false,
                caption: 'Save changes',
                label: 'Save',
                onInvoke: jest.fn(),
            },
            saveCloseAction: {
                caption: 'Save and close',
                label: 'Close',
                onInvoke: jest.fn(),
            },
            genericActions: [],
        } as any;
        application.currentTask = task;
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is ActionBar', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('ActionBar');
    });

    test('it set the footer visible base on application footerVisible', async () => {
        const wrapper = await mountComponentAsync();
        let footer = wrapper.findComponent({ name: 'WtgFooter' });
        expect(footer.classes()).toContain('d-none');
        application.currentTask!.saveAction.visible = true;
        await nextTick();

        footer = wrapper.findComponent({ name: 'WtgFooter' });
        expect(footer.classes()).not.toContain('d-none');
    });

    test('it sets the correct props for the footer to be part of the app framework, the navigation drawer (and the entity drawer) take precedence over masthead and footer', async () => {
        const wrapper = await mountComponentAsync();
        const footer = wrapper.findComponent({ name: 'WtgFooter' });
        expect(footer.props('app')).toBe(true);
        expect(footer.props('order')).toBe(4);
    });

    test('it shows the desktop footer on a desktop viewport and passes the current task', async () => {
        application.currentTask!.saveAction.visible = true;
        const wrapper = await mountComponentAsync();
        const footer = wrapper.findComponent({ name: 'DesktopBar' });
        expect(footer.exists()).toBe(true);
        expect(footer.vm.$props.task).toStrictEqual(task);
    });

    test('it shows the mobile footer on a mobile viewport and passes the current task', async () => {
        wtgUi.breakpoint.smAndUp = false;
        application.currentTask!.saveAction.visible = true;
        const wrapper = await mountComponentAsync();
        const footer = wrapper.findComponent({ name: 'MobileBar' });
        expect(footer.exists()).toBe(true);
        expect(footer.vm.$props.task).toStrictEqual(task);
    });

    test('it does not render a footer if hideAppBar is true, (hideAppBar really means hideFrame)', async () => {
        application.hideAppBar = true;
        const wrapper = await mountComponentAsync();
        let WtgAppBar = wrapper.findComponent({ name: 'WtgFooter' });
        expect(WtgAppBar.exists()).toBe(false);

        application.hideAppBar = false;
        await nextTick();
        WtgAppBar = wrapper.findComponent({ name: 'WtgFooter' });
        expect(WtgAppBar.exists()).toBe(true);
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(ActionBar) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(ActionBar);
    }
});
