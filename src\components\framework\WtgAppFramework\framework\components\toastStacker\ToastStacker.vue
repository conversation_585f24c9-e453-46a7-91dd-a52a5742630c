<template>
    <WtgToastStacker v-model="application.toasts" @update:model-value="updateToasts" />
</template>

<script setup lang="ts">
import { useApplication } from '@composables/application';
import WtgToastStacker from '../../../../../WtgToast/WtgToastStacker/WtgToastStacker.vue';
import { Toast } from '../../../../../WtgToast/types';

const application = useApplication();

function updateToasts(updatedToasts: Toast[]) {
    application.toasts = updatedToasts;
}
</script>
