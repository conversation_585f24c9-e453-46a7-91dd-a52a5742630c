export const CheckboxSandboxTemplate = `
<div style="display: flex; gap: 8px; align-items: baseline; flex-wrap: wrap;">
    <WtgCheckbox label="Default" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Readonly" :readonly="true" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Disabled" :disabled="true" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Required" :required="true" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Restricted" :restricted="true" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Success" sentiment="success" messages="Sample success message" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Success and Disabled" :disabled="true" sentiment="success" messages="Sample success message" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Success and Readonly" :readonly="true" sentiment="success" messages="Sample success message" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Warning" sentiment="warning" messages="Sample warning message" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Error" sentiment="critical" messages="Sample error message" @update:model-value="updateModel"></WtgCheckbox>
</div>
<div style="display: flex; gap: 8px; align-items: baseline; flex-wrap: wrap;">
    <WtgCheckbox label="Selected" :model-value="true" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Readonly" :model-value="true" :readonly="true" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Disabled" :model-value="true" :disabled="true" @update:model-value="updateModel"></WtgCheckbox>
	<WtgCheckbox label="Required" :model-value="true" :required="true" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Restricted" :model-value="true" :restricted="true" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Success" :model-value="true" sentiment="success" messages="Sample success message" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Success and Disabled" :model-value="true" :disabled="true" sentiment="success" messages="Sample success message" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Success and Readonly" :model-value="true" :readonly="true" sentiment="success" messages="Sample success message" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Warning" :model-value="true" sentiment="warning" messages="Sample warning message" @update:model-value="updateModel"></WtgCheckbox>
    <WtgCheckbox label="Error" :model-value="true" sentiment="critical" messages="Sample error message" @update:model-value="updateModel"></WtgCheckbox>
</div>
<div style="display: flex; gap: 8px; align-items: baseline; flex-wrap: wrap;">
    <WtgCheckbox :indeterminate="true" label="Indeterminate" @update:model-value="updateModel" style="margin-right:2em;"></WtgCheckbox>
    <WtgCheckbox :indeterminate="true" label="Readonly" :readonly="true" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate" style="margin-right:2em;"></WtgCheckbox>
    <WtgCheckbox :indeterminate="true" label="Disabled" :disabled="true" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate" style="margin-right:2em;"></WtgCheckbox>
	<WtgCheckbox :indeterminate="true" label="Required" :required="true" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate" style="margin-right:2em;"></WtgCheckbox>
    <WtgCheckbox :indeterminate="true" label="Restricted" :restricted="true" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate" style="margin-right:2em;"></WtgCheckbox>
    <WtgCheckbox :indeterminate="true" label="Success" sentiment="success" messages="Sample success message" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate"></WtgCheckbox>
    <WtgCheckbox :indeterminate="true" label="Success and Disabled" :disabled="true" sentiment="success" messages="Sample success message" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate"></WtgCheckbox>
    <WtgCheckbox :indeterminate="true" label="Success and Readonly" :readonly="true" sentiment="success" messages="Sample success message" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate"></WtgCheckbox>
    <WtgCheckbox :indeterminate="true" label="Warning" sentiment="warning" messages="Sample warning message" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate"></WtgCheckbox>
    <WtgCheckbox :indeterminate="true" label="Error" sentiment="critical" messages="Sample error message" @update:model-value="updateModel" @update:indeterminate="updateIndeterminate"></WtgCheckbox>
</div>
<WtgPanel class="mt-6" max-width="800" layout="flex" flex-justify="justify-center" caption="Ensure compatibility with previous version of our libraries by applying 'flex: 0 1 auto'">
    <WtgCheckbox label="This checkbox should be centered on a flex parent"></WtgCheckbox>
</WtgPanel>
<WtgPanel class="mt-6" max-width="800" caption="Ensure compatibility with previous version of our libraries by applying 'display: flex'">
    <WtgCheckbox label="These checkboxes should be vertically aligned"></WtgCheckbox>
    <WtgCheckbox label="These checkboxes should be vertically aligned"></WtgCheckbox>
    <WtgCheckbox label="These checkboxes should be vertically aligned"></WtgCheckbox>
</WtgPanel>

<WtgPanel class="mt-6" max-width="800">
    <WtgCheckboxGroup label="Checkbox group" description="Assistive text">
        <WtgCheckbox label="Option One" value="one"></WtgCheckbox>
        <WtgCheckbox label="Option Two" value="two"></WtgCheckbox>
        <WtgCheckbox label="Option Three" value="three"></WtgCheckbox>
    </WtgCheckboxGroup>
</WtgPanel>

<WtgPanel class="mt-6" max-width="800">
    <WtgCheckboxGroup label="Horizontal Checkbox group" description="Assistive text" :horizontal="true">
        <WtgCheckbox label="Option One" value="one"></WtgCheckbox>
        <WtgCheckbox label="Option Two" value="two"></WtgCheckbox>
        <WtgCheckbox label="Option Three" value="three"></WtgCheckbox>
    </WtgCheckboxGroup>
</WtgPanel>

<WtgPanel class="mt-6" max-width="800">
    <WtgCheckboxGroup label="Horizontal Checkbox group" description="Assistive text" :horizontal="true">
        <WtgCheckbox label="Option One" value="one"></WtgCheckbox>
        <WtgCheckbox label="Option Two" value="two"></WtgCheckbox>
        <WtgCheckbox label="Option Three" value="three"></WtgCheckbox>
    </WtgCheckboxGroup>
</WtgPanel>

 <WtgPanel class="mt-6" max-width="800" caption="Multiple Checkbox Selection Test">
    <WtgCheckbox v-for="option in options" :key="option" v-model="selectedOptions" :label="option" :value="option" multiple @update:model-value="updateModel" :data-testid="option"/>
</WtgPanel>
`;
