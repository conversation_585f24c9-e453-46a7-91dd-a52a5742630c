<template>
    <WtgAboutDialog
        :model-value="application.dialogs.about.visible"
        :dialog-title="about.dialogTitle"
        :title="about.portalName"
        @update:model-value="application.dialogs.about.close()"
    >
        <img v-if="logoUrl" :src="logoUrl.url" :alt="application.title" style="max-width: 280px; max-height: 35px" />
        <div class="d-flex flex-column">
            <WtgLabel typography="title-md-default">
                {{ about.applicationVersion ? about.versionsLabel : about.versionLabel }}
            </WtgLabel>
            <WtgLabel class="mt-2" typography="title-sm-default">
                {{ about.frameworkVersionLabel }}
            </WtgLabel>
            <WtgLabel> {{ about.frameworkVersion }}</WtgLabel>
            <template v-if="about.applicationVersion">
                <WtgLabel typography="title-sm-default">{{ about.applicationVersionLabel }}</WtgLabel>
                <WtgLabel>{{ about.applicationVersion }}</WtgLabel>
            </template>
        </div>
        <div class="mt-5 d-flex flex-column">
            <WtgLabel typography="title-md-default">
                {{ about.resourcesLabel }}
            </WtgLabel>
            <WtgLabel class="mt-2" typography="title-sm-default">{{ about.serviceURLLabel }}</WtgLabel>
            <WtgHyperlink :href="about.serviceURL">
                {{ about.serviceURL }}
            </WtgHyperlink>
            <WtgLabel class="mt-2" typography="title-sm-default">{{ about.portalURLLabel }}</WtgLabel>
            <WtgHyperlink :href="about.portalURL">{{ about.portalURL }}</WtgHyperlink>
        </div>
        <div class="mt-2">
            <WtgLabel>{{ currentUTC() }}</WtgLabel>
        </div>
        <div class="mt-2">
            <WtgLabel>&copy; {{ new Date().getFullYear() }} WiseTech Global</WtgLabel>
        </div>
    </WtgAboutDialog>
</template>

<script lang="ts" setup>
import WtgHyperlink from '@components/WtgHyperlink';
import WtgLabel from '@components/WtgLabel';
import WtgAboutDialog from '@components/framework/WtgAboutDialog';
import { useApplication } from '@composables/application';
import { useWtgUi } from '@composables/global';
import { useObjectUrl } from '@composables/object-url';
import { computed, onUnmounted } from 'vue';

const application = useApplication();

const about = computed(() => application.about);

const wtgUi = useWtgUi();
const logoUrl = computed(() => {
    const { logoLightImage, logoLightImageFileType, logoDarkImage, logoDarkImageFileType } = wtgUi.baseTheme;
    const { image, type } = wtgUi.dark
        ? { image: logoLightImage, type: logoLightImageFileType }
        : { image: logoDarkImage, type: logoDarkImageFileType };

    if (image && type) {
        return useObjectUrl(image, type);
    }

    return null;
});

const currentUTC = () => {
    const now: Date = new Date();
    const utcString: string =
        now.getUTCDate() +
        ' ' +
        now.toLocaleString('default', { month: 'short' }) +
        ' ' +
        now.getUTCFullYear() +
        ' ' +
        ('00' + now.getUTCHours()).slice(-2) +
        ':' +
        ('00' + now.getUTCMinutes()).slice(-2) +
        ':' +
        ('00' + now.getUTCSeconds()).slice(-2) +
        ' ' +
        about.value.UTCLabel;
    return utcString;
};

onUnmounted(() => {
    logoUrl.value?.dispose();
});
</script>
<style lang="scss">
.primary-text {
    color: var(--s-primary-txt-default) !important;
}
</style>
