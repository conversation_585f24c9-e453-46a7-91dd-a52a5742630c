<template>
    <VDivider :class="computedClass" :vertical="props.vertical" />
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import { VDivider } from 'vuetify/components/VDivider';

//
// Properties
//
const props = defineProps({
    /**
     * The style variant of the divider.
     * Can be either 'solid' or 'dashed'.
     */
    variant: {
        type: String as PropType<'solid' | 'dashed'>,
        default: 'solid',
    },

    /**
     * Determines if the divider is vertical.
     */
    vertical: {
        type: Boolean,
        default: false,
    },
});

//
// Computed
//
const computedClass = computed(() => ['wtg-divider', `wtg-divider--${props.variant}`]);
</script>

<style lang="scss">
.wtg-divider {
    border-width: 0px;
    border-block-start-width: 1px;
    border-inline-start-width: 1px;
    border-color: var(--s-neutral-txt-default);
    opacity: 0.12;
}

.wtg-divider--solid {
    border-style: solid;
}

.wtg-divider--dashed {
    border-style: dashed;
}
</style>
