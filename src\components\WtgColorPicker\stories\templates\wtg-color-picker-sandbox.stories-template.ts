export const ColorPickerSandboxTemplate = `
            <WtgLayoutGrid>
                <WtgPanel caption='Swatches'>
                    <span>Using the show-swatches prop you can display an array of color swatches that users can pick from. It is also possible to customize what colors are shown using the swatches prop. This prop accepts a two-dimensional array, where the first dimension defines a column, and second dimension defines the swatches from top to bottom by providing rgba hex strings. You can also set the max height of the swatches section with the swatches-max-height prop.</span>
                    <WtgColorPicker v-bind="args" />
                </WtgPanel>
                <WtgPanel caption="Canvas">
                    <span>The canvas can be hidden with the hide-canvas prop and it's height can be set using the canvas-height prop. The selection dot size can also be controlled with the dot-size prop."</span>
                    <WtgLayoutGrid>
                        <WtgColorPicker :canvas-height="300" v-model="picker" />
                        <WtgColorPicker :dot-size="30" v-model="picker" />
                        <WtgColorPicker :hide-canvas="true" v-model="picker" />
                    </WtgLayoutGrid>
                </WtgPanel>
                <WtgPanel caption="Elevation">
                    <span>Adjust the elevation of the v-color-picker component using the elevation or flat prop. The flat is equivalent to setting elevation to 0.</span>
                    <WtgColorPicker :elevation="0" v-model="picker" />
                </WtgPanel>
            </WtgLayoutGrid>
`;
