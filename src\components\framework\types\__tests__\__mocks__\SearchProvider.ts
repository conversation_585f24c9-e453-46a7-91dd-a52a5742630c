import {
    WtgFrameworkSearchEntity,
    WtgFrameworkSearchProvider,
    WtgFrameworkSearchResultEntity,
} from '../../../types';

class MockSearchProvider implements WtgFrameworkSearchProvider {
    entities: WtgFrameworkSearchEntity[] = [
        {
            entityType: 'IDummyBizo',
            entityName: 'Dummy Bizo',
            reference: 'DUMMYREFERENCE',
            color: 'AABBCC',
            searchableColumns: ['SEARCH1', 'SEARCH2'],
            keyFields: ['FIELD1', 'FIELD2'],
            shorthand: 'DUM',
        },
        {
            entityType: 'IDummyBizo2',
            entityName: 'Dummy Bizo 2',
            reference: 'DUMMYREFERENCE2',
            color: 'CCBBAA',
            searchableColumns: ['SEARCH1', 'SEARCH2'],
            keyFields: ['FIELD1', 'FIELD2'],
            shorthand: 'DM2',
        },
    ];

    shorthandToEntityType: Map<string, string> = new Map([
        ['DUM', 'IDummyBizo'],
        ['DM2', 'IDummyBizo2'],
    ]);

    /* eslint-disable @typescript-eslint/no-unused-vars */
    /* eslint-disable no-unused-vars */
    async getItemsAsync(searchTerm: string, entityTypes: string[]): Promise<WtgFrameworkSearchResultEntity[]> {
        return Promise.resolve([]);
    }

    async onSearchItemClicked(item: WtgFrameworkSearchResultEntity): Promise<void> {
        return Promise.resolve();
    }

    getCaptionForProperty(propertyName: string): string {
        return `Translated-${propertyName}`;
    }
    /* eslint-enable no-unused-vars */
    /* eslint-enable @typescript-eslint/no-unused-vars */
}

export default MockSearchProvider;
