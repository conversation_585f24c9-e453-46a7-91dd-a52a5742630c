<template>
    <WtgFooter v-if="!application.hideAppBar" app :order="4" class="footer" :class="footerClass">
        <WtgActionBar>
            <MobileBar v-if="onMobile" :task="currentTask" />
            <DesktopBar v-else :task="currentTask" />
        </WtgActionBar>
    </WtgFooter>
</template>

<script setup lang="ts">
import { WtgFrameworkTask } from '@components/framework/types';
import WtgActionBar from '@components/WtgActionBar';
import WtgFooter from '@components/WtgFooter';
import { useApplication } from '@composables/application';
import { useDisplay } from '@composables/display';
import { computed } from 'vue';
import DesktopBar from './DesktopBar.vue';
import MobileBar from './MobileBar.vue';

const { onMobile } = useDisplay();
const application = useApplication();

const currentTask = computed(() => {
    return application.currentTask ? application.currentTask : new WtgFrameworkTask();
});

const footerClass = computed(() => {
    return [
        {
            'desktop d-flex': !onMobile.value && visible.value,
            'd-none': !visible.value,
            'd-block': onMobile.value && visible.value,
        },
    ];
});

const visible = computed(() => {
    return application.footerVisible;
});
</script>

<style lang="scss" scoped>
.footer {
    margin-top: 0px !important;
    width: unset !important;
    right: 0px !important;
    z-index: 4;
    background: transparent;
    padding: unset;

    &.desktop {
        padding: 0px var(--s-padding-xl) var(--s-padding-m) var(--s-padding-xl);
    }
}

.theme--light .footer-border {
    border-top: 1px solid rgba(0, 0, 0, 0.12) !important;
}
.theme--dark .footer-border {
    border-top: 1px solid rgba(255, 255, 255, 0.12) !important;
}
</style>
