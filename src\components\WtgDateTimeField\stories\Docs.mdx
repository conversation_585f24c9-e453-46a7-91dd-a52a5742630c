import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgDateTimeField from './WtgDateTimeField.stories';

<Meta of={WtgDateTimeField} />

<div className="component-header">
    <h1>Date time field</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable} />
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img> With pending updates
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                [WI00800837](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/40635100-f301-4a00-a0d8-013c9b96c944?lang=en-gb)
            </td>
            <td>Open Input Popovers on focus.</td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">The date time field lets users select a date and a time.</p>

## API

<Canvas className="canvas-preview" of={WtgDateTimeField.Default} />
<Controls of={WtgDateTimeField.Default} sort={'alpha'} />

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
