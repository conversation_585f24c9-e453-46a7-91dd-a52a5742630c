<template>
    <label :class="required ? 'wtg-required' : ''">
        <template v-if="label">
            {{ label }}
        </template>
        <slot v-else />
    </label>
</template>

<script setup lang="ts">
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';

const props = defineProps({
    required: {
        type: Boolean,
        default: false,
    },
    label: {
        type: String,
        default: '',
    },
    ...makeLayoutGridColumnProps(),
});

useLayoutGridColumn(props);
</script>

<style lang="scss" scoped>
label {
    display: block;
    color: var(--s-neutral-txt-default);
    text-wrap: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    align-items: flex-start;
    gap: var(--s-padding-xs);
    align-self: stretch;
    font: var(--s-text-sm-default);

    &.wtg-required::after {
        color: var(--s-error-icon-default);
        content: '*';
    }
}
</style>
