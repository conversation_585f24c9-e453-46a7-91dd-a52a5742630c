import WtgPanel from '@components/WtgPanel';
import { Meta, StoryObj } from '@storybook/vue3';
import WtgConversation from '..';
import { PotentialFollowerItem } from '../types';

enum PotentialFollowerType {
    Staff = 1,
    Group = 2,
    Contact = 3,
    Organization = 4,
}

const PotentialFollowersData: Record<string, any> = {
    [PotentialFollowerType.Staff]: [
        {
            label: 'WBPTESTUSER28',
            chips: [
                { color: 'accent', label: 'Active' },
                { color: 'accent', label: 'Driver' },
            ],
            actionText: 'B28',
            value: '1df74c4e-d45b-44e0-95a3-224f2ffb9d72',
            text: 'WBPTESTUSER28',
            photo: {
                image: 'avatarImage.png',
            },
        },
        {
            label: 'BSIUSER29',
            chips: [
                { color: 'accent', label: 'Active' },
                { color: 'accent', label: 'System Account' },
            ],
            actionText: 'B29',
            value: '5682420b-4fff-414c-956e-1be3d7b2442a',
            text: 'BSIUSER29',
            photo: {
                image: 'avatarImage.png',
            },
        },
    ],
    [PotentialFollowerType.Group]: [
        {
            label: 'AUA03 DRIVERS',
            secondaryText: 'A03DRIVERS',
            value: 'd0b33299-2712-4deb-8a54-0a46dfbfad1c',
            text: 'AUA03 DRIVERS',
            icon: 's-icon-group',
        },
        {
            label: 'AUA11 DRIVERS',
            secondaryText: 'A11DRIVERS',
            value: '25dfe89c-ea08-4cca-afed-0c0475b1dbd7',
            text: 'AUA11 DRIVERS',
            icon: 's-icon-group',
        },
    ],
    [PotentialFollowerType.Contact]: [
        {
            label: 'Accounts (<EMAIL>)',
            secondaryText: 'AU 3 RECEIVABLES CORPORATION',
            actionText: 'AUSYD',
            value: '87546db2-7960-4382-b76e-09398b7709d9',
            text: 'Accounts',
            photo: {
                image: 'avatarImage.png',
            },
        },
        {
            label: 'Accounts (<EMAIL>)',
            secondaryText: 'YOUR CAMBODIA CORP',
            actionText: 'KHPNH',
            value: '385e57ae-0810-4fd0-91dc-096980aa4efb',
            text: 'Accounts',
            photo: {
                image: 'avatarImage.png',
            },
        },
    ],
    [PotentialFollowerType.Organization]: [
        {
            label: 'UNITED ENTERPRISES (VU) CORPORATION',
            secondaryText: 'UNIENTVLI',
            value: 'e2c367fc-c44e-45fe-bf30-d4e1b3be9a56',
            text: 'UNITED ENTERPRISES (VU) CORPORATION',
            icon: 's-icon-building',
        },
        {
            label: 'GB SEA CTO (K11)',
            secondaryText: 'GBSEACMAN',
            value: 'd623cff5-40e8-4905-b6fb-d5a69c09e7d8',
            text: 'GB SEA CTO (K11)',
            icon: 's-icon-building',
        },
    ],
};

const provider = {
    likeMessageAsync(message: any): Promise<number> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(message.score + 1);
            }, 1000);
        });
    },
    dislikeMessageAsync(message: any): Promise<number> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(message.score - 1);
            }, 1000);
        });
    },
    sendMessageAsync(conversation: any, body: string): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const messageDate = new Date();
                const messageDateString = messageDate.toLocaleDateString();
                const newMessage = {
                    id: 'pk-new-' + messageDate.getTime(),
                    body,
                    color: 'user',
                    isFromSelf: true,
                    sender: 'Bob',
                    time: messageDate.toLocaleString('default', { hour: 'numeric', minute: 'numeric' }),
                    score: 0,
                };

                if (!conversation.messages[messageDateString]) {
                    conversation.messages = {
                        ...conversation.messages,
                        [messageDateString]: [newMessage],
                    };
                } else {
                    const newArray = [...conversation.messages[messageDateString], newMessage];
                    conversation.messages = {
                        ...conversation.messages,
                        [messageDateString]: newArray,
                    };
                }

                setTimeout(() => {
                    const automatedMessageDate = new Date();
                    const automatedMessageDateString = automatedMessageDate.toLocaleDateString();
                    const automatedMessage = {
                        id: 'pk-reply-' + automatedMessageDate.getTime(),
                        body: 'This is an automated message. Alice will be back shortly.',
                        type: 'system',
                        isFromSelf: false,
                        sender: 'Automated Message',
                        time: automatedMessageDate.toLocaleString('default', { hour: 'numeric', minute: 'numeric' }),
                        score: 0,
                    };

                    if (!conversation.messages[automatedMessageDateString]) {
                        conversation.messages = {
                            ...conversation.messages,
                            [automatedMessageDateString]: [automatedMessage],
                        };
                    } else {
                        const newArray = [...conversation.messages[automatedMessageDateString], automatedMessage];
                        conversation.messages = {
                            ...conversation.messages,
                            [automatedMessageDateString]: newArray,
                        };
                    }
                }, 1000);
                resolve();
            }, 1000);
        });
    },
    followConversationAsync(conversation: any): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                conversation.following = true;
                resolve();
            }, 1000);
        });
    },
    unfollowConversationAsync(conversation: any): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                conversation.following = false;
                resolve();
            }, 1000);
        });
    },
    removeFollowerFromConversationAsync(follower: any, conversation: any): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const followerId = follower.id;

                conversation.followers = conversation.followers.filter((f: any) => {
                    return f.id !== followerId;
                });
                resolve();
            }, 1000);
        });
    },
    addFollowersAsync(conversation: any, followersData: any) {
        return new Promise((resolve) => {
            Object.keys(followersData).map((userType) => {
                const followers = followersData[userType];

                followers.forEach((followerPK: string) => {
                    const followerItem = PotentialFollowersData[userType].find(
                        (item: PotentialFollowerItem) => item.value === followerPK
                    );

                    conversation.followers.push({
                        id: followerPK,
                        name: followerItem?.text || '',
                        icon: followerItem?.icon,
                        photo: followerItem?.photo,
                    });
                });
            });

            setTimeout(() => {
                resolve({});
            }, 1000);
        });
    },
    getPotentialFollowersAsync(_conversation: any, userType: PotentialFollowerType, searchText?: string): Promise<any> {
        return new Promise((resolve) => {
            let potentialFollowers = PotentialFollowersData[userType];
            if (searchText) {
                potentialFollowers = potentialFollowers.filter((follower: any) =>
                    follower.description.toLowerCase().includes(searchText.toLowerCase())
                );
            }

            setTimeout(() => {
                resolve({
                    items: potentialFollowers,
                    total: potentialFollowers.length,
                });
            }, 1000);
        });
    },
};

const demo: any = {
    id: '1',
    name: 'Demo Conversation',
    type: '1',
    loadingMessages: false,
    followers: [
        {
            id: '1',
            name: 'John Doe',
            icon: 's-icon-user',
            image: '',
        },
        {
            id: '2',
            name: 'Accounting Dept',
            icon: 's-icon-group',
            image: '',
        },
        {
            id: '3',
            name: 'ABC Company',
            icon: 's-icon-building',
            image: '',
        },
    ],
    followerUserTypes: [
        { type: PotentialFollowerType.Staff, caption: 'Staff' },
        { type: PotentialFollowerType.Group, caption: 'Group' },
        { type: PotentialFollowerType.Contact, caption: 'Contact' },
        { type: PotentialFollowerType.Organization, caption: 'Organization' },
    ],
    following: false,
    messages: {
        '10/25/2022': [
            {
                id: 'one',
                body: 'Hi there!\nSend a message!',
                isFromSelf: false,
                sender: 'Automated Message',
                time: '9:40 AM',
                score: 0,
                loadingDislikeScore: false,
                loadingLikeScore: false,
                type: 'staff',
            },
        ],
    },
    provider,
};

type Story = StoryObj<typeof WtgConversation>;
const meta: Meta<typeof WtgConversation> = {
    title: 'Components/Conversation',
    component: WtgConversation,
    parameters: {
        docs: {
            description: {
                component: 'Conversation component is used to send messages and display a conversation.',
            },
        },
    },
    render: (args) => ({
        components: { WtgConversation, WtgPanel },
        setup: () => ({ args }),
        template: `<WtgPanel><WtgConversation v-bind="args"/></WtgPanel>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        conversation: demo,
    },
};
