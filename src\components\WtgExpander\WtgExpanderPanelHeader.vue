<template>
    <VExpansionPanelTitle class="wtg-expander-header">
        <template v-if="title || description">
            <div class="wtg-expander-header__container">
                <div class="wtg-expander-header__text">
                    <span class="wtg-expander-header__title">{{ title }}</span>
                    <span class="wtg-expander-header__description">{{ description }}</span>
                </div>
            </div>
        </template>
        <div v-else class="wtg-expander-header__slot">
            <slot />
        </div>
        <template #actions="{ expanded }: { expanded: boolean }">
            <div class="wtg-expander-header__actions">
                <WtgIcon :icon="expanded ? 's-icon-caret-up' : 's-icon-caret-down'" />
            </div>
        </template>
        <WtgDivider class="wtg-expander-header__divider" />
    </VExpansionPanelTitle>
</template>

<script setup lang="ts">
import WtgDivider from '@components/WtgDivider';
import WtgIcon from '@components/WtgIcon';
import { VExpansionPanelTitle } from 'vuetify/components/VExpansionPanel';

//
// Properties
//
defineProps({
    /**
     * The description text displayed in the expander panel header.
     */
    description: {
        type: String,
        default: '',
    },
    /**
     * The title text displayed in the expander panel header.
     */
    title: {
        type: String,
        default: '',
    },
});
</script>

<style lang="scss">
.wtg-expander-header {
    padding: var(--s-padding-xl);
    font: inherit;
    min-height: 0px;

    .wtg-expander-header__container {
        display: flex;
        gap: var(--s-spacing-m);
        width: 100%;

        .wtg-expander-header__text {
            display: flex;
            flex-direction: column;
            flex-grow: 1;

            .wtg-expander-header__title {
                font: var(--s-title-sm-default);
            }

            .wtg-expander-header__description {
                font: var(--s-text-md-default);
                color: var(--s-neutral-txt-weak-default);
            }
        }
    }

    .wtg-expander-header__slot {
        display: flex;
        flex: 1 1 auto;
        > * {
            flex: 1 1 auto;
        }
    }

    .wtg-expander-header__divider {
        position: absolute;
        left: var(--s-padding-xl);
        right: var(--s-padding-xl);
        bottom: 0px;
    }
}

button.wtg-expander-header:disabled {
    border-color: var(--s-neutral-bg-disabled);
    color: var(--s-neutral-txt-disabled);
    cursor: default;
}

.wtg-expander-panel:not(.v-expansion-panel--active) .wtg-expander-header__divider {
    visibility: hidden;
}
</style>
