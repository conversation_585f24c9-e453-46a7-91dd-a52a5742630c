export const ButtonWithSentimentsTemplate = `
<wtg-row>
    <wtg-col style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <wtg-button 
            v-bind="args"
            @click="action">
                Outline Default
        </wtg-button>
        <wtg-button 
            v-bind="args"
            sentiment="success"
            @click="action">
                Outline Success
        </wtg-button>
        <wtg-button 
            v-bind="args"
            sentiment="critical"
            @click="action">
                Outline Critical
        </wtg-button>
    </wtg-col>
    <wtg-col style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <wtg-button 
            v-bind="args"
            variant="fill"
            sentiment="primary"
            data-testid="testSentimentsButton-primary"
            @click="action">
                Fill Primary
        </wtg-button>
        <wtg-button 
            v-bind="args"
            variant="fill"
            sentiment="success"
            @click="action">
                Fill Success
        </wtg-button>
        <wtg-button 
            v-bind="args"
            variant="fill"
            sentiment="critical"
            @click="action">
                Fill Critical
        </wtg-button>
    </wtg-col>
    <wtg-col style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <wtg-button 
            v-bind="args"
            variant="ghost"
            @click="action">
                Ghost Default
        </wtg-button>
        <wtg-button 
            v-bind="args"
            variant="ghost"
            sentiment="success"
            @click="action">
                Ghost Success
        </wtg-button>
          <wtg-button 
            v-bind="args"
            variant="ghost"
            sentiment="critical"
            @click="action">
                Ghost Critical
        </wtg-button>
    </wtg-col>
</wtg-row>`;
