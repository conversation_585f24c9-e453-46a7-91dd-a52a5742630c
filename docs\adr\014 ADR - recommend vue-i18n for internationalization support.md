# Recommend vue-i18n for internationalization support

## Status  
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

Supply is used across regions with different language and formatting needs. While Supply itself is not yet fully internationalized, support for `vue-i18n` has been partially implemented and is maintained within the core codebase.

Several consumer applications built on Supply require internationalization—such as translating UI labels, formatting numbers and dates per locale, or adapting to regional conventions. Rather than implementing custom solutions, we recommend that consumers use the established `vue-i18n` integration already present in Supply.

## Decision

## Status
- [ ] Proposed  
- [x] Accepted  
- [ ] Rejected  
- [ ] Deprecated  
- [ ] Superseded  

All consumer applications requiring internationalization should use `vue-i18n` as provided by the Supply framework. This includes:

- Using translation keys instead of hardcoded strings
- Formatting dates, times, numbers, and currencies using locale-aware utilities
- Adopting existing i18n patterns already used in partially internationalized Supply components

Consumers are responsible for providing and managing their own locale message files. Supply offers the underlying integration and helpers, but does not bundle translations for downstream products.

## Consequences

Pros:
1. **Standardized approach**. Encourages consistency across teams and simplifies component integration.
2. **Aligned with Vue**. `vue-i18n` is the official internationalization library for Vue and works well with both the Composition and Options APIs.
3. **Built-in support**. Supply already provides base configuration and helpers for `vue-i18n`, reducing setup overhead.
4. **Future-proof**. Supports lazy-loading translations, pluralization rules, locale fallbacks, and more.

Cons:
1. **Partial internal coverage**. Not all Supply components are fully internationalized yet, which may require consumers to extend support in some places.
2. **Consumer responsibility for messages**. Each product team must maintain their own translation keys and locale files.
3. **Complexity for non-localized apps**. Teams that do not require internationalization may still encounter `t()` calls or translation keys within components.

---

### Notes
This file follows the format introduced in [Documenting Architecture Decisions](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions) by Michael Nygard. We recommend using sequential file naming (e.g., `0001-title.md`) and keeping ADRs under `docs/adr/`.

