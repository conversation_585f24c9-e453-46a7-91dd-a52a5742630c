import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgBottomSheet from './WtgBottomSheet.stories.ts';

<Meta of={WtgBottomSheet} />

<div className="component-header">
    <h1>Bottom Sheet</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td></td>
            <td>
                <img className="status-chip" src={statusPlanned}></img>
            </td>
            <td></td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

This component is a sub-component of the application layout components. For more information on how to use this component and its role within the [application layout](/docs/utilities-app--docs), please refer to the application layout documentation.

## API

<Canvas className="canvas-preview" of={WtgBottomSheet.Default} />
<Controls of={WtgBottomSheet.Default} sort={'alpha'} />
