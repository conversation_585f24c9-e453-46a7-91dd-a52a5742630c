import CountryFlag from 'vue-country-flag-next';
import WtgFlag from '..';
import WtgUi from '../../../WtgUi';
import { mount } from '@vue/test-utils';

const $wtgUi = new WtgUi();

describe('WtgFlag', () => {
    test('its name is WtgFlag', () => {
        const wrapper = mountComponent({
            propsData: {
                country: 'us',
            },
        });
        expect(wrapper.vm.$options.__name).toBe('WtgFlag');
    });

    test('it renders a CountryFlag component', () => {
        const wrapper = mountComponent({
            propsData: {
                country: 'us',
            },
        });
        expect(wrapper.classes()).toContain('flag');
    });

    test('it passes country prop to the CountryFlag component', () => {
        const wrapper = mountComponent({
            propsData: {
                country: 'au',
            },
        });
        const flag = wrapper.findComponent(CountryFlag).vm;
        expect(flag.$props.country).toBe('au');
    });

    test('it passes size prop to the CountryFlag component', () => {
        const wrapper = mountComponent({
            propsData: {
                country: 'au',
                size: 'big',
            },
        });
        const flag = wrapper.findComponent(CountryFlag).vm;
        expect(flag.$props.size).toBe('big');
    });

    test('uses normal size when size prop is not passed to the CountryFlag component', () => {
        const wrapper = mountComponent({
            propsData: {
                country: 'au',
            },
        });
        const flag = wrapper.findComponent(CountryFlag).vm;
        expect(flag.$props.size).toBe('normal');
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgFlag as any, {
            $wtgUi,
            propsData,
        });
    }
});
