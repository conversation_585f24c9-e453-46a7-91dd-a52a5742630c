<template>
    <div @click.stop="">
        <WtgList style="max-height: 300px" class="pa-0 overflow-y-auto">
            <WtgListItem v-for="item in items" :key="item.id" class="pa-0" two-line>
                <WtgCallout
                    :sentiment="item.color"
                    class="mb-0"
                    :style="{ cursor: 'pointer', width: '100%' }"
                    @click="onCalloutClick(item.id)"
                    @mouseleave="onCalloutMouseLeave(item.id)"
                    @mouseover="onCalloutMouseOver(item.id)"
                >
                    <div class="mb-1 d-flex align-end">
                        <WtgSpacer>
                            <strong>{{ item.caption }}</strong>
                        </WtgSpacer>
                        <WtgCheckbox
                            v-if="item.requiresAcknowledgement"
                            hide-details
                            :color="item.color"
                            :model-value="item.isAcknowledged"
                            class="ma-0 pa-0 alert-checkbox"
                            @change="item.toggleIsAcknowledged?.($event)"
                        />
                    </div>
                    <span>{{ item.text }}</span>
                </WtgCallout>
            </WtgListItem>
        </WtgList>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent, PropType } from 'vue';

import { useWtgUi } from '@composables/global';
import WtgCallout, { WtgCalloutSentimentType } from '@components/WtgCallout';
import WtgCheckbox from '@components//WtgCheckbox';
import WtgList, { WtgListItem } from '@components/WtgList';
import WtgSpacer from '@components/WtgSpacer';
import { WtgFrameworkNotification, WtgFrameworkNotificationType } from '@components/framework/types';
import { CurrentNotificationType } from '../../../../WtgUi';

interface NotificationItem {
    id: string;
    icon: string;
    color?: WtgCalloutSentimentType;
    caption: string;
    text: string;
    requiresAcknowledgement: boolean;
    isAcknowledged: boolean;
    toggleIsAcknowledged?: (isAcknowledged: boolean) => void;
}

export default defineComponent({
    name: 'NotificationsList',
    components: {
        WtgCallout,
        WtgCheckbox,
        WtgList,
        WtgListItem,
        WtgSpacer,
    },
    props: {
        notifications: {
            type: Array as PropType<WtgFrameworkNotification[]>,
            required: true,
        },
    },
    setup(props) {
        const wtgUi = useWtgUi();

        const items = computed((): NotificationItem[] => {
            return props.notifications.map((notification) => {
                let icon = '';
                let color: WtgCalloutSentimentType | undefined;

                switch (notification.type) {
                    case WtgFrameworkNotificationType.Information:
                        icon = 's-icon-message';
                        color = WtgCalloutSentimentType.Info;
                        break;
                    case WtgFrameworkNotificationType.Warning:
                        icon = 's-icon-warning';
                        color = WtgCalloutSentimentType.Warning;
                        break;
                    case WtgFrameworkNotificationType.MessageError:
                        icon = 's-icon-status-critical';
                        color = WtgCalloutSentimentType.Warning;
                        break;
                    case WtgFrameworkNotificationType.Error:
                        icon = 's-icon-status-critical';
                        color = WtgCalloutSentimentType.Critical;
                        break;
                }

                if (notification.requiresAcknowledgement) {
                    if (!Object.prototype.hasOwnProperty.call(notification, 'isAcknowledged')) {
                        throw new Error('Required property isAcknowledged is not found in notification');
                    }

                    if (!Object.prototype.hasOwnProperty.call(notification, 'toggleIsAcknowledged')) {
                        throw new Error('Required property toggleIsAcknowledged is not found in notification');
                    }
                }

                return {
                    id: notification.id,
                    caption: notification.caption,
                    text: notification.text,
                    icon,
                    color,
                    requiresAcknowledgement: notification.requiresAcknowledgement || false,
                    isAcknowledged: notification.isAcknowledged || false,
                    toggleIsAcknowledged: notification.toggleIsAcknowledged,
                };
            });
        });

        const onCalloutClick = (itemId: string): void => {
            const notification = props.notifications.find((n) => n.id === itemId);
            if (notification) {
                wtgUi.currentNotification = {
                    propertyName: notification.propertyName,
                    type: CurrentNotificationType.Click,
                };
            }
        };
        const onCalloutMouseLeave = (itemId: string): void => {
            const notification = props.notifications.find((n) => n.id === itemId);
            if (notification) {
                wtgUi.currentNotification = {
                    propertyName: notification.propertyName,
                    type: CurrentNotificationType.MouseOut,
                };
            }
        };
        const onCalloutMouseOver = (itemId: string): void => {
            const notification = props.notifications.find((n) => n.id === itemId);
            if (notification) {
                wtgUi.currentNotification = {
                    propertyName: notification.propertyName,
                    type: CurrentNotificationType.MouseOver,
                };
            }
        };
        return { items, onCalloutClick, onCalloutMouseLeave, onCalloutMouseOver };
    },
});
</script>
