import { WtgApp } from '@components/WtgApp';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { VAppBarTitle } from 'vuetify/components/VAppBar';
import { WtgAppBar, WtgAppBarTitle } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgAppBarTitle', () => {
    test('it renders a VAppBarTitle component', async () => {
        const wrapper = await mountComponentAsync();
        const title = wrapper.findComponent(VAppBarTitle);
        expect(title.exists()).toBe(true);
    });

    test('it applies the wtg-app-bar-title class', async () => {
        const wrapper = await mountComponentAsync();
        const title = wrapper.findComponent(WtgAppBarTitle);
        expect(title.classes()).toContain('wtg-app-bar-title');
    });

    test('it passes the default slot content to the VAppBarTitle component', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                default: () => '<div>Some Text</div>',
            },
        });
        expect(wrapper.html()).toContain('Some Text');
    });

    async function mountComponentAsync({ props = {}, slots = {} } = {}) {
        const wrapper = mount(
            {
                template:
                    '<wtg-app><wtg-app-bar><wtg-app-bar-title v-bind="$attrs"><template v-for="(_, name) in $slots" v-slot:[name]="slotData"><slot :name="name" v-bind="slotData" /></template></wtg-app-bar-title></wtg-app-bar></wtg-app>',
                components: { WtgApp, WtgAppBar, WtgAppBarTitle },
            },
            {
                propsData: {
                    ...props,
                },
                slots,
                global: {
                    plugins: [wtgUi],
                },
            }
        );
        await flushPromises();
        return wrapper;
    }
});
