<template>
    <WtgAddressField
        v-bind="{ ...attrs, ...baseInputProps, ...props, label: computedLabel }"
        hide-address-display-mode-code
        :editable="!readonly"
        :item-provider="dataProvider?.companyProvider"
        :model-value="internalValue?.address?.guid"
        :address-override-value="internalValue?.address"
        :contact="internalValue?.contact"
        :popup-contact-selected="popupContactSelected"
        :is-address-overriden="internalValue?.isAddressOverriden"
        :allow-free-text-address-entry="addressEditMode === JobAddressEditMode.RealAddressAndFreeText"
        :address-override-description="popupAddressOverrideDescription"
        show-selected-address
        return-object
        :sentiment="sentiment ?? jobAddressSentimentFromValidationState"
        :messages="messages ?? jobAddressMessageFromValidationState"
        @update:model-value="onAddressFieldChanged"
        @edit="onEdit"
    ></WtgAddressField>
    <WtgDialog v-if="popupAddress && popupContact" v-model="showDialog" width="600px" persistent>
        <div class="wtg-job-address-modal">
            <AddressTitle
                :card-address-override-description="popupAddressOverrideDescription"
                is-editor-title
                :title="computedLabel"
                :is-card-address-overriden="isPopupAddressOverriden"
                closeable
                class="wtg-job-address-modal-title"
                @cancel="onCancel"
            />
            <div class="wtg-modal__content-canvas">
                <WtgPanel>
                    <AddressEditor
                        ref="addressEditor"
                        :api-key="apiKey"
                        :company-header="companyHeader"
                        :contact-header="contactHeader"
                        :data-provider="dataProvider"
                        :is-real-address-only-mode="isRealAddressOnlyMode"
                        :address-header="addressHeader"
                        :popup-address="popupAddress"
                        :popup-contact="popupContact"
                        :popup-contact-selected="popupContactSelected"
                        :company="company"
                        :hide-map-location="hideMapLocation"
                        :hide-country="hideCountry"
                        :hide-street-details="hideStreetDetails"
                        :validation-states="validationStates"
                        :validation-property-mapping="validationPropertyMapping"
                        @address-changed="onPopupAddressChanged"
                        @company-changed="onPopupAddressChanged"
                        @contact-changed="onPopupContactChanged"
                    />
                </WtgPanel>
            </div>
            <div class="wtg-job-address-modal-actions">
                <WtgButton :min-width="88" variant="ghost" @click="onClear">
                    {{ clearCaption }}
                </WtgButton>
                <WtgButton @click="onCancel">
                    {{ cancelCaption }}
                </WtgButton>
                <WtgButton variant="fill" sentiment="primary" :loading="internalLoading" @click="onPopupApply">
                    {{ applyCaption }}
                </WtgButton>
            </div>
        </div>
    </WtgDialog>
</template>

<script setup lang="ts">
import {
    AddressLookupSearchContent as AddressItem,
    AddressLookupSearchContent,
    ContactLookupSearchContent as ContactItem,
    WtgAddressField,
} from '@components/WtgAddressField';
import WtgButton from '@components/WtgButton';
import WtgDialog from '@components/WtgDialog';
import WtgPanel from '@components/WtgPanel';
import { basePropsFromProps, makeInputProps, usePendingCommits } from '@composables/input';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import { makeMeasureProps } from '@composables/measure';
import { AlertLevel, ValidationState } from '@composables/notifications';
import { makeValidationProps } from '@composables/validation';
import { computed, PropType, ref, useAttrs, watch } from 'vue';
import AddressEditor from './components/AddressEditor/AddressEditor.vue';
import AddressTitle from './components/AddressTitle/AddressTitle.vue';
import {
    WtgJobAddressChangeOverride as ChangeOverride,
    JobAddressEditMode,
    WtgJobAddressData,
    WtgJobAddressProvider,
} from './types';

//
// Properties
//
const props = defineProps({
    addressEditMode: {
        type: String as PropType<JobAddressEditMode>,
        default: JobAddressEditMode.RealAddressAndFreeText,
    },
    apiKey: {
        type: String,
        default: '',
    },
    dataProvider: {
        type: Object as PropType<WtgJobAddressProvider>,
        default: undefined,
    },
    hideMapLocation: {
        type: Boolean,
        default: false,
    },
    inlineEdit: {
        type: Boolean,
        default: false,
    },
    modelValue: {
        type: Object as PropType<WtgJobAddressData>,
        default: (): WtgJobAddressData => {
            return {
                address: undefined as AddressItem | undefined,
                contact: undefined as ContactItem | undefined,
                isAddressOverriden: false,
            };
        },
    },
    showAdditionalInformation: {
        type: Boolean,
        default: true,
    },
    showContact: {
        type: Boolean,
        default: true,
    },
    validationPropertyMapping: {
        type: Object,
        default: undefined,
    },
    validationStates: {
        type: Array as PropType<Readonly<ValidationState>[]>,
        default: () => [],
    },
    variant: {
        type: String as PropType<'' | 'content'>,
        default: '',
    },

    ...makeInputProps(),
    ...makeValidationProps(),
    ...makeLayoutGridColumnProps(),
    ...makeMeasureProps(),

    /**
     * @deprecated Use `label` instead.
     */
    title: {
        type: String,
        default: undefined,
    },
    /**
     * @deprecated For compatibility purposes. Will be removed in the future.
     */
    hideCountry: {
        type: Boolean,
        default: false,
    },
    /**
     * @deprecated For compatibility purposes. Will be removed in the future.
     */
    hideStreetDetails: {
        type: Boolean,
        default: false,
    },
});

//
// Emits
//
const emit = defineEmits<{
    'update:modelValue': [value?: WtgJobAddressData];
}>();

//
// State
//
const addressOverrideDescription = ref('');
const internalLoading = ref(false);
const showDialog = ref(false);
const internalValue = ref<WtgJobAddressData>();
const popupAddress = ref<AddressItem>();
const popupContact = ref<ContactItem>();
const providerAddress = ref<AddressItem>();
const providerContact = ref<ContactItem>();
const popupContactSelected = ref(false);
const prevValue = ref<WtgJobAddressData>();

//
// Composables
//
const attrs = useAttrs();
const { formatCaption } = useLocale();
const { waitForPendingCommits } = usePendingCommits();

useLayoutGridColumn(props);

//
// Computed
//
const computedLabel = computed(() => props.label || props.title);

const baseInputProps = computed(() => basePropsFromProps(props));

const isRealAddressOnlyMode = computed((): boolean => {
    return props.addressEditMode === JobAddressEditMode.RealAddressOnly;
});

const clearCaption = computed(() => formatCaption('dialog.clear'));

const cancelCaption = computed(() => formatCaption('dialog.cancel'));

const applyCaption = computed(() => formatCaption('dialog.apply'));

const additionalInfoCaption = computed((): string => {
    return formatCaption('jobAddress.additionalInfoLabel');
});

const countryCaption = computed((): string => {
    return formatCaption('jobAddress.countryFieldLabel');
});

const cityCaption = computed((): string => {
    return formatCaption('jobAddress.cityFieldLabel');
});

const errorState = computed((): string => {
    return formatCaption('jobAddress.errorState');
});

const warningState = computed((): string => {
    return formatCaption('jobAddress.warningState');
});

const streetAltCaption = computed((): string => {
    return formatCaption('jobAddress.address2FieldLabel');
});

const streetCaption = computed((): string => {
    return formatCaption('jobAddress.addressFieldLabel');
});

const stateCaption = computed((): string => {
    return formatCaption('jobAddress.stateFieldLabel');
});

const postcodeCaption = computed((): string => {
    return formatCaption('jobAddress.postcodeFieldLabel');
});

const addressOverrides = computed((): ChangeOverride[] => {
    const overrides: Array<ChangeOverride> = [];
    checkOverride(overrides, providerAddress.value?.streetAlt, popupAddress.value?.streetAlt, streetAltCaption.value);
    checkOverride(overrides, providerAddress.value?.street, popupAddress.value?.street, streetCaption.value);
    checkOverride(overrides, providerAddress.value?.city, popupAddress.value?.city, cityCaption.value);
    checkOverride(overrides, providerAddress.value?.state, popupAddress.value?.state, stateCaption.value);
    checkOverride(overrides, providerAddress.value?.postcode, popupAddress.value?.postcode, postcodeCaption.value);
    checkOverride(overrides, providerAddress.value?.countryCode, popupAddress.value?.countryCode, countryCaption.value);
    checkOverride(
        overrides,
        providerAddress.value?.additionalInfo,
        popupAddress.value?.additionalInfo,
        additionalInfoCaption.value
    );

    return overrides;
});

const isAddressDirty = computed((): boolean => {
    if (!popupAddress.value) {
        return false;
    }

    if (!providerAddress.value && popupAddress.value.company) {
        return true;
    }

    return addressOverrides.value.length > 0;
});

const contactNameCaption = computed((): string => {
    return formatCaption('jobAddress.contactFieldLabel');
});

const phoneCaption = computed((): string => {
    return formatCaption('jobAddress.phoneFieldLabel');
});

const mobileCaption = computed((): string => {
    return formatCaption('jobAddress.mobileFieldLabel');
});

const emailCaption = computed((): string => {
    return formatCaption('jobAddress.emailFieldLabel');
});

const contactOverrides = computed((): ChangeOverride[] => {
    const overrides: Array<ChangeOverride> = [];
    if (popupContact.value) {
        checkOverride(overrides, providerContact.value?.name, popupContact.value?.name, contactNameCaption.value);
        const providerSource = popupContactSelected.value ? providerContact.value : providerAddress.value;
        const popupSource = popupContactSelected.value ? popupContact.value : popupAddress.value;
        checkOverride(overrides, providerSource?.phone, popupSource?.phone, phoneCaption.value);
        checkOverride(overrides, providerSource?.mobile, popupSource?.mobile, mobileCaption.value);
        checkOverride(overrides, providerSource?.email, popupSource?.email, emailCaption.value);
    }
    return overrides;
});

const isContactDirty = computed((): boolean => {
    return contactOverrides.value.length > 0;
});

const isPopupAddressOverriden = computed((): boolean => {
    if (isRealAddressOnlyMode.value) {
        return false;
    }

    return isAddressDirty.value || isContactDirty.value;
});

const popupAddressOverrideDescription = computed((): string => {
    if (isPopupAddressOverriden.value) {
        if (!providerAddress.value) {
            return 'This is a free-text address';
        } else {
            let changes: Array<ChangeOverride> = [];
            changes = changes.concat(addressOverrides.value, contactOverrides.value);

            if (changes.length) {
                const changeItems = changes.map(
                    ({ caption, from, to }) => `<li><strong>${caption}</strong> changed from '${from}' to '${to}'</li>`
                );

                return `This is a free-text overridden address due to the following changes: <ul style="padding-left: 15px;">${changeItems.join(
                    ''
                )}</ul>`;
            }
        }
    }

    return '';
});

const addressHeader = computed((): string => {
    return formatCaption('jobAddress.addressTab');
});

const companyHeader = computed((): string => {
    return formatCaption('jobAddress.companyNameLabel');
});

const contactHeader = computed((): string => {
    return formatCaption('jobAddress.contactTab');
});

const company = computed((): string | undefined => {
    return popupAddress.value?.company;
});

const hasErrors = computed((): boolean => {
    const messages = props.validationStates.filter((state: ValidationState) => state.alertLevel === 4);
    return messages.length > 0;
});

const highestSentimentFromValidationState = computed((): AlertLevel => {
    let highestAlertLevel = AlertLevel.None;
    props.validationStates.forEach((validationState) => {
        if (validationState.alertLevel > highestAlertLevel) {
            highestAlertLevel = validationState.alertLevel;
        }
    });
    return highestAlertLevel;
});

const jobAddressSentimentFromValidationState = computed((): 'critical' | 'warning' | undefined => {
    switch (highestSentimentFromValidationState.value) {
        case AlertLevel.Error:
            return 'critical';
        case AlertLevel.Warning:
            return 'warning';
        default:
            return undefined;
    }
});

const jobAddressMessageFromValidationState = computed((): string | undefined => {
    switch (highestSentimentFromValidationState.value) {
        case AlertLevel.Error:
            return errorState.value;
        case AlertLevel.Warning:
            return warningState.value;
        default:
            return undefined;
    }
});

//
// Watchers
//
watch(
    showDialog,
    (): void => {
        if (showDialog.value) {
            initPopupAddress();
        }
    },
    { immediate: true }
);

watch(
    () => props.modelValue,
    (value: WtgJobAddressData) => {
        internalValue.value = {
            ...value,
            address: value.address && { ...value.address },
            contact: value.contact && { ...value.contact },
        };
        if (props.inlineEdit) {
            initPopupAddress();
        }
    },
    { immediate: true, deep: true }
);

watch(
    () => popupAddress.value?.companyGuid,
    (value): void => {
        props.dataProvider?.setActiveCompany?.(value!);
    }
);

//
// Event handlers
//
function onPopupApply(): void {
    commitAndCloseAsync();
}

function onCancel(): void {
    showDialog.value = false;
    emit('update:modelValue', prevValue.value ?? createEmptyJobAddressItem());
    internalValue.value = prevValue.value;
}

function onEdit() {
    showDialog.value = true;
    prevValue.value = internalValue.value;
}

function onClear(): void {
    clearAddress();
    clearContact();
}

function onPopupAddressChanged(value: AddressItem) {
    popupAddress.value = value;
    clearContact();
    loadProviderAddressAndContactAsync(value);
}

function onPopupContactChanged(value: ContactItem) {
    if (isRealAddressOnlyMode.value && !value.name) {
        clearContact();
    } else if (popupContact.value?.name || value.name || popupContactSelected.value) {
        popupContact.value = value;
        popupContactSelected.value = true;
    } else {
        if (!popupAddress.value) {
            popupAddress.value = {};
        }
        popupAddress.value.phone = value.phone;
        popupAddress.value.mobile = value.mobile;
        popupAddress.value.email = value.email;
    }
    loadProviderAddressAndContactAsync();
}

function onAddressFieldChanged(value?: AddressItem | string): void {
    const address = value as AddressItem;

    internalValue.value = { address, contact: undefined, isAddressOverriden: address?.isAddressOverriden ?? false };
    providerAddress.value = address;
    providerContact.value = undefined;
    loadProviderAddressAndContactAsync();

    emit('update:modelValue', internalValue.value);
}

//
// Helpers
//
function initPopupAddress(): void {
    popupAddress.value = { ...(internalValue.value?.address as AddressItem) };
    popupContact.value = { ...(internalValue.value?.contact as ContactItem) };

    loadProviderAddressAndContactAsync();
}

async function loadProviderAddressAndContactAsync(associatedContacts?: AddressItem) {
    internalLoading.value = true;
    try {
        if (popupAddress.value?.guid) {
            if (providerAddress.value?.guid !== popupAddress.value?.guid) {
                const address = (
                    await props.dataProvider?.companyProvider?.getItemForValueAsync(popupAddress.value.guid)
                )?.address;
                providerAddress.value = address;
            }
        } else {
            providerAddress.value = undefined;
        }

        if (popupContact.value?.name) {
            const providerContactValue = (
                await props.dataProvider?.contactProvider?.getItemForValueAsync(popupContact.value.name)
            )?.contact;
            if (providerContactValue) {
                providerContact.value = providerContactValue;
            } else if (associatedContacts !== undefined) {
                providerContact.value = { ...associatedContacts };
            }
        }
    } finally {
        internalLoading.value = false;
    }
}

function clearAddress(): void {
    popupAddress.value = {};
    providerAddress.value = undefined;
}

function clearContact(): void {
    popupContact.value = {};
    providerContact.value = undefined;
    popupContactSelected.value = false;
}

async function commitAndCloseAsync(): Promise<void> {
    internalLoading.value = true;
    await waitForPendingCommits();

    if (popupAddress.value && !isEmptyObj(popupAddress.value)) {
        internalValue.value = {};
        internalValue.value.address = popupAddress.value;
        internalValue.value.contact = popupContact.value;
        internalValue.value.isAddressOverriden = isPopupAddressOverriden.value;
    } else {
        internalValue.value = undefined;
    }
    addressOverrideDescription.value = popupAddressOverrideDescription.value;

    setTimeout(() => {
        internalLoading.value = false;
        if (!hasErrors.value) {
            showDialog.value = false;
        }
    }, 500);

    emit('update:modelValue', internalValue.value);
}

function createEmptyJobAddressItem(): WtgJobAddressData {
    return {
        address: {
            guid: undefined,
            companyGuid: undefined,
            company: undefined,
            street: undefined,
            streetAlt: undefined,
            city: undefined,
            state: undefined,
            postcode: undefined,
            countryCode: undefined,
            phone: undefined,
            mobile: undefined,
            email: undefined,
        },
        contact: {
            guid: undefined,
            name: undefined,
            phone: undefined,
            mobile: undefined,
            email: undefined,
        },
        isAddressOverriden: false,
    };
}

function isEmptyObj(object: AddressLookupSearchContent) {
    for (const property in object) {
        if (Object.prototype.hasOwnProperty.call(object, property)) {
            return false;
        }
    }
    return true;
}

function checkOverride(
    overrides: Array<ChangeOverride>,
    from: string | undefined,
    to: string | undefined,
    caption: string
) {
    from = from || '';
    to = to || '';
    if (from !== to) {
        overrides.push({
            caption,
            from,
            to,
        });
    }
}
</script>

<style lang="scss">
.wtg-job-address-modal {
    background-color: var(--s-neutral-bg-default);
    border-radius: var(--s-radius-m);
    color: var(--s-neutral-txt-default);
    max-width: 1000px;
    overflow: auto;
    display: flex;
    flex-direction: column;

    &-title {
        border: 1px solid var(--s-neutral-border-weak-default);
        border-bottom: none;
        border-style: solid solid none solid;
        padding: var(--s-padding-xl);
    }
    &-actions {
        border: 1px solid var(--s-neutral-border-weak-default);
        border-top: none;
        border-style: none solid solid solid;
        display: flex;
        justify-content: flex-end;
        gap: var(--s-padding-m);
        padding: var(--s-padding-xl);
    }
}
</style>
