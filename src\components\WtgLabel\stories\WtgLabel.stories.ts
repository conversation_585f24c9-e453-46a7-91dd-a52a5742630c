import WtgLabel, { createTypographyClasses } from '@components/WtgLabel/';
import { Meta, StoryObj } from '@storybook/vue3';
import { ComponentProps } from 'vue-component-type-helpers';

type LabelPropsAndCustomArgs = ComponentProps<typeof WtgLabel> & { default?: string };
type Story = StoryObj<LabelPropsAndCustomArgs>;

const meta: Meta<LabelPropsAndCustomArgs> = {
    title: 'Components/Label',
    component: WtgLabel,
    parameters: {
        docs: {
            description: {
                component: 'The label component is an easy-to-use utility component for formatting text.',
            },
        },
        slots: {
            default: {
                text: 'Lorem ipsum dolor sit amet.',
            },
        },
    },
    render: (args) => ({
        components: { WtgLabel },
        setup: () => ({ args }),
        methods: {},
        template: `
            <WtgLabel v-bind="args">
                {{ args.default }}
            </WtgLabel>`,
    }),
    argTypes: {
        display: {
            options: ['', 'block', 'inline'],
            control: 'select',
        },
        fontWeight: {
            options: ['', 'black', 'bold', 'medium', 'regular', 'light', 'thin'],
            control: 'select',
        },
        typography: {
            options: [
                '',
                ...createTypographyClasses()
                    .sort()
                    .map((typographyClass) => typographyClass.text),
            ],
            control: 'select',
        },
        align: {
            options: ['left', 'center', 'right'],
            control: 'select',
        },

        default: {
            control: 'text',
        },
    },
} as Story;
export default meta;

export const Default: Story = {
    args: {
        default: 'Lorem ipsum dolor sit amet.',
    },
    render: (args: LabelPropsAndCustomArgs) => ({
        components: { WtgLabel },
        setup: () => ({ args }),
        template: `
            <WtgLabel v-bind="args">
                {{ args.default }}
            </WtgLabel>`,
    }),
};
