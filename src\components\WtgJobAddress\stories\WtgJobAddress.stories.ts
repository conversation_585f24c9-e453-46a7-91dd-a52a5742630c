import WtgCol from '@components/WtgCol';
import WtgJobAddress from '@components/WtgJobAddress/WtgJobAddress.vue';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgRow from '@components/WtgRow';
import { ValidationState } from '@composables';
import { inputArgTypes } from '@composables/input';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { computed, ref } from 'vue';
import { dataProvider, db } from '../__tests__/WtgJobAddressDataProvider';
import { JobAddressEditMode, WtgJobAddressData } from '../types';

const meta: Meta<typeof WtgJobAddress> = {
    title: 'Components/Job Address',
    component: WtgJobAddress,
    parameters: {
        docs: {
            description: {
                component: 'Job address is used to select an address and a contact linked to a company.',
            },
        },
    },
    render: (args) => ({
        components: { WtgJobAddress },
        setup: () => {
            const validationStates = computed(() => {
                return currentValidationStates.value;
            });
            const modelValue = computed(() => {
                return currentModelValue.value;
            });
            return { args, validationStates, modelValue };
        },
        methods: {
            onUpdate(value: WtgJobAddressData) {
                currentModelValue.value = value;
                if (!value || !value.address) {
                    currentValidationStates.value = getEmptyValidationStates();
                    return;
                }

                const validationStates: ValidationState[] = [];
                for (const bindingProp of bindingProperties) {
                    if (bindingProp === 'E2_CompanyName' && isEmpty(value.address?.company)) {
                        validationStates.push(getAlertValidationState(bindingProp, 'Company name is required'));
                    } else if (bindingProp === 'E2_Address1' && isEmpty(value.address?.street)) {
                        validationStates.push(getAlertValidationState(bindingProp, 'Address Line 1 is required'));
                    } else if (bindingProp === 'E2_City' && isEmpty(value.address?.city)) {
                        validationStates.push(getAlertValidationState(bindingProp, 'City is required'));
                    } else if (bindingProp === 'E2_RN_NKCountryCode' && isEmpty(value.address?.countryCode)) {
                        validationStates.push(getAlertValidationState(bindingProp, 'Country is required'));
                    } else {
                        validationStates.push(getEmptyValidationState(bindingProp));
                    }
                }
                currentValidationStates.value = validationStates;
            },
        },
        template: `<div style="width: 337px"><WtgJobAddress v-bind="args" :modelValue="modelValue" :validationStates="validationStates" @update:model-value="onUpdate" ></WtgJobAddress></div>`,
    }),
    argTypes: { ...inputArgTypes },
};

export default meta;

type Story = StoryObj<typeof WtgJobAddress>;

db.allowFilteringContactsByCompany = true;
dataProvider.setActiveCompany(db.addresses[0].companyGuid!);
const currentValidationStates = ref<ValidationState[]>();
const currentModelValue = ref<WtgJobAddressData>({
    address: db.addresses[0],
    contact: db.contacts[0],
});
const bindingProperties = [
    'E2_OA_Address',
    'E2_CompanyName',
    'E2_Address1',
    'E2_Address2',
    'E2_Postcode',
    'E2_City',
    'E2_State',
    'E2_RN_NKCountryCode',
    'E2_Contact',
    'E2_Phone',
    'E2_Mobile',
    'E2_Email',
    'E2_AdditionalAddressInformation',
];
const isEmpty = (value: string | undefined) => {
    return !value || value === '';
};
const getEmptyValidationState = (bindingProp: string) => {
    return {
        alertLevel: 0,
        targetKey: undefined,
        targetProperty: bindingProp,
        messages: [],
        error: false,
        warning: false,
    } as ValidationState;
};
const getAlertValidationState = (bindingProp: string, errorMessage: string) => {
    return {
        alertLevel: 4,
        targetKey: undefined,
        targetProperty: bindingProp,
        messages: [errorMessage],
        error: true,
        warning: false,
    } as ValidationState;
};

const getEmptyValidationStates = () => {
    const validationStates: ValidationState[] = [];
    for (const bindingProp of bindingProperties) {
        validationStates.push(getEmptyValidationState(bindingProp));
    }
    return validationStates;
};

export const Info: Story = {
    args: {
        label: 'Organisation Address',
        info: 'Some help information',
        dataProvider,
        validationPropertyMapping: {
            company: 'E2_CompanyName',
            street: 'E2_Address1',
            streetAlt: 'E2_Address2',
            city: 'E2_City',
            state: 'E2_State',
            postcode: 'E2_Postcode',
            countryCode: 'E2_RN_NKCountryCode',
        },
    },
};

export const Sentiments: Story = {
    args: {
        dataProvider,
    },
    render: (args) => ({
        components: { WtgJobAddress, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
        <WtgRow>
        <WtgCol style="max-width: 337px; gap: 8px;" class="d-flex flex-column col-md-3">
            <WtgLayoutGrid>
                <WtgJobAddress v-bind="args" 
                    label="Default"
                    :modelValue="modelValue" >
                ></WtgJobAddress>
                <WtgJobAddress v-bind="args" 
                    label="Success" 
                    sentiment="success" 
                    messages="Sample success message"             
                    :modelValue="modelValue">
                ></WtgJobAddress>
                <WtgJobAddress v-bind="args" 
                    label="Warning" 
                    sentiment="warning" 
                    messages="Sample warning message"             
                    :modelValue="modelValue">
                ></WtgJobAddress>
                <WtgJobAddress v-bind="args" 
                    label="Error" 
                    sentiment="critical" 
                    messages="Sample error message"             
                    :modelValue="modelValue">
                ></WtgJobAddress>
                <WtgJobAddress v-bind="args" 
                    label="Info" 
                    sentiment="info" 
                    messages="Sample info message"             
                    :modelValue="modelValue">
                ></WtgJobAddress>
            </WtgLayoutGrid>
        </WtgCol>
    </WtgRow>`,
    }),
};

export const RealAddressAndFreeText: Story = {
    args: {
        label: 'Organisation Address',
        dataProvider,
        validationPropertyMapping: {
            company: 'E2_CompanyName',
            street: 'E2_Address1',
            streetAlt: 'E2_Address2',
            city: 'E2_City',
            state: 'E2_State',
            postcode: 'E2_Postcode',
            countryCode: 'E2_RN_NKCountryCode',
        },
    },
};

export const RealAddressOnly: Story = {
    args: {
        ...RealAddressAndFreeText.args,
        addressEditMode: JobAddressEditMode.RealAddressOnly,
        validationPropertyMapping: {
            company: 'E2_CompanyName',
            street: 'E2_Address1',
            streetAlt: 'E2_Address2',
            city: 'E2_City',
            state: 'E2_State',
            postcode: 'E2_Postcode',
            countryCode: 'E2_RN_NKCountryCode',
        },
    },
};
