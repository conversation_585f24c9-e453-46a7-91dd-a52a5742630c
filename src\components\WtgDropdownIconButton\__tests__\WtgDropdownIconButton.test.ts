import WtgDropdownIconButton from '@components/WtgDropdownIconButton';
import WtgIconButton from '@components/WtgIconButton';
import WtgPopover from '@components/WtgPopover';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgDropdownIconButton', () => {
    test('it renders an IconButton', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent(WtgIconButton).exists()).toBe(true);
    });

    test('it passes its props to WtgIconButton', () => {
        const wrapper = mountComponent({
            propsData: {
                color: 'red',
                disabled: true,
                icon: 's-icon-placeholder',
                iconSize: 's',
                sentiment: 'primary',
                size: 'xxl',
                tooltip: 'Some Tooltip',
                variant: 'ghost',
            },
        });
        const iconButton = wrapper.findComponent(WtgIconButton);
        expect(iconButton.props('color')).toBe('red');
        expect(iconButton.props('disabled')).toBe(true);
        expect(iconButton.props('icon')).toBe('s-icon-placeholder');
        expect(iconButton.props('iconSize')).toBe('s');
        expect(iconButton.props('sentiment')).toBe('primary');
        expect(iconButton.props('size')).toBe('xxl');
        expect(iconButton.props('tooltip')).toBe('Some Tooltip');
        expect(iconButton.props('variant')).toBe('ghost');
    });

    test('it has tooltip capability mixed in', () => {
        const wrapper = mountComponent({
            propsData: { tooltip: { content: 'Some tooltip', placement: 'left' } },
        });

        const vm: any = wrapper.vm;
        expect(vm.tooltipDirective.content).toBe('Some tooltip');
        expect(vm.tooltipDirective.placement).toBe('left');
    });

    test('it renders a WtgPopover', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent(WtgPopover).exists()).toBe(true);
    });

    test('it passes location: top right to WtgPopover when openPosition is set to top', () => {
        const wrapper = mountComponent({ propsData: { openPosition: 'top' } });
        expect(wrapper.findComponent({ name: 'WtgPopover' }).props('location')).toBe('top right');
    });

    test('it passes location: bottom right to WtgPopover when openPosition is set to bottom', () => {
        const wrapper = mountComponent({ propsData: { openPosition: 'bottom' } });
        expect(wrapper.findComponent({ name: 'WtgPopover' }).props('location')).toBe('bottom right');
    });

    test('it passes the popover slot content to be rendered', async () => {
        document.body.innerHTML = `<div id="app" data-app></div>`;
        const wrapper = mountComponent();
        wrapper.element.setAttribute('data-app', 'true');
        await wrapper.vm.$nextTick();

        const iconButton = wrapper.findComponent({ name: 'WtgIconButton' });
        await iconButton.trigger('click');

        const menu = wrapper.findComponent({ name: 'v-menu' });
        expect(menu.exists()).toBe(true);
        expect(document.body.innerHTML).toContain('Popover Item');
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgDropdownIconButton, {
            propsData,
            slots: {
                popover: '<div>Popover Item</div>',
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
