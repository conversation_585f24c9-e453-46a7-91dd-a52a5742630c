# Use Vue Single File Components (SFCs)

## Status
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

In Vue.js, a Single File Component (SFC) is a powerful feature that allows you to encapsulate all the parts of a Vue component (HTML, CSS, and JavaScript) into a single .vue file. This is different from using Vue JSX, which allows you to write Vue components using JavaScript syntax similar to React's JSX.

## Decision

## Status
- [ ] Proposed
- [x] Accepted
- [ ] Rejected
- [ ] Deprecated
- [ ] Superseded

In general, Vue SFC is the more common and widely used in the Vue.js ecosystem, and for most projects, it’s the preferred approach. Due to the broad developer base and skill level of WiseTech developers is suits our purpose as it is often closer to traditional HTML\CSS\JS web development paradigms.

## Consequences

Pros:
1. Clear separation of concerns: The HTML, JavaScript, and CSS are neatly organized in one file, making it easy to manage.
2. Ease of use: Most developers are familiar with HTML, JavaScript, and CSS, so SFCs are easy to adopt.
3. Vue-specific syntax: You use <PERSON>ue's template syntax directly, which includes directives like v-bind, v-for, v-if, etc., and Vue's reactive data binding.
4. Scoped CSS: You can scope your styles to the component by adding scoped to the style tag, which ensures that styles don't leak outside of the component.
6. Onboarding: They are beneficial for onboarding new team members, helping them understand the reasoning behind architectural choices.

Cons:
1. Vue SFC technology is still catching up to the latest and greates typescript has to offer meaning there can be some gaps with developer expectations of compiler support.

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.
