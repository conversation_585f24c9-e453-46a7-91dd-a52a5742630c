import { AddressLookupSearchContent } from '../types';

const companies = [
    { id: '1', name: 'WiseTech Global', code: 'WTG' },
    { id: '2', name: 'Ant Tech', code: 'ANT' },
    { id: '3', name: 'Demo Tech', code: 'DMT' },
    { id: '4', name: '7-Eleven', code: '711' },
    { id: '5', name: 'Test Tech', code: 'TST' },
    { id: '6', name: 'Bear Tech', code: 'BTC' },
    { id: '7', name: 'Armour Tech', code: 'ACT' },
    { id: '8', name: 'Binary Tech', code: 'BIT' },
];
const addresses: AddressLookupSearchContent[] = [
    {
        guid: '1',
        companyGuid: companies[0].id,
        company: companies[0].name,
        companyCode: companies[0].code,
        code: '1LO',
        street: `Unit 3A, 72 O'Riordan Street`,
        city: 'Alexandria',
        postcode: '2015',
        state: 'NSW',
        countryCode: 'AU',
        isActive: true,
        email: '<EMAIL>',
        phone: '12345678',
        mobile: '12345678',
    },
    {
        guid: '2',
        companyGuid: companies[1].id,
        company: companies[1].name,
        companyCode: companies[1].code,
        code: '1YES',
        street: '1 Yes st',
        city: 'SunTown',
        postcode: '2017',
        state: 'NSW',
        countryCode: 'AU',
        isActive: true,
    },
    {
        guid: '3',
        companyGuid: companies[2].id,
        company: companies[2].name,
        companyCode: companies[2].code,
        code: 'DMT',
        street: '37 Stonehenge Dr',
        city: 'Harkness',
        postcode: '3337',
        state: 'VIC',
        countryCode: 'AU',
        isActive: true,
    },
    {
        guid: '4',
        companyGuid: companies[3].id,
        company: companies[3].name,
        companyCode: companies[3].code,
        code: '711',
        street: '2 Work st',
        city: 'Eltown',
        postcode: '2027',
        state: 'QLD',
        countryCode: 'AU',
        isActive: true,
    },
    {
        guid: '5',
        companyGuid: companies[4].id,
        company: companies[4].name,
        companyCode: companies[4].code,
        code: 'TST',
        street: `Unit 3A, 72 O'Riordan Street`,
        city: 'Alexandria',
        postcode: '2015',
        state: 'NSW',
        countryCode: 'AU',
        isActive: true,
    },
    {
        guid: '6',
        companyGuid: companies[5].id,
        company: companies[5].name,
        companyCode: companies[5].code,
        code: 'BTC',
        street: '1 Yes st',
        city: 'SunTown',
        postcode: '2017',
        state: 'NSW',
        countryCode: 'AU',
        isActive: true,
    },
    {
        guid: '7',
        companyGuid: companies[6].id,
        company: companies[6].name,
        companyCode: companies[6].code,
        code: 'ACT',
        street: '3 Mall st',
        city: 'Tempe',
        postcode: '2060',
        state: 'VIC',
        countryCode: 'AU',
        isActive: true,
    },
    {
        guid: '8',
        companyGuid: companies[7].id,
        company: companies[7].name,
        companyCode: companies[7].code,
        code: 'BIT',
        street: '2 Work st',
        city: 'Eltown',
        postcode: '2027',
        state: 'QLD',
        countryCode: 'AU',
        isActive: true,
    },
];

export default addresses;
