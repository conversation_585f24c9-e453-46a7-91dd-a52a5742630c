import WtgChart from '@components/WtgChart/WtgChart.vue';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { Chart, ChartData, ChartOptions, LinearScale, LineElement, PointElement, ScatterController } from 'chart.js';
import { defineComponent, h, PropType, VNode } from 'vue';

Chart.register(LinearScale, LineElement, PointElement, ScatterController);

export default defineComponent({
    name: 'WtgScatterChart',
    props: {
        data: {
            type: Object as PropType<ChartData>,
            default: (): ChartData => {
                return {
                    datasets: [],
                };
            },
        },
        options: {
            type: Object as PropType<ChartOptions>,
            default: (): ChartOptions => {
                return {};
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    setup(props) {
        useLayoutGridColumn(props);
    },
    render(): VNode {
        return h(WtgChart, {
            type: 'scatter',
            data: this.data,
            options: this.options,
            loading: this.loading,
        });
    },
});
