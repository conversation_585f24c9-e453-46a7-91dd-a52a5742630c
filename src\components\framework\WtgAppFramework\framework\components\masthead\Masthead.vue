<!-- eslint-disable vue/multi-word-component-names -->
<template>
    <WtgMasthead
        v-if="!application.hideAppBar"
        class="wtg-typography"
        :aria-label="application.ariaLabels.masthead"
        :extension-height="extensionHeight"
        :order="3"
    >
        <MastheadSearch
            v-if="application.searchProvider"
            :overlay="searchOverlay"
            @update:overlay="searchOverlay = $event"
        />
        <template v-if="isMobile">
            <div class="wtg-masthead--mobile__leading">
                <WtgIconButton
                    :aria-label="navigationIconAriaLabel"
                    variant="ghost"
                    icon="s-icon-menu-hamburger"
                    :style="navButtonIconStyle"
                    @click="onNavigationIconClick"
                />
                <WtgIconButton
                    v-if="showBackButton"
                    data-testid="mobile-masthead-back-button"
                    variant="ghost"
                    icon="$arrowLeft"
                    :aria-label="application.ariaLabels.backButton"
                    @click="onTaskCloseClick"
                />
                <MastheadTitle />
            </div>
            <div class="wtg-masthead--mobile__trailing-icon-buttons">
                <MobileMastheadMenu
                    v-if="showMobileMastheadMenu"
                    :task="currentTask"
                    @toggle-search="searchOverlay = !searchOverlay"
                />
            </div>
        </template>
        <template v-else>
            <WtgIconButton
                v-if="showNavigationIcon"
                :aria-label="navigationIconAriaLabel"
                variant="ghost"
                icon="s-icon-menu-hamburger"
                :style="navButtonIconStyle"
                @click="onNavigationIconClick"
            />
            <MastheadTitle />
            <WtgSpacer />
            <MastheadCreationMenu />
            <WtgIconButton
                v-if="application.searchProvider"
                icon="s-icon-search"
                :aria-label="application.ariaLabels.searchDialogIcon"
                aria-haspopup="dialog"
                :aria-expanded="searchOverlay ? 'true' : 'false'"
                data-testid="search-button"
                @click="searchOverlay = !searchOverlay"
            />
            <MastheadNotifications v-show="showNotifications" />
        </template>
        <template v-if="showMastheadExtension" #extension>
            <MastheadExtension />
        </template>
    </WtgMasthead>
</template>

<script setup lang="ts">
import {
    WtgFrameworkTabsInfo,
    WtgFrameworkTask,
    WtgFrameworkTaskGenericActionPlacement,
} from '@components/framework/types';
import WtgIconButton from '@components/WtgIconButton';
import WtgMasthead from '@components/WtgMasthead';
import WtgSpacer from '@components/WtgSpacer';
import { hasTaskActions, useAppLevelTabs, useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { computed, ref, StyleValue } from 'vue';
import MastheadCreationMenu from './MastheadCreationMenu';
import MastheadExtension from './MastheadExtension';
import MastheadNotifications from './MastheadNotifications';
import MastheadTitle from './MastheadTitle';
import MobileMastheadMenu from './MobileMastheadMenu';
import MastheadSearch from './MastheadSearch';

const application = useApplication();
const { isMobile, isTabletOrMobile, framework } = useFramework();
const showTaskActions = hasTaskActions();

const atHomePage = computed((): boolean => {
    const currentMenuItem = application.menu.find((item) => item.active && item.home);
    return currentMenuItem ? true : false;
});

const appLevelTabs = useAppLevelTabs();
const tabInfo = computed((): WtgFrameworkTabsInfo => {
    return (
        appLevelTabs.value ?? {
            tabs: [],
            current: 0,
            visible: false,
        }
    );
});

const currentTask = computed((): WtgFrameworkTask => {
    return application.currentTask ?? new WtgFrameworkTask();
});

const extensionHeight = computed((): number => {
    if (isMobile.value) {
        return 36;
    }
    let height = 48;
    if (tabInfo.value.tabs.length > 0 && currentTask.value.showTaskTitle) {
        height += 40;
    }
    return height;
});

const showNavigationIcon = computed((): boolean => {
    return isTabletOrMobile.value;
});

const navButtonIconStyle = computed((): StyleValue => {
    return isMobile.value
        ? { marginLeft: framework.value === 'mobile' ? undefined : '8px' }
        : {
              color: 'var(--s-primary-icon-default)',
          };
});

const navigationIconAriaLabel = computed((): string => {
    return application.ariaLabels.navigationDrawerIcon;
});

const onNavigationIconClick = (): void => {
    application.navDrawer.open();
};

const showTaskAction = computed((): boolean => {
    return (
        (currentTask.value.showEDocsAction?.visible ||
            currentTask.value.showLogsAction?.visible ||
            currentTask.value.documents?.visible ||
            currentTask.value.showWorkflowActions?.visible ||
            currentTask.value.showMessagesAction?.visible ||
            currentTask.value.showNotesAction?.visible ||
            currentTask.value.genericActions.some(
                (action) => action.placement === WtgFrameworkTaskGenericActionPlacement.TaskAction
            )) ??
        false
    );
});

const showMastheadExtension = computed((): boolean => {
    if (isMobile.value) {
        return tabInfo.value.visible;
    }
    return currentTask.value.showTaskTitle || showTaskAction.value || tabInfo.value.visible;
});

const showBackButton = computed((): boolean => {
    return (
        currentTask.value.showTaskTitle &&
        !atHomePage.value &&
        !!currentTask.value.showBackButton &&
        !application.hideBackButton
    );
});

const onTaskCloseClick = (): void => {
    if (currentTask.value?.cancelAction.onInvoke) {
        currentTask.value?.cancelAction.onInvoke();
    }
};
const showNotifications = computed((): boolean => {
    return currentTask.value?.showNotifications;
});

const showMobileMastheadMenu = computed((): boolean => {
    return showNotifications.value || showTaskActions.value || !!application.searchProvider;
});

const searchOverlay = ref(false);
</script>

<style>
.wtg-masthead--mobile__leading {
    display: flex;
    align-items: center;
    gap: var(--s-spacing-xs);
    flex: 1 0 0;
    overflow: hidden;
}
.wtg-masthead--mobile__trailing-icon-buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: var(--s-spacing-xs);
}
</style>
