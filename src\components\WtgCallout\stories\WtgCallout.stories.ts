import WtgCallout from '@components/WtgCallout/WtgCallout.vue';
import WtgCol from '@components/WtgCol';
import WtgHyperlink from '@components/WtgHyperlink';
import WtgLabel from '@components/WtgLabel';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgRow from '@components/WtgRow';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { CalloutSandboxTemplate } from './templates/wtg-callout-sandbox.stories-template';

type Story = StoryObj<typeof WtgCallout>;

const meta: Meta<typeof WtgCallout> = {
    title: 'Components/Callout',
    component: WtgCallout,
    parameters: {
        docs: {
            description: {
                component:
                    'Callouts are part of the Alerts component family. They are used to communicate important information within a screen or specific section of a screen. They are embedded in the UI and sit inline with content. Callouts can be either persistent or dismissible, depending on the scenario.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?node-id=305-20913&t=YE048JaYx5MCHKr1-0',
        },
        layout: 'centered',
    },

    render: (args) => ({
        components: { WtgCallout },
        setup: () => ({ args }),
        methods: {
            closeAction: action('close'),
        },
        template: '<WtgCallout v-bind="args" @close="closeAction"></WtgCallout>',
    }),
    argTypes: {
        sentiment: {
            options: ['info', 'success', 'warning', 'critical'],
            control: { type: 'select' },
        },
        variant: {
            options: ['default', 'inline'],
            control: { type: 'select' },
        },
    },
};

export default meta;

// More on writing stories with args: https://storybook.js.org/docs/vue/writing-stories/args
export const Default: Story = {
    args: {
        dismissible: true,
        sentiment: 'info',
        title: 'Callout title',
        description: 'Description of important information',
    },
    render: (args) => ({
        components: { WtgCallout, WtgRow, WtgCol },
        data() {
            return {
                key: 0, // Unique key to force re-render
            };
        },
        methods: {
            resetCallout() {
                this.key++; // Increment key to reset the component
                action('Callout dismissed and reset')();
            },
        },
        setup: () => ({ args }),
        template: `
        <div style="gap: 8px; width: 400px;" class="d-flex flex-column align-start">
            <WtgCallout v-bind="args" :key="key" @close="resetCallout"></WtgCallout>
        </div>`,
    }),
};

export const Inline: Story = {
    args: {
        description: 'Hello, I am an example of an inline callout',
    },

    render: (args) => ({
        components: { WtgCallout, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <div style="gap: 8px;" class="d-flex flex-column align-start">
            <WtgCallout @close="closeAction" sentiment="info" title="Info" variant="inline" v-bind="args"></WtgCallout>
            <WtgCallout @close="closeAction" sentiment="success" title="Success" variant="inline" v-bind="args"></WtgCallout>
            <WtgCallout @close="closeAction" sentiment="warning" title="Warning" variant="inline" v-bind="args"></WtgCallout>
            <WtgCallout @close="closeAction" sentiment="critical" title="Critical" variant="inline" v-bind="args"></WtgCallout>
        </div>`,
    }),
};

export const Sentiments: Story = {
    args: {
        description: 'Hello, I am an example of a default callout',
    },
    render: (args) => ({
        components: { WtgCallout, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <div style="gap: 8px; width: 400px;" class="d-flex flex-column align-start">
            <WtgCallout @close="closeAction" sentiment="info" title="Info" v-bind="args"></WtgCallout>
            <WtgCallout @close="closeAction" sentiment="success" title="Success" v-bind="args"></WtgCallout>
            <WtgCallout @close="closeAction" sentiment="warning" title="Warning" v-bind="args"></WtgCallout>
            <WtgCallout @close="closeAction" sentiment="critical" title="Critical" v-bind="args"></WtgCallout>
        </div>`,
    }),
};

export const Success: Story = {
    args: {
        description: 'Description of important information',
        sentiment: 'success',
        title: 'Success',
    },
    tags: ['!dev'],
    render: (args) => ({
        components: { WtgCallout, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <div style="gap: 8px; width: 400px;" class="d-flex flex-column align-start">
            <WtgCallout @close="closeAction" v-bind="args" style="width: 100%"></WtgCallout>
        </div>`,
    }),
};

export const Info: Story = {
    args: {
        description: 'Description of important information',
        sentiment: 'info',
        title: 'Info',
    },
    tags: ['!dev'],
    render: (args) => ({
        components: { WtgCallout, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <div style="gap: 8px; width: 400px;" class="d-flex flex-column align-start">
            <WtgCallout @close="closeAction" v-bind="args" style="width: 100%"></WtgCallout>
        </div>`,
    }),
};

export const Warning: Story = {
    args: {
        description: 'Description of important information',
        sentiment: 'warning',
        title: 'Warning',
    },
    tags: ['!dev'],
    render: (args) => ({
        components: { WtgCallout, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <div style="gap: 8px; width: 400px;" class="d-flex flex-column align-start">
            <WtgCallout @close="closeAction" sentiment="success" title="Success" v-bind="args" style="width: 100%"></WtgCallout>
        </div>`,
    }),
};

export const Critical: Story = {
    args: {
        description: 'Description of important information',
        sentiment: 'critical',
        title: 'Critical',
    },
    tags: ['!dev'],
    render: (args) => ({
        components: { WtgCallout, WtgRow, WtgCol },
        setup: () => ({ args }),
        template: `
        <div style="gap: 8px; width: 400px;" class="d-flex flex-column align-start">
            <WtgCallout @close="closeAction" sentiment="success" title="Success" v-bind="args" style="width: 100%"></WtgCallout>
        </div>`,
    }),
};

export const Sandbox: Story = {
    args: {
        title: 'Callout Title',
        description: 'Description of important information',
    },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgCallout, WtgCol, WtgRow, WtgLabel, WtgLayoutGrid, WtgHyperlink },
        setup: () => ({ args }),
        methods: {},
        template: templateWithRtl(CalloutSandboxTemplate),
    }),
};
