# Standardize file naming conventions and folder structure for components

## Status
**Status**: Proposed  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

As the Supply design system grows, a consistent folder and file structure is essential for scalability, discoverability, and ease of contribution. Without structure, components risk becoming difficult to maintain or navigate—especially across a large team.

This ADR proposes a standardized approach to organizing components within the `src` directory and clarifies when to use the `Wtg*` naming convention, which indicates whether a component is intended for public use or internal-only use.

## Decision

- Each component is placed in its own folder under the `src` directory.
- Published components follow the `Wtg*` naming convention (e.g. `WtgButton.vue`, `WtgDatePicker.vue`).
- If a component folder contains additional subcomponents:
  - These also use the `Wtg*` prefix if they are published externally.
  - Internal-only components (used only within their parent component) do **not** require the `Wtg*` prefix.
- This approach applies consistently across all core and utility components in the library.

Example structure:

```
src/
  WtgMenuBar/
    WtgMenuBar.vue
    PopupMenuList.vue            ← internal use, no prefix
  WtgPanel/
    WtgPanel.vue
    WtgPanelHeader.vue  ← public, prefixed
```

## Consequences

### Benefits:
- Predictable and discoverable structure that scales well as the library grows.
- Reinforces the separation between public and internal components.
- Encourages encapsulation by co-locating related components.
- Aligns with typical practices in Vue projects using component libraries.

### Trade-offs / Risks:
- Slight overhead in folder depth, especially for simple components.
- Requires clear documentation and discipline to avoid misuse of the `Wtg*` prefix.
- May require migration of older components to match the structure.

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.