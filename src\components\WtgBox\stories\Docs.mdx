import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import info from '../../../storybook/assets/info.png';

import { Meta, Title, Description, Story, Canvas, Controls, ArgTypes } from '@storybook/blocks';
import * as WtgBox from './WtgBox.stories.ts';

<Meta of={WtgBox} />

<div className="component-header">
    <h1>Box</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td></td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

The Box component is a versatile helper and wrapper designed around the standard HTML div element. It streamlines the process of building layouts by abstracting common layout behaviors and styling, making it an essential tool for creating clean and consistent UI structures in web applications.

## Purpose

The primary purpose of the Box component is to simplify the often repetitive and verbose task of managing layout-related HTML and CSS. By wrapping the div element with intuitive properties and preconfigured styles, Box reduces the need for boilerplate code and allows developers to focus more on building application features rather than managing layout intricacies.

## Key features and benefits

<ul>
    <li>
        <b>Simplifies layout management:</b> The Box component abstracts away common layout and style concerns, like
        spacing, alignment, padding, margins, flexbox properties, and grid configurations. Instead of manually adding
        multiple CSS classes or inline styles, developers can leverage intuitive props or attributes on the Box
        component for a cleaner, more declarative approach.
    </li>
    <li>
        <b>Cleaner and more readable remplates:</b> By consolidating layout behaviors into the Box component, Vue
        templates become less cluttered and easier to read, maintain, and update. It reduces the mental overhead of
        interpreting complex or repetitive class names or inline styles, particularly in larger projects.
    </li>
    <li>
        <b>Consistency across applications:</b> The Box component helps enforce consistent layout practices by adhering
        to predefined design tokens, spacing scales, or utility systems in your design system. This consistency leads to
        a unified look and feel across all parts of the application.{' '}
    </li>
    <li>
        <b>Accessible to all skill levels:</b> For new developers, Box acts as a soft landing, providing an approachable
        way to manage layouts without diving into the complexities of raw HTML and CSS. For experienced developers, Box
        offers convenience without being restrictive, enabling them to drop down to raw div elements whenever deeper
        customization or control is required.{' '}
    </li>
    <li>
        <b>Optional but complementary:</b> While it provides clear advantages for simplifying layout and improving
        maintainability, the Box component is entirely optional. Developers who prefer working directly with HTML and
        CSS can bypass it without impacting the overall functionality of the application. Instead of manually specifying
        class, style, and layout properties in a div, the Box component provides a more structured and readable way to
        apply these configurations.{' '}
    </li>
</ul>

## Why use the box component

<ul>
    <li>
        <b>Time savings:</b> Quickly scaffold layouts without needing to recall intricate CSS syntax or utility class
        names.
    </li>
    <li>
        <b>Readability:</b> Make templates more declarative and intuitive for future developers.
    </li>
    <li>
        <b>Scalability:</b> Build layouts that integrate seamlessly with a design system, ensuring your app remains
        scalable and easy to extend.
    </li>
</ul>

## When to skip using box

While the Box component simplifies layout management, experienced developers with specific styling needs or performance considerations may prefer to work directly with raw div elements. This approach is perfectly valid and can provide maximum control where needed.

In essence, the Box component bridges the gap between raw HTML elements and design systems, offering simplicity for beginners and efficiency for seasoned developers. It enhances productivity, maintains consistency, and helps deliver polished, maintainable layouts for modern web applications.

## Related components

-   [Grid Implementation](/docs/guidelines-layout-grid-implementation--overview)
-   [Layout Grid](/docs/utilities-layout-grid--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
