import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgLabel } from '../';
import WtgUi from '../../../WtgUi';
import { createTypographyClasses } from '../types';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgLabel', () => {
    test('when the typography is not specified, it renders a <span> element', () => {
        const wrapper = mountComponent({
            propsData: {
                typography: 'SOME UNSUPPORTED VALUE',
            },
        });
        expect(wrapper.html()).toContain('span');
    });

    createTypographyClasses().forEach((typographyClass) => {
        test('when the typography is specified it applies the framework typography class', async () => {
            const wrapper = mountComponent();

            await wrapper.setProps({ typography: typographyClass.text });
            expect(wrapper.element.tagName).toBe('SPAN');
            expect(wrapper.element.classList).toContain(typographyClass.value);
            expect(wrapper.html()).toContain('Some text');
        });
    });

    test('if typography value is unknown is add the value as a class', async () => {
        const wrapper = mountComponent();

        await wrapper.setProps({ typography: 'unknown-typography' });
        expect(wrapper.element.tagName).toBe('SPAN');
        expect(wrapper.element.classList).toContain('unknown-typography');
        expect(wrapper.html()).toContain('Some text');
    });

    test('when the typography is set to h1, it renders a <h1> element and it applies the supply typography class', () => {
        const wrapper = mountComponent({
            propsData: {
                typography: 'h1',
            },
        });
        expect(wrapper.html()).toContain('h1');
        expect(wrapper.classes()).toContain('wtg-typography-h1');
        expect(wrapper.text()).toBe('Some text');
    });

    test('when the typography is set to h2, it renders a <h2> element and it applies the supply typography class', () => {
        const wrapper = mountComponent({
            propsData: {
                typography: 'h2',
            },
        });
        expect(wrapper.html()).toContain('h2');
        expect(wrapper.classes()).toContain('wtg-typography-h2');
        expect(wrapper.text()).toBe('Some text');
    });

    test('when the typography is set to h3, it renders a <h3> element and it applies the supply typography class', () => {
        const wrapper = mountComponent({
            propsData: {
                typography: 'h3',
            },
        });
        expect(wrapper.html()).toContain('h3');
        expect(wrapper.classes()).toContain('wtg-typography-h3');
        expect(wrapper.text()).toBe('Some text');
    });

    test('when the typography is set to h4, it renders a <h4> element and it applies the supply typography class', () => {
        const wrapper = mountComponent({
            propsData: {
                typography: 'h4',
            },
        });
        expect(wrapper.html()).toContain('h4');
        expect(wrapper.classes()).toContain('wtg-typography-h4');
        expect(wrapper.text()).toBe('Some text');
    });

    test('when the typography is set to h5, it renders a <h5> element and it applies the supply typography class', () => {
        const wrapper = mountComponent({
            propsData: {
                typography: 'h5',
            },
        });
        expect(wrapper.html()).toContain('h5');
        expect(wrapper.classes()).toContain('wtg-typography-h5');
        expect(wrapper.text()).toBe('Some text');
    });

    test('when the typography is set to h6, it renders a <h6> element and it applies the supply typography class', () => {
        const wrapper = mountComponent({
            propsData: {
                typography: 'h6',
            },
        });
        expect(wrapper.html()).toContain('h6');
        expect(wrapper.classes()).toContain('wtg-typography-h6');
        expect(wrapper.text()).toBe('Some text');
    });

    test('when the href property is set, it will wrap the typography element up in an <a> element', () => {
        const wrapper = mountComponent({
            propsData: {
                href: 'www.google.com',
                typography: 'h1',
            },
        });
        expect(wrapper.html()).toContain('a');
        expect(wrapper.html()).toContain('www.google.com');
        expect(wrapper.text()).toBe('Some text');
        expect(wrapper.find('h1').text()).toBe('Some text');
    });

    test('when the noWrap property is set to true, it will add class and styles to stop the label wrapping', () => {
        const wrapper = mountComponent({
            propsData: {
                noWrap: true,
            },
        });
        expect(wrapper.html()).toContain('wtg-label-nowrap');
    });

    test('when the noWrap property is set to false, it will not add class and styles to stop the label wrapping', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.classList).not.toContain('wtg-label-nowrap');
    });

    test('it has a property called italic that adds the font-italic class', async () => {
        const wrapper = mountComponent({
            propsData: {
                italic: true,
            },
        });
        expect(wrapper.html()).toContain('font-italic');

        await wrapper.setProps({
            italic: false,
        });
        expect(wrapper.element.classList).not.toContain('font-italic');
    });

    test('it has a property called display that adds the correct display class', async () => {
        const wrapper = mountComponent({
            propsData: {
                display: 'block',
            },
        });
        expect(wrapper.html()).toContain('d-block');

        await wrapper.setProps({
            display: 'inline',
        });
        expect(wrapper.html()).toContain('d-inline');
    });

    test('it has a property called font-weight that adds the correct font-weight class', async () => {
        const wrapper = mountComponent({
            propsData: {
                fontWeight: 'black',
            },
        });
        expect(wrapper.html()).toContain('font-weight-black');

        await wrapper.setProps({
            fontWeight: 'bold',
        });
        expect(wrapper.html()).toContain('font-weight-bold');

        await wrapper.setProps({
            fontWeight: 'medium',
        });
        expect(wrapper.html()).toContain('font-weight-medium');

        await wrapper.setProps({
            fontWeight: 'light',
        });
        expect(wrapper.html()).toContain('font-weight-light');

        await wrapper.setProps({
            fontWeight: 'thin',
        });
        expect(wrapper.html()).toContain('font-weight-thin');
    });

    test('it has a property called align that adds the correct alignment classes', async () => {
        const wrapper = mountComponent({
            propsData: {
                align: 'left',
            },
        });
        expect(wrapper.html()).toContain('text-left');

        await wrapper.setProps({
            align: 'center',
        });
        expect(wrapper.html()).toContain('text-center');

        await wrapper.setProps({
            align: 'right md-center',
        });
        expect(wrapper.html()).toContain('text-right');
        expect(wrapper.html()).toContain('text-md-center');
    });

    describe('when color is set', () => {
        test('it sets text color and border color to color prop', () => {
            const wrapper = mountComponent({
                propsData: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('text-red');
        });

        test('it sets the element style from vars', () => {
            const wrapper = mountComponent({
                propsData: {
                    color: 'var(--s-error-txt-default)',
                },
            });
            expect((wrapper.vm as any).computedStyle.color).toBe('var(--s-error-txt-default)');
            expect((wrapper.vm as any).computedStyle.caretColor).toBe('var(--s-error-txt-default)');
        });

        test('it sets the element style from rgb', () => {
            const wrapper = mountComponent({
                propsData: {
                    color: 'rgb(255,0,0)',
                },
            });
            expect((wrapper.vm as any).computedStyle.color).toBe('rgb(255,0,0)');
            expect((wrapper.vm as any).computedStyle.caretColor).toBe('rgb(255,0,0)');
        });

        test('it adds the color as a class when the resolved color is invalid', () => {
            const wrapper = mountComponent({
                propsData: {
                    color: 'brand',
                },
            });
            expect(wrapper.classes()).toContain('text-brand');
        });
    });

    test('it does not add the wtg-cursor-pointer class if the label does not have a click event defined', () => {
        const wrapper = mountComponent();
        expect(wrapper.html()).not.toContain('wtg-cursor-pointer');
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    describe('when the label is clickable', () => {
        it('adds wtg-cursor-pointer class when onClick is present', () => {
            const wrapper = mountComponent({
                propsData: {
                    onClick: () => {},
                },
            });
            const label = wrapper.find('.wtg-label');
            expect(label.classes()).toContain('wtg-cursor-pointer');
        });

        it('does not add wtg-cursor-pointer class when onClick is not present', () => {
            const wrapper = mountComponent();
            const label = wrapper.find('.wtg-label');
            expect(label.classes()).not.toContain('wtg-cursor-pointer');
        });
    });

    function mountComponent({ propsData = {}, provide = {} } = {}) {
        return mount(WtgLabel, {
            propsData,
            slots: {
                default: 'Some text',
            },
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
