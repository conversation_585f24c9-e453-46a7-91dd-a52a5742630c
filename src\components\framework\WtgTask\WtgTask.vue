<template>
    <div>
        <slot />
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { useApplication } from '@composables/application';
import { WtgFrameworkTask } from '@components/framework/types';

export default defineComponent({
    name: 'WtgTask',
    props: {
        task: {
            type: Object as PropType<WtgFrameworkTask>,
            default: (): WtgFrameworkTask => new WtgFrameworkTask(),
        },
    },
    setup() {
        const application = useApplication();
        return { application };
    },
    created() {
        this.application.currentTask = this.task;
    },
    beforeUnmount() {
        this.application.currentTask = null;
    },
});
</script>
