<template>
    <div :class="displayClasses">
        <slot />
    </div>
</template>

<script setup lang="ts">
import { useDisplay } from '@composables/display';

const { displayClasses } = useDisplay('action-bar');
</script>

<style lang="scss">
.wtg-desktop-action-bar {
    display: flex;
    padding: var(--s-padding-xl);
    justify-content: flex-end;
    align-items: center;
    gap: var(--s-spacing-xl);
    flex: 1 1 0;
    min-width: 0px;
    align-self: stretch;
    border-radius: var(--s-radius-M, 8px);
    border: 1px solid var(--s-neutral-border-weak-default, rgba(107, 107, 104, 0.5));
    background: var(--s-neutral-bg-default, #fff);

    box-shadow: var(--s-elevation-200);
}

.wtg-mobile-action-bar {
    display: flex;
    width: 100%;
    padding: var(--s-padding-xl) var(--s-padding-l) var(--s-padding-xl) var(--s-padding-l);
    justify-items: center;
    background: var(--s-neutral-bg-default, #fff);
    border-top: 1px solid var(--s-neutral-border-weak-default, rgba(107, 107, 104, 0.5));

    .wtg-button.wtg-icon-button {
        max-width: 36px !important;
        max-height: 36px !important;
    }

    .v-overlay__content {
        left: unset !important;
    }
}
</style>
