<template>
    <WtgEntityActions :style="onMobile ? 'max-width: 38px; max-height: 38px;' : ''">
        <template v-if="onMobile">
            <TaskActionsOverflow :task="currentTask" :aria-labels="ariaLabels" variant="ghost" />
        </template>
        <template v-else>
            <TaskAction v-for="action in transitionTasks" :key="action.id" :action="action" />
            <EDocsAction :task="currentTask" />
            <DocumentsAction :task="currentTask" />
            <LogsAction :task="currentTask" />
            <MessagesAction :task="currentTask" />
            <NotesAction :task="currentTask" />
            <WorkflowsAction :task="currentTask" />
        </template>
    </WtgEntityActions>
</template>

<script setup lang="ts">
import WtgEntityActions from '@components/WtgEntityActions';
import {
    WtgFrameworkAriaLabels,
    WtgFrameworkTask,
    WtgFrameworkTaskGenericAction,
    WtgFrameworkTaskGenericActionPlacement,
} from '@components/framework/types';
import { useDisplay } from '@composables/display';
import { computed, PropType } from 'vue';
import DocumentsAction from './DocumentsAction.vue';
import EDocsAction from './EDocsAction.vue';
import LogsAction from './LogsAction.vue';
import MessagesAction from './MessagesAction.vue';
import NotesAction from './NotesAction.vue';
import TaskAction from './TaskAction.vue';
import TaskActionsOverflow from './TaskActionsOverflow.vue';
import WorkflowsAction from './WorkflowsAction.vue';

const props = defineProps({
    task: { type: Object as PropType<WtgFrameworkTask>, default: undefined },
    ariaLabels: { type: Object as PropType<WtgFrameworkAriaLabels>, default: undefined },
});

const { onMobile } = useDisplay();

const currentTask = computed((): WtgFrameworkTask => {
    return props.task ?? new WtgFrameworkTask();
});

const genericActions = computed((): WtgFrameworkTaskGenericAction[] => {
    return currentTask.value.genericActions;
});

const transitionTasks = computed((): WtgFrameworkTaskGenericAction[] => {
    const filteredActions = genericActions.value.filter(
        (action) => action.placement === WtgFrameworkTaskGenericActionPlacement.TaskAction
    );
    return filteredActions;
});
</script>
