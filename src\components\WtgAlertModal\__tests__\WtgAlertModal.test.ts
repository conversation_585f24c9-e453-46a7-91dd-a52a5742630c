import WtgAlertModal from '@components/WtgAlertModal/WtgAlertModal.vue';
import WtgButton from '@components/WtgButton';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import { VDialog } from 'vuetify/components/VDialog';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const validateModalSelector = (selector: string, contentText: string, exactMatch?: boolean) => {
    const modalContent = document.querySelector(selector);
    expect(modalContent).not.toBeNull();

    if (exactMatch) {
        const match = modalContent?.innerHTML.match(/class="([^"]*)"/);
        if (match) {
            expect(match[1]).toBe(contentText);
        }
    } else {
        if (contentText === '') {
            expect(modalContent?.innerHTML).toBe(contentText);
        } else {
            expect(modalContent?.innerHTML).toContain(contentText);
        }
    }
};

const validateModal = ({
    contentText,
    title,
    iconName,
    closeExists,
}: {
    contentText: string;
    title: string;
    closeExists: boolean;
    iconName: string;
}) => {
    validateModalSelector('.wtg-alert-modal__container', contentText);
    validateModalSelector('.wtg-alert-modal__title', title);
    validateModalSelector('.wtg-alert-modal__icon', iconName, true);

    const closeModalButton = document.querySelector('[aria-label="Cancel"]');
    closeExists ? expect(closeModalButton).not.toBeNull() : expect(closeModalButton).toBeNull();
};

describe('WtgAlertModal', () => {
    test('it passes its props to the base WtgDialog', () => {
        const wrapper = mountComponent({
            propsData: {
                eager: true,
                height: 300,
                fullscreen: true,
                maxHeight: 400,
                maxWidth: 500,
                minHeight: 600,
                minWidth: 700,
                persistent: true,
                transition: 'fade-transition',
                width: 800,
            },
        });
        const dialog = wrapper.findComponent({ name: 'WtgDialog' });
        expect(dialog.props('eager')).toBe(true);
        expect(dialog.props('height')).toBe(300);
        expect(dialog.props('fullscreen')).toBe(true);
        expect(dialog.props('maxHeight')).toBe(400);
        expect(dialog.props('maxWidth')).toBe(500);
        expect(dialog.props('minHeight')).toBe(600);
        expect(dialog.props('minWidth')).toBe(700);
        expect(dialog.props('persistent')).toBe(true);
        expect(dialog.props('transition')).toBe('fade-transition');
        expect(dialog.props('width')).toBe(800);
    });

    test('it renders component with content closed', () => {
        const wrapper = mountComponent();

        expect(wrapper.findComponent({ name: 'VDialog' }).exists()).toBe(true);

        expect(wrapper.find('button').exists()).toBe(true);

        expect(document.querySelector('.wtg-alert-modal__container')).toBeNull();
    });

    test('it renders default-styled modal content if modal is opened and no props provided', async () => {
        const wrapper = mountComponent();

        const dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.exists()).toBe(true);

        expect(document.querySelector('.wtg-alert-modal__container')).toBeNull();

        const openModalButton = wrapper.find('button');
        expect(openModalButton.exists()).toBe(true);

        await openModalButton.trigger('click');
        await wrapper.vm.$nextTick();

        validateModal({
            contentText: 'modal content!',
            title: '',
            closeExists: true,
            iconName: 'wtg-icon s-icon-status-success',
        });

        expect(document.querySelector('.wtg-alert-modal__header')?.classList).toContain('wtg-alert-modal--success');
    });

    it.each<[string, string, string, string]>([
        ['success', 'Alert reason', 'wtg-icon s-icon-status-success', 'wtg-alert-modal--success'],
        ['error', 'Alert reason', 'wtg-icon s-icon-status-critical', 'wtg-alert-modal--error'],
        ['warning', 'Alert reason', 'wtg-icon s-icon-status-warning', 'wtg-alert-modal--warning'],
        ['info', 'Alert reason', 'wtg-icon s-icon-info-circle', 'wtg-alert-modal--info'],
        ['question', 'Alert reason', 'wtg-icon s-icon-message-alert', 'wtg-alert-modal--question'],
        ['message-error', 'Alert reason', 'wtg-icon s-icon-email', 'wtg-alert-modal--message-error'],
    ])(
        "renders '%s' modal with title '%s', icon '%s' and sentiment class '%s'",
        async (modalSentiment, title, expectedIcon, sentimentClass) => {
            const wrapper = mountComponent({
                propsData: {
                    sentiment: modalSentiment,
                    title,
                },
            });

            const dialog = wrapper.findComponent({ name: 'VDialog' });
            expect(dialog.exists()).toBe(true);

            expect(document.querySelector('.wtg-alert-modal__container')).toBeNull();

            const openModalButton = wrapper.find('button');
            expect(openModalButton.exists()).toBe(true);

            await openModalButton.trigger('click');
            await wrapper.vm.$nextTick();

            validateModal({
                contentText: 'modal content!',
                title,
                closeExists: true,
                iconName: expectedIcon,
            });

            expect(document.querySelector('.wtg-alert-modal__header')?.classList).toContain(sentimentClass);
        }
    );

    test('it renders persistent modal that is not dismissible - close icon is not present', async () => {
        const wrapper = mountComponent({
            propsData: {
                persistent: true,
            },
        });

        const dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.exists()).toBe(true);

        expect(document.querySelector('.wtg-alert-modal__container')).toBeNull();

        const openModalButton = wrapper.find('button');
        expect(openModalButton.exists()).toBe(true);

        await openModalButton.trigger('click');
        await wrapper.vm.$nextTick();

        validateModal({
            contentText: 'modal content!',
            title: '',
            closeExists: false,
            iconName: 'wtg-icon s-icon-status-success',
        });
    });

    test('it closes modal when triggering modal close button', async () => {
        const wrapper = mountComponent({});

        const dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.exists()).toBe(true);
        expect(document.querySelector('.wtg-alert-modal__container')).toBeNull();

        const openModalButton = wrapper.find('button');
        expect(openModalButton.exists()).toBe(true);

        await openModalButton.trigger('click');
        await wrapper.vm.$nextTick();

        expect(document.querySelector('.wtg-alert-modal__container')).not.toBeNull();
        expect(dialog.vm.modelValue).toBe(true);

        const closeModalButton = document.querySelector<HTMLButtonElement>('#close_dialog_action');
        expect(closeModalButton).not.toBeNull();

        closeModalButton!.click();
        await wrapper.vm.$nextTick();

        expect(dialog.vm.modelValue).toBe(false);
    });

    test('it closes modal when clicking an action button', async () => {
        const wrapper = mountComponent();

        const dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.exists()).toBe(true);
        expect(document.querySelector('.wtg-alert-modal__container')).toBeNull();

        const openModalButton = wrapper.find('button');
        expect(openModalButton.exists()).toBe(true);

        await openModalButton.trigger('click');
        await wrapper.vm.$nextTick();

        expect(document.querySelector('.wtg-alert-modal__container')).not.toBeNull();
        expect(dialog.vm.modelValue).toBe(true);

        const closeModalButton = document.querySelector<HTMLButtonElement>('#close_dialog_action');
        expect(closeModalButton).not.toBeNull();

        closeModalButton!.click();
        await wrapper.vm.$nextTick();

        expect(dialog.vm.modelValue).toBe(false);
    });

    test('it implements v-model through the modelValue property', async () => {
        const component = {
            components: { WtgAlertModal, WtgButton },
            template: `<wtg-button variant="fill" sentiment="primary" @click="isActive = true">Open Error Modal</wtg-button>
                <wtg-alert-modal v-model="isActive" persistent title="Tasks cleared" sentiment="success">
                    <template #default>
                        All tasks in your channel for this week have been cleared.
                    </template>
                    <template #actions>
                        <wtg-button variant="fill" sentiment="primary" @click="isActive = false">Okay</wtg-button>
                    </template>
                </wtg-alert-modal>`,
            data: () => {
                return {
                    isActive: true,
                };
            },
        };
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });
        const modal = wrapper.findComponent(VDialog);
        expect(modal.props('modelValue')).toBe(true);

        await modal.vm.$emit('update:modelValue', false);
        const data = wrapper.vm.$data as any;
        expect(data.isActive).toBe(false);

        await nextTick();
        expect(modal.props('modelValue')).toBe(false);
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgAlertModal, {
            props: propsData,
            slots: {
                activator: '<button v-bind="params.props">Activator</button>',
                default: '<div id="modal_content">modal content!</div>',
                actions:
                    '<button id="close_dialog_action" @click="() => params.isActive.value = false">Close Dialog</button>',
            },
            global: {
                plugins: [wtgUi],
                provide: {
                    darkMode: false,
                },
            },
        });
    }
});
