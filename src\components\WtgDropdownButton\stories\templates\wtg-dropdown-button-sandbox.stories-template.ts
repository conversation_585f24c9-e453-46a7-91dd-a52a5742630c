export const DropdownButtonSandboxTemplate = `
<WtgRow>
    <WtgCol style="max-width: fit-content; gap: 30px; margin-right: 30px" class="d-flex flex-column">
        <WtgDropdownButton 
            v-bind="args"
            @click="action"
            @dropdown-click="dropdownAction">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            sentiment="primary"
            @click="action"
            @dropdown-click="dropdownAction">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="outline"
            sentiment="success"
            @click="action"
            @dropdown-click="dropdownAction">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            sentiment="success"
            @click="action"
            @dropdown-click="dropdownAction">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            sentiment="critical"
            @click="action"
            @dropdown-click="dropdownAction">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            sentiment="critical"
            @click="action"
            @dropdown-click="dropdownAction">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            :loading="true"
            variant="fill"
            sentiment="primary"
            @click="action"
            @dropdown-click="dropdownAction">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 30px; margin-right: 30px" class="d-flex flex-column">
        <WtgDropdownButton 
            v-bind="args"
            :isSplitButton="false"
            @click="action">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            sentiment="primary"
            :isSplitButton="false"
            @click="action">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            :isSplitButton="false"
            variant="outline"
            sentiment="success"
            @click="action">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            :isSplitButton="false"
            sentiment="success"
            @click="action">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            :isSplitButton="false"
            sentiment="critical"
            @click="action">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            :isSplitButton="false"
            sentiment="critical"
            @click="action">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            variant="fill"
            :loading="true"
            :isSplitButton="false"
            sentiment="primary"
            @click="action">
                Label
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 30px;" class="d-flex flex-column">
        <WtgDropdownButton 
            v-bind="args"
            maxWidth="100"
            @click="action"
            @dropdown-click="dropdownAction">
                Save details and also click here so and so
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
        <WtgDropdownButton 
            v-bind="args"
            :isSplitButton="false"
            maxWidth="100"
            @click="action">
                Save details and also click here so and so
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownButton>
    </WtgCol>
</WtgRow>`;
