import WtgChart from '@components/WtgChart/WtgChart.vue';
import WtgScatterChart from '@components/WtgChart/WtgScatterChart/WtgScatterChart';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import 'jest-canvas-mock';
import WtgUi from '../../../../WtgUi';

const wtgUi = new WtgUi({
    theme: {
        colors: {
            light: {
                controls: {
                    chart: {
                        background: '#371FE1',
                        backdrop: 'rgba(255, 255, 255, 0.75)',
                        border: '#666',
                        grid: 'rgba(0, 0, 0, 0.6)',
                        text: '#666',
                        ticks: 'rgba(0, 0, 0, 0.6)',
                    },
                },
            },
            dark: {
                controls: {
                    chart: {
                        background: '#2387EE',
                        backdrop: 'rgba(0, 0, 0, 0.75)',
                        border: '#FFF',
                        grid: 'rgba(255, 255, 255, 0.7)',
                        text: '#FFF',
                        ticks: 'rgba(255, 255, 255, 0.7)',
                    },
                },
            },
        },
    },
});

enableAutoUnmount(afterEach);

describe('WtgScatterChart', () => {
    test('its name is WtgScatterChart', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('WtgScatterChart');
    });

    test('it renders a WtgChart of type scatter', () => {
        const wrapper = mountComponent();
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('type')).toBe('scatter');
    });

    test('it passes its options on to the WtgChart component to render', () => {
        const options = {
            plugins: {
                title: {
                    text: 'A scatter chart',
                },
            },
        };
        const wrapper = mountComponent({
            propsData: {
                options,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('options')).toStrictEqual(options);
    });

    test('it passes its data on to the WtgChart component to render', () => {
        const data = {
            datasets: [
                {
                    label: 'Some dataset',
                    data: [
                        {
                            x: -10,
                            y: 0,
                        },
                        {
                            x: -4,
                            y: 9,
                        },
                        {
                            x: 10,
                            y: 3,
                        },
                        {
                            x: 0.5,
                            y: 5.5,
                        },
                    ],
                },
            ],
        };
        const wrapper = mountComponent({
            propsData: {
                data,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('data')).toStrictEqual(data);
    });

    test('it passes loading flag to the WtgChart component', () => {
        const wrapper = mountComponent({
            propsData: {
                loading: true,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('loading')).toBe(true);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    function mountComponent({ propsData = {}, provide = {} } = {}) {
        return mount(WtgScatterChart, {
            propsData,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
