export const EmptyStateSandboxTemplate = `
            <WtgLayoutGrid>
              <WtgPanel caption='Default'>
                    <span>Default First Use Empty State.</span>
                    <WtgEmptyState v-bind="args" @click="action">
                        <WtgButton @click="onClick" leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                               
                            Label                                
                        </WtgButton>
                    </WtgEmptyState>
                </WtgPanel>
                <WtgPanel caption='Condensed'>
                    <span>Condensed First Use Empty State.</span>
                    <WtgEmptyState v-bind="args" variant='condensed' @click="action">
                        <WtgButton @click="onClick"  leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                              
                            Label                                
                        </WtgButton>
                    </WtgEmptyState>
                </WtgPanel>
               <WtgPanel caption='User Cleared'>
                    <span>User Cleared Empty State.</span>
                    <WtgEmptyState v-bind="args" type='userCleared' @click="action">
                        <WtgButton @click="onClick"  leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                                
                            Label                                
                        </WtgButton>
                    </WtgEmptyState>
                </WtgPanel>
                <WtgPanel caption='Missing Page'>
                    <span>Missing Page Empty State.</span>
                    <WtgEmptyState v-bind="args" type='missingPage' @click="action">
                        <WtgButton @click="onClick"  leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                               
                            Label                                
                        </WtgButton>
                    </WtgEmptyState>
                </WtgPanel>
                <WtgPanel caption='Error'>
                    <span>Error Empty State.</span>
                    <WtgEmptyState v-bind="args" type='error' @click="action">
                        <WtgButton @click="onClick"  leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                               
                            Label                                
                        </WtgButton>
                    </WtgEmptyState>
                </WtgPanel>
                <WtgPanel caption='Restricted'>
                    <span>Restricted Empty State.</span>
                    <WtgEmptyState v-bind="args" type='restricted' @click="action">
                        <WtgButton @click="onClick"  leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                                 
                            Label                                
                        </WtgButton>
                    </WtgEmptyState>
                </WtgPanel>
                <WtgPanel caption='No Data'>
                    <span>No Data Page Empty State.</span>
                    <WtgEmptyState v-bind="args" type='noData' @click="action">
                        <WtgButton @click="onClick"  leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                               
                            Label                                
                        </WtgButton>
                    </WtgEmptyState>
                </WtgPanel>
                <WtgPanel caption='No Image'>
                    <span>No Image Page Empty State.</span>
                    <WtgEmptyState v-bind="args" type='' @click="action">
                        <WtgButton @click="onClick"  leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                              
                            Label                                
                        </WtgButton>
                    </WtgEmptyState>
                </WtgPanel>
            </WtgLayoutGrid>
`;
