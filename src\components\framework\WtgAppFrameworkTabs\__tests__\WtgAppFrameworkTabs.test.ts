import { WtgFramework, WtgFrameworkTabsInfo, WtgFrameworkTask } from '@components/framework/types';
import { WtgAppFrameworkTabs } from '@components/framework/WtgAppFrameworkTabs';
import { WtgTab, WtgTabsWindowItem } from '@components/WtgTabs';
import { setApplication, tabsInfoInjectionKey } from '@composables/application';
import { enableAutoUnmount, mount, type VueWrapper } from '@vue/test-utils';
import { nextTick, type Ref, ref } from 'vue';
import WtgUi from '../../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgAppFrameworkTabs', () => {
    let application: WtgFramework;
    let tabs: Ref<WtgFrameworkTabsInfo | undefined>;

    beforeEach(() => {
        application = new WtgFramework();
        application.currentTask = new WtgFrameworkTask();
        setApplication(application);

        tabs = ref(undefined);
    });

    test('its name is WtgAppFrameworkTabs', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('WtgAppFrameworkTabs');
    });

    test('it is a wrapper around a WtgTabs and a WtgTabsWindow component', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent({ name: 'WtgTabs' }).exists()).toBe(true);
        expect(wrapper.findComponent({ name: 'WtgTabsWindow' }).exists()).toBe(true);
    });

    test('it passes its value to both the the WtgTabs and the WtgTabsWindow component', () => {
        const wrapper = mountComponent({
            propsData: {
                value: 1,
            },
        });

        const tabs = wrapper.findComponent({ name: 'WtgTabs' });
        expect(tabs.props('modelValue')).toBe(1);

        const tabsWindow = wrapper.findComponent({ name: 'WtgTabsWindow' });
        expect(tabsWindow.props('modelValue')).toBe(1);
    });

    test('it should have wtg-application-tabs class', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('wtg-application-tabs');
    });

    test('it has wtg-fit-to-height class when fitToHeight prop value is true', () => {
        const wrapper = mountComponent({
            propsData: {
                fitToHeight: true,
            },
        });
        expect(wrapper.classes()).toContain('wtg-fit-to-height');
    });

    test('it renders all children marked as tabs inside the WtgTabs component section, and all remaining children inside the WthTabsWindow component', async () => {
        const component = {
            components: { WtgAppFrameworkTabs, WtgTab, WtgTabsWindowItem },
            template: `
                <wtg-app-framework-tabs >
                    <wtg-tab data-tab="true">ONE</wtg-tab>
                    <wtg-tab data-tab="true">TWO</wtg-tab>
                    <wtg-tab data-tab="true">THREE</wtg-tab>
                    <wtg-tabs-window-item>content one</wtg-tabs-window-item>
                    <wtg-tabs-window-item>content two</wtg-tabs-window-item>
                    <wtg-tabs-window-item>content three</wtg-tabs-window-item>
                </wtg-app-framework-tabs>`,
            data: () => {
                return {
                    tab: 1,
                };
            },
        };
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
                provide: {
                    provide: { [tabsInfoInjectionKey]: undefined },
                },
            },
        });

        const tabs = wrapper.findComponent({ name: 'WtgTabs' });
        expect(tabs.findAllComponents({ name: 'WtgTab' }).length).toBe(3);
        expect(tabs.findAllComponents({ name: 'WtgTabsWindowItem' }).length).toBe(0);

        const tabsWindow = wrapper.findComponent({ name: 'WtgTabsWindow' });
        expect(tabsWindow.findAllComponents({ name: 'WtgTab' }).length).toBe(0);
        expect(tabsWindow.findAllComponents({ name: 'WtgTabsWindowItem' }).length).toBe(3);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
    });

    describe('when the app property is set to true', () => {
        let wrapper: VueWrapper;
        beforeEach(() => {
            wrapper = mountComponent({
                propsData: {
                    app: true,
                },
            });
        });

        test('it renders the tabs so they can register themselves with the app framework but keeps them hidden when the app property is set to true', () => {
            const tabs = wrapper.find('.wtg-tabs');
            expect(tabs.classes('d-none')).toBe(true);
        });

        test('it instructs the application to show the tabs in the appbar', () => {
            expect(tabs.value?.visible).toBe(true);
        });

        test('it emits a change event when the application active tab updates', async () => {
            expect(wrapper.emitted('change')).toBeUndefined();
            tabs.value!.current = 1;
            await nextTick();
            expect(wrapper.emitted('change')!.length).toBe(1);
        });
    });

    describe('when the app property is set to false', () => {
        let wrapper: VueWrapper;

        beforeEach(() => {
            wrapper = mountComponent({
                propsData: {
                    app: false,
                },
            });
        });

        test('it does NOT hide the tab headers when the app property is set to false', () => {
            const tabs = wrapper.find('.wtg-tabs');
            expect(tabs.classes('d-none')).toBe(false);
        });

        test('it does not register as the current tasks tabInfo (ensuring nested tabs work correctly when on a page that already has the app=true tabs defined)', () => {
            expect(tabs.value).toBeUndefined();
        });
    });

    test('it published the selected tab through the value property and the change event', async () => {
        const component = {
            components: { WtgAppFrameworkTabs, WtgTab },
            template:
                '<wtg-app-framework-tabs :value="tab" @change="tab = $event"><wtg-tab>ONE</wtg-tab><wtg-tab>TWO</wtg-tab><wtg-tab>THREE</wtg-tab></wtg-app-framework-tabs>',
            data: () => {
                return {
                    tab: 1,
                };
            },
        };
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
                provide: {
                    provide: { [tabsInfoInjectionKey]: undefined },
                },
            },
        });
        const tabs = wrapper.findComponent({ name: 'WtgTabs' });
        expect(tabs.props('modelValue')).toBe(1);

        tabs.vm.$emit('update:modelValue', 2);
        await nextTick();

        expect(wrapper.vm.tab).toBe(2);

        await nextTick();
        expect(tabs.props('modelValue')).toBe(2);
    });

    test('it passes the correct alignTabs prop to WtgTabs based on right prop', () => {
        const wrapper = mountComponent({
            propsData: {
                right: true,
            },
        });
        const wtgTabs = wrapper.findComponent({ name: 'WtgTabs' });
        expect(wtgTabs.exists()).toBe(true);
        expect(wtgTabs.props('alignTabs')).toBe('end');
    });

    function mountComponent({ components = {}, propsData = {}, slots = {} } = {}) {
        return mount(WtgAppFrameworkTabs, {
            components,
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
                provide: {
                    [tabsInfoInjectionKey]: tabs,
                },
            },
        });
    }
});
