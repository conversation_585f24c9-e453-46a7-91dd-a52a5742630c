import RecentList from '../RecentList.vue';
import WtgUi from '../../../../WtgUi';
import { mount, enableAutoUnmount, VueWrapper } from '@vue/test-utils';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('RecentList', () => {
    test('its name is RecentList', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('RecentList');
    });

    test('it shows a recent items subheader', () => {
        const wrapper = mountComponent();
        const subHeaders = wrapper.findAllComponents({ name: 'WtgLabel' });
        expect(subHeaders.length).toBe(1);
        expect(subHeaders.at(0)?.text()).toBe('Recent');
    });

    describe('when given a list of favorites', () => {
        let wrapper: VueWrapper;
        beforeEach(() => {
            wrapper = mountComponent({
                recentsCaption: 'Recent Test',
                items: {
                    favorites: [
                        {
                            id: '5715d3bf-242b-4ffe-8e6e-801f90beca3f',
                            caption: 'Item One',
                            href: 'Link One',
                            favorite: true,
                            actions: [],
                            onAddToFavorites: jest.fn(),
                            onRemoveFromFavorites: jest.fn(),
                        },
                        {
                            id: '36d28d28-3df0-4160-8237-30d9fec08cda',
                            caption: 'Item Two',
                            href: 'Link Two',
                            favorite: true,
                            actions: [],
                            onAddToFavorites: jest.fn(),
                            onRemoveFromFavorites: jest.fn(),
                        },
                    ],
                    recents: [],
                },
            });
        });

        test('it creates a recent item for every favorite', () => {
            const listItems = wrapper.findAllComponents({ name: 'RecentListItem' });
            expect(listItems.length).toBe(2);
        });

        test('each recent item has the correct caption and list items are placed top to bottom in the given order', () => {
            const listItems = wrapper.findAllComponents({ name: 'RecentListItem' });
            expect(listItems.at(0)?.text()).toBe('Item One');
            expect(listItems.at(1)?.text()).toBe('Item Two');
        });

        test('it shows a list of favorites and recent items, separated by subheaders', () => {
            const subHeaders = wrapper.findAllComponents({ name: 'WtgLabel' });
            expect(subHeaders.length).toBe(2);
            expect(subHeaders.at(0)?.text()).toBe('Favorites');
            expect(subHeaders.at(1)?.text()).toBe('Recent Test');
        });

        test('it emits an item click event when a favourite is clicked', async () => {
            const listItems = wrapper.findAllComponents({ name: 'RecentListItem' });
            await listItems.at(0)?.vm.$emit('item-click');

            expect(wrapper.emitted('item-click')?.length).toBe(1);
        });
    });

    describe('when given a list of recent items', () => {
        let wrapper: VueWrapper;
        beforeEach(() => {
            wrapper = mountComponent({
                favoritesCaption: 'Favorites Test',
                recentsCaption: 'Recent Test',
                items: {
                    favorites: [],
                    recents: [
                        {
                            id: '5715d3bf-242b-4ffe-8e6e-801f90beca3f',
                            caption: 'Item One',
                            href: 'Link One',
                            favorite: false,
                            actions: [],
                            onAddToFavorites: jest.fn(),
                            onRemoveFromFavorites: jest.fn(),
                        },
                        {
                            id: '36d28d28-3df0-4160-8267-30d9fec08cda',
                            caption: 'Item Two',
                            href: 'Link Two',
                            favorite: false,
                            actions: [],
                            onAddToFavorites: jest.fn(),
                            onRemoveFromFavorites: jest.fn(),
                        },
                    ],
                },
            });
        });

        test('it creates a recent item for every entry', () => {
            const listItems = wrapper.findAllComponents({ name: 'RecentListItem' });
            expect(listItems.length).toBe(2);
        });

        test('each recent item has the correct caption and list items are placed top to bottom in the given order', () => {
            const listItems = wrapper.findAllComponents({ name: 'RecentListItem' });
            expect(listItems.at(0)?.text()).toBe('Item One');
            expect(listItems.at(1)?.text()).toBe('Item Two');
        });

        test('it emits an item click event with correct argument length when a recent item is clicked', async () => {
            const listItems = wrapper.findAllComponents({ name: 'RecentListItem' });
            await listItems.at(0)?.vm.$emit('item-click');

            expect(wrapper.emitted('item-click')?.length).toBe(1);
            expect(wrapper.emitted('item-click')?.at(0)?.length).toBe(1);
        });
    });

    describe('when given a list of recent and favorite items', () => {
        let wrapper: VueWrapper;

        beforeEach(() => {
            wrapper = mountComponent({
                favoritesCaption: 'Favorites Test',
                recentsCaption: 'Recent Test',
                items: {
                    favorites: [
                        {
                            id: '36d28d28-3df0-4160-b237-30d9fec08cda',
                            caption: 'Item Two',
                            href: 'Link Two',
                            favorite: true,
                            actions: [],
                            onAddToFavorites: jest.fn(),
                            onRemoveFromFavorites: jest.fn(),
                        },
                    ],
                    recents: [
                        {
                            id: '5715d3bf-242b-4ffe-8e6e-801f90beca3f',
                            caption: 'Item One',
                            href: 'Link One',
                            favorite: false,
                            actions: [],
                            onAddToFavorites: jest.fn(),
                            onRemoveFromFavorites: jest.fn(),
                        },
                        {
                            id: '36d28d28-3df0-4160-8267-30d9fec08cda',
                            caption: 'Item Two',
                            href: 'Link Two',
                            favorite: false,
                            actions: [],
                            onAddToFavorites: jest.fn(),
                            onRemoveFromFavorites: jest.fn(),
                        },
                    ],
                },
            });
        });

        test('it creates a recent item for every entry', () => {
            const listItems = wrapper.findAllComponents({ name: 'RecentListItem' });
            expect(listItems.length).toBe(3);
        });

        test('each recent item has the correct caption and list items are placed top to bottom with favorites first', () => {
            const listItems = wrapper.findAllComponents({ name: 'RecentListItem' });
            expect(listItems.at(0)?.text()).toBe('Item Two');
            expect(listItems.at(1)?.text()).toBe('Item One');
        });
    });

    function mountComponent(propsData = {}) {
        return mount(RecentList, {
            propsData,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
