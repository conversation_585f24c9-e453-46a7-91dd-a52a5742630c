import WtgExpander from '@components/WtgExpander/WtgExpander.vue';
import WtgExpanderPanel from '@components/WtgExpander/WtgExpanderPanel.vue';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { defineComponent, nextTick } from 'vue';
import { VExpansionPanels } from 'vuetify/components/VExpansionPanel';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgExpander', () => {
    test('it renders a VExpansionPanels component', () => {
        const wrapper = mountComponent();
        const expander = wrapper.findComponent(VExpansionPanels);
        expect(expander.exists()).toBe(true);
        expect(expander.classes()).toContain('wtg-expander');
    });

    test('it resolves the wtg-expander--mobile class from the mobile property', async () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).not.toContain('wtg-expander--mobile');
        await wrapper.setProps({ mobile: true });
        expect(wrapper.classes()).toContain('wtg-expander--mobile');
        await wrapper.setProps({ mobile: false });
        expect(wrapper.classes()).not.toContain('wtg-expander--mobile');
    });

    test('it removes vuetify styling so SUPPLY styling can be applied', () => {
        const wrapper = mountComponent();
        const expander = wrapper.findComponent(VExpansionPanels);
        expect(expander.props('flat')).toBe(true);
        expect(expander.props('tile')).toBe(true);
    });

    test('it passes the default slot to the VExpansionPanels component', () => {
        const wrapper = mountComponent({
            slots: {
                default: '<div class="my-div">Some Text</div>',
            },
        });
        expect(wrapper.find('.my-div').text()).toBe('Some Text');
    });

    test('it implements v-model through the value property and the change event', async () => {
        const component = {
            components: { WtgExpanderPanel, WtgExpander },
            template:
                '<wtg-expander v-model="panel"><wtg-expander-panel>ONE</wtg-expander-panel><wtg-expander-panel>TWO</wtg-expander-panel><wtg-expander-panel>THREE</wtg-expander-panel></wtg-expander>',
            data: () => {
                return {
                    panel: 1,
                };
            },
        };
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });
        const expander = wrapper.findComponent(VExpansionPanels);
        expect(expander.props('modelValue')).toBe(1);

        expander.vm.$emit('update:modelValue', 2);
        const data = wrapper.vm.$data as any;
        expect(data.panel).toBe(2);

        await nextTick();
        expect(expander.props('modelValue')).toBe(2);
    });

    test('it has (deprecated) VALUE property and INPUT event that allows it to be backwards compatible with the V-MODEL handling of the VUE 2 implementation', async () => {
        const component = defineComponent({
            components: { WtgExpanderPanel, WtgExpander },
            data: () => {
                return {
                    panel: 1 as string | number | string[] | number[] | undefined,
                };
            },
            methods: {
                onInput(value: string | number | string[] | number[]) {
                    this.panel = value;
                },
            },
            template:
                '<wtg-expander :value="panel" @input="onInput"><wtg-expander-panel>ONE</wtg-expander-panel><wtg-expander-panel>TWO</wtg-expander-panel><wtg-expander-panel>THREE</wtg-expander-panel></wtg-expander>',
        });
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });
        const wtgExpander = wrapper.findComponent(WtgExpander);
        const vExpander = wrapper.findComponent(VExpansionPanels);
        expect(wtgExpander.props('value')).toBe(1);
        expect(vExpander.props('modelValue')).toBe(1);

        vExpander.vm.$emit('update:modelValue', 2);
        const data = wrapper.vm.$data as any;
        expect(data.panel).toBe(2);

        await nextTick();
        expect(vExpander.props('modelValue')).toBe(2);
        expect(wtgExpander.props('value')).toBe(2);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    function mountComponent({ components = {}, props = {}, slots = {}, provide = {} } = {}) {
        return mount(WtgExpander, {
            props,
            components,
            slots,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
