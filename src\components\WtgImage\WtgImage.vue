<template>
    <VImg
        :alt="alt"
        :aspect-ratio="aspectRatio"
        :cover="cover"
        :eager="eager"
        :gradient="gradient"
        :height="height"
        :lazy-src="lazySrc"
        :max-height="maxHeight"
        :max-width="maxWidth"
        :min-height="minHeight"
        :min-width="minWidth"
        :sizes="sizes"
        :src="computedSrc"
        :srcset="srcset"
        :transition="transition"
        :width="width"
        @error="onError"
    >
        <slot />
    </VImg>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { VImg } from 'vuetify/components/VImg';

//
// Properties
//
const props = defineProps({
    /**
     * Alternative text for the image.
     */
    alt: {
        type: String,
        default: undefined,
    },
    /**
     * Aspect ratio (width/height) of the image.
     */
    aspectRatio: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * If true, the image will cover its container.
     */
    cover: {
        type: Boolean,
        default: false,
    },
    /**
     * If true, the image will be loaded eagerly.
     */
    eager: {
        type: <PERSON>olean,
        default: false,
    },
    /**
     * Icon to display if the image fails to load.
     */
    fallbackIcon: {
        type: String,
        default: '',
    },
    /**
     * Fallback image URL to use if the main image fails to load.
     */
    fallbackImage: {
        type: String,
        default: '',
    },
    /**
     * CSS gradient to apply over the image.
     */
    gradient: {
        type: String,
        default: undefined,
    },
    /**
     * Height of the image (in pixels or CSS units).
     */
    height: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Source for a low-quality placeholder image to show while loading.
     */
    lazySrc: {
        type: String,
        default: undefined,
    },
    /**
     * Maximum height of the image (in pixels or CSS units).
     */
    maxHeight: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Maximum width of the image (in pixels or CSS units).
     */
    maxWidth: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Minimum height of the image (in pixels or CSS units).
     */
    minHeight: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Minimum width of the image (in pixels or CSS units).
     */
    minWidth: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Sizes attribute for responsive images.
     */
    sizes: {
        type: String,
        default: undefined,
    },
    /**
     * Image source URL.
     */
    src: {
        type: String,
        default: undefined,
    },
    /**
     * Srcset attribute for responsive images.
     */
    srcset: {
        type: String,
        default: undefined,
    },
    /**
     * Transition effect when loading the image.
     * Can be a boolean or a transition name.
     */
    transition: {
        type: [Boolean, String],
        default: 'fade-transition',
    },
    /**
     * Width of the image (in pixels or CSS units).
     */
    width: {
        type: [Number, String],
        default: undefined,
    },
});

//
// State
//
const showFallbackImage = ref(false);

//
// Computed
//
const computedSrc = computed(() => {
    return showFallbackImage.value ? props.fallbackImage ?? '' : props.src;
});

//
// Event Handlers
//
function onError(): void {
    showFallbackImage.value = true;
}
</script>
