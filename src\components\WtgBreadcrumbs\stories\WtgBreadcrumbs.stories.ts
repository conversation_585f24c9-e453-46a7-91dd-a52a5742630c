import WtgBreadcrumbs from '@components/WtgBreadcrumbs/WtgBreadcrumbs.vue';
import WtgThemeProvider from '@components/WtgThemeProvider/WtgThemeProvider.vue';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { Meta, StoryObj } from '@storybook/vue3';
import { vueRouter } from 'storybook-vue3-router';

type Story = StoryObj<typeof WtgBreadcrumbs>;

const meta: Meta<typeof WtgBreadcrumbs> = {
    title: 'Components/Breadcrumbs',
    component: WtgBreadcrumbs,
    parameters: {
        docs: {
            description: {
                component:
                    'A breadcrumb displays the current location within a hierarchy. It allows going back to states higher up in the hierarchy of pages and entities.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=305-20672&mode=design&t=ULH2WbmuCaOClr2K-0',
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgBreadcrumbs },
        setup: () => ({ args }),
        template: `<WtgBreadcrumbs v-bind="args"></WtgBreadcrumbs>`,
    }),
    argTypes: {
        items: {
            control: { type: 'object' },
        },
    },
};

export default meta;
const link = 'javascript:void(0);';

export const Default: Story = {
    args: {
        items: [
            { caption: 'Portal name', link },
            { caption: 'Page name', link },
            { caption: 'Form caption', link },
        ],
    },
};

export const Overflow: Story = {
    args: {
        items: [
            { caption: 'Portal name', link },
            { caption: 'Page name', link },
            { caption: 'Page name 1', link },
            { caption: 'Form caption', link },
        ],
    },
};

const route = [
    {
        path: '/portal',
        name: 'portal',
        component: { template: '<div>Portal</div>' },
    },
    {
        path: '/page',
        name: 'page',
        component: { template: '<div>Page</div>' },
    },
    {
        path: '/form',
        name: 'form',
        component: { template: '<div>Form</div>' },
    },
];

const RouterLinkTemplate = `<div class="d-flex flex-column">
Router Links
<wtg-breadcrumbs :items="args.routerLinkItems"></wtg-breadcrumbs>
<div>
Current view is: <router-view />
</div>
</div>`;

const BreadcrumbSandboxTemplate = `<div class="d-flex flex-column">
<WtgBreadcrumbs :items="[{ caption: 'Portal name', link: 'javascript:void(0);' }]"></WtgBreadcrumbs>
<WtgBreadcrumbs :items="args.twoItems"></WtgBreadcrumbs>
<WtgBreadcrumbs :items="args.threeItems"></WtgBreadcrumbs>
<WtgBreadcrumbs :items="args.fourItems"></WtgBreadcrumbs>
</div>`;

export const Sandbox: StoryObj = {
    args: {
        twoItems: [
            { caption: 'Portal name', link },
            { caption: 'Form caption', link },
        ],
        threeItems: [
            { caption: 'Portal name', link },
            { caption: 'Page name', link },
            { caption: 'Form caption', link },
        ],
        fourItems: [
            { caption: 'Portal name', link },
            { caption: 'Page name', link },
            { caption: 'Page name 1', link },
            { caption: 'Form caption', link },
        ],
        routerLinkItems: [
            { caption: 'Portal name', link, to: { name: 'portal' } as any },
            { caption: 'Page name', link, to: { name: 'page' } as any },
            { caption: 'Form caption', link, to: { name: 'form' } as any },
        ],
    },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgBreadcrumbs, WtgThemeProvider },
        setup: () => ({ args }),
        template:
            templateWithRtl(BreadcrumbSandboxTemplate) +
            '<br><hr><br>' +
            templateWithRtl(
                '<WtgThemeProvider framework="mobile">' + BreadcrumbSandboxTemplate + '</WtgThemeProvider>'
            ) +
            RouterLinkTemplate,
    }),
    decorators: [
        vueRouter(route, {
            initialRoute: '/form',
        }),
    ],
};
