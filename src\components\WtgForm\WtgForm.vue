<template>
    <VForm
        ref="formRef"
        :model-value="internalValue"
        :disabled="disabled"
        @submit="onSubmit"
        @update:model-value="onUpdateModelValue"
    >
        <slot></slot>
    </VForm>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { VForm } from 'vuetify/components/VForm';

//
// Properties
//
const props = defineProps({
    disabled: {
        type: Boolean,
        default: false,
    },
    modelValue: {
        type: Boolean,
        default: undefined,
    },

    /**
     * @deprecated Use modelValue instead
     */
    value: {
        type: Boolean,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    submit: [event: SubmitEvent];
    input: [value: boolean | null];
    'model-compat:input': [value: any];
    'update:modelValue': [value: boolean | null];
}>();

//
// State
//
const formRef = ref<any>(null);
const internalValue = ref<boolean | null>(false);

//
// Watchers
//
watchEffect(() => {
    internalValue.value = props.modelValue ?? props.value ?? false;
});

//
// Event Handlers
//
function onSubmit(event: SubmitEvent) {
    emit('submit', event);
}

function onUpdateModelValue(state: boolean | null) {
    internalValue.value = state;
    emit('input', internalValue.value);
    emit('model-compat:input', internalValue.value);
    emit('update:modelValue', internalValue.value);
}

//
// Helpers
//
async function validate(): Promise<{
    valid: boolean;
    errors: { id: string | number; errorMessages: string[] }[];
}> {
    if (formRef.value) {
        return await formRef.value.validate();
    }
    return {
        valid: false,
        errors: [],
    };
}

function resetValidation() {
    if (formRef.value) {
        formRef.value.resetValidation();
    }
}

function reset() {
    if (formRef.value) {
        formRef.value.reset();
    }
}

//
// Expose
//
defineExpose({
    validate,
    reset,
    resetValidation,
});
</script>
