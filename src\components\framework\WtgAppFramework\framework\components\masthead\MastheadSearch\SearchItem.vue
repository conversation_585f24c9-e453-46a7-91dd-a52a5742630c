<template>
    <WtgBox v-if="item" layout="flex" flex-align="align-center">
        <WtgHover v-slot="{ isHovering, props }">
            <div
                v-bind="props"
                :class="`search-item 
                    ${searchItemIsEntity(item) ? 'rounded-xl px-3 py-3' : 'rounded-lg px-2 py-2'} 
                    ${item.isActive ? 'wtg-neutral-bg-weak-active' : isHovering ? 'wtg-neutral-bg-weak-hover' : ''}`"
                :tabindex="index"
                @click="$emit('click', item)"
            >
                <WtgBox layout="flex" flex-align="align-center">
                    <slot></slot>
                </WtgBox>

                <WtgBox v-if="actionCaption" class="mx-4" layout="flex" flex-align="align-center" aria-hidden="true">
                    <WtgLabel style="height: 24px" color="secondary-text" typography="subtitle2">
                        <span v-show="item.isActive">
                            <WtgTag>Enter</WtgTag>
                            to
                        </span>

                        <span>
                            {{ actionCaption }}
                        </span>
                    </WtgLabel>
                </WtgBox>
            </div>
        </WtgHover>
    </WtgBox>
</template>

<script lang="ts">
import { PropType, defineComponent } from 'vue';
import WtgBox from '@components/WtgBox';
import WtgTag from '@components/WtgTag';
import WtgHover from '@components/WtgHover';
import WtgLabel from '@components/WtgLabel';

import { WtgFrameworkSearchResultRecord } from '@components/framework/types';
import { searchItemIsEntity } from './types';

export default defineComponent({
    name: 'SearchItem',
    components: {
        WtgBox,
        WtgHover,
        WtgTag,
        WtgLabel,
    },
    props: {
        item: {
            type: Object as PropType<WtgFrameworkSearchResultRecord>,
            default: undefined,
        },
        index: {
            type: Number,
            default: -1,
        },
        actionCaption: {
            type: String,
            default: '',
        },
    },
    emits: ['click'],
    setup() {
        return { searchItemIsEntity };
    },
});
</script>
<style lang="scss" scoped>
.search-item {
    scroll-margin-top: 20px;
    display: flex;
    width: 100%;
    justify-content: space-between;
    min-height: 40px;
}
.search-item:hover {
    cursor: pointer;
}
</style>
