import { nextTick } from 'vue';
import SearchItem from '../SearchItem.vue';
import WtgUi from '../../../../../../../../WtgUi';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('SearchItem', () => {
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    it('should show nothing if no item', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.findComponent({ name: 'WtgBox' }).exists()).toBe(false);
    });

    it.each([true, false])('should display help chip when item is active (active = %s)', async (active) => {
        const wrapper = await mountComponentAsync({
            props: {
                item: { shorthand: 'DUM', isActive: active },
                actionCaption: 'Jump To',
            },
        });

        const label = wrapper.findComponent({ name: 'WtgLabel' });
        expect(label.text()).toContain('Jump To');
        expect(label.findComponent({ name: 'WtgTag' }).isVisible()).toBe(active);
    });

    it('should emit `click` with item as payload when clicked', async () => {
        const item = { shorthand: 'DUM', isActive: true };
        const wrapper = await mountComponentAsync({
            props: {
                item,
                actionCaption: 'Jump To',
            },
        });

        wrapper.find('.search-item').trigger('click');
        await nextTick();

        expect(wrapper.emitted().click).toBeTruthy();
        expect((wrapper.emitted().click as unknown as any[][])?.[0][0]).toStrictEqual(item);
    });

    test('it will render action caption label with aria-hidden attribute', async () => {
        const item = { shorthand: 'DUM', isActive: true };
        const wrapper = await mountComponentAsync({
            props: {
                item,
                actionCaption: 'Jump To',
            },
        });
        const actionCaption = wrapper.findAllComponents({ name: 'WtgBox' }).at(2);
        expect(actionCaption && actionCaption.attributes('aria-hidden')).toBe('true');
    });
});

async function mountComponentAsync({ props = {}, slots = {} } = {}) {
    const wrapper = mount(SearchItem, {
        props,
        slots,
        global: {
            plugins: [wtgUi],
        },
    });
    await flushPromises();
    return wrapper;
}
