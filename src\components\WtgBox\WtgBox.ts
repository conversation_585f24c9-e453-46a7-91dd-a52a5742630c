import WtgLayoutGrid from '@components/WtgLayoutGrid';
import { makeLayoutProps, useLayout } from '@composables/layoutGrid';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn/';
import { makeMeasureProps, useMeasure } from '@composables/measure';
import { defineComponent, h, PropType, useSlots, VNode } from 'vue';

export default defineComponent({
    name: 'WtgBox',
    props: {
        absolute: {
            type: Boolean,
            default: false,
        },
        bottom: {
            type: [Number, String] as PropType<number | string | undefined>,
            default: undefined,
        },
        fillAvailable: {
            type: Boolean,
            default: false,
        },
        left: {
            type: [Number, String] as PropType<number | string | undefined>,
            default: undefined,
        },
        right: {
            type: [Number, String] as PropType<number | string | undefined>,
            default: undefined,
        },
        top: {
            type: [Number, String] as PropType<number | string | undefined>,
            default: undefined,
        },
        ...makeLayoutGridColumnProps(),
        ...makeLayoutProps(),
        ...makeMeasureProps(),
    },
    setup(props) {
        useLayoutGridColumn(props);
        const { layoutClasses, isGridLayout, isGridFillLayout } = useLayout(props);
        const { measurableStyles } = useMeasure(props);
        const slots = useSlots();

        return { layoutClasses, isGridLayout, isGridFillLayout, measurableStyles, slots };
    },
    computed: {
        style(): {} {
            if (!this.absolute) {
                return this.measurableStyles;
            }

            let bottom = this.bottom;
            let left = this.left;
            let right = this.right;
            let top = this.top;

            let number = Number(bottom);
            if (!isNaN(number)) {
                bottom = number.toString() + 'px';
            }

            number = Number(left);
            if (!isNaN(number)) {
                left = number.toString() + 'px';
            }

            number = Number(right);
            if (!isNaN(number)) {
                right = number.toString() + 'px';
            }

            number = Number(top);
            if (!isNaN(number)) {
                top = number.toString() + 'px';
            }

            return {
                ...this.measurableStyles,
                position: 'absolute',
                bottom,
                left,
                right,
                top,
            };
        },
        boxClasses(): string[] {
            const classes = [...this.layoutClasses];

            if (this.fillAvailable) {
                classes.push('wtg-fill-available');
            }

            return classes;
        },
    },
    render(): VNode {
        const slot = this.slots.default ? this.slots.default() : [];
        const data = {
            class: this.boxClasses,
            ...this.$props,
            fillAvailable: this.isGridFillLayout,
            style: this.style,
        };
        if (this.isGridLayout || this.isGridFillLayout) {
            return h(WtgLayoutGrid, data, () => slot);
        } else {
            return h('div', data, slot);
        }
    },
});
