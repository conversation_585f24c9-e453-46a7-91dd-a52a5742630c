import { h, nextTick, reactive } from 'vue';
import MastheadSearch from '../MastheadSearch.vue';
import MockSearchProvider from '@components/framework/types/__tests__/__mocks__/SearchProvider';
import WtgUi from '../../../../../../../../WtgUi';
import { enableAutoUnmount, flushPromises, mount, VueWrapper } from '@vue/test-utils';
import {
    WtgFramework,
    WtgFrameworkMenuItem,
    WtgFrameworkMenuItemType,
    WtgFrameworkSearchHandler,
    WtgFrameworkSearchResultEntity,
} from '@components/framework/types';
import { setApplication } from '@composables/application';
import WtgApp from '@components/WtgApp';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

window.HTMLElement.prototype.scrollIntoView = jest.fn();

describe('MastheadSearch', () => {
    let wrapper: VueWrapper;
    let el: HTMLElement;
    let menu: WtgFrameworkMenuItem[];
    let application: WtgFramework;

    const searchProvider = new MockSearchProvider();
    const searchHandler = new WtgFrameworkSearchHandler(searchProvider);

    const item: WtgFrameworkSearchResultEntity = {
        PK: 'some-guid',
        entityType: 'IDummyBizo',
        entityName: 'Dummy Bizo',
        color: 'AABBCC',
        jobReference: 'DUMMYREFERENCE',
        matchedColumn: 'SEARCH1',
        keyFields: [{ caption: 'FIELD1-CAPTION', value: 'FIELD1' }],
    };

    beforeEach(async () => {
        application = reactive(new WtgFramework());
        application.searchHandler = searchHandler;
        application.searchProvider = searchProvider;

        jest.spyOn(MockSearchProvider.prototype, 'onSearchItemClicked');

        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        menu = [
            {
                action: WtgFrameworkMenuItemType.Link,
                caption: 'Test Link',
                icon: 'mdi-icon-1',
                href: '#/page/bc1cd66d-fb7b-444b-bc9b-3e9f16b81ee3',
                to: '#/page/bc1cd66d-fb7b-444b-bc9b-3e9f16b81ee3',
                id: 'ad4b0edc-e9dd-40dc-a2bf-19e8f54875b5',
                active: false,
            },
            {
                action: WtgFrameworkMenuItemType.Menu,
                caption: 'Test Menu',
                icon: 'mdi-icon-2',
                id: '5258a62e-da0a-458e-8a8c-1197e9e2318d',
                active: false,
                items: [
                    {
                        action: WtgFrameworkMenuItemType.Link,
                        caption: 'Test Level 2 Link',
                        icon: 'mdi-icon-3',
                        href: '#/page/5ba46f99-dd52-45d6-934b-3477cfdf263a',
                        to: '#/page/5ba46f99-dd52-45d6-934b-3477cfdf263a',
                        id: '960e0928-2715-49fd-bc7f-3939e3f6b338',
                        active: false,
                    },
                    {
                        action: WtgFrameworkMenuItemType.Menu,
                        caption: 'Test Level 2 Menu',
                        id: '7e9eee7b-9002-41fb-9bed-f8c43b923460',
                        active: false,
                        items: [
                            {
                                action: WtgFrameworkMenuItemType.Link,
                                caption: 'Test Level 3 Menu Link',
                                icon: 'mdi-icon-4',
                                href: '#/page/7b671dd3-e629-485e-9ca8-697e15dbde82',
                                to: '#/page/7b671dd3-e629-485e-9ca8-697e15dbde82',
                                id: '1b6928b0-44b6-4dff-9a33-510396a713c3',
                                active: false,
                            },
                        ],
                    },
                ],
            },
        ];
        application.menu = menu;

        setApplication(application);

        wrapper = await mountComponentAsync({ props: { overlay: true } });
        (wrapper.vm as any).availableShorthands = [];
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is MastheadSearch', () => {
        expect(wrapper.vm.$options.name).toBe('MastheadSearch');
    });

    describe('Opening and closing the dialog', () => {
        test('it uses the overlay prop to display & activate the dialog', async () => {
            wrapper.setProps({ overlay: false });
            await nextTick();
            expect(wrapper.findComponent({ name: 'WtgDialog' }).props('modelValue')).toBe(false);

            wrapper.setProps({ overlay: true });
            await nextTick();
            expect(wrapper.findComponent({ name: 'WtgDialog' }).props('modelValue')).toBe(true);
        });

        test('it will render a dialog with aria-label attribute', async () => {
            wrapper.setProps({ overlay: true });
            await nextTick();

            const dialog = wrapper.findComponent({ name: 'WtgDialog' });
            const card = dialog.findComponent({ name: 'WtgCard' });
            expect(card.attributes('aria-label')).toBe('Search');
        });

        test('it will render a text field with searchbox role attribute', async () => {
            wrapper.setProps({ overlay: true });
            await nextTick();

            const textField = wrapper.findComponent({ name: 'WtgTextField' });
            expect(textField.attributes('role')).toBe('searchbox');
        });

        test('it will render suggested filters shorthand with aria-hidden attribute', async () => {
            wrapper.setProps({ overlay: true });
            await nextTick();

            const shorthandTags = wrapper.findAllComponents({ name: 'WtgTag' });
            expect(shorthandTags?.at(0)?.attributes('aria-hidden')).toBe('true');
        });

        test('it can also be opened via CTRL + /', async () => {
            wrapper.setProps({ overlay: false });
            await nextTick();

            document.dispatchEvent(
                new KeyboardEvent('keyup', {
                    key: '/',
                    ctrlKey: true,
                    metaKey: true,
                })
            );

            expect(wrapper.emitted('update:overlay')![0][0]).toBe(true);
        });

        test('it will close the dialog when you click outside of it', async () => {
            const card = wrapper.findComponent({ name: 'WtgCard' });
            expect(card.exists()).toBe(true);
            card.vm.$emit('click-outside');
            await nextTick();

            expect(wrapper.emitted('update:overlay')![0][0]).toBe(false);
        });

        test('its initial state will have suggested filters', () => {
            expect((wrapper.vm as any).suggestedFilters.map((entity: any) => entity.shorthand)).toEqual(['DUM', 'DM2']);
        });
    });

    describe('Searching', () => {
        beforeEach(async () => {
            application.searchHandler.selectedShorthands = [];
            jest.useFakeTimers();
        });

        test('it will not be searching initially', () => {
            expect((wrapper.vm as any).searching).toBe(false);
        });

        test('it will be searching when more than 2 characters are entered', () => {
            const searchField = wrapper.findComponent({ name: 'WtgTextField' });
            searchField.vm.$emit('input', 'a');
            expect((wrapper.vm as any).searching).toBe(false);
            searchField.vm.$emit('input', 'aa');
            expect((wrapper.vm as any).searching).toBe(false);
            searchField.vm.$emit('input', 'aaa');
            expect((wrapper.vm as any).searching).toBe(true);
        });

        test('it will create a tag when you enter a suggested filter value', async () => {
            const searchField = wrapper.findComponent({ name: 'WtgTextField' });
            let filterTags = searchField.findAllComponents({ name: 'WtgTag' });
            expect(filterTags).toHaveLength(0);
            searchField.vm.$emit('input', 'DUM:');
            jest.runOnlyPendingTimers();
            await nextTick();

            filterTags = searchField.findAllComponents({ name: 'WtgTag' });
            expect(filterTags).toHaveLength(1);
            expect(filterTags.at(0)?.text()).toBe('Dummy Bizo');
        });

        test('search query containing multiple shorthands creates Multiple tag', async () => {
            const searchField = wrapper.findComponent({ name: 'WtgTextField' });
            let filterTags = searchField.findAllComponents({ name: 'WtgTag' });
            expect(filterTags).toHaveLength(0);
            searchField.vm.$emit('input', 'DUM:DM2:');
            jest.runOnlyPendingTimers();
            await nextTick();

            filterTags = searchField.findAllComponents({ name: 'WtgTag' });
            expect(filterTags).toHaveLength(1);
            expect(filterTags.at(0)?.text()).toBe('Multiple');
        });

        test('search query containing multiple same shorthands not create Multiple tag', async () => {
            const searchField = wrapper.findComponent({ name: 'WtgTextField' });
            let filterTag = searchField.findComponent({ name: 'WtgTag' });
            expect(filterTag.exists()).toBe(false);
            searchField.vm.$emit('input', 'DM2:DM2:');
            jest.runOnlyPendingTimers();
            await nextTick();

            filterTag = searchField.findComponent({ name: 'WtgTag' });
            expect(filterTag.exists()).toBe(true);
            expect(filterTag.text()).toBe('Dummy Bizo 2');
        });

        test('it will display the no results message when no results are found ', async () => {
            const searchField = wrapper.findComponent({ name: 'WtgTextField' });
            searchField.vm.$emit('input', 'xxx');
            jest.runOnlyPendingTimers();
            await nextTick();
            await nextTick();
            await nextTick();
            await nextTick();
            await nextTick();

            const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(3);
            expect(content?.text()).toBe('No matches found');
            expect(content?.find('[role="alert"]').exists()).toBe(true);
        });

        describe('Menu Items', () => {
            test('it will display application menu items that contain the search term', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'Level 3 Menu');
                jest.runOnlyPendingTimers();
                await nextTick();
                await nextTick();
                await nextTick();
                await nextTick();
                await nextTick();

                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const searchItems = content?.findAllComponents({ name: 'SearchItem' });
                expect(searchItems?.length).toBe(1);
                expect(searchItems?.at(0)?.text()).toContain('Level 3 Menu');
            });

            test('it will set the url to the href of the menu item', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'Level 3 Menu');
                jest.runOnlyPendingTimers();
                await nextTick();
                await nextTick();
                await nextTick();
                await nextTick();
                await nextTick();

                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const searchItems = content?.findAllComponents({ name: 'SearchItem' });
                searchItems?.at(0)?.vm.$emit('click', {
                    id: 'menuItem',
                    href: '#/page/6baeb77b-1828-4dcd-888b-e64f6e0fea2c',
                });
                expect(window.location.href).toContain('#/page/6baeb77b-1828-4dcd-888b-e64f6e0fea2c');
            });
        });

        describe('Suggested filters', () => {
            it.each([
                ['DUM', 0, 'Dummy Bizo'],
                ['DM2', 1, 'Dummy Bizo 2'],
            ])(
                'should add %s shorthand and tag when clicking suggested filter',
                async (shorthand, index, entityName) => {
                    const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                    let filterTag = searchField.findComponent({ name: 'WtgTag' });
                    const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                    const suggestedFilter1 = content?.findAllComponents({ name: 'SearchItem' }).at(index);
                    expect(suggestedFilter1?.text()).toContain(shorthand + ':');
                    expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);

                    suggestedFilter1?.vm.$emit('click', { shorthand });
                    jest.runOnlyPendingTimers();
                    await nextTick();

                    filterTag = searchField.findComponent({ name: 'WtgTag' });
                    expect(filterTag.exists()).toBe(true);
                    expect(filterTag.text()).toBe(entityName);
                    expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([shorthand]);
                }
            );

            it('should set multiple search handler shorthands', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                let filterTag = searchField.findComponent({ name: 'WtgTag' });
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });
                expect(suggestedFilters?.at(0)?.text()).toContain('DUM:');
                expect(suggestedFilters?.at(1)?.text()).toContain('DM2:');
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);

                suggestedFilters?.at(0)?.vm.$emit('click', { shorthand: 'DUM' });
                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(true);
                expect(filterTag.text()).toBe('Multiple');
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual(['DUM', 'DM2']);
            });

            it('should focus child input component when selecting different shorthands', async () => {
                const searchComponent = wrapper.vm.$refs.search as any;
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });
                expect(suggestedFilters?.at(0)?.text()).toContain('DUM:');
                expect(suggestedFilters?.at(1)?.text()).toContain('DM2:');
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);
                const searchInput = searchComponent.$el.getElementsByTagName('input')[0];
                expect(searchInput).toBeDefined();
                jest.spyOn(searchInput, 'focus');

                suggestedFilters?.at(0)?.vm.$emit('click', { shorthand: 'DUM' });
                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                jest.runOnlyPendingTimers();
                await nextTick();

                expect(searchInput.focus).toHaveBeenCalledTimes(2);
            });

            it('should focus child input component when re-selecting same shorthand', async () => {
                const searchComponent = wrapper.vm.$refs.search as any;
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });
                expect(suggestedFilters?.at(0)?.text()).toContain('DUM:');
                expect(suggestedFilters?.at(1)?.text()).toContain('DM2:');
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);
                const searchInput = searchComponent.$el.getElementsByTagName('input')[0];
                expect(searchInput).toBeDefined();
                jest.spyOn(searchInput, 'focus');

                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                jest.runOnlyPendingTimers();
                await nextTick();

                expect(searchInput.focus).toHaveBeenCalledTimes(2);
            });

            test('initial text for adding and shorthand selection is empty', async () => {
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });

                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);
                expect(suggestedFilters?.at(0)?.findAllComponents({ name: 'WtgLabel' })?.at(1)?.text()).toBe(
                    'Add Dummy Bizo to the search restriction'
                );
                expect(suggestedFilters?.at(1)?.findAllComponents({ name: 'WtgLabel' })?.at(1)?.text()).toBe(
                    'Add Dummy Bizo 2 to the search restriction'
                );
                expect(suggestedFilters?.at(0)?.props().actionCaption).toBe('Add');
                expect(suggestedFilters?.at(1)?.props().actionCaption).toBe('Add');
            });

            test('single filter selection changes text for removal', async () => {
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });

                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                jest.runOnlyPendingTimers();
                await nextTick();

                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual(['DM2']);
                expect(suggestedFilters?.at(0)?.findAllComponents({ name: 'WtgLabel' })?.at(1)?.text()).toBe(
                    'Add Dummy Bizo to the search restriction'
                );
                expect(suggestedFilters?.at(1)?.findAllComponents({ name: 'WtgLabel' })?.at(1)?.text()).toBe(
                    'Remove Dummy Bizo 2 from the search restriction'
                );
                expect(suggestedFilters?.at(0)?.props().actionCaption).toBe('Add');
                expect(suggestedFilters?.at(1)?.props().actionCaption).toBe('Remove');
            });

            it('re-selecting single filter selection reverts to text for adding', async () => {
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });

                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                jest.runOnlyPendingTimers();
                await nextTick();

                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);
                expect(suggestedFilters?.at(0)?.findAllComponents({ name: 'WtgLabel' })?.at(1)?.text()).toBe(
                    'Add Dummy Bizo to the search restriction'
                );
                expect(suggestedFilters?.at(1)?.findAllComponents({ name: 'WtgLabel' })?.at(1)?.text()).toBe(
                    'Add Dummy Bizo 2 to the search restriction'
                );
                expect(suggestedFilters?.at(0)?.props().actionCaption).toBe('Add');
                expect(suggestedFilters?.at(1)?.props().actionCaption).toBe('Add');
            });

            it('separate multi-filter selection changes text for removal', async () => {
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);

                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DUM' });
                jest.runOnlyPendingTimers();
                await nextTick();

                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual(['DM2', 'DUM']);
                expect(suggestedFilters?.at(0)?.findAllComponents({ name: 'WtgLabel' })?.at(1)?.text()).toBe(
                    'Remove Dummy Bizo from the search restriction'
                );
                expect(suggestedFilters?.at(1)?.findAllComponents({ name: 'WtgLabel' })?.at(1)?.text()).toBe(
                    'Remove Dummy Bizo 2 from the search restriction'
                );
                expect(suggestedFilters?.at(0)?.props().actionCaption).toBe('Remove');
                expect(suggestedFilters?.at(1)?.props().actionCaption).toBe('Remove');
            });
        });

        describe('Tooltips on tag', () => {
            test('no tooltip shows on single entity tag', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'DM2:');
                jest.runOnlyPendingTimers();
                await nextTick();

                const filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.props().tooltip).toBeUndefined();
            });

            test('tooltip shows on Multiple tag', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                let filterTag = searchField.findComponent({ name: 'WtgTag' });
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });

                suggestedFilters?.at(0)?.vm.$emit('click', { shorthand: 'DUM' });
                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.props().tooltip).toBe('Dummy Bizo, Dummy Bizo 2');
            });

            test('tooltip on Multiple tag is same when shorthand selection reordered', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                let filterTag = searchField.findComponent({ name: 'WtgTag' });
                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const suggestedFilters = content?.findAllComponents({ name: 'SearchItem' });

                suggestedFilters?.at(1)?.vm.$emit('click', { shorthand: 'DM2' });
                suggestedFilters?.at(0)?.vm.$emit('click', { shorthand: 'DUM' });
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.props().tooltip).toBe('Dummy Bizo, Dummy Bizo 2');
            });
        });

        describe('Backspacing on tag', () => {
            it('should remove shorthand and tag if search value is empty and backspace is pressed', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'DUM:');
                jest.runOnlyPendingTimers();
                await nextTick();
                let filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(true);

                (wrapper.vm as any).onInputKeyDown({ key: 'Backspace' } as KeyboardEvent);
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(false);
                expect(searchField.props().modelValue).toBe('');
            });

            it('should remove all multiple shorthands and tag if search value is empty and backspace is pressed', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'DUM:DM2:');
                jest.runOnlyPendingTimers();
                await nextTick();
                let filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(true);
                expect(filterTag.text()).toBe('Multiple');

                (wrapper.vm as any).onInputKeyDown({ key: 'Backspace' } as KeyboardEvent);
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(false);
                expect(searchField.props().modelValue).toBe('');
            });

            it('should do nothing if search value has text', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'DUM:abc');
                jest.runOnlyPendingTimers();
                await nextTick();
                let filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(true);

                (wrapper.vm as any).onInputKeyDown({ key: 'Backspace' } as KeyboardEvent);
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(true);
            });

            it('should do nothing if not a backspace', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'DUM:');
                jest.runOnlyPendingTimers();
                await nextTick();
                let filterTags = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTags.exists()).toBe(true);

                (wrapper.vm as any).onInputKeyDown({ key: 'Delete' } as KeyboardEvent);
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTags = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTags.exists()).toBe(true);
            });
        });

        describe('Clicking remove on tag', () => {
            it('should remove single shorthand and tag', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'DUM:abc');
                jest.runOnlyPendingTimers();
                await nextTick();
                let filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(true);
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual(['DUM']);

                filterTag.vm.$emit('click');
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(false);
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);
                expect(searchField.props().modelValue).toBe('abc');
            });

            it('should remove all multiple shorthands and tag', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'DUM:DM2:abc');
                jest.runOnlyPendingTimers();
                await nextTick();
                let filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(true);
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual(['DUM', 'DM2']);

                filterTag.vm.$emit('click');
                jest.runOnlyPendingTimers();
                await nextTick();

                filterTag = searchField.findComponent({ name: 'WtgTag' });
                expect(filterTag.exists()).toBe(false);
                expect((wrapper.vm as any).searchHandler.selectedShorthands).toEqual([]);
                expect(searchField.props().modelValue).toBe('abc');
            });
        });

        describe('Entities', () => {
            beforeEach(() => {
                jest.spyOn(WtgFrameworkSearchHandler.prototype, 'getItemsAsync').mockImplementation(() =>
                    Promise.resolve([item])
                );
            });

            test('it will display the matching entities search result', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'something');
                jest.runOnlyPendingTimers();
                await nextTick();
                await nextTick();

                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const searchItems = content?.findAllComponents({ name: 'SearchItem' });
                expect(searchItems?.length).toBe(1);
                expect(searchItems?.at(0)?.text()).toContain('Dummy Bizo');
            });

            test('it will call the search provider click handler when selected', async () => {
                const searchField = wrapper.findComponent({ name: 'WtgTextField' });
                searchField.vm.$emit('input', 'something');
                jest.runOnlyPendingTimers();
                await nextTick();
                await nextTick();

                const content = wrapper.findAllComponents({ name: 'WtgBox' }).at(5);
                const searchItems = content?.findAllComponents({ name: 'SearchItem' });
                searchItems?.at(0)?.vm.$emit('click', item);
                expect(searchProvider.onSearchItemClicked).toHaveBeenCalled();
            });
        });
    });

    describe('Search Help', () => {
        test('it will when opened show the search field as selected', async () => {
            const searchableFieldsButton = wrapper.findAllComponents({ name: 'WtgButton' }).at(0);
            await searchableFieldsButton?.trigger('click');

            expect((wrapper.vm as any).searchHelp).toBe(true);
        });

        test('it will close when the close button is clicked', async () => {
            const searchableFieldsButton = wrapper.findAllComponents({ name: 'WtgButton' }).at(0);
            await searchableFieldsButton?.trigger('click');

            const searchHelpDialog = wrapper.findComponent({ name: 'WtgModal' });
            expect(searchHelpDialog.exists()).toBe(true);
            const closeButton = searchHelpDialog.findComponent({ name: 'WtgButton' });
            expect(closeButton.text()).toBe('Close');
            await closeButton.trigger('click');

            expect((wrapper.vm as any).searchHelp).toBe(false);
        });

        test('it will present displaying the search fields for each filter', async () => {
            const searchableFieldsButton = wrapper.findAllComponents({ name: 'WtgButton' }).at(0);
            await searchableFieldsButton?.trigger('click');

            const searchHelpDialog = wrapper.findComponent({ name: 'WtgModal' });
            const filterTags = searchHelpDialog.findAllComponents({ name: 'WtgTag' });
            expect(filterTags.length).toBe(6);
            expect(filterTags?.at(0)?.text()).toBe('Dummy Bizo:');
            expect(filterTags?.at(1)?.text()).toBe('Translated-SEARCH1');
            expect(filterTags?.at(2)?.text()).toBe('Translated-SEARCH2');
            expect(filterTags?.at(3)?.text()).toBe('Dummy Bizo 2:');
            expect(filterTags?.at(4)?.text()).toBe('Translated-SEARCH1');
            expect(filterTags?.at(5)?.text()).toBe('Translated-SEARCH2');
        });
    });

    test('it renders .v-overlay__content.search-overlay-content and .v-card', async () => {
        const overlayContent = document.body.querySelector('.v-overlay__content.search-overlay-content');
        expect(overlayContent).not.toBeNull();

        const vCard = overlayContent?.querySelector('.v-card');
        expect(vCard).not.toBeNull();
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(MastheadSearch) } } = {}) {
        const app = mount(WtgApp as any, { global: { plugins: [wtgUi] } });
        const wrapper = mount(MastheadSearch, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
            attachTo: app.element,
        });
        await flushPromises();
        return wrapper;
    }
});
