<template>
    <div :class="messageBoxClass" class="message-box pa-2">
        <div class="d-flex flex-column-reverse flex-sm-row justify-space-between">
            <div class="mr-2">
                <span style="font-size: 12px" class="font-weight-bold message_font">{{ message.sender }}</span>
                <span style="font-size: 12px" class="pl-2 message_font">{{ message.time }}</span>
            </div>
            <div class="d-flex">
                <WtgButton size="xs" :style="scoreButtonStyle" class="rounded-e-0" @click="onLikeClick">
                    {{ likes }}
                    <WtgIcon right size="m" class="ml-1" :icon="likeIcon" />
                </WtgButton>
                <WtgButton size="xs" :style="scoreButtonStyle" class="rounded-s-0" @click="onDislikeClick">
                    {{ dislikes }}
                    <WtgIcon right size="m" class="ml-1" :icon="dislikeIcon" />
                </WtgButton>
            </div>
        </div>
        <div class="pt-1 message_font">{{ message.body }}</div>
    </div>
</template>

<script lang="ts">
import { WtgButton } from '@components/WtgButton';
import { WtgIcon } from '@components/WtgIcon';
import { defineComponent, PropType } from 'vue';
import { ConversationMessage, ConversationMessageType } from '../types';

export default defineComponent({
    name: 'WtgConversationMessage',
    components: {
        WtgButton,
        WtgIcon,
    },
    props: {
        message: {
            type: Object as PropType<ConversationMessage>,
            required: true,
        },
    },
    emits: ['like-click', 'dislike-click'],
    data() {
        return {};
    },
    computed: {
        messageBoxClass(): string[] {
            return [this.selfClass(), this.typeClass()];
        },
        likes(): number {
            const score = this.message.score;
            return score > 0 ? score : 0;
        },
        dislikes(): number {
            const score = this.message.score;
            return score < 0 ? -score : 0;
        },
        loadingDislikeScore(): boolean {
            return this.message.loadingDislikeScore ?? false;
        },
        loadingLikeScore(): boolean {
            return this.message.loadingLikeScore ?? false;
        },
        scoreButtonStyle(): Record<string, unknown> {
            return {
                pointerEvents: this.loadingDislikeScore || this.loadingLikeScore ? 'none' : 'auto',
            };
        },
        likeIcon(): string {
            return this.loadingLikeScore ? 's-icon-loading' : 's-icon-thumbs-up';
        },
        dislikeIcon(): string {
            return this.loadingDislikeScore ? 's-icon-loading' : 's-icon-thumbs-down';
        },
    },
    methods: {
        onLikeClick(): void {
            this.$emit('like-click', this.message);
        },
        onDislikeClick(): void {
            this.$emit('dislike-click', this.message);
        },
        typeClass(): string {
            if (this.message.type === ConversationMessageType.staff) {
                return 'bg-primary white--text';
            }
            if (this.message.type === ConversationMessageType.system) {
                return 'bg-accent white--text';
            }
            return 'bg-secondary white--text';
        },
        selfClass(): string {
            return this.message.isFromSelf ? 'ml-auto' : 'mr-auto';
        },
    },
});
</script>

<style lang="scss" scoped>
.message-box {
    width: fit-content;
    border-radius: 5px;
    max-width: 70%;
}
.message_font {
    font-family: Inter;
    white-space: pre-line;
}
</style>
