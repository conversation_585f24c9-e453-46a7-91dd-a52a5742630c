import WtgAlertModal from '@components/WtgAlertModal';
import WtgButton from '@components/WtgButton';
import WtgContainer from '@components/WtgContainer';
import { createDessertsService } from '@components/WtgDataTable/stories/DessertsService';
import WtgIcon from '@components/WtgIcon';
import WtgNumberField from '@components/WtgNumberField';
import WtgPanel, { WtgPanelHeader } from '@components/WtgPanel';
import WtgSearchDataTable from '@components/WtgSearchList/WtgSearchDataTable/WtgSearchDataTable.vue';
import WtgStatus from '@components/WtgStatus';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import { Meta, StoryContext, StoryObj } from '@storybook/vue3';
import { computed, reactive, ref, watchEffect } from 'vue';

type Story = StoryObj<typeof WtgSearchDataTable>;
const meta: Meta<typeof WtgSearchDataTable> = {
    title: 'Components/Data Table',
    component: WtgSearchDataTable,
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: [
                'headers',
                'items',
                'itemsLength',
                'loading',
                'itemValue',
                'search',
                'itemsPerPage',
                'itemsPerPageOptions',
                'page',
                'selectionMode',
            ],
        },
        docs: {
            description: {
                component: 'The data-table component is designed for displaying and searching information.',
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: { WtgPanel, WtgSearchDataTable },
        setup: () => ({ args }),
        template: `<div />`,
    }),
    argTypes: {
        mobile: {
            control: 'boolean',
        },
        mobileBreakpoint: {
            control: 'select',
            options: ['', 'xs', 'sm', 'md', 'lg', 'xl', 'xxl'],
        },
    },
};

function createProps(storyContext: StoryContext): string {
    const props = [];
    for (const arg in storyContext.args) {
        props.push(`${arg}="${storyContext.args[arg] + ''}"`);
    }
    return props.sort().join(' ');
}

export default meta;

function serverSnippetContent(args1: string): string {
    return `
            <div class="d-flex align-center mb-2 ga-2">
                <WtgPanelHeader id="server-label" class="mb-0">Server side table simulated items</WtgPanelHeader>
                <WtgNumberField aria-labelledby="server-label" v-model="options.simulatedItems" style="max-width: 200px"/>
            </div>    
            <WtgSearchDataTable
                caption="Desserts"                
                v-model:items-per-page="options.itemsPerPage"
                v-model:search="options.search"
                :fields="options.fields"
                fit-to-height
                :items-length="options.totalItems"
                :items="options.items"
                :loading="options.loading"
                :search="options.search"
                :layout="options.layout"
                show-actions
                :settings-actions="[{ id: 'export', caption: 'Export to excel', click: () => onExportClick() }, { id: 'import', caption: 'Import', click: () => onImportClick() }]"
                :selection-actions="selectionActions"
                v-model="modelValue"
                @add="onAdd"
                @refresh="onRefresh"
                @item-actions="onItemActions"
                @lazy-load="options.lazyItems += 50"
                @update:layout="options.layout = JSON.parse(JSON.stringify($event))"
                @update:options="options.loadItems($event)"
                @update:grouped-columns="options.groupBy = JSON.parse(JSON.stringify($event)); options.layout = { ...options.layout, groupedColumns: JSON.parse(JSON.stringify($event))};"
                ${args1}>
            </WtgSearchDataTable>
            <WtgAlertModal v-model="showDialog" :title="showDialogTitle" sentiment="warning">
                <template #default>
                    <div>
                        <p>
                            {{ showDialogText }}
                        </p>
                    </div>
                </template>
                <template #actions>
                    <wtg-button @click="showDialog = false">Cancel</wtg-button>
                </template>
            </WtgAlertModal>`;
}

export const Default: Story = {
    args: {
        showAdd: true,
        showFilters: true,
        showItemActions: true,
        showRefresh: true,
        showSelect: true,
        showCustomize: true,
    },
    parameters: {
        ...getChromaticParameters(),
        docs: {
            source: {
                transform: (source: string, storyContext: StoryContext) => `
<template>
  <WtgContainer layout="fill">
      <WtgPanel class="mb-2">
          <WtgCallout
            description="Search Data Table is a robust and versatile solution in our design system, serving as the primary data table for all applications. It offers a rich set of features tailored for enterprise-grade requirements."
            sentiment="info"
            title="Search Data Table"
          />
      </WtgPanel>             
      <WtgPanel fill-available layout="fill">   
      ${serverSnippetContent(`${createProps(storyContext)} `)}
      </WtgPanel>             
  </WtgContainer>
</template>

<script setup lang="ts">
import {WtgContainer, WtgAlertModal, WtgButton, WtgNumberField, WtgPanelHeader, WtgPanel, WtgCallout, WtgSearchDataTable} from '@wtg/wtg-components';
import { computed, ref } from 'vue';
import { createDessertsService } from './DessertsData/DessertsService';

const showDialogTitle = ref('');
const showDialogText = ref('');
const showDialog = ref(false);

const dessertsService = createDessertsService({lazyItems: 50});

const options = computed(() => {
    return dessertsService._options;
});

function onAdd(): void {
    showDialogTitle.value = 'Add';
    showDialogText.value = 'Add will be performed.';
    showDialog.value = true;
}

function onRefresh(): void {
    options.value.loadItems!(options.value);
}

function onItemActions(item): void {
    item.actions = [
        { id: '1', caption: 'Item Action 1', click: () => onItemActionClick() },
        { id: '2', caption: 'Item Action 2', click: () => onItemActionClick() },
        { id: '3', caption: 'Item Action 3', click: () => onItemActionClick() }
    ];
}

function onExportClick(): void {
    showDialogTitle.value = 'Export';
    showDialogText.value = 'Export will be performed.';
    showDialog.value = true;
}

function onImportClick(): void {
    showDialogTitle.value = 'Import';
    showDialogText.value = 'Import will be performed.';
    showDialog.value = true;
}

function onBulkActionClick(): void {
    showDialogTitle.value = 'Bulk Action';
    showDialogText.value = 'Bulk action will be performed.';
    showDialog.value = true;
}

const selectionActions = computed(() => {
    if (modelValue.value.length <= 1) {
        return [];
    } else {
        return [
            { id: '1', caption: 'Bulk action 1', click: () => onBulkActionClick() },
            { id: '2', caption: 'Bulk action 2', click: () => onBulkActionClick() },
            { id: '3', caption: 'Bulk action 3', click: () => onBulkActionClick() },
        ];
    }
});

function onItemActionClick(): void {
    showDialogTitle.value = 'Item action';
    showDialogText.value = 'Item action will be performed.';
    showDialog.value = true;
}

const modelValue = ref([]);
</script>`,
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: {
            WtgAlertModal,
            WtgButton,
            WtgContainer,
            WtgIcon,
            WtgSearchDataTable,
            WtgPanel,
            WtgPanelHeader,
            WtgNumberField,
            WtgStatus,
        },
        setup: () => {
            const dessertsService = createDessertsService({ lazyItems: 50 });

            const showDialogTitle = ref('');
            const showDialogText = ref('');
            const showDialog = ref(false);
            const modelValue = ref([]);
            function onBulkActionClick(): void {
                showDialogTitle.value = 'Bulk Action';
                showDialogText.value = 'Bulk action will be performed.';
                showDialog.value = true;
            }
            const selectionActions = computed(() => {
                if (modelValue.value.length <= 1) {
                    return [];
                } else {
                    return [
                        { id: '1', caption: 'Bulk action 1', click: () => onBulkActionClick() },
                        { id: '2', caption: 'Bulk action 2', click: () => onBulkActionClick() },
                        { id: '3', caption: 'Bulk action 3', click: () => onBulkActionClick() },
                    ];
                }
            });

            return {
                args,
                options: dessertsService._options,
                showDialog,
                showDialogText,
                showDialogTitle,
                onAdd: () => {
                    showDialogTitle.value = 'Add';
                    showDialogText.value = 'Add will be performed.';
                    showDialog.value = true;
                },
                onExportClick: () => {
                    showDialogTitle.value = 'Export';
                    showDialogText.value = 'Export will be performed.';
                    showDialog.value = true;
                },
                onImportClick: () => {
                    showDialogTitle.value = 'Import';
                    showDialogText.value = 'Import will be performed.';
                    showDialog.value = true;
                },
                onItemActions: (item: any) => {
                    function onItemActionClick() {
                        showDialogTitle.value = 'Item action';
                        showDialogText.value = 'Item action will be performed.';
                        showDialog.value = true;
                    }

                    item.actions = [
                        { id: '1', caption: 'Item Action 1', click: () => onItemActionClick() },
                        { id: '2', caption: 'Item Action 2', click: () => onItemActionClick() },
                        { id: '3', caption: 'Item Action 3', click: () => onItemActionClick() },
                    ];
                },
                onRefresh: () => {
                    dessertsService._options.loadItems!(dessertsService._options);
                },
                modelValue,
                selectionActions,
            };
        },
        template: `<WtgContainer color="canvas" layout="fill"> <WtgPanel fill-available layout="fill"> ${serverSnippetContent(
            `v-bind="args" `
        )}</WtgPanel>
        </WtgContainer>`,
    }),
};

function serverEditableSnippetContent(args1: string): string {
    return `
            <WtgSearchDataTable
                caption="Desserts"                
                v-model:items-per-page="options.itemsPerPage"
                v-model:search="options.search"
                v-model:showAlertsOnly="showAlertsOnly"
                :fields="options.fields"
                fit-to-height
                :items-length="options.totalItems"
                :items="currentItems"
                inline-edit="cell"
                :loading="options.loading"
                :search="options.search"
                :layout="options.layout"
                show-actions
                :settings-actions="[{ id: 'export', caption: 'Export', click: () => onExportClick() }, { id: 'import', caption: 'Import', click: () => onImportClick() }]"
                :selection-actions="[{ id: 'delete', caption: 'Delete selected', click: () => onDeleteClick() }]"
                :total-alerts-count="totalAlertsCount"
                :warning-alerts-count="warningAlertsCount"
                :error-alerts-count="errorAlertsCount"
                @inline-add="onAdd"
                @item-actions="onItemActions"
                @lazy-load="options.lazyItems += 50"
                @update:layout="options.layout = JSON.parse(JSON.stringify($event))"
                @update:options="options.loadItems($event)"
                ${args1}>
            </WtgSearchDataTable>
            <WtgAlertModal v-model="showDialog" :title="showDialogTitle" sentiment="warning">
                <template #default>
                    <div>
                        <p>
                            {{ showDialogText }}
                        </p>
                    </div>
                </template>
                <template #actions>
                    <wtg-button @click="showDialog = false">Cancel</wtg-button>
                </template>
            </WtgAlertModal>`;
}

export const Editable: Story = {
    args: {
        showFilters: true,
        showInlineAdd: true,
        showItemActions: true,
        showSelect: true,
        showCustomize: true,
    },
    parameters: {
        ...getChromaticParameters(),
        docs: {
            source: {
                transform: (source: string, storyContext: StoryContext) => `
<template>
  <WtgContainer color="canvas" layout="fill">
      <WtgPanel class="mb-2">
          <WtgCallout
            description="Search Data Table supports inline editing and is fully customizable including support for user validations."
            sentiment="info"
            title="Search Data Table"
          />
      </WtgPanel>             
      <WtgPanel fill-available layout="fill">   
      ${serverEditableSnippetContent(`${createProps(storyContext)} `)}
      </WtgPanel>             
  </WtgContainer>
</template>

<script setup lang="ts">
import {WtgContainer, WtgAlertModal, WtgButton, WtgPanel, WtgCallout, WtgSearchDataTable} from '@wtg/wtg-components';
import { computed, ref, watchEffect, reactive } from 'vue';
import { createDessertsService } from './DessertsData/DessertsService';

const showDialogTitle = ref('');
const showDialogText = ref('');
const showDialog = ref(false);
const showAlertsOnly = ref(false);

const dessertsService = createDessertsService({lazyItems: 50, simulatedItems: 10});

const currentItems = computed(() => {
    const items = dessertsService._options.items.map((dessertItem: any): any => {
        const item = reactive({
            ...dessertItem,
            cell: {
                name: {
                    messages: [],
                },
                fat: {
                    messages: [],
                },
                calories: {
                    messages: [],
                },
                available: {
                    messages: [],
                },
                expiry: {
                    messages: [],
                },
                country: {
                    messages: [],
                },
                status: {
                    messages: [],
                },
                unit: {
                    messages: [],
                },
            },
        });

        const maxCalories = 500;
        const maxFat = 10;
        watchEffect(() => {
            item.cell.calories.messages = [];
            if (item.calories > maxCalories) {
                item.cell.calories.messages.push({
                    level: 1,
                    text: "no of calories is greater than 500",
                });
            }
        });

        watchEffect(() => {
            item.cell.fat.messages = [];
            if (item.fat > maxFat) {
                item.cell.fat.messages.push({ text: "Fat amount is more than 10", level: 3 });
            }
        });

        watchEffect(() => {
            item.cell.country.messages = [];
            if (item.country !== 'AU') {
                item.cell.country.messages.push({
                    text: 'This item is unavaliable in NZ',
                    level: 3,
                });
            }
        });

        watchEffect(() => {
            item.cell.status.messages = [];
            if (item.status === 'out' || item.status === 'OOS') {
                item.cell.status.messages.push({ text: 'This item is out of stock', level: 4 });
            }
        });

        watchEffect(() => {
            item.cell.available.messages = [];
            if (!item.available) {
                item.cell.available.messages.push({ text: 'This item is unavailable', level: 3 });
            }
        });

        watchEffect(() => {
            item.cell.expiry.messages = [];
            if (item.expiry) {
                const expiryDate = new Date(item.expiry);
                const limitation = new Date('2025-01-01');
                if (expiryDate < limitation) {
                    item.cell.expiry.messages.push({ text: 'This item is expired', level: 4 });
                }
            }
        });

        watchEffect(() => {
            item.cell.unit.messages = [];
            if (item.unit.unit === 'IN') {
                item.cell.unit.messages.push({ text: 'It cannot be measured in inches', level: 4 });
            }
        });

        return item;
    });

    if (showAlertsOnly.value) {
        return items.filter((item: any) => {
            return Object.keys(item.cell).some((field) => item.cell[field].messages.length > 0);
        });
    }

    return items;
});

function countAlerts(alertType: number): number {
    let count = 0;
    currentItems.value.forEach((item: any) => {
        Object.keys(item.cell).forEach((field) => {
            const messages = item.cell[field].messages;
            if (messages) {
                count += messages.filter((message: any) => message.level === alertType).length;
            }
        });
    });
    return count;
}
const errorAlertsCount = computed(() => {
    return countAlerts(4);
});
const warningAlertsCount = computed(() => {
    return countAlerts(2) + countAlerts(3);
});
const totalAlertsCount = computed(() => {
    return countAlerts(1) + warningAlertsCount.value + errorAlertsCount.value;
});

const options = computed(() => {
    return dessertsService._options;
});

function onAdd(): void {
    showDialogTitle.value = 'Add';
    showDialogText.value = 'Add will be performed.';
    showDialog.value = true;
}

function onItemActions(item): void {
    item.actions = [
        { id: '1', caption: 'Item Action 1', click: () => onItemActionClick() },
        { id: '2', caption: 'Item Action 2', click: () => onItemActionClick() },
        { id: '3', caption: 'Item Action 3', click: () => onItemActionClick() }
    ];
}

function onExportClick(): void {
    showDialogTitle.value = 'Export';
    showDialogText.value = 'Export will be performed.';
    showDialog.value = true;
}

function onImportClick(): void {
    showDialogTitle.value = 'Import';
    showDialogText.value = 'Import will be performed.';
    showDialog.value = true;
}

function onDeleteClick(): void {
    showDialogTitle.value = 'Delete selected';
    showDialogText.value = 'Delete will be performed.';
    showDialog.value = true;
}

function onItemActionClick(): void {
    showDialogTitle.value = 'Item action';
    showDialogText.value = 'Item action will be performed.';
    showDialog.value = true;
}

</script>`,
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: {
            WtgAlertModal,
            WtgButton,
            WtgContainer,
            WtgIcon,
            WtgSearchDataTable,
            WtgPanel,
            WtgStatus,
        },
        setup: () => {
            const dessertsService = createDessertsService({ lazyItems: 50, simulatedItems: 10 });

            const showDialogTitle = ref('');
            const showDialogText = ref('');
            const showDialog = ref(false);
            const showAlertsOnly = ref(false);

            const currentItems = computed(() => {
                const items = dessertsService._options.items.map((dessertItem: any): any => {
                    const item = reactive({
                        ...dessertItem,
                        cell: {
                            name: {
                                messages: [],
                            },
                            fat: {
                                messages: [],
                            },
                            calories: {
                                messages: [],
                            },
                            available: {
                                messages: [],
                            },
                            expiry: {
                                messages: [],
                            },
                            country: {
                                messages: [],
                            },
                            status: {
                                messages: [],
                            },
                            unit: {
                                messages: [],
                            },
                        },
                    });

                    const maxCalories = 500;
                    const maxFat = 10;
                    watchEffect(() => {
                        item.cell.calories.messages = [];
                        if (item.calories > maxCalories) {
                            item.cell.calories.messages.push({
                                level: 1,
                                text: `no of calories ${item.calories} is greater than ${maxCalories}`,
                            });
                        }
                    });

                    watchEffect(() => {
                        item.cell.fat.messages = [];
                        if (item.fat > maxFat) {
                            item.cell.fat.messages.push({ text: `Fat amount is more than ${maxFat}`, level: 3 });
                        }
                    });

                    watchEffect(() => {
                        item.cell.country.messages = [];
                        if (item.country !== 'AU') {
                            item.cell.country.messages.push({
                                text: `This item is unavaliable in ${item.country}`,
                                level: 3,
                            });
                        }
                    });

                    watchEffect(() => {
                        item.cell.status.messages = [];
                        if (item.status === 'out' || item.status === 'OOS') {
                            item.cell.status.messages.push({ text: 'This item is out of stock', level: 4 });
                        }
                    });

                    watchEffect(() => {
                        item.cell.available.messages = [];
                        if (!item.available) {
                            item.cell.available.messages.push({ text: 'This item is unavailable', level: 3 });
                        }
                    });

                    watchEffect(() => {
                        item.cell.expiry.messages = [];
                        if (item.expiry) {
                            const expiryDate = new Date(item.expiry);
                            const limitation = new Date('2025-01-01');
                            if (expiryDate < limitation) {
                                item.cell.expiry.messages.push({ text: 'This item is expired', level: 4 });
                            }
                        }
                    });

                    watchEffect(() => {
                        item.cell.unit.messages = [];
                        if (item.unit.unit === 'IN') {
                            item.cell.unit.messages.push({ text: 'It cannot be measured in inches', level: 4 });
                        }
                    });

                    return item;
                });

                if (showAlertsOnly.value) {
                    return items.filter((item: any) => {
                        return Object.keys(item.cell).some((field) => item.cell[field].messages.length > 0);
                    });
                }

                return items;
            });

            function countAlerts(alertType: number): number {
                let count = 0;
                currentItems.value.forEach((item: any) => {
                    Object.keys(item.cell).forEach((field) => {
                        const messages = item.cell[field].messages;
                        if (messages) {
                            count += messages.filter((message: any) => message.level === alertType).length;
                        }
                    });
                });
                return count;
            }
            const errorAlertsCount = computed(() => {
                return countAlerts(4);
            });
            const warningAlertsCount = computed(() => {
                return countAlerts(2) + countAlerts(3);
            });
            const totalAlertsCount = computed(() => {
                return countAlerts(1) + warningAlertsCount.value + errorAlertsCount.value;
            });

            return {
                args,
                options: dessertsService._options,
                currentItems,
                showDialog,
                showDialogText,
                showDialogTitle,
                showAlertsOnly,
                totalAlertsCount,
                warningAlertsCount,
                errorAlertsCount,
                onAdd: () => {
                    showDialogTitle.value = 'Add';
                    showDialogText.value = 'Add will be performed.';
                    showDialog.value = true;
                },
                onExportClick: () => {
                    showDialogTitle.value = 'Export';
                    showDialogText.value = 'Export will be performed.';
                    showDialog.value = true;
                },
                onImportClick: () => {
                    showDialogTitle.value = 'Import';
                    showDialogText.value = 'Import will be performed.';
                    showDialog.value = true;
                },
                onDeleteClick: () => {
                    showDialogTitle.value = 'Delete selected';
                    showDialogText.value = 'Delete will be performed.';
                    showDialog.value = true;
                },
                onItemActions: (item: any) => {
                    function onItemActionClick() {
                        showDialogTitle.value = 'Item action';
                        showDialogText.value = 'Item action will be performed.';
                        showDialog.value = true;
                    }

                    item.actions = [
                        { id: '1', caption: 'Item Action 1', click: () => onItemActionClick() },
                        { id: '2', caption: 'Item Action 2', click: () => onItemActionClick() },
                        { id: '3', caption: 'Item Action 3', click: () => onItemActionClick() },
                    ];
                },
            };
        },
        template: `<WtgContainer color="canvas" layout="fill"> <WtgPanel fill-available layout="fill"> ${serverEditableSnippetContent(
            `v-bind="args" `
        )}</WtgPanel>
        </WtgContainer>`,
    }),
};

function snippetContent(args1: string): string {
    return `<WtgSearchDataTable
            caption="Desserts"
             ${args1}
            :fields="[
                { name: 'name', text: 'Dessert (100g serving)', type: 'string', width: 300 },
                { name: 'fat', text: 'Fat (g)', type: 'number', width: 150, align: 'end' },
                { name: 'calories', text: 'Calories', type: 'number', width: 150, align: 'end' },
            ]"
            :items="[
                { id: '1', name: 'Frozen Yogurt 1', calories: 961, fat: 1 },
                { id: '2', name: 'Ice cream sandwich 2', calories: 1032, fat: 1 },
                { id: '3', name: 'Eclair 3', calories: 780, fat: 3 },
                { id: '4', name: 'Cupcake 4', calories: 870, fat: 5 },
                { id: '5', name: 'Gingerbread 5', calories: 424, fat: 10 },
                { id: '6', name: 'Frozen Yogurt 6', calories: 299, fat: 5 },
                { id: '7', name: 'Ice cream sandwich 7', calories: 147, fat: 9 },
                { id: '8', name: 'Eclair 8', calories: 366, fat: 3 },
                { id: '9', name: 'Cupcake 9', calories: 361, fat: 2 },
                { id: '10', name: 'Gingerbread 10', calories: 363, fat: 5 },
            ]"
        >
        </WtgSearchDataTable>`;
}

export const FullPage: Story = {
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
        docs: {
            source: {
                code: `<template>
  <WtgContainer color="canvas" layout="fill">
      <WtgPanel class="mb-2">
          <WtgCallout
            description="A full-page data table fills the available area of the screen, growing and shrinking as needed to occupy the remaining viewport space. This approach is particularly useful in enterprise applications where users have large screens and need to view as much tabular data as possible."
            sentiment="info"
            title="Full-page data table"
          />
      </WtgPanel>                
      ${snippetContent('full-page ')}
  </WtgContainer>
</template>

<script setup lang="ts">
import {WtgContainer, WtgPanel, WtgCallout, WtgSearchDataTable} from '@wtg/wtg-components';
</script>`,
            },
        },
        layout: 'fullscreen',
    },
    tags: ['!dev'],
    render: () => ({
        components: { WtgContainer, WtgSearchDataTable },
        template: `<WtgContainer color="canvas" layout="fill"> ${snippetContent('full-page ')}</WtgContainer>`,
    }),
};

export const ShareAvailableHeight: Story = {
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
        docs: {
            source: {
                code: `<template>
  <WtgContainer color="canvas" layout="fill" class="ga-2">
      <WtgPanel class="mb-2">
          <WtgCallout
            description="A fill-available-height data table adjusts to fit the height of its parent container. This layout is useful when a table needs to take up the remaining space in a structured layout, such as when positioned beside a header/details panel or when two tables share equal vertical space."
            sentiment="info"
            title="Fill-available-height data tables"
          />
      </WtgPanel>                
      ${snippetContent('full-page ')}
      ${snippetContent('full-page ')}
  </WtgContainer>
</template>

<script setup lang="ts">
import {WtgContainer, WtgPanel, WtgCallout, WtgSearchDataTable} from '@wtg/wtg-components';
</script>`,
            },
        },
        layout: 'fullscreen',
    },
    tags: ['!dev'],
    render: () => ({
        components: { WtgContainer, WtgSearchDataTable },
        template: `<WtgContainer color="canvas" layout="fill" class="ga-2"> ${snippetContent(
            'full-page '
        )}  ${snippetContent('full-page ')}</WtgContainer>`,
    }),
};
