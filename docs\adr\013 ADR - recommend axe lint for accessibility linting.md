# Recommend axe lint for accessibility linting

## Status
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

As part of our broader efforts to ensure accessibility across Supply, we explored automated tooling to support engineers in catching accessibility issues early. While manual testing and screen reading checks remain essential, linters can help catch common violations, such as missing alt text quickly during development.

The [axe Accessibility Linter](https://marketplace.visualstudio.com/items?itemName=deque-systems.vscode-axe-linter), based on the open-source axe-core engine developed by Deque Systems, is widely adopted in the industry and considered a standard for automated accessibility checks. It integrates with VSCode and provides real-time feedback to developers.

## Decision

- Teams are recommended to use axe Accessibility Linter for accessibility linting to complement manual testing and product team evaluation.

## Consequences

- Developers can catch basic accessibility issues earlier in the dev cycle.
- Accessibility knowledge becomes more embedded in day-to-day work.
- Reduces the burden on product team members for catching accessibility oversights.
---

### Notes
This file follows the format introduced in [Documenting Architecture Decisions](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions) by <PERSON>. We recommend using sequential file naming (e.g., `0001-title.md`) and keeping ADRs under `docs/adr/`.

