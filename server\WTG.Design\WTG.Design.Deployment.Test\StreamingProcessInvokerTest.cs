using System;
using System.Text;
using System.Threading.Tasks;
using Dat.Integration;
using Moq;
using NUnit.Framework;

namespace WTG.Design.Deployment.Test
{
	public class StreamingProcessInvokerTest
	{
		[Test]
		public async Task InvokesProcessAndCapturesOutput()
		{
			await invoker.ExecuteProcessAsync("cmd.exe", new[] { "/c", "echo", "Hello World" }).ConfigureAwait(false);

			logger.Verify();

			Assert.That(sb.ToString().TrimEnd(), Is.EqualTo("\"Hello World\""));
		}

		[Test]
		public void ThrowsExceptionIfProcessFailsToStart()
		{
			Assert.That(
				async () => await invoker.ExecuteProcessAsync("D34863FB-8353-4129-9EB6-FB605065D98F.exe", Array.Empty<string>()).ConfigureAwait(false),
				Throws.Exception);
		}

		[Test]
		public void ThrowsExceptionIfProcessReturnsNonZeroExitCode()
		{
			Assert.That(
				async () => await invoker.ExecuteProcessAsync("cmd.exe", new[] { "/c", "exit", "/b", "1" }).ConfigureAwait(false),
				Throws.Exception.With.Message.EqualTo("cmd.exe failed with exit code 1."));
		}

		Mock<ITaskLogger> logger;
		IStreamingProcessInvoker invoker;
		StringBuilder sb;

		[SetUp]
		public void SetUp()
		{
			sb = new StringBuilder();
			logger = new Mock<ITaskLogger>();
			logger.Setup(l => l.RecordInfo(It.IsAny<string>())).Callback(delegate(string info) { sb.AppendLine(info); }).Verifiable();

			invoker = new StreamingProcessInvoker(logger.Object);
		}
	}
}
