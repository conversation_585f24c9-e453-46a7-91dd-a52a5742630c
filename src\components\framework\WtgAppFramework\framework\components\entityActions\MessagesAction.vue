<template>
    <div v-if="action.visible">
        <WtgIconButton
            v-if="isTabletOrMobile"
            variant="ghost"
            icon="s-icon-chat"
            :aria-label="action.caption"
            aria-haspopup="dialog"
            :tooltip="action.caption"
            @click="action.onInvoke"
        >
        </WtgIconButton>
        <WtgButton v-else variant="ghost" leading-icon="s-icon-chat" aria-haspopup="dialog" @click="action.onInvoke">
            {{ action.caption }}
        </WtgButton>
    </div>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgIconButton from '@components/WtgIconButton';
import { WtgFrameworkTask, WtgFrameworkTaskStandardAction } from '@components/framework/types';
import { useFramework } from '@composables/framework';
import { computed, PropType } from 'vue';

const props = defineProps({
    task: { type: Object as PropType<WtgFrameworkTask>, default: undefined },
});

const { isTabletOrMobile } = useFramework();

const action = computed((): WtgFrameworkTaskStandardAction => {
    return (
        props.task?.showMessagesAction ?? {
            visible: false,
            caption: 'Messages',
            label: 'Messages',
            onInvoke: (): void => undefined,
        }
    );
});
</script>
