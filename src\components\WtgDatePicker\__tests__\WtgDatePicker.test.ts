import WtgDatePicker from '@components/WtgDatePicker/WtgDatePicker.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const selectors = {
    monthViewButton:
        'button[class="v-btn v-theme--light v-btn--density-default v-btn--rounded v-btn--size-default v-btn--variant-text v-date-picker-controls__month-btn"]',
    yearViewButton:
        'button[class="v-btn v-btn--icon v-theme--light v-btn--density-comfortable v-btn--size-default v-btn--variant-text v-date-picker-controls__mode-btn"]',
    monthPrevNextButton:
        'button[class="v-btn v-btn--icon v-theme--light v-btn--density-default v-btn--size-default v-btn--variant-text"]',
    daysContainer: '.v-date-picker-month__days',
    yearOptionButton:
        'button[class="v-btn v-theme--light v-btn--density-default v-btn--rounded v-btn--size-default v-btn--variant-text"]',
    yearsContainer: '.v-date-picker-years__content',
    monthOptionButton:
        'button[class="v-btn v-theme--light v-btn--density-default v-btn--rounded v-btn--size-default v-btn--variant-text"]',
    monthsContainer: '.v-date-picker-months__content',
    selectedDay: '.v-date-picker-month__day--selected',
};

window.HTMLElement.prototype.scrollIntoView = jest.fn();

describe('WtgDatePicker', () => {
    beforeEach(() => {
        jest.useFakeTimers().setSystemTime(new Date('2020-01-19'));
    });

    afterEach(() => jest.resetAllMocks());

    test('it renders datepicker with expected components', () => {
        const wrapper = mountComponent();

        const datePicker = wrapper.findComponent({ name: 'VDatePicker' });
        expect(datePicker.exists()).toBe(true);

        expect(wrapper.classes()).toContain('wtg-date-picker-dialog');
        expect(wrapper.find('.wtg-button__content').exists()).toBe(true);
        expect(wrapper.find('.wtg-button__content').text()).toBe('Today');
        expect(wrapper.find(selectors.monthViewButton).exists()).toBe(true);
        expect(wrapper.find(selectors.yearViewButton).exists()).toBe(true);
        expect(wrapper.findAll(selectors.monthPrevNextButton).length).toBe(2);

        expect(wrapper.find(selectors.monthViewButton).text()).toBe('January 2020');
    });

    test('it handles clicking the "today"', async () => {
        const mockedDate = new Date('2020-01-19');
        jest.useFakeTimers().setSystemTime(mockedDate);
        const wrapper = mountComponent();

        const datePicker = wrapper.findComponent({ name: 'VDatePicker' });
        expect(datePicker.exists()).toBe(true);

        expect(wrapper.classes()).toContain('wtg-date-picker-dialog');
        const todayButton = wrapper.find('.wtg-button__content');
        expect(todayButton.exists()).toBe(true);
        expect(todayButton.text()).toBe('Today');

        await todayButton.trigger('click');
        await wrapper.vm.$nextTick();

        expect(datePicker.vm.modelValue).toEqual(mockedDate);
        expect(wrapper.emitted('update:modelValue')?.length).toEqual(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2020-01-19']);
    });

    test('it handles clicking the "today" button when todayDateFn is passed', async () => {
        const mockTodayDateFn = () => '2025-01-21';
        const wrapper = mountComponent({ todayDateFn: mockTodayDateFn });
        const todayButton = wrapper.find('.wtg-button__content');

        await todayButton.trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2025-01-21']);
    });

    test('it renders in day view by default', () => {
        const wrapper = mountComponent();

        const datePicker = wrapper.findComponent({ name: 'VDatePicker' });
        expect(datePicker.exists()).toBe(true);

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(true);
        const ninethDay = wrapper.findAll('.v-date-picker-month__day-btn')[10];
        expect(ninethDay.text()).toBe('9');
        expect(wrapper.find(selectors.yearsContainer).exists()).toBe(false);
        expect(wrapper.findAll(selectors.yearOptionButton).length).toBe(0);
        expect(wrapper.find(selectors.monthsContainer).exists()).toBe(false);
        expect(wrapper.findAll(selectors.monthOptionButton).length).toBe(0);
    });

    test('it renders month view by default if "monthYearOnly" prop is true', () => {
        const wrapper = mountComponent({
            monthYearOnly: true,
        });

        const datePicker = wrapper.findComponent({ name: 'VDatePicker' });
        expect(datePicker.exists()).toBe(true);

        expect(wrapper.find(selectors.monthsContainer).exists()).toBe(true);
        expect(wrapper.find(selectors.monthsContainer).element.children.length).toBe(12);
        expect(wrapper.findAll(selectors.monthOptionButton).length).toBe(11);

        expect(wrapper.find(selectors.yearsContainer).exists()).toBe(false);
        expect(wrapper.find(selectors.daysContainer).exists()).toBe(false);
    });

    test('it selects a day updates the value', async () => {
        const wrapper = mountComponent();

        const datePicker = wrapper.findComponent({ name: 'VDatePicker' });
        expect(datePicker.exists()).toBe(true);

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(true);

        const ninethDay = wrapper.findAll('.v-date-picker-month__day-btn')[10];
        expect(ninethDay.text()).toBe('9');

        await ninethDay.trigger('click');
        await wrapper.vm.$nextTick();

        expect((datePicker.vm.modelValue as Date).toLocaleDateString('en-GB')).toEqual('09/01/2020');
        expect(wrapper.emitted('update:modelValue')?.length).toEqual(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2020-01-09']);
        expect(wrapper.emitted('close')?.length).toEqual(1);
    });

    test('it selects a new day + month + year', async () => {
        const wrapper = mountComponent();

        const datePicker = wrapper.findComponent({ name: 'VDatePicker' });
        expect(datePicker.exists()).toBe(true);

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(true);

        await wrapper.find(selectors.yearViewButton).trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(false);
        expect(wrapper.find(selectors.yearsContainer).exists()).toBe(true);

        const y2k = wrapper.findAll(selectors.yearOptionButton)[80];
        expect(y2k.text()).toBe('2000');

        await y2k.trigger('click');
        await wrapper.vm.$nextTick();

        await wrapper.find(selectors.monthViewButton).trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(false);
        expect(wrapper.find(selectors.monthOptionButton).exists()).toBe(true);

        const august = wrapper.findAll(selectors.monthOptionButton)[6];
        expect(august.text()).toBe('Aug');

        await august.trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(true);

        const ninethDay = wrapper.findAll('.v-date-picker-month__day-btn')[9];
        expect(ninethDay.text()).toBe('9');

        await ninethDay.trigger('click');
        await wrapper.vm.$nextTick();

        expect((datePicker.vm.modelValue as Date).toLocaleDateString('en-GB')).toEqual('09/08/2000');
        expect(wrapper.emitted('update:modelValue')?.length).toEqual(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2000-08-09']);
        expect(wrapper.emitted('close')?.length).toEqual(1);
    });

    test("it selects date in 'yearMonthOnly' mode", async () => {
        const wrapper = mountComponent({
            monthYearOnly: true,
        });

        const datePicker = wrapper.findComponent({ name: 'VDatePicker' });
        expect(datePicker.exists()).toBe(true);

        expect(wrapper.find(selectors.monthsContainer).exists()).toBe(true);

        await wrapper.find(selectors.yearViewButton).trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(false);
        expect(wrapper.find(selectors.yearsContainer).exists()).toBe(true);

        const nineteenEightyFour = wrapper.findAll(selectors.yearOptionButton)[64];
        expect(nineteenEightyFour.text()).toBe('1984');

        await nineteenEightyFour.trigger('click');
        await wrapper.vm.$nextTick();

        await wrapper.find(selectors.monthViewButton).trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(false);
        expect(wrapper.find(selectors.monthOptionButton).exists()).toBe(true);

        const april = wrapper.findAll(selectors.monthOptionButton)[2];
        expect(april.text()).toBe('Apr');

        await april.trigger('click');
        await wrapper.vm.$nextTick();

        expect((datePicker.vm.modelValue as Date).toLocaleDateString('en-GB')).toEqual('01/04/1984');
        expect(wrapper.emitted('update:modelValue')?.length).toEqual(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['1984-04-01']);
        expect(wrapper.emitted('close')?.length).toEqual(1);
    });

    test('it should switch between day and month view', async () => {
        const wrapper = mountComponent();

        expect(wrapper.find(selectors.daysContainer).exists()).toBe(true);

        await wrapper.setProps({ monthYearOnly: true });

        expect(wrapper.find(selectors.monthsContainer).exists()).toBe(true);
    });

    test('it should show model value changes', async () => {
        const mockedDate = new Date('2020-01-08');
        jest.useFakeTimers().setSystemTime(mockedDate);
        const wrapper = mountComponent();

        const datePicker = wrapper.findComponent({ name: 'VDatePicker' });
        expect(datePicker.exists()).toBe(true);

        expect(wrapper.classes()).toContain('wtg-date-picker-dialog');
        const todayButton = wrapper.find('.wtg-button__content');
        expect(todayButton.exists()).toBe(true);
        expect(todayButton.text()).toBe('Today');

        await todayButton.trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.find(selectors.selectedDay).text()).toBe('8');
        await wrapper.setProps({ modelValue: '2020-01-19' });

        expect(wrapper.find(selectors.selectedDay).text()).toBe('19');
    });

    test('it sets the correct captions for the current language and updates them if the language changes', async () => {
        const wrapper = mountComponent();
        let button = wrapper.findComponent({ name: 'WtgButton' });
        expect(button.text()).toBe('Today');

        wtgUi.language.current = 'nl';
        await wrapper.vm.$nextTick();

        button = wrapper.findComponent({ name: 'WtgButton' });
        expect(button.text()).toBe('Vandaag');
    });

    test('it hides today button when hideTodayButton prop is true', async () => {
        const wrapper = mountComponent({
            hideTodayButton: true,
        });
        const button = wrapper.findComponent({ name: 'WtgButton' });
        expect(button.exists()).toBe(false);
    });

    test('it passes the global first-day-of-week setting for the current locale on as a property to the VDatePicker component', async () => {
        const wrapper = mountComponent();
        const datePicker = wrapper.findComponent({ name: 'VDatePicker' }).vm;

        wtgUi.language.current = 'nl';
        await nextTick();
        expect(datePicker.$props.firstDayOfWeek).toBe(1);

        wtgUi.language.current = 'en-US';
        await nextTick();
        expect(datePicker.$props.firstDayOfWeek).toBe(0);
    });

    function mountComponent(props = {}) {
        return mount(WtgDatePicker as any, {
            props,
            global: {
                plugins: [wtgUi],
                provide: {
                    darkMode: false,
                },
            },
        });
    }
});
