import WtgChip from '@components/WtgChip/WtgChip.vue';
import WtgCol from '@components/WtgCol';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import { WtgList, WtgListItem } from '@components/WtgList';
import WtgPanel from '@components/WtgPanel';
import WtgPopover from '@components/WtgPopover';
import WtgRow from '@components/WtgRow';
import { useSupplyPrefixIconsName } from '@composables/icon';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { ChipSandboxTemplate } from './templates/wtg-chip-sandbox.stories-template';

type Story = StoryObj<typeof WtgChip>;
const icons = useSupplyPrefixIconsName();

const meta: Meta<typeof WtgChip> = {
    title: 'Components/Chip',
    component: WtgChip,
    parameters: {
        docs: {
            description: {
                component:
                    'A chip is a small, modular element that is used to display key information that has been applied to or selected from another component.',
            },
        },
        layout: 'centered',
    },
    argTypes: {
        variant: {
            options: ['', 'close', 'refinement', 'dropdown'],
            control: { type: 'select' },
            table: {
                category: 'props',
            },
        },
        sentiment: {
            options: ['', 'primary', 'success', 'warning', 'critical'],
            control: { type: 'select' },
            table: {
                category: 'props',
            },
        },
        leadingIcon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
        trailingIcon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
    },
};

export default meta;

export const Default: Story = {
    args: {
        variant: 'close',
        sentiment: undefined,
        disabled: false,
        leadingIcon: 's-icon-folder',
        label: 'label',
    },
    render: (args) => ({
        components: { WtgChip },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: '<WtgChip v-bind="args" @click="action"></WtgChip>',
    }),
};

export const Primary: Story = {
    args: {
        variant: 'close',
        sentiment: 'primary',
        disabled: false,
        leadingIcon: 's-icon-placeholder',
        label: 'Label',
    },
    render: (args) => ({
        components: { WtgChip },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: '<WtgChip v-bind="args" @click="action"></WtgChip>',
    }),
};

export const Refinement: Story = {
    args: {
        variant: 'refinement',
        sentiment: undefined,
        disabled: false,
        label: 'Label',
    },
    render: (args) => ({
        components: { WtgChip },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: '<WtgChip v-bind="args" @click="action"></WtgChip>',
    }),
};

export const Dropdown: Story = {
    args: {
        variant: 'dropdown',
        sentiment: undefined,
        disabled: false,
        label: 'Label',
    },
    render: (args) => ({
        components: { WtgChip, WtgList, WtgListItem, WtgPanel },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: `<WtgChip v-bind="args" @click="action">                
            <template #dropdown>
                <WtgList>
                    <WtgList-item>Item 1</WtgList-item>
                    <WtgList-item>Item 2</WtgList-item>
                    <WtgList-item>Item 3</WtgList-item>
                </WtgList>
            </template>
        </WtgChip>`,
    }),
};

export const Sandbox: Story = {
    args: {},
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgChip, WtgRow, WtgCol, WtgLayoutGrid, WtgList, WtgListItem, WtgPopover, WtgPanel },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: templateWithRtl(ChipSandboxTemplate),
    }),
};
