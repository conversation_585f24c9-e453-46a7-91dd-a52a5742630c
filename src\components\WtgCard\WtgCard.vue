<template>
    <VCard
        :class="cardClasses"
        :exact="exact"
        :href="href"
        :link="link"
        :replace="replace"
        :style="{ ...computedStyle, ...measurableStyles }"
        :to="to"
        variant="flat"
    >
        <WtgLayoutGrid
            v-if="isGridLayout || isGridFillLayout"
            id="content"
            :class="layoutClasses"
            :no-gutters="noGutters"
            :fill-available="isGridFillLayout"
        >
            <template #default>
                <slot />
            </template>
        </WtgLayoutGrid>
        <div v-else-if="isFlexLayout || isFillLayout" id="content" :class="contentClasses">
            <slot />
        </div>
        <slot v-else />
    </VCard>
</template>

<script setup lang="ts">
import { WtgLayoutGrid } from '@components/WtgLayoutGrid';
import { useColor } from '@composables/color';
import { makeLayoutProps, useLayout } from '@composables/layoutGrid';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { makeMeasureProps, useMeasure } from '@composables/measure';
import { makeRouterProps } from '@composables/router';
import { computed } from 'vue';
import { VCard } from 'vuetify/components/VCard';

//
// Properties
//
const props = defineProps({
    /**
     * The name of the color to apply from the design system or a custom color.
     * This color will be applied to the background of the card.
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the card will expand to fill the available space in its container.
     */
    fillAvailable: {
        type: Boolean,
        default: false,
    },

    /**
     * Designates that the component is a link.
     */
    link: {
        type: Boolean,
        default: undefined,
    },

    ...makeMeasureProps(),
    ...makeLayoutProps(),
    ...makeLayoutGridColumnProps(),
    ...makeRouterProps(),
});

//
// Composables
//
const { layoutClasses, isGridLayout, isGridFillLayout, isFlexLayout, isFillLayout } = useLayout(props);
const { measurableStyles } = useMeasure(props);
const { colorClasses, colorStyles } = useColor(props, { background: true });

useLayoutGridColumn(props);

//
// Computed
//
const cardClasses = computed(() => {
    const classes = [
        {
            'wtg-fill-available': props.fillAvailable,
            'wtg-fill': isFillLayout.value,
        },
        'wtg-card',
    ];
    return [...classes, ...colorClasses.value];
});

const contentClasses = computed((): string[] => {
    const classes = [...layoutClasses.value];

    if (props.fillAvailable || props.height === '100%') {
        classes.push('wtg-h-100');
    }

    return classes;
});

const computedStyle = computed(() => {
    return { ...colorStyles.value };
});
</script>

<style lang="scss">
.wtg-card {
    border-radius: var(--s-radius-m);
    border: 1px solid var(--s-neutral-border-weak-default);
    box-shadow: none;
    background: var(--s-neutral-bg-default);
    color: var(--s-neutral-txt-default);

    // Remove Vuetify's default card padding
    overflow: initial;
    z-index: initial;

    &:link,
    &:visited {
        color: var(--s-neutral-txt-default);
        text-decoration: none;
    }
}
</style>
