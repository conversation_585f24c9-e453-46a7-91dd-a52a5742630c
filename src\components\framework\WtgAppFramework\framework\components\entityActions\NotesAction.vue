<template>
    <div v-if="action.visible">
        <WtgIconButton
            v-if="isTabletOrMobile"
            icon="s-icon-notes"
            variant="ghost"
            :aria-label="action.caption"
            aria-haspopup="dialog"
            :tooltip="action.caption"
            @click="action.onInvoke"
        >
        </WtgIconButton>
        <WtgButton v-else variant="ghost" leading-icon="s-icon-notes" aria-haspopup="dialog" @click="action.onInvoke">
            {{ action.caption }}
        </WtgButton>
    </div>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgIconButton from '@components/WtgIconButton';
import { WtgFrameworkTask, WtgFrameworkTaskStandardAction } from '@components/framework/types';
import { useFramework } from '@composables/framework';
import { computed, PropType } from 'vue';

const props = defineProps({
    task: { type: Object as PropType<WtgFrameworkTask>, default: undefined },
});

const { isTabletOrMobile } = useFramework();

const action = computed((): WtgFrameworkTaskStandardAction => {
    return (
        props.task?.showNotesAction ?? {
            visible: false,
            caption: 'Notes',
            label: 'Notes',
            onInvoke: (): void => undefined,
        }
    );
});
</script>
