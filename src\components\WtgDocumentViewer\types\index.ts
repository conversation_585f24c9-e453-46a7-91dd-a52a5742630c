export enum ScalingMode {
    FitHorizontally = 'hor',
    FitVertically = 'ver',
    ActualSize = 'act',
    FitToScreen = 'scr',
}

export type Scale = {
    scaleMode: ScalingMode;
    scaleValue: number;
    resetScale: boolean;
};

export type UrlProvider = {
    printUrl?: string;
    downloadUrl?: string;
};

export interface ImageData {
    imageWidth: number;
    imageHeight: number;
}

export interface PdfPageData {
    pageWidth: number;
    pageHeight: number;
    pageNumber: number;
}

const scalingModeLabels: { [key in ScalingMode]: string } = {
    [ScalingMode.FitHorizontally]: 'Fit horizontally',
    [ScalingMode.FitVertically]: 'Fit vertically',
    [ScalingMode.ActualSize]: 'Actual size',
    [ScalingMode.FitToScreen]: 'Fit to screen',
};

export function getScalingModeLabel(mode: ScalingMode): string {
    return scalingModeLabels[mode as ScalingMode] || '';
}
