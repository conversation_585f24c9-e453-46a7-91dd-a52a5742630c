export const CalloutSandboxTemplate = `
<wtg-row>
    <wtg-col style="max-width: 400px">
        <wtg-layout-grid>
            <wtg-label typography="title-sm-default"> Default </wtg-label>
            <wtg-callout sentiment="info" title="Info" v-bind="args"></wtg-callout>
            <wtg-callout sentiment="success" title="Success" v-bind="args"></wtg-callout>
            <wtg-callout sentiment="warning" title="Warning" v-bind="args"></wtg-callout>
            <wtg-callout sentiment="critical" title="Critical" v-bind="args"></wtg-callout>
            <wtg-label typography="title-sm-default"> Dismissible </wtg-label>
            <wtg-callout sentiment="info" dismissible="true" title="Info" v-bind="args"></wtg-callout>
            <wtg-callout sentiment="success" dismissible="true" title="Success" v-bind="args"></wtg-callout>
            <wtg-callout sentiment="warning" dismissible="true" title="Warning" v-bind="args"></wtg-callout>
            <wtg-callout sentiment="critical" dismissible="true" title="Critical" v-bind="args"></wtg-callout>
            <wtg-label typography="title-sm-default"> Inline Default </wtg-label>
            <wtg-callout sentiment="info" title="Info" v-bind="args" variant="inline"></wtg-callout>
            <wtg-callout sentiment="success" title="Success" v-bind="args" variant="inline"></wtg-callout>
            <wtg-callout sentiment="warning" title="Warning" v-bind="args" variant="inline"></wtg-callout>
            <wtg-callout sentiment="critical" title="Critical" v-bind="args" variant="inline"></wtg-callout>
        </wtg-layout-grid>
    </wtg-col>
    <wtg-col style="max-width: 400px">
        <wtg-layout-grid>
            <wtg-label typography="title-sm-default"> Wrapping </wtg-label>
            <wtg-callout title="A very very very very very long title should of course wrap around nicely"></wtg-callout>
            <wtg-callout sentiment="success" title="Long content">Very very very very very long content should of course wrap around nicely</wtg-callout>
            <wtg-callout sentiment="warning" title="Long description" description="Very very very very very long description should of course wrap around nicely"></wtg-callout>
            <wtg-callout sentiment="critical">Very very very very very long content should of course wrap around nicely</wtg-callout>
            <wtg-callout sentiment="info" title="A very very very very very long title should of course wrap around nicely" description="Very very very very very long description should of course wrap around nicely">Very very very very very long content should of course wrap around nicely</wtg-callout>
            <wtg-label typography="title-sm-default"> Dismissible </wtg-label>
            <wtg-callout dismissible title="Very very very very very long title should of course wrap around nicely"></wtg-callout>
            <wtg-callout sentiment="success" dismissible title="Long content">Very very very very very long content should of course wrap around nicely</wtg-callout>
            <wtg-callout sentiment="warning" dismissible title="Long description" description="Very very very very very long description should of course wrap around nicely"></wtg-callout>
            <wtg-callout sentiment="critical" dismissible>Very very very very very long content should of course wrap around nicely</wtg-callout>
            <wtg-callout sentiment="info" dismissible title="Very very very very very long title should of course wrap around nicely" description="Very very very very very long description should of course wrap around nicely">Very very very very very long content should of course wrap around nicely</wtg-callout>
            <wtg-label typography="title-sm-default"> Inline </wtg-label>
            <wtg-callout variant="inline" description="Very very very very very very very very long description should of course wrap around nicely"></wtg-callout>
            <wtg-callout variant="inline" sentiment="success">Very very very very very very very very long content should of course wrap around nicely</wtg-callout>
        </wtg-layout-grid>
    </wtg-col>
    <wtg-col style="max-width: 400px">
        <wtg-layout-grid>
            <wtg-label typography="title-sm-default"> Using Slot </wtg-label>
            <wtg-callout sentiment="info" title="Info" v-bind="args"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="success" title="Success" v-bind="args"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="warning" title="Warning" v-bind="args"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="critical" title="Critical" v-bind="args"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-label typography="title-sm-default"> Dismissible </wtg-label>
            <wtg-callout sentiment="info" dismissible="true" title="Info" v-bind="args"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="success" dismissible="true" title="Success" v-bind="args"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="warning" dismissible="true" title="Warning" v-bind="args"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="critical" dismissible="true" title="Critical" v-bind="args"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-label typography="title-sm-default"> Inline Using Slot </wtg-label>
            <wtg-callout sentiment="info" title="Info" v-bind="args" variant="inline"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="success" title="Success" v-bind="args" variant="inline"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="warning" title="Warning" v-bind="args" variant="inline"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
            <wtg-callout sentiment="critical" title="Critical" v-bind="args" variant="inline"><wtg-label> Slot content should be visible </wtg-label></wtg-callout>
        </wtg-layout-grid>
    </wtg-col>
    <wtg-col style="max-width: 400px">
        <wtg-layout-grid>
            <wtg-label typography="title-sm-default">Multiline</wtg-label>
            <wtg-callout class="wtg-multiline-callout" sentiment="critical" description="Organization Code - Field is mandatory.\nEmail Address - Field is mandatory.\nPassword - Field is mandatory."/>
        </wtg-layout-grid>
        <wtg-layout-grid>
            <wtg-label typography="title-sm-default">With Actions</wtg-label>
            <wtg-callout sentiment="info" title="Info" v-bind="args">
                <WtgHyperlink href="https://design.wtg.zone/">Action</WtgHyperlink>
            </wtg-callout>
        </wtg-layout-grid>
    </wtg-col>
</wtg-row>
`;
