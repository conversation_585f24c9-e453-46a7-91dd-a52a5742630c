import { WtgFrameworkTask, WtgFrameworkTaskGenericAction } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import TaskActionsOverflow from '../TaskActionsOverflow.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('task-actions-overflow', () => {
    let el: HTMLElement;
    let task: WtgFrameworkTask;
    let genericActions: WtgFrameworkTaskGenericAction[];

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        task = {
            showEDocsAction: {
                visible: true,
                caption: 'eDocs',
                label: 'eDocs',
                onInvoke: jest.fn(),
            },
            showLogsAction: {
                visible: true,
                caption: 'Logs',
                label: 'Logs',
                onInvoke: jest.fn(),
            },
            showMessagesAction: {
                visible: true,
                caption: 'Messages',
                label: 'Messages',
                onInvoke: jest.fn(),
            },
            showNotesAction: {
                visible: true,
                caption: 'Notes',
                label: 'Notes',
                onInvoke: jest.fn(),
            },
            showWorkflowActions: {
                visible: true,
                caption: 'Workflows',
                menuItems: [
                    {
                        caption: 'Milestones',
                        onInvoke: jest.fn(),
                    },
                ],
            },
            documents: {
                visible: true,
                caption: 'Documents',
                loadDocumentsAsync: jest.fn().mockResolvedValue([
                    { caption: 'Option 1', submenu: false, click: jest.fn() },
                    { caption: 'Option 2', submenu: false, click: jest.fn() },
                    {
                        caption: 'Submenu 1',
                        submenu: true,
                        loadDocumentsAsync: jest.fn().mockResolvedValue([
                            { caption: 'Option 1 1', submenu: false, click: jest.fn() },
                            { caption: 'Option 1 2', submenu: false, click: jest.fn() },
                        ]),
                    },
                ]),
            },
            genericActions: [],
        } as any;
        genericActions = [
            {
                id: 'someguid1',
                caption: 'Action 1',
                placement: 'default',
                onInvoke: jest.fn(),
            },
            {
                id: 'someguid2',
                caption: 'Action 2',
                placement: 'taskactions',
                icon: '$icon',
                onInvoke: jest.fn(),
            },
        ];
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is TaskActionsOverflow', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('TaskActionsOverflow');
    });

    test('its does not render a popup menu when the action visible is false', () => {
        const wrapper = mountComponent({ propsData: { task } });
        const menu = wrapper.findComponent({ name: 'WtgPopover' });
        expect(menu.exists()).toBe(true);
    });

    describe('when opened', () => {
        let wrapper: any;

        beforeEach(async () => {
            wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgIconButton' });
            await button.trigger('click');
        });

        test('its renders a menu item if the action is visible', () => {
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(menuItems.length).toBe(6);
            expect(menuItems.at(0).text()).toBe('eDocs');
            expect(menuItems.at(1).text()).toBe('Documents');
            expect(menuItems.at(2).text()).toBe('Logs');
            expect(menuItems.at(3).text()).toBe('Messages');
            expect(menuItems.at(4).text()).toBe('Notes');
            expect(menuItems.at(5).text()).toBe('Workflows');
        });

        test('it will render the Toggle Actions button with tooltip="Entity Actions" and "s-icon-menu-kebab" icon', () => {
            const wrapper = mountComponent();

            const actionsButton = wrapper.findComponent({ name: 'WtgIconButton' });
            expect(actionsButton.exists()).toBe(true);
            expect(actionsButton.props('tooltip')).toBe('Entity actions');
            expect(actionsButton.props('icon')).toBe('s-icon-menu-kebab');
        });

        test('it will pass the variant prop to the icon button', () => {
            const wrapper = mountComponent({ propsData: { variant: 'ghost' } });

            const actionsButton = wrapper.findComponent({ name: 'WtgIconButton' });
            expect(actionsButton.props('variant')).toBe('ghost');
        });

        test('it will render the Toggle Actions button with aria-expanded="true" attribute when Actions menu is open', async () => {
            task.showWorkflowActions.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');
            expect(button.attributes('aria-expanded')).toBe('true');
        });

        test('it renders a menu item for any transition placed as a task action', async () => {
            task!.genericActions = genericActions;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(menuItems.length).toBe(7);
            expect(menuItems.at(0)?.text()).toContain('Action 2');
        });

        test('it calls eDocs onInvoke when eDocs is clicked', async () => {
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await menuItems.at(0).trigger('click');

            expect(task?.showEDocsAction.onInvoke).toHaveBeenCalledTimes(1);
        });

        test('it calls logs onInvoke when Logs is clicked', async () => {
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await menuItems.at(2).trigger('click');

            expect(task?.showLogsAction.onInvoke).toHaveBeenCalledTimes(1);
        });

        test('it calls messages onInvoke when Messages is clicked', async () => {
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await menuItems.at(3).trigger('click');

            expect(task?.showMessagesAction.onInvoke).toHaveBeenCalledTimes(1);
        });

        test('it calls notes onInvoke when Notes is clicked', async () => {
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await menuItems.at(4).trigger('click');

            expect(task?.showNotesAction.onInvoke).toHaveBeenCalledTimes(1);
        });

        test('it renders the workflow items when workflows is clicked', async () => {
            const popupMenus = wrapper.findAllComponents({ name: 'PopupMenuList' });
            let subItems = popupMenus.at(2).findAllComponents({ name: 'WtgListItem' });
            expect(subItems.length).toBe(1);
            await subItems.at(0).trigger('click');

            subItems = popupMenus.at(2).findAllComponents({ name: 'WtgListItem' });
            expect(subItems.length).toBe(2);
            expect(subItems.at(1).text()).toBe('Milestones');
            await subItems.at(1).trigger('click');

            expect(task?.showWorkflowActions.menuItems[0].onInvoke).toHaveBeenCalledTimes(1);
        });

        test('it renders the document items when documents is clicked', async () => {
            const popupMenus = wrapper.findAllComponents({ name: 'PopupMenuList' });
            let subItems = popupMenus.at(1).findAllComponents({ name: 'WtgListItem' });
            expect(subItems.length).toBe(1);
            await subItems.at(0).trigger('click');

            subItems = popupMenus.at(1).findAllComponents({ name: 'WtgListItem' });
            expect(subItems.length).toBe(4);
            expect(subItems.at(1).text()).toBe('Option 1');
            expect(subItems.at(2).text()).toBe('Option 2');
            expect(subItems.at(3).text()).toBe('Submenu 1');
        });

        test('it renders the sub items for a document header when clicked', async () => {
            const documentHeader = task.documents;
            const menu = wrapper.findComponent({ name: 'PopupMenuList' });
            menu.vm.$emit('action', documentHeader);

            const actions = await documentHeader.loadDocumentsAsync!();
            const submenuActions = await actions[2].loadDocumentsAsync!();

            expect(submenuActions.length).toBe(2);
            expect(submenuActions[0].caption).toBe('Option 1 1');
            expect(submenuActions[1].caption).toBe('Option 1 2');
        });
    });

    describe('Property tests', () => {
        test('when props is passed', () => {
            task.showEDocsAction.caption = 'eDocs-overflow';
            task.showLogsAction.caption = 'Logs-overflow';
            task.showMessagesAction.caption = 'Messages-overflow';
            task.showNotesAction.caption = 'Notes-overflow';
            task.showWorkflowActions.caption = 'Workflows-overflow';
            task.documents.caption = 'Documents-overflow';
            task.showEDocsAction.visible = true;
            task.showLogsAction.visible = true;
            task.showMessagesAction.visible = true;
            task.showNotesAction.visible = true;
            task.showWorkflowActions.visible = true;
            task.documents.visible = true;
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task } });
            expect(wrapper.vm.actions[5].caption).toStrictEqual(task.showWorkflowActions.caption);
            expect(wrapper.vm.actions[1].caption).toStrictEqual(task.documents.caption);
            expect(wrapper.vm.actions[4].caption).toStrictEqual(task.showNotesAction.caption);
            expect(wrapper.vm.actions[3].caption).toStrictEqual(task.showMessagesAction.caption);
            expect(wrapper.vm.actions[2].caption).toStrictEqual(task.showLogsAction.caption);
            expect(wrapper.vm.actions[0].caption).toStrictEqual(task.showEDocsAction.caption);
        });

        test('when props is not passed', () => {
            const wrapper: VueWrapper<any> = mountComponent();
            expect(wrapper.vm.actions.length).toBe(0);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(TaskActionsOverflow, {
            propsData: { ...propsData },
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
