import { Meta } from '@storybook/blocks';

<Meta title="Data viz/Overview" />

<h1 className="content-header">Charts</h1>

<p className="content-description">
    Chart components are just simple wrappers around the different chart types out of
    <b>Chart.js</b>, which is a very impressive open source project for displaying charts in the browser. For more samples
    and a full description of the API, see
    <a href="https://www.chartjs.org/">www.chartjs.org</a>.
</p>

## Chart Types

<ul className="docs__unordered-list">
    <li>Bar Chart</li>
    <li>Bubble Chart</li>
    <li>Line Chart</li>
    <li>Pie Chart</li>
    <li>Polar Area Chart</li>
    <li>Radar Chart</li>
    <li>Scatter Chart</li>
</ul>

## Loading

<p className="content-description">
    If you need time to fetch and/or process the chart data, you can use the loading prop to inform the user that there
    is an operation taking place.
</p>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
