import { enableAutoUnmount, mount } from '@vue/test-utils';
import { VColorPicker } from 'vuetify/components';
import { WtgColorPicker } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgColorPicker', () => {
    test('its name is WtgColorPicker', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgColorPicker');
    });

    test('it renders a VColorPicker component', () => {
        const wrapper = mountComponent();
        const colorPicker = wrapper.findComponent({ name: 'VColorPicker' });
        expect(colorPicker.exists()).toBe(true);
    });

    test('it passes its properties to the VColorPicker component', () => {
        const propsData: any = {
            border: 6,
            canvasHeight: 100,
            color: 'red',
            disabled: true,
            dotSize: 20,
            elevation: 0,
            hideCanvas: true,
            hideInputs: true,
            hideSliders: true,
            mode: 'hexa',
            modelValue: '#000000',
            modes: ['rgb', 'rgba'],
            position: 'static',
            showSwatches: true,
            swatches: [
                ['#FF0000', '#AA0000', '#550000'],
                ['#FFFF00', '#AAAA00', '#555500'],
                ['#00FF00', '#00AA00', '#005500'],
                ['#00FFFF', '#00AAAA', '#005555'],
                ['#0000FF', '#0000AA', '#000055'],
            ],
            swatchesMaxHeight: 100,
            tile: true,
            width: 100,
        };

        const wrapper = mountComponent({
            props: propsData,
        });

        const props: any = wrapper.findComponent(VColorPicker).props();
        for (const key in propsData) {
            expect(props[key]).toEqual(propsData[key]);
        }
    });

    function mountComponent({ props = {} } = {}) {
        return mount(WtgColorPicker, {
            props,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
