<template>
    <div
        ref="root"
        v-floating-vue-tooltip="tooltipDirective"
        :class="computedClasses"
        :style="{ ...computedStyle, ...measurableStyles }"
        :role="props.variant === 'dropdown' ? 'button' : undefined"
        @click="onClick"
    >
        <WtgIcon v-if="computedLeadingIcon">{{ computedLeadingIcon }}</WtgIcon>
        <div class="wtg-chip__content">
            <slot>
                {{ label }}
            </slot>
        </div>
        <WtgIcon v-if="computedTrailingIcon" @click="onTrailingIconClick">{{ computedTrailingIcon }}</WtgIcon>
        <WtgPopover
            v-if="props.variant === 'dropdown'"
            v-model="menu"
            :activator="root"
            :close-on-content-click="closeOnContentClick"
            location="bottom left"
            nudge-bottom="8px"
            :transition="false"
        >
            <slot name="dropdown" />
        </WtgPopover>
    </div>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { WtgPopover } from '@components/WtgPopover';
import { useColor } from '@composables/color';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { makeMeasureProps, useMeasure } from '@composables/measure';
import { makeSizeProps, useSize } from '@composables/size';
import { makeTooltipProps, useTooltip } from '@composables/tooltip';
import { PropType, computed, ref } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * Determines if the dropdown menu should close when its content is clicked.
     */
    closeOnContentClick: {
        type: Boolean,
        default: true,
    },

    /**
     * The name of the color to apply from the design system or a custom color.
     * This only applies when the `sentiment` property is not set.
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * Disables the chip, making it unclickable and visually indicating its disabled state.
     */
    disabled: {
        type: Boolean,
        default: false,
    },

    /**
     * The label text to display inside the chip.
     */
    label: {
        type: String,
        default: '',
    },

    /**
     * The leading icon to display before the chip's content.
     */
    leadingIcon: {
        type: String,
        default: undefined,
    },

    /**
     * Defines the sentiment or visual style of the chip.
     * Options include 'critical', 'primary', 'success', or 'warning'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'primary' | 'success' | 'warning'>,
        default: undefined,
    },

    /**
     * The trailing icon to display after the chip's content.
     */
    trailingIcon: {
        type: String,
        default: undefined,
    },

    /**
     * Specifies the variant of the chip.
     * Options include 'close', 'refinement', or 'dropdown'.
     */
    variant: {
        type: String as PropType<'close' | 'refinement' | 'dropdown'>,
        default: undefined,
    },

    ...makeLayoutGridColumnProps(),
    ...makeMeasureProps(),
    ...makeSizeProps(),
    ...makeTooltipProps(),
});

//
// Emits
//
const emit = defineEmits<{
    click: [e: MouseEvent];
    'close-click': [e: MouseEvent];
}>();

//
// State
//
const root = ref<HTMLElement>();
const menu = ref(false);

//
// Composables
//
const { colorClasses, colorStyles } = useColor(props, { background: true });
const { sizeClass } = useSize(props, 'chip');
const { tooltipDirective } = useTooltip(props);
const { measurableStyles } = useMeasure(props);

useLayoutGridColumn(props);

//
// Computed
//
const hasCssStyleColor = computed(() => {
    return !props.disabled && !props.sentiment && (colorStyles.value.backgroundColor || colorStyles.value.color);
});

const hasCssClassColor = computed(() => {
    return !props.disabled && !props.sentiment && colorClasses.value.length > 0;
});

const computedClasses = computed(() => {
    let classes = [
        {
            'wtg-chip--disabled': props.disabled,
            'wtg-chip--default': true,
            'wtg-chip--refinement': props.variant === 'refinement',
            'wtg-chip--color': (hasCssClassColor.value || hasCssStyleColor.value) && !props.disabled,
            'wtg-chip--success': props.sentiment === 'success' && !props.disabled,
            'wtg-chip--warning': props.sentiment === 'warning' && !props.disabled,
            'wtg-chip--critical': props.sentiment === 'critical' && !props.disabled,
            'wtg-chip--primary': props.sentiment === 'primary' && !props.disabled,
        },
        sizeClass.value,
        'wtg-chip',
    ];
    if (hasCssClassColor.value && props.color) {
        classes = [...classes, ...colorClasses.value];
    }
    return classes;
});

const computedStyle = computed(() => {
    if (hasCssStyleColor.value) {
        return { ...colorStyles.value, borderColor: colorStyles.value.backgroundColor };
    }

    return {};
});

const computedLeadingIcon = computed(() => {
    return props.leadingIcon ?? (props.variant === 'refinement' ? 's-icon-add' : '');
});

const computedTrailingIcon = computed(() => {
    let icon = props.trailingIcon;
    if (!icon) {
        switch (props.variant) {
            case 'dropdown':
                icon = 's-icon-caret-down';
                break;
            case 'close':
                icon = 's-icon-close';
                break;
        }
    }
    return icon;
});

//
// Event Handlers
//
function onClick(e: MouseEvent) {
    if (!props.disabled) {
        emit('click', e);
    }
}

function onTrailingIconClick(e: MouseEvent) {
    if (props.variant === 'close') {
        emit('close-click', e);
    }
}
</script>

<style lang="scss">
.wtg-chip {
    align-items: center;
    border-radius: var(--s-radius-xxl);
    border: 1px solid;
    cursor: pointer;
    display: inline-flex;
    font: var(--s-text-sm-default);
    gap: var(--s-spacing-null);
    justify-content: center;
    padding: var(--s-padding-xs);
    position: relative;
    transition-duration: 0.28s;
    transition-property: transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;

    &:focus-visible {
        outline-offset: 1px;
        outline: 2px solid var(--s-primary-border-default);
        z-index: 1;
    }

    & > .wtg-chip__content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        z-index: 1;
        padding: var(--s-padding-null) var(--s-padding-s);
    }

    & > .wtg-icon {
        height: var(--s-sizing-m);
    }

    &.wtg-chip--default {
        background: var(--s-neutral-bg-default);
        border-color: var(--s-neutral-border-weak-default);
        color: var(--s-neutral-txt-default);

        &.wtg-chip--selected {
            background: var(--s-neutral-bg-weak-active);
        }

        &.wtg-chip--primary {
            border-color: var(--s-primary-border-weak-default);
            background: var(--s-primary-bg-weak-default);
            color: var(--s-primary-txt-default);

            &:not(.wtg-chip--selected):hover {
                background: var(--s-primary-bg-weak-hover);
                border-color: var(--s-primary-border-weak-hover);
                color: var(--s-primary-txt-hover);
            }

            &:active {
                background: var(--s-neutral-bg-default);
                border-color: var(--s-primary-border-weak-active);
                color: var(--s-primary-txt-active);
            }

            &:focus-visible {
                border-color: var(--s-primary-border-weak-active);
                color: var(--s-primary-txt-active);
            }

            &.wtg-chip--selected {
                background: var(--s-primary-bg-weak-active);
            }
        }

        &.wtg-chip--critical {
            border-color: var(--s-error-border-weak-default);
            background: var(--s-error-bg-weak-default);
            color: var(--s-error-txt-default);

            &:not(.wtg-chip--selected):hover {
                background: var(--s-error-bg-weak-hover);
                border-color: var(--s-error-border-weak-hover);
                color: var(--s-error-txt-hover);
            }

            &:active {
                background: var(--s-neutral-bg-default);
                border-color: var(--s-error-border-weak-active);
                color: var(--s-error-txt-active);
            }

            &:focus-visible {
                border-color: var(--s-error-border-weak-active);
                color: var(--s-error-txt-active);
            }

            &.wtg-chip--selected {
                background: var(--s-error-bg-weak-active);
            }
        }

        &.wtg-chip--success {
            border-color: var(--s-success-border-weak-default);
            background: var(--s-success-bg-weak-default);
            color: var(--s-success-txt-default);

            &:not(.wtg-chip--selected):hover {
                background: var(--s-success-bg-weak-hover);
                border-color: var(--s-success-border-weak-hover);
                color: var(--s-success-txt-hover);
            }

            &:active {
                background: var(--s-neutral-bg-default);
                border-color: var(--s-success-border-weak-active);
                color: var(--s-success-txt-active);
            }

            &:focus-visible {
                border-color: var(--s-success-border-weak-active);
                color: var(--s-success-txt-active);
            }

            &.wtg-chip--selected {
                background: var(--s-success-bg-weak-active);
            }
        }

        &.wtg-chip--warning {
            border-color: var(--s-warning-border-weak-default);
            background: var(--s-warning-bg-weak-default);
            color: var(--s-warning-txt-default);

            &:not(.wtg-chip--selected):hover {
                background: var(--s-warning-bg-weak-hover);
                border-color: var(--s-warning-border-weak-hover);
                color: var(--s-warning-txt-hover);
            }

            &:active {
                background: var(--s-neutral-bg-default);
                border-color: var(--s-warning-border-weak-active);
                color: var(--s-warning-txt-active);
            }

            &:focus-visible {
                border-color: var(--s-warning-border-weak-active);
                color: var(--s-warning-txt-active);
            }

            &.wtg-chip--selected {
                background: var(--s-warning-bg-weak-active);
            }
        }

        &:not(.wtg-chip--selected):hover {
            background: var(--s-neutral-bg-weak-hover);
            border-color: var(--s-neutral-border-weak-hover);
            color: var(--s-neutral-txt-hover);
        }

        &:active {
            background: var(--s-neutral-bg-default);
            border-color: var(--s-neutral-border-weak-active);
            color: var(--s-neutral-txt-active);
        }

        &.wtg-chip--color:not(:active):hover::before {
            background-color: currentColor;
            bottom: 0;
            content: '';
            left: 0;
            opacity: 0.12;
            pointer-events: none;
            position: absolute;
            right: 0;
            top: 0;
        }
    }
    &.wtg-chip--refinement {
        border-style: dashed;
    }

    &.wtg-chip--disabled {
        background: var(--s-neutral-bg-disabled);
        border-color: var(--s-neutral-border-disabled);
        color: var(--s-neutral-txt-disabled);
        pointer-events: none;
    }

    &.wtg-chip--xs {
        padding: var(--s-padding-xs) var(--s-padding-xs);
    }

    &.wtg-chip--s {
        padding: var(--s-padding-s) var(--s-padding-s);
    }

    &.wtg-chip--m {
        padding: var(--s-padding-m) var(--s-padding-m);
    }

    &.wtg-chip--l {
        padding: var(--s-padding-l) var(--s-padding-l);
    }

    &.wtg-chip--xl {
        padding: var(--s-padding-xl) var(--s-padding-xl);
    }

    &.wtg-chip--xxl {
        padding: var(--s-padding-xxl) var(--s-padding-xxl);
    }
}
</style>
