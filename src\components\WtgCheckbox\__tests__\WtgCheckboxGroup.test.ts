import { WtgCheckbox, WtgCheckboxGroup } from '@components/WtgCheckbox';
import * as notifications from '@composables/notifications';
import * as tooltip from '@composables/tooltip/tooltip';
import { ValidationState } from '@composables/notifications';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../WtgUi';
import { computed, ComputedRef, nextTick, Ref, ref } from 'vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

jest.mock('@composables/notifications');

describe('WtgCheckboxGroup', () => {
    beforeEach(() => {
        jest.spyOn(notifications, 'useCurrentNotification').mockReturnValue({
            currentNotification: ref({}),
            displayCurrentNotification: computed(() => false),
        });
    });

    test('renders correctly with default props', () => {
        const wrapper = mountComponent();
        expect(wrapper.html()).toContain('wtg-checkbox-group');
        expect(wrapper.html()).not.toContain('wtg-checkbox-group-description');
    });

    test('renders with custom props', () => {
        const label = 'Checkbox group';
        const description = 'Assistive text';
        const validationState: ValidationState = {
            messages: ['This is required'],
            alertLevel: 4,
            targetKey: '',
            targetProperty: '',
            error: false,
            warning: true,
        };
        const wrapper = mountComponent({ props: { label, description, validationState } });

        expect(wrapper.text()).toContain(label);
        expect(wrapper.text()).toContain(description);

        const validationIcon = wrapper.findComponent({ name: 'WtgIcon' });
        expect(validationIcon.exists()).toBe(true);
        expect(validationIcon.classes()).toContain('s-icon-status-critical');

        wrapper.trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<ol><li>This is required</li></ol>');
    });

    test('renders slot content correctly', () => {
        const wrapper = mountComponent({
            slots: { default: '<wtg-checkbox label="my checkbox 1" /><wtg-checkbox label="my checkbox 2" />' },
        });
        expect(wrapper.findAllComponents(WtgCheckbox)).toHaveLength(2);
    });

    test('renders label slot correctly', () => {
        const wrapper = mountComponent({ slots: { label: '<p>HTML label</p>' } });
        expect(wrapper.text()).toContain('HTML label');
    });

    test('renders vertically by default', async () => {
        const wrapper = mountComponent();

        expect(wrapper.find('div.wtg-checkbox-group-vertical').exists()).toBe(true);
        expect(wrapper.find('div.wtg-checkbox-group-horizontal').exists()).toBe(false);
    });

    test('reacts to variant prop change', async () => {
        const wrapper = mountComponent({ props: { horizontal: true } });
        expect(wrapper.find('div.wtg-checkbox-group-horizontal').exists()).toBe(true);
        expect(wrapper.find('div.wtg-checkbox-group-vertical').exists()).toBe(false);

        await wrapper.setProps({ horizontal: false });

        expect(wrapper.find('div.wtg-checkbox-group-horizontal').exists()).toBe(false);
        expect(wrapper.find('div.wtg-checkbox-group-vertical').exists()).toBe(true);
    });

    describe('render messages', () => {
        test('it applies the correct style when sentiment is critical', () => {
            const wrapper = mountComponent({ props: { sentiment: 'critical', messages: ['Test error message.'] } });
            expect(wrapper.classes()).toContain('wtg-checkbox-group--critical');

            const validationIcon = wrapper.findComponent({ name: 'WtgIcon' });
            expect(validationIcon.exists()).toBe(true);
            expect(validationIcon.classes()).toContain('s-icon-status-critical');

            wrapper.trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('<ol><li>Test error message.</li></ol>');
        });

        test('it applies the correct style when sentiment is warning', () => {
            const wrapper = mountComponent({ props: { sentiment: 'warning', messages: ['Test warning message.'] } });
            expect(wrapper.classes()).toContain('wtg-checkbox-group--warning');

            const validationIcon = wrapper.findComponent({ name: 'WtgIcon' });
            expect(validationIcon.exists()).toBe(true);
            expect(validationIcon.classes()).toContain('s-icon-status-warning');

            wrapper.trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('<ol><li>Test warning message.</li></ol>');
        });

        test('it applies the correct style when sentiment is success', () => {
            const wrapper = mountComponent({ props: { sentiment: 'success', messages: ['Test success message.'] } });
            expect(wrapper.classes()).toContain('wtg-checkbox-group--success');

            const validationIcon = wrapper.findComponent({ name: 'WtgIcon' });
            expect(validationIcon.exists()).toBe(true);
            expect(validationIcon.classes()).toContain('s-icon-status-success');

            wrapper.trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('<ol><li>Test success message.</li></ol>');
        });
    });

    describe('useCurrentNotification', () => {
        let mockdisplayCurrentNotificationRef: Ref<boolean>;
        let mockdisplayCurrentNotification: ComputedRef<boolean>;
        let createTooltipDirectiveSpy: jest.SpyInstance;

        beforeEach(() => {
            mockdisplayCurrentNotificationRef = ref(false);
            mockdisplayCurrentNotification = computed(() => mockdisplayCurrentNotificationRef.value);

            jest.spyOn(notifications, 'useCurrentNotification').mockReturnValue({
                currentNotification: ref({}),
                displayCurrentNotification: mockdisplayCurrentNotification,
            });

            jest.spyOn(tooltip, 'useTooltip').mockImplementationOnce((props) => {
                const { useTooltip } = jest.requireActual('@composables/tooltip/tooltip');
                const result = useTooltip(props) as ReturnType<typeof tooltip.useTooltip>;
                createTooltipDirectiveSpy = jest.spyOn(result, 'createTooltipDirective');
                return result;
            });
        });

        test('it should have root ref for useCurrentNotification', () => {
            const wrapper = mountComponent();
            expect(wrapper.vm.$refs.root).not.toBeUndefined();
        });

        test('it should display messages when displayCurrentNotification is true', async () => {
            mountComponent({
                props: {
                    messages: ['this is a test message'],
                },
            });
            mockdisplayCurrentNotificationRef.value = true;
            await nextTick();
            expect(createTooltipDirectiveSpy.mock.calls.at(-1)[0].shown).toBe(true);
            expect(createTooltipDirectiveSpy.mock.calls.at(-1)[0].content).toContain('this is a test message');
        });
    });

    function mountComponent({ slots = {}, props = {} } = {}) {
        return mount(WtgCheckboxGroup, {
            slots,
            props,
            global: {
                stubs: {
                    'wtg-checkbox': WtgCheckbox,
                },
                plugins: [wtgUi],
            },
        });
    }
});
