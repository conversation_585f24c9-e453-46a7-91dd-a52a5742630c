import {
    AddressLookupSearchContent,
    AddressSearchItem,
    ContactLookupSearchContent,
    ContactSearchItem,
} from '@components/WtgAddressField';
import { SearchFieldItemProvider } from '@components/WtgSearchField/types';
import { Ref } from 'vue';

export enum WtgJobAddressVariantType {
    None = '',
    Content = 'content',
}

export interface WtgJobAddressData {
    address?: AddressLookupSearchContent;
    contact?: ContactLookupSearchContent;
    isAddressOverriden?: boolean;
}

export interface WtgJobAddressProvider {
    companyProvider: SearchFieldItemProvider<AddressSearchItem>;
    contactProvider: SearchFieldItemProvider<ContactSearchItem>;
    countryProvider: SearchFieldItemProvider;
    setActiveCompany: (companyGuid: string) => void;
    setLatLngAsync: (
        address: AddressLookupSearchContent,
        latLng: Ref<{ lat: number; lng: number } | undefined>
    ) => void;
}

export interface WtgJobAddressAddressItem {
    guid?: string;
    companyGuid?: string;
    company?: string;
    street?: string;
    streetAlt?: string;
    city?: string;
    postcode?: string;
    state?: string;
    countryCode?: string;
    phone?: string;
    mobile?: string;
    email?: string;
    additionalInfo?: string;
}

export interface WtgJobAddressContactItem {
    guid?: string;
    name?: string;
    phone?: string;
    mobile?: string;
    email?: string;
}

export interface WtgJobAddressChangeOverride {
    caption?: string;
    from?: string;
    to?: string;
}

export enum JobAddressEditMode {
    RealAddressOnly = 'real-address-only',
    RealAddressAndFreeText = 'real-address-and-free-text',
}
