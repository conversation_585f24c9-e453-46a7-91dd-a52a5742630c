import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgCard from './WtgCard.stories.ts';

<Meta of={WtgCard} />

<div className="component-header">
    <h1>Card</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusPlanned}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

## API

<Canvas className="canvas-preview" of={WtgCard.Default} />
<Controls of={WtgCard.Default} sort={'alpha'} />

<div className="banner-warning">

    <div>
        <h4>⚠️ Please note</h4>
        <p>WtgCardActions, WtgCardText, WtgCardTitle and WtgCardSubtitle are included in the component library to aid our transition from Vue 2 to Vue 3. Their future use should be carefully considered and most likely they should not be used in Supply applications.</p>
    </div>

</div>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
