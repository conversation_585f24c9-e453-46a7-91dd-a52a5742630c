import WtgDisplayField from '@components/WtgDisplayField/WtgDisplayField.vue';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../WtgUi';
import WtgLabel from '@components/WtgLabel';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgDisplayField', () => {
    test('its name is WtgDisplayField', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgDisplayField');
    });

    describe('when given a label only', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    label: 'My Label',
                    align: 'center',
                },
            });
        });

        test('it renders only 1 WtgLabel', () => {
            const labelsList = wrapper.findAllComponents({ name: 'WtgLabel' });
            expect(labelsList.length).toBe(1);
            expect(labelsList.at(0)!.text()).toBe('My Label');
        });

        test('it passes the text-sm-default typography to the WtgLabel', () => {
            const label = wrapper.findComponent({ name: 'WtgLabel' });
            expect(label.props().typography).toBe('text-sm-default');
        });

        test('it passes the align to the WtgLabel', () => {
            const label = wrapper.findComponent({ name: 'WtgLabel' });
            expect(label.props().align).toBe('center');
        });
    });

    describe('when given a value only', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    align: 'center',
                    color: 'red',
                    fontWeight: 'bold',
                    noWrap: true,
                    typography: 'h5',
                    value: 'My Value',
                },
            });
        });

        test('it renders only 1 WtgLabel', () => {
            const labelsList = wrapper.findAllComponents({ name: 'WtgLabel' });
            expect(labelsList.length).toBe(1);
            expect(labelsList.at(0)!.text()).toBe('My Value');
        });

        test('it passes its props to the value WtgLabel', () => {
            const label = wrapper.findComponent({ name: 'WtgLabel' });
            expect(label.props().align).toBe('center');
            expect(label.props().color).toBe('red');
            expect(label.props().fontWeight).toBe('bold');
            expect(label.props().noWrap).toBe(true);
            expect(label.props().typography).toBe('h5');
        });
    });

    describe('when given a label and a value', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    align: 'center',
                    color: 'red',
                    label: 'My Label',
                    fontWeight: 'bold',
                    noWrap: true,
                    typography: 'h5',
                    value: 'My Value',
                },
            });
        });

        test('it renders two WtgLabel components one above the other', () => {
            const labelsList = wrapper.findAllComponents({ name: 'WtgLabel' });
            expect(labelsList.length).toBe(2);
            const label1 = labelsList.at(0)!;
            expect(label1.props().typography).toBe('label');
            expect(label1.text()).toBe('My Label');
            const label2 = labelsList.at(1)!;
            expect(label2.text()).toBe('My Value');
        });

        test('it passes the label typography to the caption WtgLabel', () => {
            const labelsList = wrapper.findAllComponents({ name: 'WtgLabel' });
            const label1 = labelsList.at(0)!;
            expect(label1.props().typography).toBe('label');
        });

        test('it passes its props to the value WtgLabel', () => {
            const labelsList = wrapper.findAllComponents({ name: 'WtgLabel' });
            const label2 = labelsList.at(1)!;
            expect(label2.props().align).toBe('center');
            expect(label2.props().color).toBe('red');
            expect(label2.props().fontWeight).toBe('bold');
            expect(label2.props().noWrap).toBe(true);
            expect(label2.props().typography).toBe('h5');
        });
    });

    describe('when value is a date and valueType is date', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    value: '2021-02-26',
                    valueType: 'date',
                },
            });
        });

        test('it gives up the value string as a formatted date string', () => {
            expect(wrapper.vm.displayValue).toBe('26-Feb-21');
        });
    });

    describe('when value is a date and valueType is datetime', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    value: '2021-02-26 03:30:45',
                    valueType: 'datetime',
                },
            });
        });

        test('it gives up the value string as a formatted datetime string', () => {
            expect(wrapper.vm.displayValue).toBe('26-Feb-21 03:30');
        });
    });

    describe('when value is a date and valueType is time', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    value: '03:30:45',
                    valueType: 'time',
                },
            });
        });

        test('it gives up the value string as a formatted time string', () => {
            expect(wrapper.vm.displayValue).toBe('03:30');
        });

        test('it passes the useSeconds property on to the time picker', async () => {
            await wrapper.setProps({ useSeconds: true });
            expect(wrapper.vm.displayValue).toBe('03:30:45');
        });
    });

    describe('when value is a duration and valueType is duration', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    value: '02:30',
                    valueType: 'duration',
                },
            });
        });

        test('it gives up the value string as a formatted duration string', () => {
            expect(wrapper.vm.displayValue).toBe('2:30');
        });
    });

    describe('when value is a number and valueType is number and decimals is a number', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    decimals: 4,
                    value: 1234,
                    valueType: 'number',
                },
            });
        });

        test('it gives up the value string as a formatted number string', () => {
            expect(wrapper.vm.displayValue).toBe('1,234.0000');
        });
    });

    describe('when value is a number and valueType is number and decimals is a string', () => {
        describe('when suppressTrailingZeroes is omitted', () => {
            let wrapper: VueWrapper<any>;

            beforeEach(() => {
                wrapper = mountComponent({
                    props: {
                        decimals: '6',
                        value: 1234,
                        valueType: 'number',
                    },
                });
            });

            test('it gives up the value string as a formatted number string, including any trailing zeroes', () => {
                expect(wrapper.vm.displayValue).toBe('1,234.000000');
            });
        });

        describe('when suppressTrailingZeroes is off', () => {
            let wrapper: VueWrapper<any>;

            beforeEach(() => {
                wrapper = mountComponent({
                    props: {
                        decimals: '6',
                        value: 1234,
                        valueType: 'number',
                        suppressTrailingZeroes: false,
                    },
                });
            });

            test('it gives up the value string as a formatted number string, including any trailing zeroes', () => {
                expect(wrapper.vm.displayValue).toBe('1,234.000000');
            });
        });

        describe('when suppressTrailingZeroes is on', () => {
            let wrapper: VueWrapper<any>;

            beforeEach(() => {
                wrapper = mountComponent({
                    props: {
                        decimals: '6',
                        value: 1234,
                        valueType: 'number',
                        suppressTrailingZeroes: true,
                    },
                });
            });

            test('it gives up the value string as a formatted number string, with trailing zeroes suppressed', () => {
                expect(wrapper.vm.displayValue).toBe('1,234');
            });
        });
    });

    describe('when value is Infinity and valueType is number', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    decimals: 4,
                    value: Infinity.toString(),
                    valueType: 'number',
                },
            });
        });

        test('it gives up the value string as a blank string', () => {
            expect(wrapper.vm.displayValue).toBe('');
        });
    });

    describe('when value is NaN and valueType is number', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    decimals: 4,
                    value: Number(0 / 0).toString(),
                    valueType: 'number',
                },
            });
        });

        test('it gives up the value string as a blank string', () => {
            expect(wrapper.vm.displayValue).toBe('');
        });
    });

    describe('when value is a measure and valueType is measure and decimals is a string', () => {
        describe('when suppressTrailingZeroes is omitted', () => {
            let wrapper: VueWrapper<any>;

            beforeEach(() => {
                wrapper = mountComponent({
                    props: {
                        decimals: '6',
                        value: { magnitude: 1234, unit: 'KG' },
                        valueType: 'measure',
                    },
                });
            });

            test('it gives up the value string as a formatted number string, including any trailing zeroes', () => {
                expect(wrapper.vm.displayValue).toBe('1,234.000000 KG');
            });
        });

        describe('when suppressTrailingZeroes is off', () => {
            let wrapper: VueWrapper<any>;

            beforeEach(() => {
                wrapper = mountComponent({
                    props: {
                        decimals: '6',
                        value: { magnitude: 1234, unit: 'KG' },
                        valueType: 'measure',
                        suppressTrailingZeroes: false,
                    },
                });
            });

            test('it gives up the value string as a formatted number string, including any trailing zeroes', () => {
                expect(wrapper.vm.displayValue).toBe('1,234.000000 KG');
            });
        });

        describe('when suppressTrailingZeroes is on', () => {
            let wrapper: VueWrapper<any>;

            beforeEach(() => {
                wrapper = mountComponent({
                    props: {
                        decimals: '6',
                        value: { magnitude: 1234, unit: 'KG' },
                        valueType: 'measure',
                        suppressTrailingZeroes: true,
                    },
                });
            });

            test('it gives up the value string as a formatted number string, with trailing zeroes suppressed', () => {
                expect(wrapper.vm.displayValue).toBe('1,234 KG');
            });
        });
    });

    describe('when value is Infinity and valueType is measure', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    decimals: 4,
                    value: { magnitude: Infinity, unit: 'KG' },
                    valueType: 'measure',
                },
            });
        });

        test('it gives up the value string as a blank string', () => {
            expect(wrapper.vm.displayValue).toBe('');
        });
    });

    describe('when value is NaN and valueType is number', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    decimals: 4,
                    value: { magnitude: Number(0 / 0), unit: 'KG' },
                    valueType: 'measure',
                },
            });
        });

        test('it gives up the value string as a blank string', () => {
            expect(wrapper.vm.displayValue).toBe('');
        });
    });

    describe('when given a suffix', () => {
        let wrapper: VueWrapper<any>;
        beforeEach(() => {
            wrapper = mountComponent({
                props: {
                    suffix: 'su',
                },
            });
        });

        test('it renders the suffix when data type is number', async () => {
            await wrapper.setProps({ value: 11, valueType: 'number' });
            expect(wrapper.vm.displayValue).toBe('11 su');
        });

        test('it renders the suffix when data type is string', async () => {
            await wrapper.setProps({ value: '11', valueType: 'string' });
            expect(wrapper.vm.displayValue).toBe('11 su');
        });

        test('it renders the suffix when data type is measure', async () => {
            await wrapper.setProps({ value: { magnitude: 1234, unit: 'KG' }, valueType: 'measure' });
            expect(wrapper.vm.displayValue).toBe('1,234 KG su');
        });

        test('it renders the suffix when data type is datetime', async () => {
            await wrapper.setProps({ value: '2025-05-12 12:33:12', valueType: 'datetime' });
            expect(wrapper.vm.displayValue).toBe('12-May-25 12:33 su');
        });

        test('it renders the suffix when data type is date', async () => {
            await wrapper.setProps({ value: '2025-05-12', valueType: 'date' });
            expect(wrapper.vm.displayValue).toBe('12-May-25 su');
        });

        test('it renders the suffix when data type is time', async () => {
            await wrapper.setProps({ value: '12:33:12', valueType: 'time' });
            expect(wrapper.vm.displayValue).toBe('12:33 su');
        });

        test('it renders the suffix when data type is duration', async () => {
            await wrapper.setProps({ value: '02:33', valueType: 'duration' });
            expect(wrapper.vm.displayValue).toBe('2:33 su');
        });
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('it has a typography of text-md-strong by default', () => {
        const wrapper = mountComponent();
        const label = wrapper.findComponent(WtgLabel);
        expect(label.props('typography')).toBe('text-md-strong');
    });

    function mountComponent({ props = {}, slots = {}, provide = {} } = {}) {
        return mount(WtgDisplayField, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
