import WtgCheckbox from '@components/WtgCheckbox/WtgCheckbox.vue';
import WtgCheckboxGroup from '@components/WtgCheckbox/WtgCheckboxGroup.vue';
import WtgCol from '@components/WtgCol';
import WtgPanel from '@components/WtgPanel';
import WtgRow from '@components/WtgRow';
import { tooltipArgTypes } from '@composables/tooltip';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { expect, userEvent, waitFor, within } from '@storybook/test';
import { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import { CheckboxSandboxTemplate } from './templates/wtg-checkbox-sandbox.stories-template';
import { CheckboxSentimentsTemplate } from './templates/wtg-checkbox-sentiments.stories-template';

type Story = StoryObj<typeof WtgCheckbox>;
const meta: Meta<typeof WtgCheckbox> = {
    title: 'Components/Checkbox',
    component: WtgCheckbox,
    parameters: {
        docs: {
            description: {
                component:
                    'Checkboxes allow user to choose one or more options from a limited set of predefined options. If you have more than 10 options, please use Select component instead.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=149-5673&mode=design&t=FoGkzhcXiCnlZFCr-0',
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgCheckbox },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
        },
        template: '<div><WtgCheckbox v-bind="args" @update:model-value="updateModel">something</WtgCheckbox></div>',
    }),
    argTypes: {
        modelValue: {
            control: 'boolean',
        },
        sentiment: {
            control: 'select',
            options: ['default', 'critical', 'warning', 'success'],
        },
        ...tooltipArgTypes,
    },
};

export default meta;

export const Default: Story = {
    args: {
        label: 'Check me',
        modelValue: false,
    },
};

export const Sentiments: Story = {
    name: 'Sentiments',
    args: {},
    render: (args) => ({
        components: { WtgCheckbox, WtgCol, WtgRow },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: CheckboxSentimentsTemplate,
    }),
};

export const Readonly: Story = {
    name: 'Read Only',
    args: {
        label: 'Read only',
        readonly: true,
    },
};

export const Disabled: Story = {
    args: {
        label: 'Disabled',
        disabled: true,
    },
};

const options = ref({ selectedOptions: [] });

export const Sandbox: Story = {
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgCheckbox, WtgCheckboxGroup, WtgCol, WtgRow, WtgPanel },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            updateIndeterminate: action('update:indeterminate'),
        },
        data: () => {
            return {
                options: [
                    'Testing Multiple Option One',
                    'Testing Multiple Option Two',
                    'Testing Multiple Option Three',
                ],
                selectedOptions: options.value.selectedOptions,
            };
        },
        template: templateWithRtl(CheckboxSandboxTemplate),
    }),
    play: async ({ canvasElement, step }) => {
        const canvas = within(canvasElement);
        const ltrContainer = canvas.getByTestId('ltrContainer');

        const ltrContainerTemplate = within(ltrContainer);

        await step('Get the LTR Checkboxes with multiple prop enabled', async () => {
            const checkbox1Ltr = ltrContainerTemplate
                .getByTestId('Testing Multiple Option One')
                .querySelector('input[type="checkbox"]') as HTMLInputElement;
            const checkbox2Ltr = ltrContainerTemplate
                .getByTestId('Testing Multiple Option Two')
                .querySelector('input[type="checkbox"]') as HTMLInputElement;
            const checkbox3Ltr = ltrContainerTemplate
                .getByTestId('Testing Multiple Option Three')
                .querySelector('input[type="checkbox"]') as HTMLInputElement;

            await step('Click on the three LTR checkboxes', async () => {
                await waitFor(() => userEvent.click(checkbox1Ltr));
                await waitFor(() => userEvent.click(checkbox2Ltr));
                await waitFor(() => userEvent.click(checkbox3Ltr));

                await step('Check if selected options array contains the selected values', async () => {
                    await waitFor(() => expect(options.value.selectedOptions).toContain('Testing Multiple Option One'));
                    await waitFor(() => expect(options.value.selectedOptions).toContain('Testing Multiple Option Two'));
                    await waitFor(() =>
                        expect(options.value.selectedOptions).toContain('Testing Multiple Option Three')
                    );
                });
            });
        });
    },
};
