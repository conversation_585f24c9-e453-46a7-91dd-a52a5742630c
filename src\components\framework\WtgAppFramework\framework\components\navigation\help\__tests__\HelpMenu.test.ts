import { WtgFramework, WtgFrameworkHelpItem } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import HelpMenu from '../HelpMenu.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('help-menu', () => {
    let el: HTMLElement;
    let application: WtgFramework;
    let wrapper: any;
    let helpGroup: any;

    beforeEach(async () => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.captions = {
            help: 'Help',
            pageHelp: 'Page Help',
            about: 'About',
        } as any;
        application.dialogs = {
            about: {
                open: jest.fn(),
            },
        } as any;
        application.pageHelp = {
            visible: false,
            loading: false,
            alwaysOpenHelp: false,

            name: 'My Page',
            text: 'Help Text',

            onAlwaysOpenHelpChanged: jest.fn(),
            onPageHelpClosing: jest.fn(),
            onPageHelpOpening: jest.fn,
        };
        setApplication(application);

        wrapper = await mountComponentAsync();
        helpGroup = wrapper.findComponent({ name: 'WtgListItem' });
        wtgUi.breakpoint.mdAndDown = false;
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is HelpMenu', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('HelpMenu');
    });

    test('it gets it title from application captions', () => {
        expect(helpGroup.text()).toBe('Help');
    });

    test('it displays a list of help-items', async () => {
        application.helpItems.push(new WtgFrameworkHelpItem('Item 1', 'Item 1 Title', 'link1'));
        application.helpItems.push(new WtgFrameworkHelpItem('Item 2', 'Item 2 Title', 'link2'));
        application.helpItems.push(new WtgFrameworkHelpItem('Item 3', 'Item 3 Title', 'link3'));

        const wrapper = await mountComponentAsync();
        const listGroup = wrapper.find('.wtg-list-item__container');
        await listGroup.trigger('click');

        const menu = wrapper.findComponent({ name: 'WtgPopover' });
        let listItems = menu.findComponent({ name: 'WtgList' }).findAllComponents({ name: 'WtgListItem' });
        expect(listItems.length - 2).toBe(3);

        application.helpItems.push(new WtgFrameworkHelpItem('Item 4', 'Item 4 Title', 'link4'));
        await wrapper.vm.$nextTick();

        listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
        expect(listItems.length - 2).toBe(5);
    });

    test('when you click on a list-item, it calls the click-handler of the associated menu item', async () => {
        const clickHandler = jest.fn();
        application.helpItems.push(new WtgFrameworkHelpItem('Item 1', 'Item 1 Title', 'link1', clickHandler));

        const wrapper = await mountComponentAsync();
        const listGroup = wrapper.find('.wtg-list-item__container');
        await listGroup.trigger('click');

        const menu = wrapper.findComponent({ name: 'WtgPopover' });
        const listItems = menu.findComponent({ name: 'WtgList' }).findAllComponents({ name: 'WtgListItem' });
        listItems.at(1)?.trigger('click');
        await wrapper.vm.$nextTick();
        expect(clickHandler).toHaveBeenCalledTimes(1);
    });

    test('it applies the title and  href of the associated menu item', async () => {
        const clickHandler = jest.fn();
        application.helpItems.push(new WtgFrameworkHelpItem('Item 1', 'Item 1 Title', 'link1', clickHandler));

        const wrapper = await mountComponentAsync();
        const listGroup = wrapper.find('.wtg-list-item__container');
        await listGroup.trigger('click');

        const menu = wrapper.findComponent({ name: 'WtgPopover' });
        const listItems = menu.findComponent({ name: 'WtgList' }).findAllComponents({ name: 'WtgListItem' });
        expect(listItems.at(1)?.text()).toBe('Item 1 Title');
        expect(listItems.at(1)?.props('href')).toBe('link1');
    });

    test('it shows the page help component when the page help is clicked', async () => {
        const wrapper = await mountComponentAsync();
        const listGroup = wrapper.find('.wtg-list-item__container');
        await listGroup.trigger('click');

        const menu = wrapper.findComponent({ name: 'WtgPopover' });
        const pageHelpItem = menu.findComponent({ name: 'WtgList' }).findAllComponents({ name: 'WtgListItem' }).at(0);
        await pageHelpItem?.trigger('click');
        expect(application.pageHelp?.visible).toBe(true);
    });

    test('it calls the dialog open for about when the about item is clicked', async () => {
        const wrapper = await mountComponentAsync();
        const listGroup = wrapper.find('.wtg-list-item__container');
        await listGroup.trigger('click');

        const menu = wrapper.findComponent({ name: 'WtgPopover' });
        const aboutItem = menu.findComponent({ name: 'WtgList' }).findAllComponents({ name: 'WtgListItem' }).at(-1);
        await aboutItem?.trigger('click');
        expect(application.dialogs.about.open).toHaveBeenCalledTimes(1);
    });

    describe('when on mobile', () => {
        beforeEach(() => {
            wtgUi.breakpoint.mdAndDown = true;
        });

        afterEach(() => {
            wtgUi.breakpoint.mdAndDown = false;
        });

        test('it when render the help menu item component', async () => {
            const wrapper = await mountComponentAsync();
            const menuItem = wrapper.findComponent({ name: 'HelpInlineMenu' });
            expect(menuItem.exists()).toBe(true);
        });
    });

    describe('Accessibility', () => {
        beforeEach(() => {
            const clickHandler = jest.fn();
            application.helpItems.push(new WtgFrameworkHelpItem('Item 1', 'Item 1 Title', 'link1', clickHandler));
        });

        test('it will render a list item as the activator with aria properties', async () => {
            const wrapper = await mountComponentAsync();
            const listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(listItems.at(0)?.attributes('role')).toBe('menuitem');
            expect(listItems.at(0)?.attributes('aria-label')).toBe(application.captions.help);
            expect(listItems.at(0)?.attributes('aria-haspopup')).toBe('menu');
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(HelpMenu) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(HelpMenu);
    }
});
