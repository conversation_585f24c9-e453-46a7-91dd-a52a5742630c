import WtgChart from '@components/WtgChart/WtgChart.vue';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { ArcElement, Chart, ChartData, ChartOptions, PolarAreaController, RadialLinearScale } from 'chart.js';
import { PropType, VNode, defineComponent, h } from 'vue';

Chart.register(ArcElement, PolarAreaController, RadialLinearScale);

export default defineComponent({
    name: 'WtgPolarAreaChart',
    props: {
        data: {
            type: Object as PropType<ChartData>,
            default: (): ChartData => {
                return {
                    datasets: [],
                };
            },
        },
        options: {
            type: Object as PropType<ChartOptions>,
            default: (): ChartOptions => {
                return {};
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    setup(props) {
        useLayoutGridColumn(props);
    },
    render(): VNode {
        return h(WtgChart, {
            type: 'polarArea',
            data: this.data,
            options: this.options,
            loading: this.loading,
        });
    },
});
