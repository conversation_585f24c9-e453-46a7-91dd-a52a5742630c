import { VueWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../../WtgUi';
import { Conversation, ConversationFollower } from '../../types';
import AddFollowersDialog from '../AddFollowersDialog.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi({
    language: {
        captionProvider: (languageCode: string, key: string, params?: (string | number)[]) => `C:${key}${params}`,
    },
});

const conversation: Conversation = {
    id: '1',
    name: 'Conversation One',
    type: '',
    followers: [] as ConversationFollower[],
    following: false,
    followerUserTypes: [
        { type: 1, caption: 'Staff' },
        { type: 2, caption: 'Contact' },
        { type: 3, caption: 'Group' },
        { type: 4, caption: 'Organization' },
    ],
    loadingMessages: false,
    messages: {},
    provider: {
        dislikeMessageAsync: jest.fn(),
        followConversationAsync: jest.fn(),
        likeMessageAsync: jest.fn(),
        sendMessageAsync: jest.fn(),
        unfollowConversationAsync: jest.fn(),
        sendUnsentMessageAsync: jest.fn(),
        removeFollowerFromConversationAsync: jest.fn(),
        addFollowersAsync: jest.fn().mockResolvedValue({}),
        getPotentialFollowersAsync: jest.fn().mockResolvedValue({
            items: [],
            total: 0,
        }),
    },
};

describe('AddFollowersDialog', () => {
    let el: HTMLElement, wrapper: VueWrapper<any>;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        wrapper = mountComponent();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is AddFollowersDialog', () => {
        expect(wrapper.vm.$options.name).toBe('AddFollowersDialog');
    });

    test('item providers are NOT set', () => {
        expect(wrapper.vm.itemProviders[1]).toBeUndefined();
        expect(wrapper.vm.itemProviders[2]).toBeUndefined();
        expect(wrapper.vm.itemProviders[3]).toBeUndefined();
        expect(wrapper.vm.itemProviders[4]).toBeUndefined();
    });

    describe('when dialog opened', () => {
        beforeEach(async () => {
            conversation.provider.getPotentialFollowersAsync = jest.fn().mockResolvedValue({
                items: [
                    {
                        actionText: 'B28',
                        chips: [],
                        photo: {
                            fallbackImage: '/GlowWeb/Images/DefaultProfile.png',
                            image: 'img.jpg',
                        },
                        label: 'Staff',
                        secondaryText: 'Test',
                        text: 'Staff',
                        value: '********-0000-0000-0000-************',
                    },
                    {
                        actionText: undefined,
                        chips: undefined,
                        icon: 'mdi-account-group',
                        label: 'Group',
                        secondaryText: 'G1',
                        value: '2fd0bec6-8f25-4bbb-b4fb-51c0d84390a4',
                        text: 'Group',
                    },
                    {
                        actionText: 'B29',
                        chips: [{ label: 'Active' }, { label: 'Driver' }],
                        photo: {
                            image: '/GlowWeb/Images/DefaultProfile.png',
                        },
                        label: 'Chips',
                        text: 'Contact',
                        value: '********-0000-0000-0000-************',
                    },
                ],
                total: 3,
            });

            await wrapper.setProps({ value: true });
        });

        test('when cancel button clicked sets value to false', async () => {
            expect(wrapper.vm.internalValue).toBe(true);

            const buttons = wrapper.findAllComponents({ name: 'wtg-button' });
            await buttons.at(buttons.length - 2)!.trigger('click');
            expect(wrapper.vm.internalValue).toBe(false);
        });

        test('item providers are set', () => {
            expect(wrapper.vm.itemProviders[1]).toBeDefined();
            expect(wrapper.vm.itemProviders[2]).toBeDefined();
            expect(wrapper.vm.itemProviders[3]).toBeDefined();
            expect(wrapper.vm.itemProviders[4]).toBeDefined();
        });

        test('item providers are reset when closed and reopened', async () => {
            const itemProvider1 = wrapper.vm.itemProviders[1];
            const itemProvider2 = wrapper.vm.itemProviders[2];
            const itemProvider3 = wrapper.vm.itemProviders[3];
            const itemProvider4 = wrapper.vm.itemProviders[4];

            await wrapper.setProps({ value: false });
            await wrapper.setProps({ value: true });

            expect(wrapper.vm.itemProviders[1]).not.toBe(itemProvider1);
            expect(wrapper.vm.itemProviders[2]).not.toBe(itemProvider2);
            expect(wrapper.vm.itemProviders[3]).not.toBe(itemProvider3);
            expect(wrapper.vm.itemProviders[4]).not.toBe(itemProvider4);
        });

        describe('search', () => {
            let userTypeTabs: VueWrapper<any>[], selectFields: VueWrapper<any>[];

            beforeEach(() => {
                const userTabs = wrapper.findComponent({ name: 'wtg-tabs' });
                userTypeTabs = userTabs.findAllComponents({ name: 'wtg-tab' });
                selectFields = wrapper.findAllComponents({ name: 'wtg-select-field' });
            });

            test('tabs show pagination arrows', () => {
                const vTabs = wrapper.findComponent({ name: 'v-tabs' });
                expect(vTabs.props('showArrows')).toBe(true);
                expect(vTabs.props('nextIcon')).toBe('s-icon-move-right');
                expect(vTabs.props('prevIcon')).toBe('s-icon-move-left');
            });

            test('toggle userType buttons correctly', async () => {
                expect(userTypeTabs.length).toBe(4);

                expect(userTypeTabs.at(0)!.text()).toBe(conversation.followerUserTypes[0].caption);
                expect(userTypeTabs.at(1)!.text()).toBe(conversation.followerUserTypes[1].caption);
                expect(userTypeTabs.at(2)!.text()).toBe(conversation.followerUserTypes[2].caption);
                expect(userTypeTabs.at(3)!.text()).toBe(conversation.followerUserTypes[3].caption);

                expect(wrapper.vm.userType).toBe(conversation.followerUserTypes[0].type);

                userTypeTabs.at(1)!.trigger('click');
                await nextTick();

                expect(wrapper.vm.userType).toBe(conversation.followerUserTypes[1].type);
            });

            test('display and toggle select field class correctly', async () => {
                expect(selectFields.length).toBe(1);
                expect(selectFields.at(0)!.isVisible()).toBeTruthy();
                expect(selectFields.at(0)!.props('placeholder')).toBe('C:conversation.searchPotentialFollowersStaff');

                userTypeTabs.at(1)!.trigger('click');
                await nextTick();

                selectFields = wrapper.findAllComponents({ name: 'wtg-select-field' });
                expect(selectFields.length).toBe(2);
                expect(selectFields.at(0)!.isVisible()).toBeFalsy();
                expect(selectFields.at(1)!.isVisible()).toBeTruthy();
                expect(selectFields.at(1)!.props('placeholder')).toBe('C:conversation.searchPotentialFollowersContact');

                userTypeTabs.at(2)!.trigger('click');
                await nextTick();

                selectFields = wrapper.findAllComponents({ name: 'wtg-select-field' });
                expect(selectFields.length).toBe(3);
                expect(selectFields.at(0)!.isVisible()).toBeFalsy();
                expect(selectFields.at(1)!.isVisible()).toBeFalsy();
                expect(selectFields.at(2)!.isVisible()).toBeTruthy();
                expect(selectFields.at(2)!.props('placeholder')).toBe('C:conversation.searchPotentialFollowersGroup');

                userTypeTabs.at(3)!.trigger('click');
                await nextTick();

                selectFields = wrapper.findAllComponents({ name: 'wtg-select-field' });
                expect(selectFields.length).toBe(4);
                expect(selectFields.at(0)!.isVisible()).toBeFalsy();
                expect(selectFields.at(1)!.isVisible()).toBeFalsy();
                expect(selectFields.at(2)!.isVisible()).toBeFalsy();
                expect(selectFields.at(3)!.isVisible()).toBeTruthy();
                expect(selectFields.at(3)!.props('placeholder')).toBe(
                    'C:conversation.searchPotentialFollowersOrganization'
                );

                userTypeTabs.at(0)!.trigger('click');
                await nextTick();

                selectFields = wrapper.findAllComponents({ name: 'wtg-select-field' });
                expect(selectFields.length).toBe(4);
                expect(selectFields.at(0)!.isVisible()).toBeTruthy();
                expect(selectFields.at(0)!.props('placeholder')).toBe('C:conversation.searchPotentialFollowersStaff');
                expect(selectFields.at(1)!.isVisible()).toBeFalsy();
                expect(selectFields.at(2)!.isVisible()).toBeFalsy();
                expect(selectFields.at(3)!.isVisible()).toBeFalsy();
            });

            test('display photos, icons and tags for potential followers', async () => {
                expect(selectFields.length).toBe(1);
                await selectFields.at(0)?.trigger('click');

                const items = wrapper.findAllComponents({ name: 'wtg-list-item' });
                expect(items.length).toBe(4);

                const listItem1 = items.at(1);
                const avatar1 = listItem1?.findComponent({ name: 'wtg-avatar' });
                expect(avatar1?.exists()).toBe(true);
                const img1 = avatar1?.findComponent({ name: 'wtg-image' });
                expect(img1?.vm.src).toBe('img.jpg');
                const icon1 = avatar1?.findComponent({ name: 'wtg-icon' });
                expect(icon1?.exists()).toBe(false);

                const listItem2 = items.at(2);
                const avatar2 = listItem2?.findComponent({ name: 'wtg-avatar' });
                expect(avatar2?.exists()).toBe(true);
                const img2 = avatar2?.findComponent({ name: 'wtg-image' });
                expect(img2?.exists()).toBe(false);
                const icon2 = avatar2?.findComponent({ name: 'wtg-icon' });
                expect(icon2?.element.className).toContain('mdi-account-group');

                const listItem3 = items.at(3);
                const avatar3 = listItem3?.findComponent({ name: 'wtg-avatar' });
                expect(avatar3?.exists()).toBe(true);
                const img3 = avatar3?.findComponent({ name: 'wtg-image' });
                expect(img3?.vm.src).toBe('/GlowWeb/Images/DefaultProfile.png');
                const icon3 = avatar3?.findComponent({ name: 'wtg-icon' });
                expect(icon3?.exists()).toBe(false);
                const tags3 = listItem3?.findAllComponents({ name: 'wtg-tag' });
                expect(tags3?.length).toBe(2);
                expect(tags3?.at(0)?.text()).toBe('Active');
                expect(tags3?.at(1)?.text()).toBe('Driver');
            });
        });

        describe('add followers correctly', () => {
            let addFollowersButton: VueWrapper;

            beforeEach(async () => {
                await wrapper.setData({
                    selectedValues: {
                        1: ['MOCKED_STAFF_PK_1', 'MOCKED_STAFF_PK_2'],
                        2: ['MOCKED_CONTACT_PK_1', 'MOCKED_CONTACT_PK_2'],
                        3: ['MOCKED_GROUP_PK_1', 'MOCKED_GROUP_PK_2'],
                        4: ['MOCKED_ORGANIZATION_PK_1', 'MOCKED_ORGANIZATION_PK_2'],
                    },
                });

                addFollowersButton = wrapper.findAllComponents({ name: 'wtg-button' }).at(2)!;
                expect(addFollowersButton.text()).toBe('C:conversation.addFollowers');
                conversation.provider.addFollowersAsync = jest.fn().mockResolvedValue({});
            });

            test('when success', async () => {
                const promise = addFollowersButton.trigger('click');
                expect(wrapper.vm.loading).toBe(true);
                await promise;

                expect(conversation.provider.addFollowersAsync).toHaveBeenCalledTimes(1);
                expect(conversation.provider.addFollowersAsync).toHaveBeenCalledWith(conversation, {
                    1: ['MOCKED_STAFF_PK_1', 'MOCKED_STAFF_PK_2'],
                    2: ['MOCKED_CONTACT_PK_1', 'MOCKED_CONTACT_PK_2'],
                    3: ['MOCKED_GROUP_PK_1', 'MOCKED_GROUP_PK_2'],
                    4: ['MOCKED_ORGANIZATION_PK_1', 'MOCKED_ORGANIZATION_PK_2'],
                });

                await nextTick();

                expect(wrapper.vm.loading).toBe(false);
                expect(wrapper.vm.selectedValues).toEqual({ 1: [], 2: [], 3: [], 4: [] });
            });

            test('when partially failed', async () => {
                conversation.provider.addFollowersAsync = jest.fn().mockResolvedValue({
                    1: ['MOCKED_STAFF_PK_1', 'MOCKED_STAFF_PK_2'],
                    2: ['MOCKED_CONTACT_PK_1', 'MOCKED_CONTACT_PK_2'],
                });

                const promise = addFollowersButton.trigger('click');
                expect(wrapper.vm.loading).toBe(true);
                await promise;

                expect(conversation.provider.addFollowersAsync).toHaveBeenCalledTimes(1);
                expect(conversation.provider.addFollowersAsync).toHaveBeenCalledWith(conversation, {
                    1: ['MOCKED_STAFF_PK_1', 'MOCKED_STAFF_PK_2'],
                    2: ['MOCKED_CONTACT_PK_1', 'MOCKED_CONTACT_PK_2'],
                    3: ['MOCKED_GROUP_PK_1', 'MOCKED_GROUP_PK_2'],
                    4: ['MOCKED_ORGANIZATION_PK_1', 'MOCKED_ORGANIZATION_PK_2'],
                });

                await nextTick();

                expect(wrapper.vm.loading).toBe(false);
                expect(wrapper.vm.internalValue).toBe(true);

                await nextTick();

                const errorDialog = wrapper.findAllComponents({ name: 'wtg-alert-modal' }).at(0);
                expect(errorDialog?.props('title')).toBe('C:conversation.dialog.addFollowersFailure.title');
                const message = document.querySelector('#message');
                expect(message?.innerHTML).toContain('C:conversation.dialog.addFollowersFailure.messageStaff, Contact');

                expect(wrapper.vm.selectedValues).toEqual({
                    1: ['MOCKED_STAFF_PK_1', 'MOCKED_STAFF_PK_2'],
                    2: ['MOCKED_CONTACT_PK_1', 'MOCKED_CONTACT_PK_2'],
                    3: [],
                    4: [],
                });
            });
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(AddFollowersDialog as any, {
            propsData: {
                conversation,
                value: false,
                ...propsData,
            },
            global: {
                plugins: [wtgUi],
            },
            slots,
        });
    }
});
