import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../../WtgUi';
import RecentInlineMenu from '../RecentInlineMenu.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const items = {
    favorites: [
        {
            id: 'favoriteItem1',
            caption: 'Favorite Item 1',
            href: '/',
            favorite: true,
            actions: [
                {
                    id: 'string',
                    caption: 'string',
                    href: '/',
                },
            ],
            onRemoveFromFavorites: () => {},
            onAddToFavorites: () => {},
        },
    ],

    recents: [
        {
            id: 'recentItem1',
            caption: 'Recent Item 1',
            href: '/',
            favorite: true,
            actions: [
                {
                    id: 'string',
                    caption: 'string',
                    href: '/',
                },
            ],
            onRemoveFromFavorites: () => {},
            onAddToFavorites: () => {},
        },
    ],
};

const propsData = {
    items,
    captionClose: 'Close',
    favoritesCaption: 'Favorites Test',
    recentsCaption: 'Recent Test',
    onClick: jest.fn(),
    onDialogOpen: jest.fn(),
};

describe('recent-inline-menu', () => {
    test('its name is RecentInlineMenu', async () => {
        const wrapper = await mountComponent();
        expect(wrapper.vm.$options.__name).toBe('RecentInlineMenu');
    });

    test('its displays when on mobile', async () => {
        wtgUi.breakpoint.mdAndDown = true;
        const wrapper = await mountComponent();
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        expect(listItem.exists()).toBe(true);
    });

    test('its DOES NOT display when on desktop', async () => {
        wtgUi.breakpoint.mdAndDown = false;
        const wrapper = await mountComponent();
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        expect(listItem.exists()).toBe(false);
    });

    test('its title comes from the properties captions', async () => {
        wtgUi.breakpoint.mdAndDown = true;
        const wrapper = await mountComponent(propsData);
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        expect(listItem.text()).toBe('Favorites Test');
    });

    test('it will call favorites dialog methods when clicked', async () => {
        wtgUi.breakpoint.mdAndDown = true;
        const wrapper = await mountComponent(propsData);
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        await listItem.trigger('click');

        expect(propsData.onClick).toHaveBeenCalledTimes(1);
        expect(propsData.onDialogOpen).toHaveBeenCalledTimes(1);
    });

    describe('Accessibility', () => {
        test('it renders a list item with a role and aria has popup', async () => {
            wtgUi.breakpoint.mdAndDown = true;
            const wrapper = await mountComponent(propsData);
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            expect(listItem.attributes('role')).toBe('menuitem');
            expect(listItem.attributes('aria-label')).toBe('Favorites Test');
            expect(listItem.attributes('aria-haspopup')).toBe('menu');
        });
    });

    function mountComponent(propsData = {}) {
        return mount(RecentInlineMenu, {
            propsData,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
