import WtgBottomSheet from '@components/WtgBottomSheet';
import WtgButton from '@components/WtgButton';
import WtgDivider from '@components/WtgDivider';
import WtgSpacer from '@components/WtgSpacer';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';

type Story = StoryObj<typeof WtgBottomSheet>;

const meta: Meta<typeof WtgBottomSheet> = {
    title: 'Utilities/Bottom Sheet',
    component: WtgBottomSheet,
    parameters: {
        docs: {
            description: {
                component: `The bottom sheet is a modified dialog that slides from the bottom of the screen. It is primarily used for mobile applications.`,
            },
        },
        layout: 'centered',
        controls: {
            sort: 'alpha',
        },
    },
    argTypes: {
        modelValue: { control: 'boolean', description: 'Controls visibility of the bottom sheet' },
    },
    render: (args) => ({
        components: { WtgBottomSheet, Wtg<PERSON><PERSON><PERSON>, Wtg<PERSON><PERSON><PERSON>, WtgSpacer },
        setup() {
            const toggleSheet = () => {
                isOpen.value = !isOpen.value;
            };

            return { args, isOpen, toggleSheet };
        },
        methods: {
            updateModel: (v: boolean) => {
                action('update:modelValue');
                isOpen.value = v;
            },
            closeSheet: () => {
                isOpen.value = false;
            },
        },
        template: `
            <div class="d-flex flex-column align-center justify-center">
                <WtgButton variant="fill" sentiment="primary" @click="toggleSheet">Open Bottom Sheet</WtgButton>
                <WtgBottomSheet 
                    v-bind="args"
                    :model-value="isOpen"
                    @update:modelValue="updateModel"
                >
                    <div class="d-flex flex-column ga-2">
                        <span class="text-title-md-default">
                            Bottom Sheet Title
                        </span>
                        <span>
                            This is the content of the bottom sheet.
                        </span>
                        <WtgDivider class="mt-4"/>
                        <div class="d-flex ga-2 pt-2">
                            <WtgSpacer />
                            <WtgButton  @click="closeSheet">Cancel</WtgButton>
                        </div>
                    </div>
                </WtgBottomSheet>
            </div>
        `,
    }),
    decorators: [
        () => ({
            template: `
                <div style="display: flex; align-items: center; justify-content: center; min-width: 300px; min-height: 300px;">
                    <story />
                </div>
            `,
        }),
    ],
};

export default meta;
const isOpen = ref(false);
export const Default: Story = {
    args: {},
};
