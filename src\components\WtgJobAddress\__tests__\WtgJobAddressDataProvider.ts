import {
    AddressLookupSearchContent,
    AddressSearchItem,
    ContactLookupSearchContent,
    ContactSearchItem,
} from '@components/WtgAddressField';
import CountriesItemProvider from '@components/WtgSearchField/__tests__/CountriesItemProvider';
import { SearchFieldDisplayMode, SearchFieldItem, SearchFieldItemProvider } from '@components/WtgSearchField/types';
import { WtgJobAddressProvider as AddressProvider } from '../types';

const companies = [
    { id: '1', name: 'Big Corp', code: 'BIG' },
    { id: '2', name: 'Ant Tech', code: 'ANT' },
];

const countryProvider = new CountriesItemProvider();

const addresses: AddressLookupSearchContent[] = [
    {
        guid: '1',
        companyGuid: companies[0].id,
        company: companies[0].name,
        companyCode: companies[0].code,
        code: '1LO',
        street: '111/22 Address line 1',
        streetAlt: 'Address line 2',
        city: 'Suburb',
        postcode: 'postcode',
        state: 'region',
        countryCode: 'GR',
        phone: '+**********',
        mobile: '+**********',
        email: '<EMAIL>',
        additionalInfo: 'Additional information',
    },
    {
        guid: '2',
        companyGuid: companies[1].id,
        company: companies[1].name,
        companyCode: companies[1].code,
        code: '1YES',
        street: '1 Yes st',
        city: 'SunTown',
        postcode: '2017',
        state: 'NSW',
        countryCode: 'AU',
        additionalInfo: 'Additional Information',
    },
    {
        guid: '3',
        companyGuid: companies[0].id,
        company: companies[0].name,
        companyCode: companies[0].code,
        code: '3MALL',
        street: '3 Mall st',
        city: 'Tempe',
        postcode: '2060',
        state: 'VIC',
        countryCode: 'AU',
    },
    {
        guid: '4',
        companyGuid: companies[1].id,
        company: companies[1].name,
        companyCode: companies[1].code,
        code: '2WORK',
        street: '2 Work st',
        city: 'Eltown',
        postcode: '2027',
        state: 'QLD',
        countryCode: 'AU',
    },
];

const contacts: ContactLookupSearchContent[] = [
    {
        guid: '1',
        name: 'Alberto',
        phone: '+61444444490',
        mobile: '+6141234567',
        email: '<EMAIL>',
    },
    {
        guid: '2',
        name: 'Jeff Colbici',
        phone: '+61431987809',
        email: '<EMAIL>',
    },
    {
        guid: '3',
        name: 'Steve Macheti',
        phone: '+61985634090',
        email: '<EMAIL>',
    },
    {
        guid: '4',
        name: 'Robert Salamender',
        phone: '+61346771090',
        email: '<EMAIL>',
    },
    {
        guid: '5',
        name: undefined,
        phone: '+61987698988',
        email: '<EMAIL>',
    },
];

const companyContacts = new Map<string, ContactLookupSearchContent[]>();
companyContacts.set(companies[0].id, [contacts[0], contacts[1], contacts[2]]);
companyContacts.set(companies[1].id, [contacts[3]]);

export const db = {
    companies,
    addresses,
    allowFilteringContactsByCompany: false,
    get contacts() {
        if (!db.allowFilteringContactsByCompany) {
            return contacts;
        }

        return companyContacts.get(activeCompany!) || [];
    },
};

const companyProvider: SearchFieldItemProvider<AddressSearchItem> = {
    getItemAsync: (value: string): Promise<AddressSearchItem | undefined> => {
        const address = db.addresses.find((entry) => entry.company === value);

        if (!address) {
            return Promise.resolve(undefined);
        }

        const item: AddressSearchItem = {
            address,
            value: address.guid,
            description: address.company!,
            code: address.code!,
        };

        return Promise.resolve(item);
    },
    getItemForValueAsync: (value: string): Promise<AddressSearchItem | undefined> => {
        const address = db.addresses.find((entry) => entry.guid === value); // addresses.find -->          address = {guid:'1' , company:'Big Corp' , code:'1L0'}};

        if (!address) {
            return Promise.resolve(undefined);
        }

        const item: SearchFieldItem = {
            address,
            value: address.guid,
            description: address.company!,
            code: address.code!,
        };

        return Promise.resolve(item);
    },
    getItemsAsync: (search: string): Promise<{ items: AddressSearchItem[]; total: number }> => {
        search = search.toLowerCase();
        const items = db.addresses
            .filter(
                (address) =>
                    (address.company && address.company.toLowerCase().indexOf(search) >= 0) ||
                    (address.street && address.street.toLowerCase().indexOf(search) >= 0)
            )
            .map((address: any) => ({
                address,
                value: address.guid,
                description: address.company,
                code: address.code,
            }));
        const response = {
            total: items.length,
            items,
        };

        return Promise.resolve(response);
    },
    getDisplayModeAsync: (): Promise<SearchFieldDisplayMode> => {
        return Promise.resolve(SearchFieldDisplayMode.DescOnly);
    },
    getSearchResultsDisplayModeAsync: (): Promise<SearchFieldDisplayMode> => {
        return Promise.resolve(SearchFieldDisplayMode.CodeDesc);
    },
    getDisplayStringAsync: (value: string): Promise<string> => {
        const address = db.addresses.find((entry) => entry.guid === value);

        return address?.company ? Promise.resolve(address.company) : Promise.resolve('');
    },
    getEmptyValueAsync: (): Promise<string> => {
        return Promise.resolve('');
    },
    validateAsync: (): Promise<boolean> => {
        return Promise.resolve(true);
    },
};

const contactProvider: SearchFieldItemProvider<ContactSearchItem> = {
    getItemAsync: (value: string): Promise<ContactSearchItem | undefined> => {
        const contact = db.contacts.find((entry) => entry.name === value);

        if (!contact) {
            return Promise.resolve(undefined);
        }

        const item: ContactSearchItem = {
            contact,
            code: contact.name!,
            description: contact.name!,
        };

        return Promise.resolve(item);
    },
    getItemsAsync: (search: string): Promise<{ items: ContactSearchItem[]; total: number }> => {
        search = search.toLowerCase();
        const items = db.contacts
            .filter((contact) => contact.name!.toLowerCase().indexOf(search) >= 0)
            .map((contact) => ({
                contact,
                code: contact.name!,
                description: contact.name!,
            }));
        const response = {
            total: items.length,
            items,
        };

        return Promise.resolve(response);
    },
    getDisplayModeAsync: (): Promise<SearchFieldDisplayMode> => {
        return Promise.resolve(SearchFieldDisplayMode.DescOnly);
    },
    getSearchResultsDisplayModeAsync: (): Promise<SearchFieldDisplayMode> => {
        return Promise.resolve(SearchFieldDisplayMode.DescOnly);
    },
    getDisplayStringAsync: (value: string): Promise<string> => {
        return Promise.resolve(value);
    },
    getEmptyValueAsync: (): Promise<string> => {
        return Promise.resolve('');
    },
    validateAsync: (): Promise<boolean> => {
        return Promise.resolve(true);
    },
    getItemForValueAsync: function (value: string): Promise<ContactSearchItem | undefined> {
        const contact = db.contacts.find((c) => c.name === value);
        if (contact) {
            return Promise.resolve({ contact, code: contact.name!, description: contact.name! });
        }

        return Promise.resolve(undefined);
    },
};

let activeCompany: string | undefined = undefined;

export const dataProvider: AddressProvider = {
    companyProvider,
    contactProvider,
    countryProvider,
    setActiveCompany: (value: string) => {
        if (db.allowFilteringContactsByCompany) {
            activeCompany = value;
        }
    },
    setLatLngAsync: (address, latLng) => {
        latLng.value = { lat: -33.91657, lng: 151.19541 };
    },
};
