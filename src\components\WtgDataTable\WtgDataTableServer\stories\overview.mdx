import * as WtgDataTableServer from './WtgDataTableServer.stories.ts';

import { Canvas, ColorItem, ColorPalette, Description, Meta, Story, Title } from '@storybook/blocks';

<Meta title="Components/Data Table/Variants/Data Table Server" />

<div className="component-header">
    <h1>Data Table Server</h1>
</div>

## Overview

This section provides a collection of Data Table Server examples.

## Default

The data table server component is meant to be used for very large datasets, where it would be inefficient to load all the data into the client. It supports sorting, filtering, pagination, and selection like a standard data table, but all the logic must be handled externally by your backend or database.

<div class="docs-page">
    <Canvas of={WtgDataTableServer.Default} />
</div>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
