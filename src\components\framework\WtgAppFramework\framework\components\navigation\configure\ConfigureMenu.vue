<template>
    <ConfigureInlineMenu v-if="isTabletOrMobile" @item-click="emit('item-click')" />
    <WtgListItem
        v-else-if="showThemeConfig"
        :class="!railActive ? '' : 'd-flex justify-center'"
        role="menuitem"
        aria-haspopup="menu"
        :aria-label="application.captions.configure"
        leading-icon="s-icon-settings"
        :collapsed="railActive"
        @click="onThemeConfigClick"
    >
        {{ application.captions.configure }}
    </WtgListItem>
</template>

<script setup lang="ts">
import { WtgFrameworkUser } from '@components/framework/types';
import { WtgListItem } from '@components/WtgList';
import { useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { computed } from 'vue';
import ConfigureInlineMenu from './ConfigureInlineMenu.vue';

const application = useApplication();
const { isTabletOrMobile } = useFramework();

const emit = defineEmits<{
    'item-click': [];
}>();

const railActive = computed(() => {
    return isTabletOrMobile.value ? false : application.navDrawer.isRailActive;
});

const currentUser = computed((): WtgFrameworkUser => {
    return application.user ?? { name: '', image: {} };
});

const showThemeConfig = computed((): boolean | undefined => {
    return !!currentUser.value.onThemeConfiguration;
});

function onThemeConfigClick(): void {
    if (currentUser.value && currentUser.value.onThemeConfiguration) {
        currentUser.value.onThemeConfiguration();
    }
}
</script>
