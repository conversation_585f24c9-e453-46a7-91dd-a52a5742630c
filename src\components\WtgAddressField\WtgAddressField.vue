<template>
    <div>
        <WtgSearchField
            v-bind="baseInputProps"
            :item-provider="itemProvider"
            :search-provider="searchProvider"
            :model-value="
                addressOverrideValue || allowFreeTextAddressEntry ? internalValue?.company : internalGuidValue
            "
            :allow-free-text-entry="allowFreeTextAddressEntry"
            :placeholder="searchByAddress ? searchAddressPlaceholderCaption : placeholder"
            :promptable="computedPromptable"
            :override-search-result="searchByAddress"
            :custom-search-result="internalValue?.street"
            :search-item-comparator="searchItemComparator"
            @update:model-value="onUpdateModelValue"
        >
            <template #item="{ item }">
                <div v-if="item" class="wtg-address-search__item">
                    <div>
                        <strong>{{ item.address?.company }} </strong>
                        ({{ item.address?.companyCode }})
                    </div>
                    <div v-for="(line, index) in getAddressSubtitles(item.address)" :key="index">{{ line }}</div>
                </div>
            </template>
        </WtgSearchField>
        <WtgPanel v-if="showSelectedAddress" class="wtg-address-search__panel">
            <template v-if="internalValue && internalValue.company">
                <AddressTitle
                    v-if="hideAddressDisplayModeCode"
                    :title="internalValue?.companyCode ? `(${internalValue?.companyCode})` : ''"
                    :is-card-address-overriden="isAddressOverriden"
                    :card-address-override-description="addressOverrideDescription"
                    :editable="editable"
                    @edit="emit('edit')"
                />
                <AddressDisplayMode
                    :company-header="internalValue?.company"
                    :company-code="hideAddressDisplayModeCode ? undefined : `(${internalValue?.companyCode})`"
                    :address-lines="getAddressSubtitles(internalValue)"
                    :show-organization-name="showOrganizationName"
                    :show-additional-information="showAdditionalInformation"
                    :show-contact="showContact"
                    :contact="contactLines"
                    :additional-information="internalValue?.additionalInfo"
                />
            </template>
            <div v-else class="wtg-address-search__panel__no-item">
                <WtgLabel typography="text-md-default" class="d-flex">
                    {{ noAddressSelected }}
                </WtgLabel>
                <WtgHyperlink v-if="editable" style="cursor: pointer; text-decoration: underline" @click="emit('edit')">
                    {{ enterManually }}
                </WtgHyperlink>
            </div>
        </WtgPanel>
    </div>
</template>

<script setup lang="ts">
import WtgHyperlink from '@components/WtgHyperlink/WtgHyperlink.vue';
import AddressDisplayMode from '@components/WtgJobAddress/components/AddressDisplayMode/AddressDisplayMode.vue';
import AddressTitle from '@components/WtgJobAddress/components/AddressTitle/AddressTitle.vue';
import WtgLabel from '@components/WtgLabel';
import WtgPanel from '@components/WtgPanel';
import WtgSearchField from '@components/WtgSearchField';
import {
    SearchControlSearchProvider,
    SearchFieldItem,
    SearchFieldItemProvider,
} from '@components/WtgSearchField/types';
import { basePropsFromProps, makeInputProps, usePendingCommits } from '@composables/input';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import { makeValidationProps } from '@composables/validation';
import { computed, PropType, ref, watch } from 'vue';
import {
    AddressLookupSearchContent,
    AddressSearchItem,
    AddressVerificationStatus,
    ContactLookupSearchContent,
} from './types';

//
// Properties
//
const props = defineProps({
    addressOverrideDescription: {
        type: String,
        default: '',
    },
    addressOverrideValue: {
        type: Object as PropType<AddressLookupSearchContent>,
        default: undefined,
    },
    allowFreeTextAddressEntry: {
        type: Boolean,
        default: false,
    },
    contact: {
        type: Object as PropType<ContactLookupSearchContent>,
        default: undefined,
    },
    editable: {
        type: Boolean,
        default: false,
    },
    hideAddressDisplayModeCode: {
        type: Boolean,
        default: false,
    },
    isAddressOverriden: {
        type: Boolean,
        default: false,
    },
    itemProvider: {
        type: Object as PropType<SearchFieldItemProvider<AddressSearchItem>>,
        default: undefined,
    },
    modelValue: {
        type: String,
        default: undefined,
    },
    popupContactSelected: {
        type: Boolean,
        default: false,
    },
    promptable: {
        type: Boolean,
        default: undefined,
    },
    returnObject: {
        type: Boolean,
        default: false,
    },
    searchByAddress: {
        type: Boolean,
        default: false,
    },
    searchProvider: {
        type: Object as PropType<SearchControlSearchProvider>,
        default: undefined,
    },
    showAdditionalInformation: {
        type: Boolean,
        default: true,
    },
    showContact: {
        type: Boolean,
        default: true,
    },
    showOrganizationName: {
        type: Boolean,
        default: true,
    },
    showSelectedAddress: {
        type: Boolean,
        default: false,
    },
    verificationStatus: {
        type: Object as PropType<AddressVerificationStatus>,
        default: undefined,
    },

    ...makeInputProps(),
    ...makeValidationProps(),
    ...makeLayoutGridColumnProps(),

    /**
     * @deprecated Use modelValue instead
     */
    value: {
        type: String,
        default: undefined,
    },

    /**
     * @deprecated Use modelValue instead
     */
    showPrompter: {
        type: Boolean,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    change: [value?: string | AddressLookupSearchContent];
    'update:modelValue': [value?: string | AddressLookupSearchContent];
    edit: [];
}>();

//
// State
//
const internalValue = ref<AddressLookupSearchContent>();
const internalGuidValue = ref<string>();

//
// Composables
//
const { formatCaption } = useLocale();
const { trackPendingCommit } = usePendingCommits();

useLayoutGridColumn(props);

//
// Computed
//
const enterManually = computed(() => formatCaption('addressField.enterManually'));
const noAddressSelected = computed(() => formatCaption('addressField.noAddressSelected'));
const searchAddressPlaceholderCaption = computed(() => formatCaption('jobAddress.searchAddressPlaceholder'));

const baseInputProps = computed(() => {
    return {
        ...basePropsFromProps(props),
        modelValue: props.modelValue ?? props.value,
        value: undefined,
    };
});

const computedPromptable = computed(() => props.promptable ?? props.showPrompter ?? false);

const contactLines = computed(() => {
    if (!props.contact) {
        return '';
    }

    const { name, phone, mobile, email } = props.contact;
    const contactLines = [
        name,
        props.contact.name || props.popupContactSelected ? email : internalValue.value?.email,
        props.contact.name || props.popupContactSelected ? phone : internalValue.value?.phone,
        props.contact.name || props.popupContactSelected ? mobile : internalValue.value?.mobile,
    ];

    return contactLines.filter((x) => !isEmpty(x)).join(', ');
});

//
// Watchers
//
watch(
    () => props.modelValue ?? props.value ?? '',
    async (value) => {
        if (!props.allowFreeTextAddressEntry) {
            internalGuidValue.value = value;
        }
        if (!props.addressOverrideValue) {
            const item = await props.itemProvider?.getItemForValueAsync(value);
            internalValue.value = item?.address && { ...item.address };
        }
    },

    { immediate: true }
);

watch(
    () => props.addressOverrideValue,
    (value) => {
        internalValue.value = value && { ...value };
    },
    { immediate: true, deep: true }
);

//
// Event Handlers
//
function onUpdateModelValue(value?: string) {
    const promise = selectAddressItemAsync(value);
    trackPendingCommit(promise);
}

//
// Helpers
//
function getAddressSubtitles(address: AddressLookupSearchContent | undefined): string[] {
    if (!address) {
        return [];
    }

    const filterCallback = (x: string | undefined): x is string => !isEmpty(x);
    const addressLines = [
        [address.code, address.street, address.streetAlt].filter(filterCallback).join(', ').trim(),
        [address.city, address.postcode, address.state, address.countryCode, address.unloco]
            .filter(filterCallback)
            .join(', ')
            .trim(),
    ];

    return addressLines.filter(filterCallback);
}

async function selectAddressItemAsync(value?: string) {
    if (!props.allowFreeTextAddressEntry) {
        internalGuidValue.value = value;
    }

    if (!value && !props.searchByAddress) {
        internalValue.value = undefined;
    } else {
        const item = await props.itemProvider?.getItemForValueAsync(value ?? '');
        if (item) {
            internalValue.value = { ...item.address, guid: value };
        } else if (props.allowFreeTextAddressEntry) {
            internalValue.value = props.searchByAddress
                ? {
                      ...internalValue.value,
                      street: value,
                      isAddressOverriden: true,
                  }
                : {
                      ...internalValue.value,
                      guid: undefined,
                      companyGuid: undefined,
                      company: value,
                      isAddressOverriden: true,
                  };
        } else {
            internalValue.value = undefined;
        }
    }
    if (props.returnObject) {
        emit('change', internalValue.value);
        emit('update:modelValue', internalValue.value);
    } else {
        emit('change', value);
        emit('update:modelValue', value);
    }
}

function isEmpty(value: string | undefined): boolean {
    return !value || value.trim().length === 0;
}

function searchItemComparator(internalValue: string | string[] | undefined, searchFieldItem: SearchFieldItem): boolean {
    return props.allowFreeTextAddressEntry
        ? internalValue === searchFieldItem.address.company
        : internalValue === searchFieldItem.value;
}
</script>

<style lang="scss">
.wtg-address-search__panel {
    background: var(--s-neutral-bg-default);
    border-radius: var(--s-radius-s);
    box-shadow: none;
    color: var(--s-neutral-txt-default);
    margin-top: 12px;

    &__no-item {
        align-items: center;
        color: var(--s-neutral-txt-weak-default);
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 44px;
    }

    &__title {
        text-wrap: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &__subtitle {
        text-wrap: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.wtg-address-search__item {
    display: flex;
    flex-direction: column;
}
</style>
