import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { DOMWrapper, enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgDurationField from '..';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgDurationField', () => {
    test('its name is WtgDurationField', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgDurationField');
    });

    test('it passes its props to the base WtgInput', () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
                displayOnly: true,
                flat: true,
                id: '111',
                label: 'My Label',
                leadingIcon: 'icon1',
                loading: true,
                messages: 'message',
                placeholder: 'placeholder',
                readonly: true,
                required: true,
                restricted: true,
                sentiment: 'primary',
            },
        });
        const props = wrapper.findComponent({ name: 'WtgInput' }).props();
        expect(props.disabled).toBe(true);
        expect(props.displayOnly).toBe(true);
        expect(props.filled).toBe(false);
        expect(props.flat).toBe(true);
        expect(props.id).toBe('111');
        expect(props.label).toBe('My Label');
        expect(props.leadingIcon).toBe('icon1');
        expect(props.loading).toBe(true);
        expect(props.messages).toBe('message');
        expect(props.placeholder).toBe('placeholder');
        expect(props.readonly).toBe(true);
        expect(props.required).toBe(true);
        expect(props.restricted).toBe(true);
        expect(props.sentiment).toBe('primary');
    });

    test('it passes attributes to the base WtgInput element', () => {
        const wrapper = mountComponent({
            props: {
                dataTestId: 'my test id',
            },
        });
        expect(wrapper.attributes('datatestid')).toBe('my test id');
    });

    test("it has the autocomplete attribute set to 'off'", () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        expect(input.attributes('autocomplete')).toBe('off');
    });

    test('it updates the base WtgInput filled property when the input has content', async () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        input.setValue('20-05-2018');
        await wrapper.vm.$nextTick();

        const props = wrapper.findComponent({ name: 'WtgInput' }).props();
        expect(props.filled).toBe(true);
    });

    describe('when the input changes', () => {
        let input: DOMWrapper<HTMLInputElement>;
        let wrapper: VueWrapper<any>;

        beforeEach(async () => {
            wrapper = mountComponent();
            input = wrapper.find('input')!;
            input.setValue('3:');
            await input.trigger('change');
        });

        test('to a new value, the value is formatted and a change is emitted', async () => {
            expect(wrapper.vm.displayValue).toBe('3:00');
            input.setValue('4');
            await input.trigger('change');
            expect(wrapper.vm.displayValue).toBe('4:00');
        });

        test('to a value that formats to the same value, a change is not emitted', async () => {
            expect(wrapper.vm.displayValue).toBe('3:00');
            expect(wrapper.emitted().blur).toBeUndefined();
            input.setValue('3');
            await input.trigger('change');
            expect(wrapper.vm.displayValue).toBe('3:00');
            expect(wrapper.emitted().blur).toBeUndefined();
        });
    });

    test('it formats new value that is entered', async () => {
        const wrapper: VueWrapper<any> = mountComponent();
        const input = wrapper.find('input');
        input.setValue('3:');
        await input.trigger('change');
        expect(wrapper.vm.displayValue).toBe('3:00');

        input.setValue('50:30');
        await input.trigger('change');
        expect(wrapper.vm.displayValue).toBe('50:30');

        input.setValue('10:');
        await input.trigger('change');
        expect(wrapper.vm.displayValue).toBe('10:00');
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('when disabled, it sets the disabled attribute on the input field', async () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });
        expect(wrapper.find('input').attributes('disabled')).toBeDefined();

        await wrapper.setProps({ disabled: false });
        expect(wrapper.find('input').attributes('disabled')).toBeUndefined();
    });

    test('it passes the aria* properties to the input to ensure fields with a hidden label can still meet accessibility requirements', async () => {
        const wrapper = mountComponent({
            props: {
                ariaLabel: 'Aria label',
                ariaLabelledby: 'Aria labelledby',
            },
        });
        expect(wrapper.find('input').attributes('aria-label')).toBe('Aria label');
        expect(wrapper.find('input').attributes('aria-labelledby')).toBe('Aria labelledby');
    });

    test('when readonly, it sets the readonly attribute on the input field', async () => {
        const wrapper = mountComponent({
            props: {
                readonly: true,
            },
        });
        expect(wrapper.find('input').attributes('readonly')).toBeDefined();

        await wrapper.setProps({ readonly: false });
        expect(wrapper.find('input').attributes('readonly')).toBeUndefined();
    });

    test('it has a (deprecated) inputId property that gets applied if no id is specified to aid the GLOW VUE 3 migration', async () => {
        const wrapper = mountComponent();
        await wrapper.setProps({ inputId: 'id1' });
        expect(wrapper.find('input').attributes('id')).toBe('id1');

        await wrapper.setProps({ id: 'id2' });
        expect(wrapper.find('input').attributes('id')).toBe('id2');
    });

    function mountComponent({ props = {}, slots = {}, provide = {} } = {}) {
        return mount(WtgDurationField, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
