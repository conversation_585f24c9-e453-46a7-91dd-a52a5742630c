export const ButtonSandboxTemplate = `
<wtg-row>
    <wtg-col md="2">
        <wtg-layout-grid>
            <b>Button Variants</b>
            <wtg-button 
                v-bind="args"
                :data-testid="datatest"
                @click="action">
                    Outline
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="primary"
                :data-testid="datatest + '-fill'"
                @click="action">
                    Fill
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                :data-testid="datatest + '-ghost'"
                @click="action">
                    Ghost
            </wtg-button>
        </wtg-layout-grid>
    </wtg-col>
    <wtg-col md="2">
        <wtg-layout-grid>
            <b>Button Sentiments</b>
            <wtg-button 
                v-bind="args"
                @click="action">
                    Outline Default
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="primary"
                @click="action">
                    Outline Primary
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="success"
                @click="action">
                    Outline Success
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="critical"
                @click="action">
                    Outline Critical
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                @click="action">
                    Fill Default
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="primary"
                data-testid="testSentimentsButton-primary"
                @click="action">
                    Fill Primary
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="success"
                @click="action">
                    Fill Success
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="critical"
                @click="action">
                    Fill Critical
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                @click="action">
                    Ghost Default
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="primary"
                @click="action">
                    Ghost Primary
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="success"
                @click="action">
                    Ghost Success
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="critical"
                @click="action">
                    Ghost Critical
            </wtg-button>
        </wtg-layout-grid>
    </wtg-col>
    <wtg-col md="2">
        <wtg-layout-grid>
            <b>Button with icons</b>
            <wtg-button 
                v-bind="args"
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                    @click="action">
                {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                :leading-icon="args.icon" 
                    @click="action">
                {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                :trailing-icon="args.icon" 
                    @click="action">
                {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="primary"
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="primary"
                variant="fill"
                :leading-icon="args.icon" 
                    @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="primary"
                variant="fill"
                :trailing-icon="args.icon" 
                    @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                    @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                :leading-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                :trailing-icon="args.icon" 
                    @click="action">
                    {{args.label}}
            </wtg-button>
        </wtg-layout-grid>
    </wtg-col>
        <wtg-col md="2">
        <wtg-layout-grid>
            <b>Active buttons</b>
            <wtg-button 
                v-bind="args"
                active
                @click="action">
                    Outline Default
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="primary"
                active
                @click="action">
                    Outline Primary
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="success"
                active
                @click="action">
                    Outline Success
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="critical"
                active
                @click="action">
                    Outline Critical
            </wtg-button>
            <wtg-button 
                v-bind="args"
                active
                variant="fill"
                @click="action">
                    Fill Default
            </wtg-button>
            <wtg-button 
                v-bind="args"
                active
                variant="fill"
                sentiment="primary"
                data-testid="testSentimentsButton-primary"
                @click="action">
                    Fill Primary
            </wtg-button>
            <wtg-button 
                v-bind="args"
                active
                variant="fill"
                sentiment="success"
                @click="action">
                    Fill Success
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="critical"
                active
                @click="action">
                    Fill Critical
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                active
                @click="action">
                    Ghost Default
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="primary"
                active
                @click="action">
                    Ghost Primary
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="success"
                active
                @click="action">
                    Ghost Success
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="critical"
                active
                @click="action">
                    Ghost Critical
            </wtg-button>
        </wtg-layout-grid>
    </wtg-col>
    <wtg-col md="2">
        <wtg-layout-grid>
            <b>Button with loading</b>
            <wtg-button 
                v-bind="args"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                    @click="action">
                {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="primary"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="success"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                    @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                sentiment="critical"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="primary"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="success"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="fill"
                sentiment="critical"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="primary"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="success"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
            <wtg-button 
                v-bind="args"
                variant="ghost"
                sentiment="critical"
                loading
                :leading-icon="args.icon" 
                :trailing-icon="args.icon" 
                @click="action">
                    {{args.label}}
            </wtg-button>
        </wtg-layout-grid>
    </wtg-col>
    <wtg-col md="3">
        <wtg-layout-grid>
            <b>Colors</b>
            <wtg-button v-bind="args" color="green-darken-3">Default and green</wtg-button>
            <wtg-button v-bind="args" color="rgba(55,30,225,.8)">Default and rgba(55,30,225,.8)</wtg-button>
            <wtg-button v-bind="args" color="var(--s-error-txt-default)">Default and var(--s-error-txt-default)</wtg-button>
            <wtg-button v-bind="args" variant="ghost" color="green-darken-3">Ghost and green</wtg-button>
            <wtg-button v-bind="args" variant="ghost" color="rgba(55,30,225,.8)">Ghost and rgba(55,30,225,.8)</wtg-button>
            <wtg-button v-bind="args" variant="ghost" color="var(--s-error-txt-default)">Ghost and var(--s-error-txt-default)</wtg-button>
            <wtg-button v-bind="args" variant="fill" color="green-darken-3">Fill and green</wtg-button>
            <wtg-button v-bind="args" variant="fill" color="rgb(55,30,225)">Fill and rgb(55,30,225)</wtg-button>
            <wtg-button v-bind="args" variant="fill" color="rgb(255,200,0)">Fill and rgb(255,255,0)</wtg-button>
            <wtg-button v-bind="args" variant="fill" color="var(--s-error-txt-default)">Fill and var(--s-error-txt-default)</wtg-button>
        </wtg-layout-grid>
    </wtg-col>
</wtg-row>
<wtg-panel class="mt-6" max-width="800" layout="flex" flex-justify="justify-center" caption="This button should not display the <a> underline">
    <wtg-button href="https://wisetechglobal.com/">Button</wtg-button>
</wtg-panel>
`;
