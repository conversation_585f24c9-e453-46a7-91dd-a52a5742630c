<template>
    <a :class="computedClasses" :href="href" :target="target" @click="onClick">
        <slot />
    </a>
</template>

<script setup lang="ts">
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { computed } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * The URL that the hyperlink points to.
     * Example: 'https://example.com'.
     */
    href: {
        type: String,
        default: undefined,
    },

    /**
     * Specifies where to open the linked document.
     * Example: '_blank' to open in a new tab, '_self' to open in the same tab.
     */
    target: {
        type: String,
        default: '_blank',
    },

    ...makeLayoutGridColumnProps(),
});

//
// Emits
//
const emit = defineEmits<{
    click: [e: MouseEvent];
}>();

//
// Composables
//
useLayoutGridColumn(props);

//
// Computed
//
const computedClasses = computed(() => ['wtg-hyperlink']);

//
// Event Handlers
//
function onClick(e: MouseEvent) {
    emit('click', e);
}
</script>

<style lang="scss">
.wtg-hyperlink {
    color: var(--s-primary-txt-default);
    font: var(--s-text-md-default-link);

    &:focus-visible {
        border-radius: 2px;
        outline: 2px solid var(--s-primary-border-default);
    }

    &:active {
        color: var(--s-primary-txt-hover);
        text-decoration: none;
    }

    &:hover {
        color: var(--s-primary-txt-hover);
        text-decoration: underline;
    }
}
</style>
