import WtgAboutDialog from '@components/framework/WtgAboutDialog/WtgAboutDialog.vue';
import WtgButton from '@components/WtgButton';
import WtgDialog from '@components/WtgDialog';
import WtgIconButton from '@components/WtgIconButton';
import { DOMWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../../WtgUi';

enableAutoUnmount(afterEach);
const wtgUi = new WtgUi();

describe('WtgAboutDialog', () => {
    let el: HTMLElement;

    beforeEach(() => {
        wtgUi.breakpoint.smAndUp = true;
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
    });

    describe('when the value property is set to true', () => {
        test('it brings up a dialog', () => {
            const wrapper = mountComponent({ propsData: { modelValue: true } });
            const dialog = wrapper.findComponent(WtgDialog);
            expect(dialog.props('modelValue')).toBe(true);
        });

        test('it displays the dialog title', () => {
            const wrapper = mountComponent({
                propsData: { modelValue: true, dialogTitle: 'About', title: 'Staff Portals HomePage' },
            });
            const dialogTitle = wrapper.findAllComponents({ name: 'WtgLabel' }).at(0);
            expect(dialogTitle!.text()).toBe('About Staff Portals HomePage');
        });

        test('it displays the dialog content', () => {
            const wrapper = mountComponent({
                propsData: { modelValue: true, title: 'Staff Portals HomePage' },
            });
            const content = wrapper.findAllComponents({ name: 'WtgPanel' }).at(0);
            expect(content?.exists()).toBe(true);
        });

        test('it should emit update:modelValue when "x" button is clicked', async () => {
            const wrapper = mountComponent({ propsData: { modelValue: true } });
            const cancel = wrapper.findAllComponents(WtgIconButton).at(0);
            await cancel?.trigger('click');
            await nextTick();

            expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        });

        test('it should emit update:modelValue when close is clicked', async () => {
            const wrapper = mountComponent({ propsData: { modelValue: true } });
            const cancel = wrapper.findAllComponents(WtgButton).at(0);
            await cancel?.trigger('click');
            await nextTick();

            expect(wrapper.emitted()['update:modelValue']).toBeTruthy();
        });

        test('it renders the default slot', () => {
            mountComponent({
                propsData: { modelValue: true },

                slots: {
                    default: '<div class="test">Some Text</div>',
                },
            });

            const x = new DOMWrapper(document.body).find('.test');
            expect(x.text()).toBe('Some Text');
        });
    });

    function mountComponent({ propsData = {}, slots = {} }) {
        return mount(WtgAboutDialog, {
            propsData: { ...propsData },
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
