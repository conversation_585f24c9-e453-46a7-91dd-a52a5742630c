import { <PERSON>a, StoryObj } from '@storybook/vue3';
import { WtgBubbleChart } from '../..';

type Story = StoryObj<typeof WtgBubbleChart>;
const meta: Meta<typeof WtgBubbleChart> = {
    title: 'Data viz/Bubble Chart',
    component: WtgBubbleChart,
    parameters: {
        docs: {
            description: {
                component:
                    'Bubble charts are often used to present financial data. Different bubble sizes are useful to visually emphasize specific values. Scatter charts use sets of x values and y values, but bubble charts use sets of x values, y values, and z values.',
            },
        },
    },
    render: (args) => ({
        components: { WtgBubbleChart },
        setup: () => ({ args }),
        template: `<wtg-bubble-chart v-bind="args"/>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        data: {
            datasets: [
                {
                    label: 'Europe',
                    data: [
                        {
                            x: 20,
                            y: 30,
                            r: 15,
                        },
                        {
                            x: 25,
                            y: 15,
                            r: 50,
                        },
                        {
                            x: 40,
                            y: 20,
                            r: 10,
                        },
                        {
                            x: 43,
                            y: 13,
                            r: 16,
                        },
                        {
                            x: 10,
                            y: 10,
                            r: 30,
                        },
                    ],
                    backgroundColor: '#4CAF5080',
                },
                {
                    label: 'Asia',
                    data: [
                        {
                            x: 25,
                            y: 30,
                            r: 15,
                        },
                        {
                            x: 21,
                            y: 25,
                            r: 30,
                        },
                        {
                            x: 11,
                            y: 45,
                            r: 20,
                        },
                    ],
                    backgroundColor: '#FF980080',
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
            },
        },
        loading: false,
    },
};
