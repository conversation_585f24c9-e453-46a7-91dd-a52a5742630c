import {
    WtgFrameworkTask,
    WtgFrameworkTaskWorkflowAction,
    WtgFrameworkTaskWorkflowActionMenuItem,
} from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import WorkflowsAction from '../WorkflowsAction.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('workflows-action', () => {
    let task: WtgFrameworkTask;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        task = new WtgFrameworkTask();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is WorkflowsAction', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WorkflowsAction');
    });

    describe('when given showWorkflowActions', () => {
        beforeEach(() => {
            const menuItems: WtgFrameworkTaskWorkflowActionMenuItem[] = [
                { caption: 'Option 1', onInvoke: jest.fn() },
                { caption: 'Option 2', onInvoke: jest.fn() },
                { caption: 'Option 3', onInvoke: jest.fn() },
            ];

            task.showWorkflowActions = {
                visible: false,
                caption: 'Workflows',
                menuItems,
            };
        });

        test('its does not render a button when the action visible is false', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            expect(buttons.length).toBe(0);
        });

        test('its does render a button when the action visible is true', () => {
            task.showWorkflowActions.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            expect(buttons.length).toBe(1);
        });

        test('it renders the button with aria-haspopup="menu" attributes', () => {
            task.showWorkflowActions.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.attributes('aria-haspopup')).toBe('menu');
        });

        test('it renders the button with aria-expanded="true" attribute when workflows menu is open', async () => {
            task.showWorkflowActions.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            expect(button.attributes('aria-expanded')).toBe('true');
        });

        describe('on a large screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = false;
                task.showWorkflowActions.visible = true;
            });

            test('it display as a ghost button', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().variant).toBe('ghost');
                expect(button.text()).toBe('Workflows');
            });

            test('it displays the button with a leading icon', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().leadingIcon).toBe('s-icon-workflow');
            });
        });

        describe('on a small screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = true;
                task.showWorkflowActions.visible = true;
            });

            test('it display as a icon button with a tooltip', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.props().icon).toBe('s-icon-workflow');
                expect(button.props().tooltip).toBe('Workflows');
            });
        });

        test('when clicked a menu is rendered', async () => {
            task.showWorkflowActions.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            await buttons.at(0)!.trigger('click');

            const menus = wrapper.findAllComponents({ name: 'WtgList' });
            expect(menus.at(0)!.exists()).toBe(true);
        });

        test('when open the menu display the menu items', async () => {
            task.showWorkflowActions.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            await buttons.at(0)!.trigger('click');

            const menus = wrapper.findAllComponents({ name: 'WtgList' });
            const listItems = menus.at(0)!.findAllComponents({ name: 'WtgListItem' });
            expect(listItems.length).toBe(3);
            expect(listItems.at(0)!.text()).toBe('Option 1');
            expect(listItems.at(1)!.text()).toBe('Option 2');
            expect(listItems.at(2)!.text()).toBe('Option 3');
        });

        test('when clicked the menu item calls the clickHandler for the menuItem', async () => {
            task.showWorkflowActions.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            await buttons.at(0)!.trigger('click');

            const menus = wrapper.findAllComponents({ name: 'WtgList' });
            const listItems = menus.at(0)!.findAllComponents({ name: 'WtgListItem' });

            await listItems.at(1)!.trigger('click');
            expect(task.showWorkflowActions.menuItems[1].onInvoke).toBeCalledTimes(1);
        });
    });
    describe('Property tests', () => {
        test('when props is passed', () => {
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task } });
            expect(wrapper.vm.action).toStrictEqual(task.showWorkflowActions);
        });

        test('when props is not passed and application.currentTask is not there', () => {
            const defaultValue: WtgFrameworkTaskWorkflowAction = {
                visible: false,
                caption: 'Workflows',
                menuItems: [],
            };
            const wrapper: VueWrapper<any> = mountComponent();
            expect(wrapper.vm.action.caption).toStrictEqual(defaultValue.caption);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WorkflowsAction, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
