import WtgLayoutGrid from '@components/WtgLayoutGrid';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { VContainer } from 'vuetify/components/VGrid';
import { WtgContainer } from '../';
import WtgUi from '../../../WtgUi';
import WtgDialog from '@components/WtgDialog';
import { nextTick } from 'vue';

enableAutoUnmount(afterEach);

window.innerWidth = 2000;
const wtgUi = new WtgUi();

describe('WtgContainer', () => {
    test('it renders a VContainer component', () => {
        const wrapper = mountComponent();
        const container = wrapper.findComponent(VContainer);
        expect(container.exists()).toBe(true);
    });

    test('it passes the default slot to the container', () => {
        const wrapper = mountComponent();
        expect(wrapper.text()).toBe('Some Content');
    });

    test('it defaults the VContainer component to fluid', () => {
        const wrapper = mountComponent();
        const container = wrapper.findComponent(VContainer);
        expect(container.props('fluid')).toBe(true);
    });

    test('it passes its properties to the VContainer component', () => {
        const wrapper = mountComponent({
            props: {
                fluid: false,
            },
        });
        const container = wrapper.findComponent(VContainer);
        expect(container.props('fluid')).toBe(false);
    });

    test('it passes measurable styles to div element', () => {
        const [height, minHeight, minWidth, maxHeight, maxWidth, width] = [
            '100px',
            '50px',
            '25px',
            '200px',
            '250px',
            '150px',
        ];
        const wrapper = mountComponent({
            props: {
                height,
                minHeight,
                minWidth,
                maxHeight,
                maxWidth,
                width,
            },
        });
        expect((wrapper.element as HTMLElement).style.height).toBe(height);
        expect((wrapper.element as HTMLElement).style.minHeight).toBe(minHeight);
        expect((wrapper.element as HTMLElement).style.minWidth).toBe(minWidth);
        expect((wrapper.element as HTMLElement).style.maxHeight).toBe(maxHeight);
        expect((wrapper.element as HTMLElement).style.maxWidth).toBe(maxWidth);
        expect((wrapper.element as HTMLElement).style.width).toBe(width);
    });

    test('for fill layout, it sizes the container to the max visible viewport and adds the fill, padding and overflow classes (necessary for a nested full page data table to show its vertical scroll correctly)', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                layout: 'fill',
            },
        });
        expect(wrapper.classes()).toContain('wtg-container-fill-height');
        expect(wrapper.classes()).toContain('overflow-y-auto');
        expect(wrapper.classes()).toContain('overflow-x-hidden');
        const computedStyle = window.getComputedStyle(wrapper.vm.$el);
        expect(computedStyle.display).toBe('block');
        expect(computedStyle.flexDirection).toBe('');
        const containerStyle = wrapper.vm.computedStyles;
        expect(containerStyle.height).toBe(
            'max(100vh - (var(--v-layout-top, 0px) + var(--v-layout-bottom, 0px) + 0px + 0px), 600px)'
        );
    });

    test('for grid-fill layout, it sizes the container to the max visible viewport and adds the fill, padding and overflow classes (necessary for a nested full page data table to show its vertical scroll correctly)', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                layout: 'grid-fill',
            },
        });
        expect(wrapper.classes()).toContain('wtg-container-fill-height');
        expect(wrapper.classes()).toContain('overflow-y-auto');
        expect(wrapper.classes()).toContain('overflow-x-hidden');
        const computedStyle = window.getComputedStyle(wrapper.vm.$el);
        expect(computedStyle.display).toBe('block');
        expect(computedStyle.flexDirection).toBe('');
        const containerStyle = wrapper.vm.computedStyles;
        expect(containerStyle.height).toBe(
            'max(100vh - (var(--v-layout-top, 0px) + var(--v-layout-bottom, 0px) + 0px + 0px), 600px)'
        );
    });

    test('it has layout capability mixed in allowing you to use flex', () => {
        const wrapper = mountComponent({
            props: { layout: 'flex' },
        });
        expect(wrapper.classes()).toContain('d-flex');
    });

    test('when the layout property is set to GRID, it renders a layout grid', () => {
        const wrapper = mountComponent({
            props: { layout: 'grid' },
        });
        expect(wrapper.findComponent(WtgLayoutGrid).exists()).toBe(true);
        expect(wrapper.findComponent(WtgLayoutGrid).props('fillAvailable')).toBe(false);
    });

    test('when the layout property is set to GRID-FILL, it renders a layout grid', () => {
        const wrapper = mountComponent({
            props: { layout: 'grid-fill' },
        });
        expect(wrapper.findComponent(WtgLayoutGrid).exists()).toBe(true);
        expect(wrapper.findComponent(WtgLayoutGrid).props('fillAvailable')).toBe(true);
    });

    test('it passes the default slot to the layout grid', () => {
        const wrapper = mountComponent({
            props: { layout: 'grid' },
        });
        expect(wrapper.text()).toBe('Some Content');
    });

    test('when the layout property is set to GRID, it applies the no-gutters property', () => {
        const wrapper = mountComponent({
            props: {
                layout: 'grid',
                noGutters: true,
            },
        });
        expect(wrapper.findComponent(WtgLayoutGrid).props('noGutters')).toBe(true);
    });

    test('it has responsive fit-to-height support for when the container is embedded in a WtgDialog', async () => {
        const component = {
            components: { WtgDialog, WtgContainer },
            template:
                '<wtg-dialog v-model="open" :fullscreen="fullscreen" ><wtg-container fit-to-height>CONTAINER CONTENT</wtg-container></wtg-dialog>',
            data: () => {
                return {
                    open: true,
                    fullscreen: false,
                };
            },
        };
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });
        await nextTick();

        const dialog = wrapper.findComponent(WtgDialog);
        const container = dialog.findComponent(WtgContainer);
        const containerStyle = (container.element as HTMLElement).style;
        expect(containerStyle.height).toBe('75vh');
    });

    describe('when color is set', () => {
        test('it sets the element style from valid css style prop colors', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('bg-red');
        });

        test('it sets the element style from vars', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'var(--s-error-txt-default)',
                },
            });
            expect((wrapper.vm as any).computedStyles.background).toBe('var(--s-error-txt-default)');
            expect((wrapper.vm as any).computedStyles.color).toBe('var(--s-primary-txt-inv-default)');
        });
    });

    describe('mobile behaviour', () => {
        test('it defaults to mobile display at 600 when the mobile prop is not set', async () => {
            let wrapper = mountComponent();
            expect(wrapper.classes()).not.toContain('wtg-container--mobile');
            const innerWidth = window.innerWidth;
            window.innerWidth = 590;
            wrapper = mountComponent({}, new WtgUi());
            expect(wrapper.classes()).toContain('wtg-container--mobile');
            window.innerWidth = innerWidth;
        });
    });

    describe('align prop', () => {
        test('it applies ml-0 class when align is left', () => {
            const wrapper = mountComponent({
                props: { align: 'left' },
            });
            expect(wrapper.classes()).toContain('ml-0');
            expect(wrapper.classes()).not.toContain('mr-0');
        });

        test('it applies mr-0 class when align is right', () => {
            const wrapper = mountComponent({
                props: { align: 'right' },
            });
            expect(wrapper.classes()).toContain('mr-0');
            expect(wrapper.classes()).not.toContain('ml-0');
        });

        test('it applies neither ml-0 nor mr-0 when align is center (default)', () => {
            const wrapper = mountComponent();
            expect(wrapper.classes()).not.toContain('ml-0');
            expect(wrapper.classes()).not.toContain('mr-0');
        });
    });

    function mountComponent({ props = {} } = {}, localWtgUi?: WtgUi) {
        return mount(WtgContainer, {
            props,
            slots: {
                default: '<span>Some Content</span>',
            },
            global: {
                plugins: [localWtgUi ?? wtgUi],
            },
        });
    }
});
