<template>
    <VFooter :app="app" :order="order">
        <slot />
    </VFooter>
</template>

<script setup lang="ts">
import { VFooter } from 'vuetify/components/VFooter';

//
// Properties
//
defineProps({
    /**
     * Determines the position of the footer. If true, the footer would be given a fixed
     * position at the bottom of the viewport. If false, the footer is set to the bottom of the page.
     */
    app: {
        type: Boolean,
        default: false,
    },
    /**
     * Adjust the order of the footer in relation to its registration order.
     */
    order: {
        type: [String, Number],
        default: 0,
    },
});
</script>
