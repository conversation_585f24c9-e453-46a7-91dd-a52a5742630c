<template>
    <div class="wtg-date-time-picker-container">
        <WtgDatePicker
            class="wtg-date-time-picker-date"
            :model-value="internalValue.date"
            :hide-today-button="true"
            @update:model-value="onUpdateDate"
        />
        <WtgTimePicker
            class="wtg-date-time-picker-time"
            :model-value="internalValue.time"
            :use-seconds="useSeconds"
            :hide-now-button="true"
            @update:model-value="onUpdateTime"
        />
        <WtgSelectField
            v-if="showTimeZone"
            :items="getTimezonesWithOffsets"
            item-text="formatted"
            item-value="offset"
            label="Timezone"
            :model-value="internalValue.timeZone"
            @update:model-value="onUpdateTimeZone"
        />
        <WtgButton class="wtg-date-time-picker-today" @click="() => onClickToday()">{{ todayCaption }}</WtgButton>
    </div>
</template>

<script setup lang="ts">
import { WtgButton } from '@components/WtgButton';
import WtgDatePicker from '@components/WtgDatePicker';
import { todayWithTz } from '@components/WtgDateTimeField/utils';
import { WtgSelectField } from '@components/WtgSelectField';
import WtgTimePicker from '@components/WtgTimePicker';
import { useLocale } from '@composables/locale';
import { computed, onMounted, PropType, ref, watch } from 'vue';
import { extractDateTimeOffsetFromIsoStr, supportedUniqueTimeZoneList } from '../../language/formatters/dayjs';

const props = defineProps({
    /**
     * If true, the time picker will include seconds in the selection.
     */
    useSeconds: {
        type: Boolean,
        default: false,
    },

    /**
     * The current value of the date-time picker, used for two-way binding.
     * Should be a string in the format "YYYY-MM-DD HH:mm:ss" or similar.
     */
    modelValue: {
        type: String,
        default: '',
    },

    /**
     * A custom function to determine the "Today" date.
     * Should return a string in the format supported by the date picker.
     */
    todayDateFn: {
        type: Function as PropType<(showTimeZone?: boolean) => string>,
        default: undefined,
    },
    showTimeZone: {
        type: Boolean,
        default: false,
    },
});

const { formatCaption } = useLocale();

const todayCaption = computed(() => formatCaption('dateField.today'));

const emit = defineEmits<{
    'update:modelValue': [value: string];
}>();

const internalValue = ref({
    date: '',
    time: '',
    timeZone: '',
});

const updateValue = () => {
    if (props.showTimeZone) {
        const { date, time, timeZone } = internalValue.value;
        emit('update:modelValue', [date, time, timeZone].filter(Boolean).join(' '));
    } else {
        const { date, time } = internalValue.value;
        emit('update:modelValue', [date, time].filter(Boolean).join(' '));
    }
};

const onUpdateDate = (newDate: string) => {
    internalValue.value.date = newDate;
    updateValue();
};

const onUpdateTime = (newTime: string) => {
    internalValue.value.time = newTime;
    updateValue();
};

const onUpdateTimeZone = (newTimeZoneOffset: string) => {
    internalValue.value.timeZone = newTimeZoneOffset;
    updateValue();
};

const onClickToday = () => {
    const customTodayDate = props.todayDateFn?.(props.showTimeZone);
    const { date, time, offset } = customTodayDate
        ? extractDateTimeOffsetFromIsoStr(customTodayDate)
        : todayWithTz(new Date(), props.useSeconds);

    internalValue.value.date = date;
    internalValue.value.time = time;
    internalValue.value.timeZone = offset;
    updateValue();
};

const getTimezonesWithOffsets = computed(() => {
    return supportedUniqueTimeZoneList();
});

watch(
    () => props.modelValue,
    () => {
        const [formatedDate, formatedTime, timeZoneOffSet] = props.modelValue.split(' ');
        internalValue.value = {
            date: formatedDate,
            time: formatedTime,
            timeZone: timeZoneOffSet,
        };
    },
    { immediate: true }
);

onMounted(() => {
    if (!props.modelValue) {
        const today = props.todayDateFn?.(props.showTimeZone);
        const { date, time, offset } = today
            ? extractDateTimeOffsetFromIsoStr(today)
            : todayWithTz(new Date(), props.useSeconds);

        internalValue.value = {
            date: date,
            time: time,
            timeZone: offset,
        };
    }
});
</script>

<style lang="scss">
.wtg-date-time-picker-date {
    grid-area: date;
}
.wtg-date-time-picker-time {
    grid-area: time;
}
.wtg-date-time-picker-timeZone {
    grid-area: timeZone;
}
.wtg-date-time-picker-today {
    grid-area: today;
}
.wtg-date-time-picker-container {
    display: grid;
    justify-items: center;
    grid-template-areas:
        'date time'
        'timeZone today';
    gap: 4px 8px;
}
</style>
