import WtgAddressField from '@components/WtgAddressField/WtgAddressField.vue';
import jobAddressStyles from '@components/WtgGoogleMap/utils/jobAddressStyles';
import WtgSearchField from '@components/WtgSearchField';
import WtgTextArea from '@components/WtgTextArea/WtgTextArea.vue';
import WtgTextField from '@components/WtgTextField';
import { useWtgUi } from '@composables/global';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import flushPromises from 'flush-promises';
import { dataProvider, db } from '../../../__tests__/WtgJobAddressDataProvider';
import AddressEditor from '../AddressEditor.vue';

enableAutoUnmount(afterEach);

const wtgUi = useWtgUi();

describe('AddressEditor', () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(async () => {
        jest.runAllTimers();
        await flushPromises();
        jest.clearAllMocks();
    });
    test('it renders the component', () => {
        const wrapper = mountComponent();
        expect(wrapper.isVisible()).toBe(true);
    });

    describe('isReadlAddressOnlyMode=true', () => {
        test('it displays the provided address', async () => {
            const wrapper = mountComponent({
                propsData: {
                    isRealAddressOnlyMode: true,
                    searchByAddress: true,
                    popupAddress: {
                        ...db.addresses[0],
                    },
                    popupContact: {
                        ...db.contacts[0],
                    },
                },
            });
            await flushPromises();

            const searchField = wrapper.findAllComponents(WtgSearchField);
            expect(searchField.at(0)!.props('customSearchResult')).toBe(db.addresses[0].street);
            expect(searchField.at(1)!.props('modelValue')).toBe(db.addresses[0].countryCode);

            const address = wrapper.findComponent(WtgAddressField);
            expect(address.props('modelValue')).toEqual(db.addresses[0].guid);

            const inputs = wrapper.findAllComponents(WtgTextField);
            expect(inputs.length).toBe(8);
            expect(inputs.at(1)!.props('modelValue')).toBe(db.addresses[0].streetAlt);
            expect(inputs.at(2)!.props('modelValue')).toBe(db.addresses[0].city);
            expect(inputs.at(3)!.props('modelValue')).toBe(db.addresses[0].postcode);
            expect(inputs.at(4)!.props('modelValue')).toBe(db.addresses[0].state);
            expect(inputs.at(5)!.props('modelValue')).toBe(db.contacts[0].email);
            expect(inputs.at(6)!.props('modelValue')).toBe(db.contacts[0].phone);
            expect(inputs.at(7)!.props('modelValue')).toBe(db.contacts[0].mobile);
        });

        test('WtgAddressField will emit address-changed and contact-changed when updateModelValue is triggered', () => {
            const wrapper = mountComponent({
                propsData: {
                    isRealAddressOnlyMode: true,
                },
            });
            const addressField = wrapper.findComponent(WtgAddressField);
            addressField.vm.$emit('update:modelValue');
            expect(wrapper.emitted('address-changed')).toBeTruthy();
            expect(wrapper.emitted('contact-changed')).toBeTruthy();
        });
    });

    describe('isRealAddressOnlyMode=false', () => {
        test('it displays the provided address when isRealAddressOnlyMode=false', () => {
            const wrapper = mountComponent({
                propsData: {
                    isRealAddressOnlyMode: false,
                    searchByAddress: true,
                    popupAddress: {
                        ...db.addresses[0],
                    },
                    popupContact: {
                        ...db.contacts[0],
                    },
                    popupContactSelected: true,
                },
            });
            const searchField = wrapper.findAllComponents(WtgSearchField);
            expect(searchField.at(0)!.props('customSearchResult')).toBe(db.addresses[0].street);
            expect(searchField.at(1)!.props('modelValue')).toBe(db.addresses[0].countryCode);

            const address = wrapper.findComponent(WtgAddressField);
            expect(address.props('addressOverrideValue')).toEqual({
                ...db.addresses[0],
            });

            const inputs = wrapper.findAllComponents(WtgTextField);
            expect(inputs.length).toBe(8);
            expect(inputs.at(1)!.props('modelValue')).toBe(db.addresses[0].streetAlt);
            expect(inputs.at(2)!.props('modelValue')).toBe(db.addresses[0].city);
            expect(inputs.at(3)!.props('modelValue')).toBe(db.addresses[0].postcode);
            expect(inputs.at(4)!.props('modelValue')).toBe(db.addresses[0].state);
            expect(inputs.at(5)!.props('modelValue')).toBe(db.contacts[0].email);
            expect(inputs.at(6)!.props('modelValue')).toBe(db.contacts[0].phone);
            expect(inputs.at(7)!.props('modelValue')).toBe(db.contacts[0].mobile);
        });

        test('AddressField to have allow-free-text-address-entry to true', () => {
            const wrapper = mountComponent({ propsData: { isRealAddressOnlyMode: false } });
            const addressField = wrapper.findComponent(WtgAddressField);
            expect(addressField.props('allowFreeTextAddressEntry')).toBe(true);
        });

        test('validation states is set correctly', () => {
            const wrapper = mountComponent({
                propsData: {
                    validationStates: [
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_CompanyName',
                            messages: ['Company is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_Address1',
                            messages: ['Address 1 is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_Address2',
                            messages: ['Address 2 is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_City',
                            messages: ['City is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_State',
                            messages: ['State is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_Postcode',
                            messages: ['Postcode is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_RN_NKCountryCode',
                            messages: ['Country is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_AdditionalAddressInformation',
                            messages: ['Additional info is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_Contact',
                            messages: ['Contact is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_Email',
                            messages: ['Email is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_Mobile',
                            messages: ['Mobile is required'],
                            error: true,
                            warning: false,
                        },
                        {
                            alertLevel: 4,
                            targetKey: undefined,
                            targetProperty: 'E2_Phone',
                            messages: ['Phone is required'],
                            error: true,
                            warning: false,
                        },
                    ],
                    validationPropertyMapping: {
                        company: 'E2_CompanyName',
                        street: 'E2_Address1',
                        streetAlt: 'E2_Address2',
                        city: 'E2_City',
                        state: 'E2_State',
                        postcode: 'E2_Postcode',
                        countryCode: 'E2_RN_NKCountryCode',
                        name: 'E2_Contact',
                        email: 'E2_Email',
                        mobile: 'E2_Mobile',
                        phone: 'E2_Phone',
                        additionalInfo: 'E2_AdditionalAddressInformation',
                    },
                },
            });

            const addressField = wrapper.findComponent(WtgAddressField);
            const companyField = wrapper.findAllComponents(WtgTextField).at(0);
            const streetAltField = wrapper.findAllComponents(WtgTextField).at(1);
            const cityField = wrapper.findAllComponents(WtgTextField).at(2);
            const postcodeField = wrapper.findAllComponents(WtgTextField).at(3);
            const stateField = wrapper.findAllComponents(WtgTextField).at(4);
            const countryCodeField = wrapper.findAllComponents(WtgSearchField).at(1);
            const additionalInfoField = wrapper.findComponent(WtgTextArea);
            const nameField = wrapper.findAllComponents(WtgSearchField).at(2);
            const emailField = wrapper.findAllComponents(WtgTextField).at(5);
            const phoneField = wrapper.findAllComponents(WtgTextField).at(6);
            const mobileField = wrapper.findAllComponents(WtgTextField).at(7);

            expect(addressField.props('sentiment')).toBe('critical');
            expect(addressField.props('messages')).toStrictEqual(['Address 1 is required']);
            expect(companyField?.props('sentiment')).toBe('critical');
            expect(companyField?.props('messages')).toStrictEqual(['Company is required']);
            expect(streetAltField?.props('sentiment')).toBe('critical');
            expect(streetAltField?.props('messages')).toStrictEqual(['Address 2 is required']);
            expect(cityField?.props('sentiment')).toBe('critical');
            expect(cityField?.props('messages')).toStrictEqual(['City is required']);
            expect(postcodeField?.props('sentiment')).toBe('critical');
            expect(postcodeField?.props('messages')).toStrictEqual(['Postcode is required']);
            expect(stateField?.props('sentiment')).toBe('critical');
            expect(stateField?.props('messages')).toStrictEqual(['State is required']);
            expect(countryCodeField?.props('sentiment')).toBe('critical');
            expect(countryCodeField?.props('messages')).toStrictEqual(['Country is required']);
            expect(additionalInfoField?.props('sentiment')).toBe('critical');
            expect(additionalInfoField?.props('messages')).toStrictEqual(['Additional info is required']);
            expect(nameField?.props('sentiment')).toBe('critical');
            expect(nameField?.props('messages')).toStrictEqual(['Contact is required']);
            expect(emailField?.props('sentiment')).toBe('critical');
            expect(emailField?.props('messages')).toStrictEqual(['Email is required']);
            expect(phoneField?.props('sentiment')).toBe('critical');
            expect(phoneField?.props('messages')).toStrictEqual(['Phone is required']);
            expect(mobileField?.props('sentiment')).toBe('critical');
            expect(mobileField?.props('messages')).toStrictEqual(['Mobile is required']);
        });
    });

    test('it shows the associated contacts from address when no name selected', () => {
        const wrapper = mountComponent({
            propsData: {
                isRealAddressOnlyMode: false,
                popupAddress: {
                    phone: 'phone',
                    mobile: 'mobile',
                    email: 'email',
                },
                popupContact: {
                    name: undefined,
                },
            },
        });

        const emailField = wrapper.findAllComponents(WtgTextField).at(5);
        const phoneField = wrapper.findAllComponents(WtgTextField).at(6);
        const mobileField = wrapper.findAllComponents(WtgTextField).at(7);

        expect(emailField?.props('modelValue')).toBe('email');
        expect(phoneField?.props('modelValue')).toBe('phone');
        expect(mobileField?.props('modelValue')).toBe('mobile');
    });

    test('it renders a WtgGoogleMap component with the correct properties', async () => {
        const testLatLng = { lat: 100, lng: -100 };
        dataProvider.setLatLngAsync = jest.fn((addr, latLng) => (latLng.value = testLatLng));
        const wrapper = mountComponent({ propsData: { apiKey: 'test' } });
        const map = wrapper.findComponent({ name: 'WtgGoogleMap' });
        map.vm.$emit('api-loaded');
        await flushPromises();
        expect(map.props('apiKey')).toBe('test');
        expect(map.props('markers')).toStrictEqual([{ position: testLatLng }]);
        expect(map.props('options')).toStrictEqual({
            center: testLatLng,
            panControl: false,
            streetViewControl: false,
            zoom: 15,
            zoomControl: true,
            styles: jobAddressStyles,
        });
        expect(map.props('height')).toBe('200px');
    });

    test('it hides the WtgGoogleMap component when hideMapLocation is true', async () => {
        const wrapper = mountComponent({ propsData: { hideMapLocation: true } });
        const map = wrapper.findComponent({ name: 'WtgGoogleMap' });
        expect(map.exists()).toBe(false);
    });

    test('when the user modifies the address, it starts a timer to get the latitude and longitude and set the map props once the user has stopped typing', async () => {
        let testLatLng = { lat: 100, lng: -100 };
        await flushPromises();
        dataProvider.setLatLngAsync = jest.fn((addr, latLng) => (latLng.value = testLatLng));
        const wrapper = mountComponent({
            propsData: { apiKey: 'test', popupAddress: { street: 'Old Street' } },
        });
        const map = wrapper.getComponent({ name: 'WtgGoogleMap' });
        map.vm.$emit('api-loaded');

        await flushPromises();
        expect(map.props('markers')).toStrictEqual([{ position: testLatLng }]);
        expect(map.props('options').center).toStrictEqual(testLatLng);
        expect(dataProvider.setLatLngAsync).toHaveBeenCalledTimes(1);

        testLatLng = { lat: -200, lng: 200 };

        wrapper.setProps({ popupAddress: { street: 'Is' } });
        await wrapper.vm.$nextTick();
        wrapper.setProps({ popupAddress: { street: 'Isla' } });
        await wrapper.vm.$nextTick();
        wrapper.setProps({ popupAddress: { street: 'Island' } });
        await wrapper.vm.$nextTick();
        jest.advanceTimersByTime(200);
        await flushPromises();
        expect(map.props('markers')).toStrictEqual([{ position: testLatLng }]);
        expect(map.props('options').center).toStrictEqual(testLatLng);
        expect(dataProvider.setLatLngAsync).toHaveBeenCalledTimes(2);
        expect((dataProvider.setLatLngAsync as jest.Mock).mock.calls[1][0]).toStrictEqual({ street: 'Island' });
    });

    test('should clear contact info if name is not provided and isRealAddressOnlyMode', async () => {
        const wrapper = mountComponent({
            propsData: {
                isRealAddressOnlyMode: true,
                popupContact: {
                    phone: 'contact phone',
                    mobile: 'contact mobile',
                    email: 'contact email',
                },
                dataProvider: {
                    contactProvider: {
                        getItemForValueAsync: jest.fn().mockResolvedValue(null),
                    },
                },
            },
        });

        await (wrapper.vm as any).onContactSelected('');

        expect(wrapper.emitted('contact-changed')).toBeTruthy();
        expect(wrapper.emitted()['contact-changed']![0]).toStrictEqual([{}]);
    });

    test('should retain contact info if name is not provided and !isRealAddressOnlyMode', async () => {
        const wrapper = mountComponent({
            propsData: {
                popupContact: {
                    phone: 'contact phone',
                    mobile: 'contact mobile',
                    email: 'contact email',
                },
                dataProvider: {
                    contactProvider: {
                        getItemForValueAsync: jest.fn().mockResolvedValue(null),
                    },
                },
            },
        });

        await (wrapper.vm as any).onContactSelected('');

        expect(wrapper.emitted('contact-changed')).toBeTruthy();
        expect(wrapper.emitted()['contact-changed']![0]).toStrictEqual([
            {
                phone: 'contact phone',
                mobile: 'contact mobile',
                email: 'contact email',
                name: '',
            },
        ]);
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(AddressEditor, {
            propsData: {
                ...propsData,
                dataProvider,
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
