import WtgGoogleMap from '@components/WtgGoogleMap/WtgGoogleMap.vue';
import { Meta, StoryObj } from '@storybook/vue3';
import GoogleMapDemo from '../../../../src/storybook/maps/GoogleMapDemo.vue';

type Story = StoryObj<typeof WtgGoogleMap>;
const meta: Meta<typeof WtgGoogleMap> = {
    title: 'Components/Google Map',
    component: WtgGoogleMap,
    parameters: {
        docs: {
            description: {
                component: `This component uses the Google Maps api to display a map. The maps can also display markers and polylines. The map,
                            along with markers and polylines are configured by using the Google map Classes from the google maps javascript api.
                            For further information please refer to the
                            <a href="https://developers.google.com/maps/documentation">Google Maps documentation</a>.`,
            },
        },
    },
    argTypes: {},
    render: (args) => ({
        components: { WtgGoogleMap },
        setup: () => ({ args }),
        template: `<WtgGoogleMap v-bind="args"></WtgGoogleMap>`,
    }),
};

export default meta;

const apiKey = '';
const mapHeight = '1000px';
const mapWidth = '100%';
export const Default: Story = {
    args: {
        apiKey: apiKey,
    },
    render: (args) => ({
        components: { GoogleMapDemo },
        setup: () => ({ args }),

        template: `<google-map-demo></google-map-demo>`,
    }),
};

export const ClusterMap: Story = {
    args: {
        apiKey: apiKey,
        zoom: 3,
        center: { lat: -28.024, lng: 140.887 },
        clusterer: {
            markers: [
                { title: 'A', position: { lat: -31.56391, lng: 147.154312 } },
                { title: 'B', position: { lat: -33.718234, lng: 150.363181 } },
                { title: 'C', position: { lat: -33.727111, lng: 150.371124 } },
                { title: 'D', position: { lat: -33.848588, lng: 151.209834 } },
                { title: 'E', position: { lat: -33.851702, lng: 151.216968 } },
                { title: 'F', position: { lat: -34.671264, lng: 150.863657 } },
                { title: 'G', position: { lat: -35.304724, lng: 148.662905 } },
                { title: 'H', position: { lat: -36.817685, lng: 175.699196 } },
                { title: 'I', position: { lat: -36.828611, lng: 175.790222 } },
                { title: 'J', position: { lat: -37.75, lng: 145.116667 } },
                { title: 'K', position: { lat: -37.759859, lng: 145.128708 } },
                { title: 'L', position: { lat: -37.765015, lng: 145.133858 } },
                { title: 'M', position: { lat: -37.770104, lng: 145.143299 } },
                { title: 'N', position: { lat: -37.7737, lng: 145.145187 } },
                { title: 'O', position: { lat: -37.774785, lng: 145.137978 } },
                { title: 'P', position: { lat: -37.819616, lng: 144.968119 } },
                { title: 'Q', position: { lat: -38.330766, lng: 144.695692 } },
                { title: 'R', position: { lat: -39.927193, lng: 175.053218 } },
                { title: 'S', position: { lat: -41.330162, lng: 174.865694 } },
                { title: 'T', position: { lat: -42.734358, lng: 147.439506 } },
                { title: 'U', position: { lat: -42.734358, lng: 147.501315 } },
                { title: 'V', position: { lat: -42.735258, lng: 147.438 } },
                { title: 'W', position: { lat: -43.999792, lng: 170.463352 } },
            ],
        },
        height: mapHeight,
        width: mapWidth,
    },
};

export const PolyLinesMap: Story = {
    args: {
        apiKey: apiKey,
        options: {
            mapTypeId: 'terrain',
        },
        zoom: 2,
        center: { lat: 0, lng: -180 },
        polylines: [
            {
                geodesic: true,
                strokeColor: '#FF0000',
                strokeOpacity: 1.0,
                strokeWeight: 2,
                path: [
                    { lat: 37.772, lng: -122.214 },
                    { lat: 21.291, lng: -157.821 },
                    { lat: -18.142, lng: 178.431 },
                    { lat: -27.467, lng: 153.027 },
                ],
            },
        ],
        height: mapHeight,
        width: mapWidth,
    },
};

export const PolygonMap: Story = {
    args: {
        apiKey: apiKey,
        zoom: 5,
        center: { lat: 24.886, lng: -70.268 },
        options: {
            mapTypeId: 'terrain',
        },
        polygons: [
            {
                paths: [
                    { lat: 25.774, lng: -80.19 },
                    { lat: 18.466, lng: -66.118 },
                    { lat: 32.321, lng: -64.757 },
                    { lat: 25.774, lng: -80.19 },
                ],
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.35,
            },
        ],
        height: mapHeight,
        width: mapWidth,
    },
};

export const ShapesMap: Story = {
    args: {
        apiKey: apiKey,
        zoom: 5,
        center: { lat: -33.855488722254876, lng: 151.0365006152024 },
        options: {
            mapTypeId: 'roadmap',
        },
        circles: [
            {
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.35,
                center: { lat: -33.863160333699874, lng: 151.20741034156273 },
                radius: 90000,
            },
            {
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.35,
                center: { lat: -37.78014212945811, lng: 144.9799472631062 },
                radius: 70000,
            },
            {
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.35,
                center: { lat: -27.446751900229746, lng: 153.00807333781995 },
                radius: 45000,
            },
        ],
        height: mapHeight,
        width: mapWidth,
    },
};

const airIcon = {
    path: 'M193,286.56c.14,1.21.26,2.1.33,3,3.81,46,7.54,92.08,11.53,138.1.35,4.1-.9,6.31-4,8.68-14.63,11.27-29.09,22.76-43.57,34.22a4.06,4.06,0,0,0-1.64,2.54c-.1,8.95,0,17.9,0,27.11,1.23-.38,2.17-.65,3.1-1,20.72-6.82,41.43-13.68,62.18-20.41a9,9,0,0,1,5.17-.06c20.89,6.6,41.73,13.34,62.58,20.05.91.29,1.82.55,3.07.92-.07-8.7-.21-17-.14-25.35a5.53,5.53,0,0,0-2.46-5C274,457.77,259,446,243.79,434.38a4.51,4.51,0,0,1-1.92-4.55q5.34-70.31,10.57-140.61c.07-.88.19-1.74.31-2.87L445,371.7c0-12.68-.09-24.81-.22-36.94a3.25,3.25,0,0,0-1.25-2.15Q424.29,318,405,303.48l-74.88-56.33q-36.24-27.28-72.49-54.55a5.7,5.7,0,0,1-2.66-5.12c0-32.7-.74-65.39-3.59-98-1.82-20.76-4.16-41.46-9.89-61.6-2.06-7.24-4.56-14.33-9.13-20.47-6.2-8.34-14.74-8.34-20.86,0-4.64,6.33-7.1,13.61-9.15,21-5,18.15-7.2,36.77-8.84,55.46-3,34.59-3.61,69.27-3.38,104a4.94,4.94,0,0,1-2.05,4.61c-6.62,4.89-13.12,9.95-19.67,14.95q-42,32.07-83.95,64.14Q43.74,302.66,3.06,333.74a3.94,3.94,0,0,0-1.81,3.58c.11,11.14.11,22.28.17,33.43,0,.74.11,1.48.19,2.45Z',
    size: { width: 446, height: 500 },
};

const themedIcon = {
    dark: {
        path: airIcon.path,
        anchor: { x: airIcon.size.width / 2, y: airIcon.size.height / 2 },
        fillColor: '#000000',
        strokeColor: '#FFFFFF',
        strokeWeight: 1,
        fillOpacity: 1,
        scale: 0.075,
    },
    light: {
        path: airIcon.path,
        anchor: { x: airIcon.size.width / 2, y: airIcon.size.height / 2 },
        fillColor: '#FFFFFF',
        strokeColor: '#000000',
        strokeWeight: 1,
        fillOpacity: 1,
        scale: 0.075,
    },
};

const icon = {
    url: 'https://developers.google.com/maps/documentation/javascript/examples/full/images/beachflag.png',
};

export const Sandbox: Story = {
    args: {
        apiKey: apiKey,
        zoom: 3,
        center: { lat: -28.024, lng: 140.887 },
        clusterer: {
            markers: [
                { title: 'A', position: { lat: -31.56391, lng: 147.154312 } },
                { title: 'B', position: { lat: -33.718234, lng: 150.363181 } },
                { title: 'C', position: { lat: -33.727111, lng: 150.371124 } },
                { title: 'D', position: { lat: -33.848588, lng: 151.209834 } },
                { title: 'E', position: { lat: -33.851702, lng: 151.216968 } },
                { title: 'F', position: { lat: -34.671264, lng: 150.863657 } },
                { title: 'G', position: { lat: -35.304724, lng: 148.662905 } },
                { title: 'H', position: { lat: -36.817685, lng: 175.699196 } },
                { title: 'I', position: { lat: -36.828611, lng: 175.790222 } },
                { title: 'J', position: { lat: -37.75, lng: 145.116667 } },
                { title: 'K', position: { lat: -37.759859, lng: 145.128708 } },
                { title: 'L', position: { lat: -37.765015, lng: 145.133858 } },
                { title: 'M', position: { lat: -37.770104, lng: 145.143299 } },
                { title: 'N', position: { lat: -37.7737, lng: 145.145187 } },
                { title: 'O', position: { lat: -37.774785, lng: 145.137978 } },
                { title: 'P', position: { lat: -37.819616, lng: 144.968119 } },
                { title: 'Q', position: { lat: -38.330766, lng: 144.695692 } },
                { title: 'R', position: { lat: -39.927193, lng: 175.053218 } },
                { title: 'S', position: { lat: -41.330162, lng: 174.865694 } },
                { title: 'T', position: { lat: -42.734358, lng: 147.439506 } },
                { title: 'U', position: { lat: -42.734358, lng: 147.501315 } },
                { title: 'V', position: { lat: -42.735258, lng: 147.438 } },
                { title: 'W : Icon', position: { lat: -44.735258, lng: 180.438 }, icon: icon },
                {
                    title: 'X : Icon that changes based on Light & Dark Mode',
                    position: { lat: -43.999792, lng: 170.463352 },
                    icon: themedIcon,
                },
            ],
        },
        height: mapHeight,
        width: mapWidth,
    },
    render: (args) => ({
        components: { WtgGoogleMap },
        setup: () => ({ args }),

        template: `
        <div>
            <div> Count Div should not be part of Scroll when hover on a cluster and list is displayed. Count div should always be visible and fixed at bottom of the list view.</div>
            <WtgGoogleMap v-bind="args"></WtgGoogleMap>
        </div>`,
    }),
};
