<template>
    <WtgButton
        v-if="action.visible"
        variant="ghost"
        :leading-icon="isTabletOrMobile ? '' : 's-icon-documentation'"
        aria-haspopup="menu"
        :aria-expanded="open ? 'true' : 'false'"
        :tooltip="action.caption"
        @click="onClick"
    >
        <WtgIcon v-if="isTabletOrMobile" icon="s-icon-documentation" />
        {{ isTabletOrMobile ? '' : action.caption }}
        <WtgPopover v-if="isLoaded" v-model="open" activator="parent" :nudge-left="-4">
            <template #default>
                <PopupMenuList :actions="menuItems" @action="onAction" />
            </template>
        </WtgPopover>
    </WtgButton>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgIcon from '@components/WtgIcon';
import { PopupMenuList } from '@components/WtgMenuBar/popup';
import WtgPopover from '@components/WtgPopover';
import { WtgFrameworkDocuments, WtgFrameworkDocumentsMenuItem, WtgFrameworkTask } from '@components/framework/types';
import { useFramework } from '@composables/framework';
import { computed, PropType, ref, reactive } from 'vue';

const props = defineProps({
    task: { type: Object as PropType<WtgFrameworkTask>, default: undefined },
});

const { isTabletOrMobile } = useFramework();

const menuItems = ref<WtgFrameworkDocumentsMenuItem[]>([]);

const action = computed((): WtgFrameworkDocuments => {
    return (
        props.task?.documents ?? {
            visible: false,
            caption: 'Documents',
        }
    );
});

const onAction = async (action: WtgFrameworkDocumentsMenuItem) => {
    if (action.click) {
        action.click();
    } else if (action.loadDocumentsAsync) {
        action.loading = true;
        action.actions = await action.loadDocumentsAsync();
        action.loading = false;
    }
};

let nextId = 1;
let loading = false;
const isLoaded = ref(false);
const open = ref(false);

const onClick = async () => {
    if (!loading) {
        if (!open.value) {
            loading = true;
            const documentItems = (await action.value.loadDocumentsAsync?.()) ?? [];
            menuItems.value = documentItems.map((item) =>
                reactive({
                    actions: [],
                    id: 'doc-' + nextId++,
                    loading: false,
                    ...item,
                })
            );
            loading = false;
            isLoaded.value = true;
        }
        open.value = !open.value;
    }
};
</script>
