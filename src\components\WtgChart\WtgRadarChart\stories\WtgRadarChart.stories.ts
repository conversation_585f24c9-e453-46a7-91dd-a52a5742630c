import { <PERSON>a, StoryObj } from '@storybook/vue3';
import { WtgRadarChart } from '../..';

type Story = StoryObj<typeof WtgRadarChart>;
const meta: Meta<typeof WtgRadarChart> = {
    title: 'Data viz/Radar Chart',
    component: WtgRadarChart,
    parameters: {
        docs: {
            description: {
                component:
                    'A radar chart is a way of showing multiple data points and the variation between them. They are often useful for comparing the points of two or more different data sets.',
            },
        },
    },
    render: (args) => ({
        components: { WtgRadarChart },
        setup: () => ({ args }),
        template: `<wtg-radar-chart v-bind="args"/>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        data: {
            labels: ['Eating', 'Drinking', 'Sleeping', 'Designing', 'Coding', 'Cycling', 'Running'],
            datasets: [
                {
                    label: 'My First Dataset',
                    data: [65, 59, 90, 81, 56, 55, 40],
                    fill: true,
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgb(255, 99, 132)',
                    pointBackgroundColor: 'rgb(255, 99, 132)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgb(255, 99, 132)',
                },
                {
                    label: 'My Second Dataset',
                    data: [28, 48, 40, 19, 96, 27, 100],
                    fill: true,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgb(54, 162, 235)',
                    pointBackgroundColor: 'rgb(54, 162, 235)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgb(54, 162, 235)',
                },
            ],
        },
        options: {
            responsive: true,
            elements: {
                line: {
                    borderWidth: 3,
                },
            },
        },
        loading: false,
    },
};
