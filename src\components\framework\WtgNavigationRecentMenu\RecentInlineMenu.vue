<template>
    <WtgListItem
        v-if="isTabletOrMobile"
        role="menuitem"
        :aria-label="favoritesCaption"
        aria-haspopup="menu"
        @click="onFavoritesClick"
    >
        <template #leading>
            <WtgIcon>s-icon-star-empty</WtgIcon>
        </template>
        {{ favoritesCaption }}
    </WtgListItem>
    <RecentDialog
        v-model="isDialogVisible"
        :items="items"
        :recents-caption="recentsCaption"
        :favorites-caption="favoritesCaption"
        :caption-close="captionClose"
        @item-click="onItemClick"
        @update:model-value="onModelUpdate"
    ></RecentDialog>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { WtgListItem } from '@components/WtgList';
import { useFramework } from '@composables/framework';
import { PropType, ref } from 'vue';
import RecentDialog from './recentDialog/RecentDialog.vue';
import { Items, WtgRecentItem } from './types';

const { isTabletOrMobile } = useFramework();

const isDialogVisible = ref(false);
const emit = defineEmits<{
    'item-click': [item: WtgRecentItem];
}>();

const props = defineProps({
    items: {
        type: Object as PropType<Items>,
        default: () => ({
            favorites: [],
            recent: [],
        }),
    },
    favoritesCaption: {
        type: String,
        default: 'Favorites',
    },
    recentsCaption: {
        type: String,
        default: 'Recent',
    },
    captionClose: {
        type: String,
        default: 'Close',
    },
    onClick: {
        type: Function,
        default: undefined,
    },
    onDialogOpen: {
        type: Function,
        default: undefined,
    },
});

const onFavoritesClick = (): void => {
    isDialogVisible.value = true;
    if (props.onClick) {
        props.onClick();
        if (props.onDialogOpen) props.onDialogOpen();
    }
};

const onModelUpdate = (value: boolean) => {
    isDialogVisible.value = value;
};

const onItemClick = (item: WtgRecentItem) => {
    emit('item-click', item);
};
</script>
