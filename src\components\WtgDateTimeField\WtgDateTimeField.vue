<template>
    <WtgInput v-bind="baseInputProps" promptable :input="input" class="wtg-datetime-field">
        <span v-if="computedNative">{{ displayValue }}</span>
        <input
            :id="computedId"
            ref="input"
            autocomplete="off"
            :aria-label="ariaLabel"
            :aria-labelledby="ariaLabelledby"
            :disabled="disabled || displayOnly"
            :placeholder="placeholder"
            :readonly="readonly"
            :value="nativeInputValue"
            :type="inputType"
            @blur="onBlur"
            @change="onChange"
            @focus="onFocus"
            @input="onInput"
            @click="onClick"
            @keydown.f4="onF4Key"
        />
        <template #prompter>
            <WtgPopover
                v-model="menuRef"
                :close-on-content-click="false"
                :disabled="computedNative"
                location="bottom end"
                transition="slide-y-transition"
                offset="10px"
                nudge-bottom="4px"
                scroll-strategy="none"
                style="padding: var(--s-padding-xl)"
            >
                <template #activator="{ props: activatorProps }: { props: Record<string, any> }">
                    <WtgIcon
                        role="button"
                        tabindex="-1"
                        :aria-hidden="false"
                        v-bind="activatorProps"
                        :aria-label="ariaLabelPrompter"
                        class="wtg-input--interactive-element"
                        :disabled="disabled || displayOnly || readonly"
                        icon="s-icon-calendar-blank"
                        @click="onClick"
                    >
                    </WtgIcon>
                </template>
                <div>
                    <WtgDateTimePicker
                        :show-time-zone="showTimeZone"
                        :model-value="pickerModalValue"
                        :use-seconds="useSeconds"
                        :today-date-fn="todayDateFn"
                        @update:model-value="onDateTimePickerUpdate"
                    />
                </div>
            </WtgPopover>
        </template>
    </WtgInput>
</template>

<script setup lang="ts">
import WtgDateTimePicker from '@components/WtgDateTimePicker';
import { WtgIcon } from '@components/WtgIcon';
import WtgInput from '@components/WtgInput';
import WtgPopover from '@components/WtgPopover';
import { useFocus } from '@composables/focus';
import { useFramework } from '@composables/framework';
import { basePropsFromProps, makeInputProps } from '@composables/input';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import { makeValidationProps } from '@composables/validation';
import { PropType, computed, getCurrentInstance, onMounted, onUnmounted, ref, watch } from 'vue';
import { WtgDateTimeFormat, WtgDateTimeFormatter } from './types';

//
// Properties
//
const props = defineProps({
    /**
     * The formatter used to format and parse date-time values.
     */
    formatter: {
        type: Object as PropType<WtgDateTimeFormatter>,
        default: undefined,
    },

    /**
     * Determines whether seconds should be included in the date-time value.
     */
    useSeconds: {
        type: Boolean,
        default: false,
    },
    showTimeZone: {
        type: Boolean,
        default: false,
    },

    /**
     * The current value of the date-time field, used for two-way binding.
     */
    modelValue: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the native date-time picker will be used on supported devices.
     */
    native: {
        type: Boolean,
        default: undefined,
    },

    ...makeInputProps(),
    ...makeValidationProps(),
    ...makeLayoutGridColumnProps(),

    /**
     * The value of the date-time field.
     * @deprecated Use `modelValue` instead.
     */
    inputValue: {
        type: String,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    blur: [e: FocusEvent];
    focus: [e: FocusEvent];
    'update:modelValue': [value: string, isValid: boolean];
    'model-compat:input': [value: string, isValid: boolean];
    change: [value: string, isValid: boolean];
    'update:menu': [value: boolean];
}>();

//
// State
//
const input = ref<HTMLElement>();
const menuRef = ref(false);
const internalValue = ref(props.modelValue ?? props.inputValue ?? '');
const nativeInputValue = ref('');
const displayValue = ref('');
const hasPendingChange = ref(false);

//
// Composables
//
const instance = getCurrentInstance();
const { isFocused, focus, blur } = useFocus(props);
const { dateTimeFormatter, locale, formatCaption } = useLocale();
const { isMobile } = useFramework();

useLayoutGridColumn(props);

//
// Computed
//
const computedId = computed(() => props.id || props.inputId || `input-${instance!.uid}`);

const inputType = computed(() => (computedNative.value ? 'datetime-local' : 'text'));

const baseInputProps = computed(() => {
    return {
        ...basePropsFromProps(props),
        filled: nativeInputValue.value !== '',
        id: computedId.value,
        focused: isFocused.value,
        hideMessages: menuRef.value,
    };
});

const ariaLabelPrompter = computed(() => formatCaption('dateTimeField.ariaLabelPrompter'));

const computedNative = computed(() => {
    return props.native !== undefined ? props.native : isMobile.value;
});

const clickable = computed(() => !props.disabled && !props.displayOnly && !props.readonly);

const pickerModalValue = computed<string>(() => {
    const date = valuesFn(internalValue.value ?? '');
    if (!date.isValid) {
        return '';
    }
    const dateValue =
        date.year + '-' + date.month.toString().padStart(2, '0') + '-' + date.day.toString().padStart(2, '0');
    const timeValue =
        date.hour.toString().padStart(2, '0') +
        ':' +
        date.minutes.toString().padStart(2, '0') +
        ':' +
        date.seconds.toString().padStart(2, '0');

    let timeZoneValue = '';
    if (props.showTimeZone) {
        timeZoneValue = date.timeZone ? ` ${date.timeZone}` : ' +00:00';
    }
    return `${dateValue} ${timeValue}${timeZoneValue}`;
});

//
// Watchers
//
watch([() => locale.value, () => props.useSeconds], () => {
    nativeInputValue.value = formatDateTime(internalValue.value ?? '');
});

watch(
    () => props.modelValue ?? props.inputValue ?? '',
    (value: string) => {
        updateValue(value, false);
    }
);

watch(
    () => menuRef.value,
    () => {
        emit('update:menu', menuRef.value);
    }
);

//
// Event Handlers
//
function onChange(e: Event) {
    const value = (e.target as HTMLInputElement).value;
    nativeInputValue.value = value;
    if (computedNative.value) {
        updateValue(value, true);
    } else {
        setDateTime(value);
    }
}

function onClick() {
    if (clickable.value) {
        showNativePrompter();
    }
}

function onF4Key(event: KeyboardEvent): void {
    if (clickable.value) {
        if (!computedNative.value) {
            menuRef.value = !menuRef.value;
        } else {
            showNativePrompter();
        }
        event.preventDefault();
        event.stopPropagation();
    }
}

function onInput(e: Event) {
    const value = (e.target as HTMLInputElement).value;
    nativeInputValue.value = value;
    hasPendingChange.value = true;
}

function onFocus(e: FocusEvent) {
    emit('focus', e);
    if (!isFocused.value) {
        focus();
    }
}

function onBlur(e: FocusEvent) {
    emit('blur', e);
    blur();
}

function onDateTimePickerUpdate(newValue: string) {
    setDateTime(newValue);
}

//
// Helpers
//
function valuesFn(isoDateTime: string): WtgDateTimeFormat {
    return props.formatter
        ? props.formatter.values(isoDateTime, props.showTimeZone)
        : dateTimeFormatter.value.values(isoDateTime, props.showTimeZone);
}

function parsedDateTimeFn(dateTime: string, showTimeZone: boolean): string | null {
    dateTime = dateTime && dateTime.trim();
    return props.formatter
        ? props.formatter.parseDate(dateTime, showTimeZone)
        : dateTimeFormatter.value.parse(dateTime, showTimeZone);
}

function formatDateTime(dateTime: string): string {
    return props.formatter
        ? props.formatter.formatDate(dateTime, props.showTimeZone)
        : dateTimeFormatter.value.format(dateTime, props.useSeconds, props.showTimeZone, false);
}

function todayDateFn(): string {
    return props.formatter
        ? props.formatter.today(props.showTimeZone)
        : dateTimeFormatter.value.today(props.showTimeZone);
}

function updateValue(newValue: string, notify: boolean, isValid = true) {
    newValue = newValue && newValue.trim();
    if (internalValue.value !== newValue) {
        internalValue.value = newValue;
        if (notify) {
            emit('update:modelValue', internalValue.value, isValid);
            emit('model-compat:input', internalValue.value, isValid);
            emit('change', internalValue.value, isValid);
        }
    }
    if (isValid) {
        const formattedDateTimeValue = formatDateTime(internalValue.value);
        nativeInputValue.value = computedNative.value ? internalValue.value : formattedDateTimeValue;
        displayValue.value = formattedDateTimeValue;
    }
    hasPendingChange.value = false;
}

function setDateTime(dateTime: string): void {
    const parsedDate = parsedDateTimeFn(dateTime, props.showTimeZone);
    const isValid = parsedDate !== null;

    updateValue(parsedDate ?? dateTime, true, isValid);
}

function showNativePrompter() {
    if (computedNative.value && 'showPicker' in HTMLInputElement.prototype) {
        const dateInputField = input.value as HTMLInputElement;
        dateInputField?.showPicker();
    }
}

//
// Lifecycle
//
onMounted(() => {
    updateValue(props.modelValue ?? props.inputValue ?? '', false);
});

onUnmounted(() => {
    if (hasPendingChange.value) {
        setDateTime(nativeInputValue.value);
    }
    if (menuRef.value) {
        emit('update:menu', false);
    }
});
</script>

<style lang="scss">
.wtg-datetime-field {
    input[type='datetime-local'] {
        opacity: 0;
        position: absolute;
    }
}
</style>
