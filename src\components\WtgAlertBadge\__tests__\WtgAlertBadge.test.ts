import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgAlertBadge } from '..';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgAlertBadge', () => {
    test('it applies the wtg-alert-badge-error class and icon when the type attribute is set to error', async () => {
        const wrapper = mountComponent({
            propsData: {
                variant: 'error',
            },
        });

        const wtgAlertBadge = wrapper.find('.wtg-alert-badge');
        const wtgAlertBadgeIcon = wtgAlertBadge.find('i');
        expect(wtgAlertBadgeIcon.classes()).toContain('s-icon-status-critical-filled');
        expect(wtgAlertBadge.html()).toContain('wtg-alert-badge--error');
    });

    test('it applies the wtg-alert-badge-warning class and icon when the type attribute is set to warning', async () => {
        const wrapper = mountComponent({
            propsData: {
                variant: 'warning',
            },
        });

        const wtgAlertBadge = wrapper.find('.wtg-alert-badge');
        const wtgAlertBadgeIcon = wtgAlertBadge.find('i');
        expect(wtgAlertBadgeIcon.classes()).toContain('s-icon-status-warning-filled');
        expect(wtgAlertBadge.html()).toContain('wtg-alert-badge--warning');
    });

    test('it applies the wtg-alert-badge-success class and icon when the type attribute is set to success', async () => {
        const wrapper = mountComponent({
            propsData: {
                variant: 'success',
            },
        });

        const wtgAlertBadge = wrapper.find('.wtg-alert-badge');
        const wtgAlertBadgeIcon = wtgAlertBadge.find('i');
        expect(wtgAlertBadgeIcon.classes()).toContain('s-icon-status-success-filled');
        expect(wtgAlertBadge.html()).toContain('wtg-alert-badge--success');
    });

    test('it applies the wtg-alert-badge-info class and icon when the type attribute is set to info', async () => {
        const wrapper = mountComponent({
            propsData: {
                variant: 'info',
            },
        });

        const wtgAlertBadge = wrapper.find('.wtg-alert-badge');
        const wtgAlertBadgeIcon = wtgAlertBadge.find('i');
        expect(wtgAlertBadgeIcon.classes()).toContain('s-icon-info-circle-filled');
        expect(wtgAlertBadge.html()).toContain('wtg-alert-badge--info');
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgAlertBadge, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
