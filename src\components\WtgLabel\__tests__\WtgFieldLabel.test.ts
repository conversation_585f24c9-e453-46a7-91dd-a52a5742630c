import { WtgFieldLabel } from '@components/WtgLabel';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgFieldLabel', () => {
    test('it renders a <span> element', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('LABEL');
        expect(wrapper.text()).toBe('Some text');
    });

    test('it passes attributes to the label element', () => {
        const wrapper = mountComponent({
            propsData: {
                for: 'dummy_input_id',
            },
        });
        const label = wrapper.find('label');
        expect(label.attributes('for')).toBe('dummy_input_id');
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('it has a required property mixed in that allows an asterix to be added for mandatory field display', async () => {
        const wrapper = mountComponent({
            propsData: { required: true },
        });
        expect(wrapper.props('required')).toBe(true);
        expect(wrapper.classes()).toContain('wtg-required');
        await wrapper.setProps({ required: false });
        expect(wrapper.classes()).not.toContain('wtg-required');
        await wrapper.setProps({ required: undefined });
        expect(wrapper.classes()).not.toContain('wtg-required');
    });

    function mountComponent({ propsData = {}, provide = {} } = {}) {
        return mount(WtgFieldLabel, {
            propsData,
            slots: {
                default: 'Some text',
            },
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
