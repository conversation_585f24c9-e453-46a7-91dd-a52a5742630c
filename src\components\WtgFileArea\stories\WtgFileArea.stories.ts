import WtgAvatar from '@components/WtgAvatar';
import WtgBox from '@components/WtgBox';
import WtgButton from '@components/WtgButton';
import WtgDataTable from '@components/WtgDataTable';
import WtgFileArea from '@components/WtgFileArea/WtgFileArea.vue';
import WtgIcon from '@components/WtgIcon/WtgIcon.vue';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import { WtgList, WtgListItem } from '@components/WtgList';
import WtgModal from '@components/WtgModal';
import WtgPanel from '@components/WtgPanel';
import { WtgSelect } from '@components/WtgSelectField';
import WtgSpacer from '@components/WtgSpacer';
import WtgTextField from '@components/WtgTextField';
import { action } from '@storybook/addon-actions';
import { Meta, StoryContext, StoryObj } from '@storybook/vue3';
import { computed, ref, Ref } from 'vue';

type Story = StoryObj<typeof WtgFileArea>;
const meta: Meta<typeof WtgFileArea> = {
    title: 'Components/File Area',
    component: WtgFileArea,
    parameters: {
        docs: {
            description: {
                component:
                    'File area is a specialized component that enables other library components to allow file selection via clicking and/or dragging, when used in the default slot.',
            },
        },
        layout: 'padded',
    },
    render: (args) => ({
        components: { WtgFileArea },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: '<WtgFileArea v-bind="args"></WtgFileArea>',
    }),
};

export default meta;

function createProps(storyContext: StoryContext): string {
    const props = [];
    for (const arg in storyContext.args) {
        props.push(`${arg}="${storyContext.args[arg] + ''}"`);
    }
    return props.sort().join(' ');
}

function createTemplate(args: string): string {
    return `
    <div class="pa-4 w-100">
        <WtgPanel caption="Selection Area">
            <WtgFileArea ${args} v-model="cardFiles">
                <template v-slot:default="{ on, dragOverArea }">
                    <WtgPanel
                        class="pa-4 d-flex align-center justify-center"
                        :sentiment="dragOverArea ? 'grey lighten-2' : ''"
                        height="250px"
                        v-on="on"
                    >
                        <div class="d-flex flex-column align-center">
                            <WtgIcon size="xxl" icon="s-icon-upload-file"></WtgIcon>
                            <WtgLabel :class="dragOverArea ? 'mt-4 mb-4' : ''">
                                {{
                    dragOverArea
                        ? 'Great, just drop your files here'
                        : 'You can drag files onto this card or click here to select them'
                                }}
                            </WtgLabel>
                        </div>
                    </WtgPanel>
                </template>
            </WtgFileArea>
            <WtgBox class="d-flex pa-4">
                <WtgLabel typography="title-sm-default">Selected Files</WtgLabel>
                <WtgSpacer />
                <WtgButton sentiment="primary" @click="cardFiles = []">Clear All</WtgButton>
            </WtgBox>
            <WtgList aria-label="Selected files">
                <WtgListItem v-for="(file, i) in cardFiles" :key="i">
                    <template v-slot:leading>
                        <WtgAvatar :rounded="0" icon="s-icon-file">
                        </WtgAvatar>
                    </template>
                    <div class="ml-2 mr-2 d-flex flex-column flex-grow-1">
                        <div>{{ file.name }}</div>
                        <div style="color: var(--s-neutral-txt-weak-default);">{{ Math.ceil(file.size / 1024) }} KB</div>
                    </div>
                    <template v-slot:trailing>
                        <WtgIconButton aria-label="Remove file" variant="ghost" @click.stop="onRemoveClick(i)" icon="s-icon-close-circle" />
                    </template>
                </WtgListItem>
            </WtgList>
        </WtgPanel>
    </div>
`;
}

export const Default: Story = {
    parameters: {
        docs: {
            source: {
                transform: (source: string, storyContext: StoryContext) => `
<template>${createTemplate(createProps(storyContext))}</template>
<script setup lang="ts">
    const cardFiles = ref([]);
    const onRemoveClick = (index: number): void => {
        cardFiles.value.splice(index, 1);
    };
    return { cardFiles, onRemoveClick };
</script>
`,
            },
        },
    },
    args: {
        multiple: true,
    },
    render: (args) => ({
        components: {
            WtgList,
            WtgListItem,
            WtgAvatar,
            WtgBox,
            WtgButton,
            WtgFileArea,
            WtgIcon,
            WtgIconButton,
            WtgLabel,
            WtgPanel,
            WtgSpacer,
        },
        setup: () => {
            const cardFiles = ref([]);
            const onRemoveClick = (index: number): void => {
                cardFiles.value.splice(index, 1);
            };
            return { args, cardFiles, onRemoveClick };
        },
        template: createTemplate('v-bind="args"'),
    }),
};

export const AdvancedArea: Story = {
    name: 'Advanced Area',
    args: {
        multiple: true,
    },
    render: (args) => ({
        components: {
            WtgAvatar,
            WtgBox,
            WtgButton,
            WtgDataTable,
            WtgFileArea,
            WtgIcon,
            WtgIconButton,
            WtgLabel,
            WtgPanel,
            WtgSpacer,
        },
        setup: () => {
            const myFiles: Ref<File[]> = ref([]);
            const onMyFilesInput = (selectedFiles: FileList): void => {
                for (const file of Array.from(selectedFiles)) {
                    if (
                        myFiles.value
                            .map((match: File) => {
                                return match.name;
                            })
                            .indexOf(file.name) === -1
                    ) {
                        myFiles.value.push(file);
                    }
                }
            };
            const myFilesAsObjects = computed((): Record<string, any>[] => {
                return myFiles.value.map((file: File) => {
                    const modifiedDate: Date = new Date(file.lastModified);
                    return {
                        name: file.name,
                        lastModified: modifiedDate.toLocaleDateString('en-AU', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric',
                        }),
                        size: Math.ceil(file.size / 1024) + ' KB',
                    };
                });
            });
            return { args, myFiles, onMyFilesInput, myFilesAsObjects };
        },
        template: `
            <div class="pa-4 w-100">
                <WtgPanel>
                    <WtgFileArea v-bind="args" @update:modelValue="onMyFilesInput">
                        <template v-slot="{ on, dragOverArea }">
                            <WtgDataTable
                                :headers="[
                                    { title: 'Name', align: 'start', width: '70%', value: 'name' },
                                    { title: 'Modified', align: 'end', value: 'lastModified' },
                                    { title: 'Size', align: 'end', value: 'size' },
                                ]"
                                :items-per-page="10"
                                :items="myFilesAsObjects"
                                no-data-text="Drop your files here or use the new button"
                                :style="
                                    dragOverArea
                                        ? 'background-color: transparent; border: 1px solid blue'
                                        : 'background-color: transparent;'
                                "
                            >
                            <template v-slot:top>
                                <div class="d-flex mb-2">
                                    <WtgLabel typography="title-sm-default">My Files</WtgLabel>
                                    <WtgSpacer />
                                    <WtgButton sentiment="primary" v-on="on">
                                        <WtgIcon icon="s-icon-plus"></WtgIcon>New
                                    </WtgButton>
                                    <WtgButton class="ml-2" @click="myFiles = []">
                                        <WtgIcon icon="s-icon-delete"></WtgIcon>Clear All
                                    </WtgButton>
                                </div>
                            </template>
                            </WtgDataTable>
                        </template>
                    </WtgFileArea>
                </WtgPanel>
            </div>
        `,
    }),
};

export const FileUploader: Story = {
    args: {
        multiple: true,
        accept: '.pdf',
        disableDrag: true,
    },
    render: (args) => ({
        components: {
            WtgButton,
            WtgLabel,
            WtgPanel,
            WtgFileArea,
            WtgDataTable,
            WtgModal,
            WtgSelect,
            WtgTextField,
            WtgIconButton,
        },
        setup: () => {
            const myFiles: Ref<File[]> = ref([]);
            const tableItems: Ref<Record<string, any>[]> = ref([]);
            const openDialog = ref(false);
            const showAlert = ref(false);
            const selectedFile = ref();
            const selectedFileType = ref();

            const fileTypes = [
                {
                    key: '1',
                    code: 'GRA',
                    description: 'Graphic',
                    codeAndDescription: 'GRA - Graphic',
                },
                {
                    key: '2',
                    code: 'FIL',
                    description: 'Sample File',
                    codeAndDescription: 'FIL - Sample File',
                },
                {
                    key: '3',
                    code: 'INV',
                    description: 'Invoice',
                    codeAndDescription: 'INV - Invoice',
                },
            ];

            const showAllowedFileFormats = computed((): boolean => {
                return args.accept?.length !== 0;
            });

            const onMyFilesInput = (selectedFiles: FileList): void => {
                selectedFile.value = selectedFiles[0];
                openDialog.value = true;
            };

            const onSave = (): void => {
                const modifiedDate: Date = new Date(selectedFile.value.lastModified);
                const newFile = {
                    name: selectedFile.value.name,
                    lastModified: modifiedDate.toLocaleDateString('en-AU', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric',
                    }),
                    size: Math.ceil(selectedFile.value.size / 1024) + ' KB',
                    type: selectedFileType.value?.code,
                };
                tableItems.value = [...tableItems.value, newFile];
                selectedFileType.value = null;
                openDialog.value = false;
            };

            const handleClearInput = (fileToRemove: Record<string, any>): void => {
                tableItems.value = [...tableItems.value.filter((file) => file.name !== fileToRemove.name)];
            };

            return {
                args,
                myFiles,
                fileTypes,
                openDialog,
                selectedFile,
                selectedFileType,
                showAllowedFileFormats,
                tableItems,
                showAlert,
                onMyFilesInput,
                onSave,
                handleClearInput,
            };
        },
        template: `
            <div class="pa-4 w-100">
                <WtgPanel layout="grid">
                    <WtgLabel typography="title-md-default">Upload files</WtgLabel>
                    <WtgLabel v-if="showAllowedFileFormats" typography="text-md-default">Allowed file formats: {{ args.accept }}</WtgLabel>
                    <WtgFileArea v-bind="args" v-model="myFiles" @update:modelValue="onMyFilesInput">
                        <template v-slot:default="{ on, dragOverArea }">
                            <WtgButton v-on="on" sentiment="primary" variant="fill" leading-icon="s-icon-upload-file">Upload</WtgButton>
                        </template>
                    </WtgFileArea>
                </WtgPanel>
                    
                <WtgPanel class="wtg-br-1 mt-4 pa-4 text-no-wrap flex-grow-1" layout="grid">
                    <WtgDataTable
                        :headers="[
                            { title: 'File Name', align: 'start', value: 'name'},
                            { title: 'Type', align: 'start', value: 'type' },
                            { title: 'Size', align: 'start', value: 'size' },
                            { title: 'Updated', align: 'start', value: 'lastModified' },  
                            { title: 'Actions', align: 'start', value: 'delete', key: 'delete' },
                        ]"
                        :items-per-page="10"
                        :items="tableItems"
                        :style="
                            dragOverArea
                            ? 'background-color: transparent; border: 1px solid blue'
                            : 'background-color: transparent;'"
                        class="d-flex flex-column"
                        fixed-header
                        height="100%"
                        :zebra-stripes="true"
                        item-key="key"
                        dense
                        >
                            <template #item.delete="{ item }">
                                <WtgIconButton
                                    :variant="'ghost'"
                                    icon="s-icon-delete"
                                    @click.stop="handleClearInput(item)"
                                />
                            </template>
                    </WtgDataTable>
                </WtgPanel>

                <WtgModal v-bind="args" v-model="openDialog" width="500" title="Upload file">
                    <div>
                        <WtgPanel class="flex-grow-1">
                            <WtgTextField class="mb-2" label="Selected file" :value="selectedFile.name" disabled />
                            <WtgSelect label="Document type" :items="fileTypes" item-value="key" 
                                item-text="codeAndDescription" return-object v-model="selectedFileType"/>
                        </WtgPanel>
                    </div>                
                    <template #actions>
                        <WtgButton @click="openDialog = false">Cancel</WtgButton>
                        <WtgButton sentiment="primary" :disabled="!selectedFileType" variant="fill" @click="onSave">Save changes</WtgButton>
                    </template>
                </WtgModal>
            </div>
        `,
    }),
};
