import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgActionBar } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgActionBar', () => {
    describe('rendering on desktop', () => {
        test('it renders a div tag with class for desktop', () => {
            const wrapper = mountComponent();
            const outerDiv = wrapper.find('div');
            expect(outerDiv.classes().toString()).toBe('wtg-desktop-action-bar');
        });

        test('it passed the default slot to Action Bar', () => {
            const wrapper = mountComponent();

            expect(wrapper.text()).toContain('Action Bar');
        });
    });

    describe('rendering on mobile', () => {
        beforeAll(() => {
            wtgUi.breakpoint.smAndUp = false;
        });
        test('it renders a div tag with class for mobile', () => {
            const wrapper = mountComponent();
            const outerDiv = wrapper.find('div');
            expect(outerDiv.classes().toString()).toBe('wtg-mobile-action-bar');
        });
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgActionBar, {
            global: {
                provide: {
                    darkMode: false,
                },
                plugins: [wtgUi],
            },
            slots: {
                default: 'Action Bar',
            },
            propsData,
        });
    }
});
