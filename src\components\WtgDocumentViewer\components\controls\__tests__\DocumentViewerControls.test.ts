import DocumentViewerControls from '@components/WtgDocumentViewer/components/controls/DocumentViewerControls.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('Document Viewer Controls', () => {
    let wrapper: any;

    it('renders all icon buttons', () => {
        wrapper = mountComponent();
        const buttons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(buttons.length).toBe(4);

        expect(buttons.at(0).props('icon')).toBe('s-icon-thumbnails');
        expect(buttons.at(1).props('icon')).toBe('s-icon-zoom-out');
        expect(buttons.at(2).props('icon')).toBe('s-icon-zoom-in');
        expect(buttons.at(3).props('icon')).toBe('s-icon-file-rotate');
    });

    describe('renders a select field to support scale and zoom', () => {
        it('select field is not filterable', () => {
            wrapper = mountComponent();
            const selectField = wrapper.findComponent({ name: 'WtgSelectField' });
            expect(selectField.props('filterable')).toBe(false);
        });

        it('select field displays zoom percentage', () => {
            wrapper = mountComponent();
            const selectField = wrapper.findComponent({ name: 'WtgSelectField' });
            expect(selectField.props('itemTextShort')).toBe('zoomPercentage');
        });

        it('renders a list of scaling options', async () => {
            wrapper = mountComponent();
            const selectField = wrapper.findComponent({ name: 'WtgSelectField' });
            await selectField.trigger('click');

            const listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(listItems.length).toBe(4);
            expect(listItems.at(0).text()).toBe('Fit to screen');
            expect(listItems.at(0).props('active')).toBeTruthy();
            expect(listItems.at(1).text()).toBe('Fit horizontally');
            expect(listItems.at(1).props('active')).toBeFalsy();
            expect(listItems.at(2).text()).toBe('Fit vertically');
            expect(listItems.at(2).props('active')).toBeFalsy();
            expect(listItems.at(3).text()).toBe('Actual size');
            expect(listItems.at(3).props('active')).toBeFalsy();

            await listItems.at(1).trigger('click');

            expect(wrapper.emitted()['update-scale']?.length).toBe(1);
            expect(wrapper.emitted()['update-scale'][0][0]).toBe('hor');
        });

        it('updates zoomScaleOptions when zoomPercentage prop is changed', async () => {
            wrapper = mountComponent();
            const selectField = wrapper.findComponent({ name: 'WtgSelectField' });
            expect(selectField.props('items')).toStrictEqual([
                { label: 'Fit to screen', scaleMode: 'scr', zoomPercentage: '' },
                { label: 'Fit horizontally', scaleMode: 'hor', zoomPercentage: '' },
                { label: 'Fit vertically', scaleMode: 'ver', zoomPercentage: '' },
                { label: 'Actual size', scaleMode: 'act', zoomPercentage: '' },
            ]);
            expect(selectField.props('modelValue')).toStrictEqual({
                label: 'Fit to screen',
                scaleMode: 'scr',
                zoomPercentage: '',
            });

            await wrapper.setProps({ zoomPercentage: '74%' });

            expect(selectField.props('items')).toStrictEqual([
                { label: 'Fit to screen', scaleMode: 'scr', zoomPercentage: '74%' },
                { label: 'Fit horizontally', scaleMode: 'hor', zoomPercentage: '' },
                { label: 'Fit vertically', scaleMode: 'ver', zoomPercentage: '' },
                { label: 'Actual size', scaleMode: 'act', zoomPercentage: '' },
            ]);
            expect(selectField.props('modelValue')).toStrictEqual({
                label: 'Fit to screen',
                scaleMode: 'scr',
                zoomPercentage: '74%',
            });
        });
    });

    it('emits thumbnails event on thumbnails button click', async () => {
        wrapper = mountComponent();
        expect(wrapper.emitted()['thumbnails']).toBeFalsy();
        await wrapper.findAllComponents({ name: 'WtgIconButton' }).at(0).trigger('click');
        expect(wrapper.emitted()['thumbnails']).toBeTruthy();
    });

    it('increases/decreases zoom by 25 on zoom in/out button clicks', async () => {
        wrapper = mountComponent();

        await wrapper.findAllComponents({ name: 'WtgIconButton' }).at(2).trigger('click');

        expect(wrapper.emitted()['update-zoom']?.length).toBe(1);
        expect(wrapper.emitted()['update-zoom'][0][0]).toBe('25%');

        await wrapper.findAllComponents({ name: 'WtgIconButton' }).at(2).trigger('click');

        expect(wrapper.emitted()['update-zoom']?.length).toBe(2);
        expect(wrapper.emitted()['update-zoom'][1][0]).toBe('50%');

        await wrapper.findAllComponents({ name: 'WtgIconButton' }).at(1).trigger('click');

        expect(wrapper.emitted()['update-zoom']?.length).toBe(3);
        expect(wrapper.emitted()['update-zoom'][2][0]).toBe('25%');
    });

    it('increases rotation value by 90 on rotate button click', async () => {
        wrapper = mountComponent();
        const rotateButton = wrapper.findAllComponents({ name: 'WtgIconButton' }).at(3);

        await rotateButton.trigger('click');

        expect(wrapper.emitted()['update-rotate']?.length).toBe(1);
        expect(wrapper.emitted()['update-rotate'][0][0]).toBe(90);

        await rotateButton.trigger('click');

        expect(wrapper.emitted()['update-rotate']?.length).toBe(2);
        expect(wrapper.emitted()['update-rotate'][1][0]).toBe(180);

        await rotateButton.trigger('click');

        expect(wrapper.emitted()['update-rotate']?.length).toBe(3);
        expect(wrapper.emitted()['update-rotate'][2][0]).toBe(270);

        await rotateButton.trigger('click');

        expect(wrapper.emitted()['update-rotate']?.length).toBe(4);
        expect(wrapper.emitted()['update-rotate'][3][0]).toBe(0);
    });

    it('emits event when active page is updated in pagination component', async () => {
        wrapper = mountComponent();
        const pagination = wrapper.findComponent({ name: 'DocumentViewerPagination' });
        pagination.vm.$emit('active-page-updated', 3);

        expect(wrapper.emitted()['active-page-updated']?.length).toBe(1);
        expect(wrapper.emitted()['active-page-updated'][0][0]).toBe(3);
    });

    it('sets default scale mode to fit to screen when scaleConfig is undefined', async () => {
        wrapper = mountComponent();
        expect(wrapper.props('scalingMode')).toBe('scr');

        const selectField = wrapper.findComponent({ name: 'WtgSelectField' });
        expect(selectField.props('modelValue')).toStrictEqual({
            label: 'Fit to screen',
            scaleMode: 'scr',
            zoomPercentage: '',
        });
    });

    it('does not render zoom/scale controls when enableZoom is false', () => {
        wrapper = mountComponent({ props: { enableZoom: false } });
        const selectField = wrapper.findComponent({ name: 'WtgSelectField' });
        expect(selectField.exists()).toBe(false);
        const buttons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(buttons.length).toBe(2);
        expect(buttons.at(0).props('tooltip')).not.toBe('Zoom in');
        expect(buttons.at(1).props('tooltip')).not.toBe('Zoom in');
        expect(buttons.at(0).props('tooltip')).not.toBe('Zoom out');
        expect(buttons.at(1).props('tooltip')).not.toBe('Zoom out');
    });

    it('does not render pagination controls when enablePagination is false', () => {
        wrapper = mountComponent({ props: { enablePagination: false } });
        const paginationComponent = wrapper.findComponent({ name: 'WtgDocumentViewerPagination' });
        expect(paginationComponent.exists()).toBe(false);
    });

    it('does not render rotate icon button when enableRotate is false', () => {
        wrapper = mountComponent({ props: { enableRotate: false } });
        const buttons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(buttons.length).toBe(3);
        expect(buttons.at(0).props('tooltip')).not.toBe('Rotate');
        expect(buttons.at(1).props('tooltip')).not.toBe('Rotate');
        expect(buttons.at(2).props('tooltip')).not.toBe('Rotate');
    });

    it('does not render thumbnail icon button when enableThumbnails is false', () => {
        wrapper = mountComponent({ props: { enableThumbnails: false } });
        const buttons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(buttons.length).toBe(3);
        expect(buttons.at(0).props('tooltip')).not.toBe('Show thumbnails');
        expect(buttons.at(1).props('tooltip')).not.toBe('Show thumbnails');
        expect(buttons.at(2).props('tooltip')).not.toBe('Show thumbnails');
    });
});

function mountComponent({ props = {}, slots = {} } = {}) {
    return mount(DocumentViewerControls, {
        props,
        slots,
        global: {
            plugins: [wtgUi],
        },
    });
}
