import {
    WtgFramework,
    WtgFrameworkMenuItemType,
    WtgFrameworkTask,
    WtgFrameworkTaskEntityStatusDisplayMode,
} from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount, VueWrapper } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import MastheadTitle from '../MastheadTitle.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('masthead-title', () => {
    let application: WtgFramework;

    beforeEach(() => {
        application = reactive(new WtgFramework());
        application.title = 'Portal Title';
        application.menu.push({ id: 'HomeItem', caption: 'Home Item', home: true } as any);
        setApplication(application);

        wtgUi.breakpoint.smAndUp = true;
    });

    test('its name is MastheadTitle', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('MastheadTitle');
    });

    test('it renders with aria-label and role attributes', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.attributes('aria-label')).toBe('Breadcrumbs');
        expect(wrapper.attributes('role')).toBe('navigation');
    });

    test('it will display portal title on desktop', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.text()).toBe('Portal Title');
    });

    test('it sets the href of the link to the portal href', async () => {
        const wrapper = await mountComponentAsync();
        const title = wrapper.find('a');
        expect(title.attributes('href')).toBe('#/index');
    });

    describe('when given a page name', () => {
        beforeEach(() => {
            application.pageHelp = { name: 'Page Name' } as any;
        });

        test('it will display the page name', async () => {
            const wrapper = await mountComponentAsync();
            expect(wrapper.text()).toBe('Portal Title/Page Name');
        });

        test('it will hide the the page name when on the home page', async () => {
            const wrapper = await mountComponentAsync();
            expect(wrapper.text()).toBe('Portal Title/Page Name');
            application.menu[0].active = true;
            await nextTick();

            expect(wrapper.text()).toBe('Portal Title');
        });

        describe('when on mobile', () => {
            beforeEach(() => {
                wtgUi.breakpoint.smAndUp = false;
            });
            afterEach(() => {
                wtgUi.breakpoint.smAndUp = true;
            });

            test('it will hide the portal title', async () => {
                const wrapper = await mountComponentAsync();
                expect(wrapper.text()).toBe('Page Name');
            });
        });
    });

    describe('when mobile framework is active and on mobile screen size', () => {
        let wrapper: VueWrapper<any>;

        beforeEach(async () => {
            application.currentTask = new WtgFrameworkTask();
            wtgUi.breakpoint.smAndUp = false;
            wrapper = await mountComponentAsync();
        });

        describe('caption', () => {
            test('it renders the entity name if there is one', async () => {
                application.currentTask!.entityName = 'Test Entity';
                await wrapper.vm.$nextTick();
                const mastheadTitle = wrapper.find("[data-testid='mobile-masthead-caption']");
                expect(mastheadTitle.text()).toBe('Test Entity');
                application.currentTask!.entityName = '';
            });

            test('it renders the portal title if there is no entity name and on the home page', async () => {
                application.menu = [
                    {
                        active: true,
                        home: true,
                        id: 'home',
                        caption: 'Home',
                        action: WtgFrameworkMenuItemType.None,
                    },
                ];
                application.title = 'Test Page';
                await wrapper.vm.$nextTick();
                const mastheadTitle = wrapper.find("[data-testid='mobile-masthead-caption']");
                expect(mastheadTitle.text()).toBe('Test Page');
                application.menu = [];
                application.title = '';
            });

            test('it renders the current task title if there is no entity name and not on the home page', async () => {
                application.currentTask!.title = 'Test Page';
                await wrapper.vm.$nextTick();
                const mastheadTitle = wrapper.find("[data-testid='mobile-masthead-caption']");
                expect(mastheadTitle.text()).toBe('Test Page');
                application.currentTask!.title = '';
            });

            test('it renders the page help name if there is no entity name, no current task and not on the home page', async () => {
                application.pageHelp = { name: 'Test Page' } as any;
                const oldCurrentTask = application.currentTask;
                application.currentTask = null;
                await wrapper.vm.$nextTick();
                const mastheadTitle = wrapper.find("[data-testid='mobile-masthead-caption']");
                expect(mastheadTitle.text()).toBe('Test Page');
                application.pageHelp!.name = '';
                application.currentTask = oldCurrentTask;
            });
        });

        describe('status', () => {
            beforeEach(() => {
                application.currentTask!.currentStatus = {
                    code: 'TEST',
                    label: 'Test Label',
                    sentiment: 'info',
                    variant: 'fill',
                };

                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Hidden;

                application.currentTask!.showTaskTitle = true;
            });

            test('it will not render the task current status when status label is falsy', async () => {
                application.currentTask!.currentStatus!.label = '';
                await wrapper.vm.$nextTick();
                const statusWrapper = wrapper.find("[data-testid='mobile-masthead-status']");
                expect(statusWrapper.exists()).toBe(false);
            });

            test('it will render the task current status', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.ReadOnly;
                await wrapper.vm.$nextTick();
                const statusWrapper = wrapper.find("[data-testid='mobile-masthead-status']");
                expect(statusWrapper.exists()).toBe(true);
                expect(statusWrapper.text()).toBe('Test Label');
            });

            test('it will not render the task current status when currentStatus is undefined', async () => {
                application.currentTask!.currentStatus = undefined;
                await wrapper.vm.$nextTick();
                const statusWrapper = wrapper.find("[data-testid='mobile-masthead-status']");
                expect(statusWrapper.exists()).toBe(false);
            });

            test('it will not render the task current status when status display mode is Hidden', async () => {
                application.currentTask!.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Hidden;
                await wrapper.vm.$nextTick();
                const statusWrapper = wrapper.find("[data-testid='mobile-masthead-status']");
                expect(statusWrapper.exists()).toBe(false);
            });
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(MastheadTitle) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(MastheadTitle);
    }
});
