import WtgFlag from '@components/WtgFlag/WtgFlag.vue';
import { StoryObj } from '@storybook/vue3';
import FlagsPage from '../stories/pages/flagsPage.vue';
import { SupportedFlags } from './pages/SupportedFlags';

type Story = StoryObj<typeof WtgFlag>;

export default {
    title: 'Components/Flag',
    component: WtgFlag,
    parameters: {
        docs: {
            description: {
                component: 'The Flag component is an easy-to-use utility component for displaying flags.',
            },
        },
    },
    render: (args) => ({
        components: { WtgFlag },
        setup: () => ({ args }),
        methods: {},
        template: `<WtgFlag v-bind="args"></WtgFlag>`,
    }),
    argTypes: {
        size: {
            options: ['big', 'normal', 'small'],
            control: 'select',
        },
        country: {
            options: SupportedFlags.map((flag) => flag.code),
            control: 'select',
        },
    },
} as Story;

export const Default: Story = {
    args: {
        country: 'au',
        size: 'big',
    },
    render: (args) => ({
        components: { WtgFlag },
        setup: () => ({ args }),
        template: `<WtgFlag v-bind="args"></WtgFlag>`,
    }),
};

export const FlagSearch: Story = {
    render: () => ({
        components: { FlagsPage },
        template: '<flags-page></flags-page>',
    }),
};
