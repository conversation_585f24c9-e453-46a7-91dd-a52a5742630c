<template>
    <div class="wtg-entity-actions" :style="measurableStyles">
        <slot />
    </div>
</template>

<script setup lang="ts">
import { makeMeasureProps, useMeasure } from '@composables/measure';

//
// Properties
//
const props = defineProps({
    ...makeMeasureProps(),
});

//
// Composables
//
const { measurableStyles } = useMeasure(props);
</script>

<style lang="scss">
.wtg-entity-actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: var(--s-spacing-xs, 2px);
    border: 1px solid var(--s-neutral-border-weak-default, '#6B6B6880');
    border-radius: var(--s-radius-s, 4px);
}
</style>
