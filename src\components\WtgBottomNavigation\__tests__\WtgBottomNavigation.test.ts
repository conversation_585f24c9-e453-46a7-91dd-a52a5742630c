import { WtgApp } from '@components/WtgApp';
import WtgBottomNavigation from '@components/WtgBottomNavigation/WtgBottomNavigation.vue';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { VBottomNavigation } from 'vuetify/components';
import WtgUi from '../../../WtgUi';

const wtgUi = new WtgUi();
enableAutoUnmount(afterEach);

describe('WtgBottomNavigation', () => {
    test('it renders a VBottomNavigation component', async () => {
        const wrapper = await mountComponentAsync();
        const nav = wrapper.findComponent(VBottomNavigation);
        expect(nav.exists()).toBe(true);
    });

    test('it passes its props correctly', async () => {
        const wrapper = await mountComponentAsync({
            props: {
                modelValue: true,
            },
        });

        const nav = wrapper.findComponent(VBottomNavigation);
        expect(nav.props('modelValue')).toBe(true);
    });

    test('it renders the default slot', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                default: '<div class="test-content">Navigation Content</div>',
            },
            props: { modelValue: true },
        });

        const nav = wrapper.findComponent(VBottomNavigation);
        expect(nav.text()).toBe('Navigation Content');
    });

    test('it listens to the update:modelValue event on inner VBottomNavigation component, which is used to communicate the visibility state through v-model', async () => {
        const wrapper = await mountComponentAsync();
        const nav = wrapper.findComponent(WtgBottomNavigation);
        await nav.findComponent({ name: 'VBottomNavigation' }).vm.$emit('update:modelValue', true);

        expect(nav.emitted('update:modelValue')?.length).toBe(1);
        expect(nav.emitted('update:modelValue')?.[0][0]).toBe(true);
    });

    async function mountComponentAsync({ props = {}, slots = {} } = {}) {
        const wrapper = mount(
            {
                template:
                    '<wtg-app><wtg-bottom-navigation v-bind="$attrs"><template v-for="(_, name) in $slots" v-slot:[name]="slotData"><slot :name="name" v-bind="slotData" /></template></wtg-bottom-navigation></wtg-app>',
                components: { WtgApp, WtgBottomNavigation },
            },
            {
                propsData: {
                    ...props,
                },
                slots,
                global: {
                    plugins: [wtgUi],
                },
            }
        );
        await flushPromises();
        return wrapper;
    }
});
