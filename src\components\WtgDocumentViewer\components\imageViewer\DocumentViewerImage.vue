<template>
    <div class="image-viewer">
        <div class="image-container">
            <img v-if="imageSrc" ref="imageRef" :src="imageSrc" :alt="fileType" />
            <div ref="slotContainerRef" class="slot-container">
                <slot :image-data="imageData"></slot>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import UTIF from 'utif';
import { computed, nextTick, onMounted, PropType, ref, watch } from 'vue';
import { ImageData, Scale, ScalingMode } from '../../types';

const props = defineProps({
    imageSource: {
        type: ArrayBuffer,
        default: () => new ArrayBuffer(0),
    },
    fileType: {
        type: String,
        default: undefined,
    },

    imageRotation: {
        type: Number,
        default: 0,
    },
    scaleConfig: {
        type: Object as PropType<Scale>,
        default: undefined,
    },
    zoomPercentage: {
        type: String,
        default: '100%',
    },
    imageViewerWrapHeight: {
        type: Number,
        default: 0,
    },
    imageViewerWrapWidth: {
        type: Number,
        default: 0,
    },
});

const slotContainerRef = ref<HTMLDivElement | null>(null);
const imageRef = ref<HTMLImageElement | null>(null);
const imageSrc = ref<string>('');
const internalScaleConfig = ref<Scale>({
    scaleMode: ScalingMode.FitToScreen,
    scaleValue: 0,
    resetScale: false,
});
const internalZoomPercentage = ref(props.zoomPercentage);
const initialImageWidth = ref(0);
const initialImageHeight = ref(0);

const imageWidth = ref(0);
const imageHeight = ref(0);

const imageData = computed<ImageData>(() => ({
    imageWidth: imageWidth.value,
    imageHeight: imageHeight.value,
}));

const emit = defineEmits<{
    'zoom-percentage-updated': [string];
    'update-scale': [Scale];
}>();

const loadTiff = async () => {
    // Decode the TIFF file with UTIF.js
    const tiffImages = UTIF.decode(props.imageSource);
    const firstPage = tiffImages[0]; // Render the first page if multi-page

    // Decode the image data
    UTIF.decodeImage(props.imageSource, firstPage);
    const rgba = UTIF.toRGBA8(firstPage); // Convert to RGBA format

    // Create a canvas and draw the RGBA data onto it
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = firstPage.width;
    canvas.height = firstPage.height;

    const imageData = context && context.createImageData(firstPage.width, firstPage.height);
    imageData && imageData.data.set(rgba);
    context && imageData && context.putImageData(imageData, 0, 0);

    // Convert canvas to data URL in PNG format
    imageSrc.value = canvas.toDataURL('image/png');
};

const loadImage = async () => {
    if (!props.imageSource.byteLength) return;

    const blob = new Blob([props.imageSource], { type: props.fileType });
    imageSrc.value = URL.createObjectURL(blob);

    nextTick(() => {
        if (imageRef.value) {
            imageRef.value.onload = () => {
                initialImageWidth.value = imageRef.value!.clientWidth;
                initialImageHeight.value = imageRef.value!.clientHeight;
                updateScalingMode(props.scaleConfig?.scaleMode || ScalingMode.FitHorizontally);
                updateInternalZoomPercentage();
            };
        }
    });
};

const setImageDimensions = (scaleValue: number) => {
    if (!imageRef.value) return;
    const scaledWidth = Math.floor(imageRef.value.clientWidth * scaleValue);
    const scaledHeight = Math.floor(imageRef.value.clientHeight * scaleValue);

    imageWidth.value = scaledWidth;
    imageHeight.value = scaledHeight;
};

const fitVertically = async () => {
    const scaleOffset = 125; // control headers
    internalScaleConfig.value.scaleValue = (props.imageViewerWrapHeight - scaleOffset) / initialImageHeight.value;
};

const actualSize = async () => {
    if (imageRef.value) {
        const naturalWidth = imageRef.value?.naturalWidth;
        internalScaleConfig.value.scaleValue = naturalWidth / initialImageWidth.value;
    }
};

const fitToScreen = async () => {
    if (initialImageWidth.value > 0 && initialImageHeight.value > 0) {
        const containerWidth = props.imageViewerWrapWidth;
        const containerHeight = props.imageViewerWrapHeight;
        const scaleX = containerWidth / initialImageWidth.value;
        const scaleY = containerHeight / initialImageHeight.value;
        internalScaleConfig.value.scaleValue = Math.min(scaleX, scaleY);
    }
};

const updateScalingMode = async (newScalingMode: ScalingMode) => {
    switch (newScalingMode) {
        case ScalingMode.FitHorizontally:
            internalScaleConfig.value.scaleValue = 1;
            break;
        case ScalingMode.FitVertically:
            await fitVertically();
            break;
        case ScalingMode.ActualSize:
            await actualSize();
            break;
        case ScalingMode.FitToScreen:
            await fitToScreen();
            break;
    }
    internalScaleConfig.value.scaleMode = newScalingMode;
    emit('update-scale', internalScaleConfig.value);
    renderImage();
};

const updateInternalZoomPercentage = () => {
    internalZoomPercentage.value = `${Math.round(internalScaleConfig.value.scaleValue * 100)}%`;
    emit('zoom-percentage-updated', internalZoomPercentage.value);
};

watch(
    () => props.zoomPercentage,
    (newZoomPercentage: string) => {
        if (internalZoomPercentage.value !== newZoomPercentage) {
            if (
                parseInt(internalZoomPercentage.value.replace('%', ''), 10) <
                parseInt(newZoomPercentage.replace('%', ''), 10)
            ) {
                internalScaleConfig.value.scaleValue += 0.2;
            } else {
                internalScaleConfig.value.scaleValue = Math.max(0.2, internalScaleConfig.value.scaleValue - 0.2);
            }
            emit('update-scale', internalScaleConfig.value);
            internalZoomPercentage.value = newZoomPercentage;
        }

        renderImage();
    }
);

watch(
    () => props.scaleConfig,
    async (newScaleConfig: Scale | undefined) => {
        if (newScaleConfig) {
            if (newScaleConfig.scaleMode !== internalScaleConfig.value.scaleMode || newScaleConfig.resetScale) {
                await updateScalingMode(newScaleConfig.scaleMode);
                updateInternalZoomPercentage();
            }
        }
    },
    { deep: true }
);

watch(
    () => props.imageRotation,
    () => {
        renderImage();
    }
);

const renderImage = () => {
    if (imageRef.value) {
        const scaleValue = internalScaleConfig.value.scaleValue;
        imageRef.value.style.transform = `scale(${scaleValue}) rotate(${props.imageRotation}deg)`;

        if (slotContainerRef.value) {
            let offsetX = 0;
            let offsetY = 0;

            slotContainerRef.value.style.transformOrigin = 'top left';
            if (scaleValue < 1) {
                offsetX = (imageRef.value.clientWidth * (1 - scaleValue)) / 2;
                offsetY = (imageRef.value.clientHeight * (1 - scaleValue)) / 2;
                slotContainerRef.value.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scaleValue})`;
            }
        }

        setImageDimensions(scaleValue);
    }
};

const loadImageByType = () => {
    if (props.fileType === 'tiff') {
        loadTiff();
    } else {
        loadImage();
    }
};

watch(
    () => props.imageSource,
    async () => {
        loadImageByType();
    }
);

onMounted(() => {
    loadImageByType();
});

defineExpose({
    initialImageWidth,
    initialImageHeight,
    imageWidth,
    imageHeight,
});
</script>

<style scoped lang="scss">
.image-viewer {
    display: flex;
    justify-content: center;
    align-items: center;

    .image-container {
        position: relative;
        margin: 0 auto 12px auto;
        width: fit-content;

        img {
            position: relative;
            display: block;
            max-width: 100%;
            max-height: 100%;
            transition: transform 0.3s ease-in-out, width 0.3s ease-in-out, height 0.3s ease-in-out;
        }

        .slot-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }
}
</style>
