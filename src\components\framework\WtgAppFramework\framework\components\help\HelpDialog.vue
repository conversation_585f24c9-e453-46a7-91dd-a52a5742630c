<template>
    <WtgHelpDialog
        :model-value="!!pageHelp?.visible"
        :always-open-help="!!pageHelp?.alwaysOpenHelp"
        :loading="!!pageHelp?.loading"
        :loading-text="pageHelp?.captions?.loading ?? 'Loading page help'"
        :title="pageHelp?.name ?? ''"
        :help-content="pageHelp?.text ?? ''"
        :dialog-title="pageHelp?.captions?.title ?? 'Help'"
        :always-open-help-caption="pageHelp?.captions?.alwaysOpenHelp ?? 'Always open help'"
        :close-tooltip="pageHelp?.captions?.close ?? 'Close'"
        @update:model-value="onCloseClicked"
        @update:always-open="onAlwaysOpenHelpChanged"
    >
    </WtgHelpDialog>
</template>

<script setup lang="ts">
import WtgHelpDialog from '@components/framework/WtgHelpDialog';
import { useApplication } from '@composables/application';
import { computed, watch } from 'vue';

const application = useApplication();

const pageHelp = computed(() => {
    return application.pageHelp;
});

watch(
    () => !!pageHelp.value?.visible,
    (value: boolean) => {
        if (value && pageHelp.value) {
            pageHelp.value.onPageHelpOpening();
        }
    },
    { immediate: true }
);

const onCloseClicked = (): void => {
    if (pageHelp.value) {
        pageHelp.value.onPageHelpClosing();
    }
    application.closePageHelp();
};

const onAlwaysOpenHelpChanged = (value: boolean): void => {
    if (pageHelp.value) {
        pageHelp.value.onAlwaysOpenHelpChanged(value);
    }
};
</script>
