import { WtgFramework } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount, VueWrapper } from '@vue/test-utils';
import { h, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import ConfigureMenu from '../ConfigureMenu.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('configure-menu', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.captions = {
            configure: 'Configure',
            theme: 'Theme',
        } as any;
        application.user.onThemeConfiguration = jest.fn();
        setApplication(application);

        wtgUi.breakpoint.mdAndDown = false;
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is Configure Menu', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('ConfigureMenu');
    });

    test('it will render a list item called Configure', async () => {
        const wrapper = await mountComponentAsync();
        const titles = wrapper.findAll('.wtg-list-item__container');
        expect(titles.at(0)?.text()).toBe('Configure');
    });

    test('it will call the onThemeConfiguration click handler when theme is clicked', async () => {
        const wrapper: VueWrapper<any> = await mountComponentAsync();
        const titles = wrapper.findComponent({ name: 'WtgListItem' });
        await titles.trigger('click');

        expect(application.user.onThemeConfiguration).toHaveBeenCalledTimes(1);
    });

    describe('Accessibility', () => {
        test('it will render a list item with aria properties', async () => {
            const wrapper = await mountComponentAsync();
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            expect(listItem.attributes('role')).toBe('menuitem');
            expect(listItem.attributes('aria-label')).toBe(application.captions.configure);
            expect(listItem.attributes('aria-haspopup')).toBe('menu');
        });
    });

    describe('when on mobile', () => {
        beforeEach(() => {
            wtgUi.breakpoint.mdAndDown = true;
        });

        test('it when render the configure menu item component', async () => {
            const wrapper = await mountComponentAsync();
            const menuItem = wrapper.findComponent({ name: 'ConfigureInlineMenu' });
            expect(menuItem.exists()).toBe(true);
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(ConfigureMenu) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(ConfigureMenu);
    }
});
