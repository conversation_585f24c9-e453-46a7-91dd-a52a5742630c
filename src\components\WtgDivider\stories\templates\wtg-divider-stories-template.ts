export const DividerSandboxTemplate = `
<h1>Default</h1>
<wtg-divider>Hello World</wtg-divider>
<h1>Variant: Solid</h1>
<wtg-divider variant="solid">Hello World</wtg-divider>
<h1>Variant: Dashed</h1>
<wtg-divider variant="dashed">Hello World</wtg-divider>

<h1>Vertical: False</h1>
<div style="display: flex; flex-direction: column; block-size:10em; justify-content: stretch; align-items: center;">
<wtg-divider>Hello World</wtg-divider>
</div>
<h1>Vertical: True</h1>
<div style="display: flex; flex-direction: column; block-size:10em; justify-content: stretch; align-items: center;">
<wtg-divider vertical>Hello World</wtg-divider>
</div>
`;
