import { WtgFramework, WtgFrameworkAbout } from '@components/framework/types';
import WtgAppFramework from '@components/framework/WtgAppFramework/WtgAppFramework.vue';
import { enableAutoUnmount, flushPromises, mount, type VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

window.URL.createObjectURL = jest.fn().mockReturnValue('dummyBlob');

describe('WtgAppFramework', () => {
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is WtgAppFramework', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('WtgAppFramework');
    });

    test('it renders the framework', async () => {
        const wrapper = await mountComponentAsync();
        const masthead = wrapper.findComponent({ name: 'Framework' });
        expect(masthead.exists()).toBe(true);
    });

    describe('Properties', () => {
        let application: WtgFramework;
        let wrapper: VueWrapper<any>;

        beforeEach(async () => {
            wrapper = await mountComponentAsync();
            application = wrapper.vm.application;
        });

        test('its set the application about information based on teh about prop', async () => {
            const about: Partial<WtgFrameworkAbout> = {
                portalURL: 'https://testrig/portals/test',
            };
            await wrapper.setProps({ about });

            expect(application.about.portalURL).toBe(about.portalURL);
        });

        test('it sets the application title based on the titles prop', async () => {
            await wrapper.setProps({ title: 'Test Title' });

            expect(application.title).toBe('Test Title');
        });
    });

    test('it sets the application hideBackButton when the hideBackButton prop is changed', async () => {
        const wrapper = await mountComponentAsync();
        const application = (wrapper.vm as any).application;

        expect(application.hideBackButton).toBe(false);

        await wrapper.setProps({ hideBackButton: true });
        expect(application.hideBackButton).toBe(true);

        await wrapper.setProps({ hideBackButton: false });
        expect(application.hideBackButton).toBe(false);
    });

    async function mountComponentAsync({ props = {}, slots = {} } = {}) {
        const wrapper = mount(WtgAppFramework, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper;
    }
});
