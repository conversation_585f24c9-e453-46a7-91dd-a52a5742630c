import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgFlag from './WtgFlag.stories.ts';

<Meta of={WtgFlag} />

<div className="component-header">
    <h1>Flag</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th>
                <img className="status-chip" src={statusPlanned}></img>
            </th>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<div className="component-description">
    The flag component is a utility component for displaying country flags.

</div>

## API

<Canvas className="canvas-preview" of={WtgFlag.Default} />
<Controls of={WtgFlag.Default} sort={'alpha'} />

## Flag search

Click on the flag to copy code to clipboard.

<Story of={WtgFlag.FlagSearch} />

<footer>
<hr></hr> 
💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)

</footer>
