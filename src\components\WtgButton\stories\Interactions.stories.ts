import WtgButton from '@components/WtgButton/WtgButton.vue';
import { expect, userEvent, within } from '@storybook/test';
import { Meta, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgButton>;

const meta: Meta<typeof WtgButton> = {
    title: 'Components/Button/Interactions',
    component: WtgButton,
    parameters: {
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgButton },
        setup: () => ({ args }),
        template: `
            <WtgButton v-bind="args">
                {{ args.default }}
            </WtgButton>
        `,
    }),
};
export default meta;

export const PressEnter: Story = {
    args: {
        default: 'Click me',
    },
    play: async ({ step }) => {
        const canvas = within(document.body);
        const button = canvas.getByRole('button', { name: /click me/i });

        let clicked = false;
        button.addEventListener('click', () => {
            clicked = true;
        });

        await step('Focus the button', async () => {
            await userEvent.tab();
            expect(document.activeElement).toBe(button);
        });

        await step('Press Enter to trigger the button', async () => {
            await userEvent.keyboard('{Enter}');
            expect(clicked).toBe(true);
        });
    },
};

export const PressSpace: Story = {
    args: {
        default: 'Click me',
    },
    play: async ({ step }) => {
        const canvas = within(document.body);
        const button = canvas.getByRole('button', { name: /click me/i });

        let clicked = false;
        button.addEventListener('click', () => {
            clicked = true;
        });

        await step('Focus the button', async () => {
            await userEvent.tab();
            expect(document.activeElement).toBe(button);
        });

        await step('Press Space to trigger the button', async () => {
            await userEvent.keyboard('[Space]');
            expect(clicked).toBe(true);
        });
    },
};

export const DisabledButton: Story = {
    args: {
        default: 'Disabled',
        disabled: true,
    },
    play: async ({ step }) => {
        const canvas = within(document.body);
        const button = canvas.getByRole('button', { name: /disabled/i });

        await step('Verify the button is not focusable', async () => {
            await userEvent.tab();
            expect(document.activeElement).not.toBe(button);
        });

        await step('Verify the button is not actionable', async () => {
            let clicked = false;
            button.addEventListener('click', () => {
                clicked = true;
            });
            await userEvent.click(button);
            expect(clicked).toBe(false);
        });
    },
};

export const FocusOutline: Story = {
    args: {
        default: 'Focus me',
    },
    play: async ({ step }) => {
        const canvas = within(document.body);
        const button = canvas.getByRole('button', { name: /focus me/i });

        await step('Focus the button', async () => {
            await userEvent.tab();
            expect(document.activeElement).toBe(button);
        });

        await step('Verify the button has a visible focus outline', async () => {
            const outline = getComputedStyle(button).outlineColor;
            expect(outline).not.toBe('transparent');
        });
    },
};
