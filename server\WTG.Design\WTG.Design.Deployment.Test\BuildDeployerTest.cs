using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Dat.Integration;
using Dat.Integration.Deployment;
using Moq;
using NUnit.Framework;

namespace WTG.Design.Deployment.Test
{
	public class BuildDeployerTest
	{
		[Test]
		public void DoesNotSupportDeployOnDemand()
		{
			Assert.That(
				() => deployer.DeployOnDemand(default, default, default, default),
				Throws.InstanceOf<NotSupportedException>());
		}

		[Test]
		public void AutoDeployLatestBuild_ShouldDeployFiles()
		{
			var expectedServerName = "au2co-siis-401a.wtg.zone";
			deployer.AutoDeployLatestBuild(default, default, default, mockBinPath);

			var actualCommand = processInvoker.AllInvokedCommands();
			var expectedCommand = new[]
			{
				$@"""{expectedMsDeployPath}"" -verb:sync -source:contentPath=""{mockBinPath}\dist"" -dest:contentPath=""design.wtg.zone"",wmsvc={expectedServerName},userName={Configuration.Username},password=MySecretPassword -allowUntrusted -skip:objectName=dirPath,absolutePath=testrig$,skipAction=delete",
			};
			Assert.That(actualCommand, Is.EqualTo(expectedCommand));
		}

		[Test]
		public void AutoDeployLatestBuild_GivenInvalidBinPath_ShouldThrow()
		{
			var randomName = Guid.NewGuid().ToString();
			var expectedMessage = $@"Error when deploying Design site. The source path ""C:\{randomName}\dist"" does not exist.";
			Assert.That(
				() => deployer.AutoDeployLatestBuild(default, default, default, $"C:\\{randomName}"),
				Throws.InstanceOf<InvalidOperationException>().With.Message.EqualTo(expectedMessage));
		}

		[Test]
		public void AutoDeployTestedShelf_ShouldDeployFiles()
		{
			var expectedServerName = "au2co-siis-401a.wtg.zone";
			var taskInfo = new TaskInfo(default, default, "WebComponentsTestRigName: WI01234567\r\nother irrelevant task notes");
			deployer.AutoDeployTestedShelf(default, default, default, mockBinPath, taskInfo);

			var actualCommand = processInvoker.AllInvokedCommands();
			var expectedCommand = new[]
			{
				$@"""{expectedMsDeployPath}"" -verb:sync -source:contentPath=""{mockBinPath}\dist"" -dest:contentPath=""design.wtg.zone\testrig\WI01234567"",wmsvc={expectedServerName},userName={Configuration.Username},password=MySecretPassword -allowUntrusted -skip:objectName=dirPath,absolutePath=testrig$,skipAction=delete",
			};
			Assert.That(actualCommand, Is.EqualTo(expectedCommand));
		}

		[Test]
		public void AutoDeployTestedShelf_GivenNoTestRigName_ShouldThrow()
		{
			var taskInfo = new TaskInfo(default, default, "IrrelevantParameter: TestRigName\r\nother irrelevant task notes");
			Assert.That(
				() => deployer.AutoDeployTestedShelf(default, default, default, mockBinPath, taskInfo),
				Throws.InstanceOf<DeploymentCancelledException>().With.Message.EqualTo("WebComponentsTestRigName not found in task notes."));
		}

		[Test]
		public void AutoDeployTestedShelf_GivenMultipleTestRigName_ShouldThrow()
		{
			var taskInfo = new TaskInfo(default, default, "WebComponentsTestRigName: TestRigName\r\nWebComponentsTestRigName: WI01234567\r\nother irrelevant task notes");
			Assert.That(
				() => deployer.AutoDeployTestedShelf(default, default, default, mockBinPath, taskInfo),
				Throws.InstanceOf<DeploymentCancelledException>().With.Message.EqualTo("More than one WebComponentsTestRigName found in task notes."));
		}

		[Test]
		public void AutoDeployTestedShelf_GivenInvalidTestRigName_ShouldThrow()
		{
			var taskInfo = new TaskInfo(default, default, "WebComponentsTestRigName: ..\r\nother irrelevant task notes");
			Assert.That(
				() => deployer.AutoDeployTestedShelf(default, default, default, mockBinPath, taskInfo),
				Throws.InstanceOf<DeploymentCancelledException>().With.Message.EqualTo("Invalid WebComponentsTestRigName, test rig name must only contain letters and numbers."));
		}

		BuildDeployer deployer;
		StubRecordingProcessInvoker processInvoker;
		Mock<ITaskLogger> loggerMock;
		string mockBinPath;
		string expectedMsDeployPath;

		[SetUp]
		public void SetUp()
		{
			mockBinPath = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName());
			Directory.CreateDirectory(Path.Combine(mockBinPath, "dist"));

			loggerMock = new Mock<ITaskLogger>(MockBehavior.Strict);
			loggerMock.Setup(l => l.RecordTask("Deploying Design site...")).Returns(new Mock<IDisposable>().Object).Verifiable();

			processInvoker = new StubRecordingProcessInvoker();
			deployer = new BuildDeployer(
				loggerMock.Object,
				processInvoker,
				(p) => p == Configuration.PasswordEncrypted ? "MySecretPassword" : string.Empty);

			expectedMsDeployPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "IIS", "Microsoft Web Deploy V3", "msdeploy.exe");
		}

		[TearDown]
		public void TearDown()
		{
			if (Directory.Exists(mockBinPath))
			{
				Directory.Delete(mockBinPath, recursive: true);
			}
		}

		sealed class StubRecordingProcessInvoker : IStreamingProcessInvoker
		{
			public StubRecordingProcessInvoker()
			{
				commands = new List<string>();
			}

			readonly List<string> commands;

			public IEnumerable<string> AllInvokedCommands() => commands.AsReadOnly();

			public Task<int> ExecuteProcessAsync(string path, string[] arguments)
			{
				var argumentList = new List<string>(arguments.Select(a => Escape(a)));
				commands.Add(string.Join(" ", argumentList.Prepend(Escape(path))));
				return Task.FromResult(0);
			}

			static string Escape(string text) => text.IndexOf(' ') >= 0 ? $"\"{text}\"" : text;
		}
	}
}
