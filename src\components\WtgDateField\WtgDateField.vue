<template>
    <WtgInput v-bind="baseInputProps" promptable :input="input" class="wtg-date-field">
        <span v-if="computedNative">{{ displayValue }}</span>
        <input
            :id="computedId"
            ref="input"
            autocomplete="off"
            :aria-label="ariaLabel"
            :aria-labelledby="ariaLabelledby"
            :disabled="disabled || displayOnly"
            :placeholder="placeholder"
            :readonly="readonly"
            :type="inputType"
            :value="nativeInputValue"
            @blur="onBlur"
            @change="onChange"
            @focus="onFocus"
            @input="onInput"
            @click="onClick"
            @keydown.f4="onF4Key"
        />
        <template #prompter>
            <WtgPopover
                v-model="menuRef"
                :close-on-content-click="false"
                :disabled="computedNative"
                location="bottom end"
                transition="slide-y-transition"
                offset="10px"
                nudge-bottom="4px"
                scroll-strategy="none"
            >
                <template #activator="{ props: activatorProps }: { props: Record<string, any> }">
                    <WtgIcon
                        role="button"
                        tabindex="-1"
                        :aria-hidden="false"
                        v-bind="activatorProps"
                        :aria-label="ariaLabelPrompter"
                        class="wtg-input--interactive-element"
                        :disabled="disabled || displayOnly || readonly"
                        icon="s-icon-calendar-blank"
                        @click="onClick"
                    >
                    </WtgIcon>
                </template>
                <div>
                    <WtgDatePicker
                        :model-value="internalValue"
                        :month-year-only="monthYearOnly"
                        :max="max"
                        :min="min"
                        :formatter="formatter"
                        :today-date-fn="todayDateFn"
                        @update:model-value="onDatePickerChange"
                        @close="menuRef = false"
                    />
                </div>
            </WtgPopover>
        </template>
    </WtgInput>
</template>

<script setup lang="ts">
import WtgDatePicker from '@components/WtgDatePicker';
import WtgIcon from '@components/WtgIcon';
import WtgInput from '@components/WtgInput';
import WtgPopover from '@components/WtgPopover';
import { useFocus } from '@composables/focus';
import { useFramework } from '@composables/framework';
import { basePropsFromProps, makeInputProps } from '@composables/input';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import { makeValidationProps } from '@composables/validation';
import { computed, getCurrentInstance, onMounted, onUnmounted, PropType, ref, watch } from 'vue';
import { WtgDateFormatter } from './types';
import { formatDate, formatMonthYearDisplayValue, formatMonthYearParsedValue, parseDate, today } from './utils';

//
// Properties
//
const props = defineProps({
    /**
     * The formatter used to format and parse date values.
     */
    formatter: {
        type: Object as PropType<WtgDateFormatter>,
        default: undefined,
    },

    /**
     * If true, the date field will only allow selecting a month and year.
     */
    monthYearOnly: {
        type: Boolean,
        default: undefined,
    },

    /**
     * The current value of the date field, used for two-way binding.
     */
    modelValue: {
        type: String,
        default: undefined,
    },

    /**
     * The maximum date that can be selected.
     * Should be a string in the format supported by the formatter.
     */
    max: {
        type: String,
        default: undefined,
    },

    /**
     * The minimum date that can be selected.
     * Should be a string in the format supported by the formatter.
     */
    min: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the native date picker will be used on supported devices.
     */
    native: {
        type: Boolean,
        default: undefined,
    },

    ...makeInputProps(),
    ...makeValidationProps(),
    ...makeLayoutGridColumnProps(),

    /**
     * The value of the date field.
     * @deprecated Use `modelValue` instead.
     */
    inputValue: {
        type: String,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    blur: [e: FocusEvent];
    focus: [e: FocusEvent];
    change: [value: string, isValid: boolean];
    'model-compat:input': [value: string, isValid: boolean];
    'update:modelValue': [value: string, isValid: boolean];
    'update:menu': [value: boolean];
}>();

//
// State
//
const menuRef = ref(false);
const internalValue = ref('');
const nativeInputValue = ref('');
const displayValue = ref('');
const hasPendingChange = ref(false);
const input = ref<HTMLElement>();

//
// Composables
//
const instance = getCurrentInstance();
const { isFocused, focus, blur } = useFocus(props);
const { locale, formatCaption } = useLocale();
const { isMobile } = useFramework();
useLayoutGridColumn(props);

//
// Computed
//
const ariaLabelPrompter = computed(() => formatCaption('dateField.ariaLabelPrompter'));
const computedId = computed(() => props.id || props.inputId || `input-${instance!.uid}`);
const inputType = computed(() => (computedNative.value ? 'date' : 'text'));
const clickable = computed(() => !props.disabled && !props.displayOnly && !props.readonly);

const baseInputProps = computed(() => {
    return {
        ...basePropsFromProps(props),
        filled: nativeInputValue.value !== '',
        id: computedId.value,
        focused: isFocused.value,
        hideMessages: menuRef.value,
    };
});

const computedNative = computed(() => {
    return props.native !== undefined ? props.native : isMobile.value;
});

//
// Watchers
//
watch(
    () => props.modelValue ?? props.inputValue ?? '',
    (value: string) => updateValue(value, false)
);

watch(
    () => props.monthYearOnly,
    () => updateValue(props.modelValue ?? props.inputValue ?? '', false)
);

watch(
    () => locale.value,
    () => (nativeInputValue.value = formatDate(internalValue.value, props.formatter))
);

watch(
    () => menuRef.value,
    () => {
        emit('update:menu', menuRef.value);
    }
);

//
// Event Handlers
//
function onDatePickerChange(value: string) {
    updateValue(value, true);
}

function onChange(e: Event) {
    let value = (e.target as HTMLInputElement).value;
    value = value && value.trim();
    nativeInputValue.value = value;

    let parsedDate = parseDate(value, props.formatter);
    if (parsedDate && props.monthYearOnly) {
        parsedDate = formatMonthYearParsedValue(parsedDate);
    }

    updateValue(parsedDate ?? value, true, parsedDate !== null);
}

function onInput(e: Event) {
    const value = (e.target as HTMLInputElement).value;
    nativeInputValue.value = value;
    hasPendingChange.value = true;
}

function onFocus(e: FocusEvent) {
    emit('focus', e);
    if (!isFocused.value) {
        focus();
    }
}

function onBlur(e: FocusEvent) {
    emit('blur', e);
    blur();
}

function onClick() {
    if (clickable.value) {
        showNativePrompter();
    }
}

function onF4Key(event: KeyboardEvent): void {
    if (clickable.value) {
        if (!computedNative.value) {
            menuRef.value = !menuRef.value;
        } else {
            showNativePrompter();
        }
        event.preventDefault();
        event.stopPropagation();
    }
}

//
// Helpers
//
function updateValue(newValue: string, notify: boolean, isValid = true): void {
    newValue = newValue && newValue.trim();
    if (internalValue.value !== newValue) {
        internalValue.value = newValue;
        if (notify) {
            emit('update:modelValue', internalValue.value, isValid);
            emit('model-compat:input', internalValue.value, isValid);
            emit('change', internalValue.value, isValid);
        }
    }

    if (isValid) {
        const formattedValue = props.monthYearOnly
            ? formatMonthYearDisplayValue(internalValue.value)
            : formatDate(internalValue.value, props.formatter);

        nativeInputValue.value = computedNative.value ? internalValue.value : formattedValue;
        displayValue.value = formattedValue;
    }

    hasPendingChange.value = false;
}

function showNativePrompter() {
    if (computedNative.value && 'showPicker' in HTMLInputElement.prototype) {
        const dateInputField = input.value as HTMLInputElement;
        dateInputField?.showPicker();
    }
}

const todayDateFn = () => today(props.formatter);

//
// Lifecycle
//
onMounted(() => {
    updateValue(props.modelValue ?? props.inputValue ?? '', false);
});

onUnmounted(() => {
    if (hasPendingChange.value) {
        let inputValueValue = nativeInputValue.value;
        inputValueValue = inputValueValue && inputValueValue.trim();
        const parsedDate = parseDate(inputValueValue, props.formatter);
        updateValue(parsedDate ?? nativeInputValue.value, true, parsedDate != null);
    }
    if (menuRef.value) {
        emit('update:menu', false);
    }
});
</script>

<style lang="scss">
.wtg-date-field {
    input[type='date'] {
        opacity: 0;
        position: absolute;
    }
}
</style>
