import { enableAutoUnmount, mount } from '@vue/test-utils';
import { VLazy } from 'vuetify/components/VLazy';
import { WtgLazy } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgLazy', () => {
    test('it renders a VLazy component', () => {
        const wrapper = mountComponent();
        const lazy = wrapper.findComponent(VLazy);
        expect(lazy.exists()).toBe(true);
    });

    test('it passes the default slot content to the VLazy component when triggered', () => {
        const wrapper = mountComponent({ props: { modelValue: true } });
        expect(wrapper.html()).toContain('Some Text');
    });

    function mountComponent({ props = {}, provide = {} } = {}) {
        return mount(WtgLazy, {
            props,
            slots: {
                default: '<span id="text">Some Text</span>',
            },
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
