import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgBreadcrumbsItem } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgBreadcrumbsItem.vue', () => {
    it('renders correctly with default variant', () => {
        const wrapper = mountComponent({ caption: 'Home', variant: 'default' });
        expect(wrapper.text()).toContain('Home');
        expect(wrapper.classes()).toContain('wtg-breadcrumbs__item');
    });

    it('renders correctly with current variant', () => {
        const wrapper = mountComponent({ caption: 'Home', variant: 'current' });
        expect(wrapper.text()).toContain('Home');
        expect(wrapper.classes()).toContain('wtg-breadcrumbs__current-item');
    });

    it('renders an anchor element when href is provided', () => {
        const wrapper = mountComponent({ caption: 'Link', href: 'https://example.com' });
        expect(wrapper.find('a').exists()).toBe(true);
        expect(wrapper.find('a').attributes('href')).toBe('https://example.com');
        expect(wrapper.find('a').text()).toBe('Link');
    });

    it('renders a router-link element when to is provided', () => {
        const wrapper = mountComponent({ caption: 'Router Link', to: '/Router' });
        expect(wrapper.find('router-link').exists()).toBe(true);
        expect(wrapper.find('router-link').attributes('to')).toBe('/Router');
        expect(wrapper.find('router-link').text()).toBe('Router Link');
    });

    it('renders a span element when to and href are not provided', () => {
        const wrapper = mountComponent({ caption: 'Span' });
        expect(wrapper.find('span').exists()).toBe(true);
        expect(wrapper.find('span').text()).toBe('Span');
    });

    const mountComponent = (propsData: any) => {
        return mount(WtgBreadcrumbsItem, {
            propsData: propsData,
            global: { plugins: [wtgUi] },
        });
    };
});
