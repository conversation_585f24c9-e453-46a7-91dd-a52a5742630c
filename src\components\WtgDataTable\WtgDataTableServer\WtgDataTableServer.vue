<template>
    <VDataTableServer
        v-bind="props"
        v-model="model"
        :page="internalPage"
        :items-per-page="internalItemsPerPage"
        :class="computedClasses"
        :no-data-text="computedNoDataText"
        sort-asc-icon="s-icon-arrow-up"
        sort-desc-icon="s-icon-arrow-down"
        @update:options="onUpdateOptions"
    >
        <template #bottom="data">
            <WtgDataPagination
                v-if="!hideDefaultFooter"
                :page="data.page"
                :items-length="itemsLength"
                :items-per-page="data.itemsPerPage"
                :items-per-page-options="itemsPerPageOptions"
                @update:page="onUpdatePage"
                @update:items-per-page="onUpdateItemsPerPage"
                @update:options="onUpdateOptions"
            />
        </template>
        <template v-for="(_, name) in ($slots as {})" #[name]="slotData">
            <slot :name="name" v-bind="slotData as {} || {}" />
        </template>
    </VDataTableServer>
</template>

<script setup lang="ts">
import WtgDataPagination from '@components/WtgDataTable/WtgDataPagination.vue';
import { useLocale } from '@composables/locale';
import { computed, PropType, ref, watch } from 'vue';
import { VDataTableServer } from 'vuetify/components/VDataTable';

const model = defineModel<any[]>({ default: () => [] });

const props = defineProps({
    density: {
        type: String as PropType<null | 'default' | 'comfortable' | 'compact'>,
        default: 'compact',
    },
    editableStyling: {
        type: Boolean,
        default: false,
    },
    fillAvailable: {
        type: Boolean,
        default: false,
    },
    fixedHeader: {
        type: Boolean,
        default: true,
    },
    hideDefaultFooter: {
        type: Boolean,
        default: false,
    },
    items: {
        type: Array,
        default: (): [] => [],
    },
    itemsLength: {
        type: Number,
        default: 0,
    },
    itemsPerPage: {
        type: [Number, String],
        default: '10',
    },
    itemsPerPageOptions: {
        type: Array as PropType<(number | { title: string; value: number })[]>,
        default: undefined,
    },
    itemValue: {
        type: String,
        default: '',
    },
    loading: {
        type: Boolean,
        default: false,
    },
    mobile: {
        type: Boolean,
        default: undefined,
    },
    mobileBreakpoint: {
        type: [Number, String, undefined] as PropType<number | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'>,
        default: 'sm',
    },
    noDataText: {
        type: String,
        default: undefined,
    },
    page: {
        type: [Number, String],
        default: '1',
    },
    search: {
        type: String,
        default: '',
    },
    selectionMode: {
        type: Boolean,
        default: false,
    },
});

const { formatCaption } = useLocale();

const emit = defineEmits<{
    'update:items-per-page': [value: number];
    'update:options': [value: any];
    'update:page': [value: number];
}>();

const internalOptions = ref({});
const internalPage = ref(Number(props.page));
const internalItemsPerPage = ref(Number(props.itemsPerPage));

const computedNoDataText = computed(() => props.noDataText ?? formatCaption('dataTable.noItemsFound'));
const computedClasses = computed(() => [
    {
        'wtg-fill-available-table': props.fillAvailable,
        'wtg-fill': props.fillAvailable,
        'wtg-data-table--selection-mode': props.selectionMode,
        'wtg-data-table--mobile': props.mobile,
    },
    'wtg-data-table',
    props.editableStyling ? 'wtg-data-table-editable' : 'wtg-data-table-display',
]);

watch(
    () => props.page,
    (value) => {
        internalPage.value = Number(value);
    }
);

watch(
    () => props.itemsPerPage,
    (value) => {
        internalItemsPerPage.value = Number(value);
    }
);

function updateOptions(options: any): void {
    internalOptions.value = { ...options };
    emit('update:options', internalOptions.value);
}

const onUpdatePage = (newPage: number): void => {
    if (internalPage.value !== newPage) {
        internalPage.value = newPage;
        emit('update:page', internalPage.value);
        updateOptions({ ...internalOptions.value, page: internalPage.value });
    }
};

const onUpdateItemsPerPage = (newItemsPerPage: number) => {
    if (internalItemsPerPage.value !== newItemsPerPage) {
        internalItemsPerPage.value = newItemsPerPage;
        emit('update:items-per-page', newItemsPerPage);
        updateOptions({ ...internalOptions, itemsPerPage: internalItemsPerPage.value });
    }
};
function onUpdateOptions(options: any): void {
    updateOptions(options);
}
</script>
