import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import info from '../../../storybook/assets/info.png';

import { Meta, Title, Description, Story, Canvas, Controls, ArgTypes } from '@storybook/blocks';
import * as WtgCol from './WtgCol.stories.ts';

<Meta of={WtgCol} />

<div className="component-header">
    <h1>Col</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td></td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    Col is a content holder that must be a direct child of wtg-row. It's use is often implicit as the primary use case
    is implemented as part of wtg-layout-grid. For more information see [grid
    implementation](/docs/guidelines-layout-grid-implementation--overview)
</p>

<div className="banner-warning">

    <div>
        <h4>⚠️ Please note</h4>
        <p>[Layout Grid](/docs/utilities-layout-grid--docs) and [Box](/docs/utilities-box--docs) with layout="grid" or [Panel](/docs/components-panel--docs) with layout="grid" all serve as shortcuts to the grid layout system and serve the purpose of simplifying component template code. Most page layout requirements are handled by these controls. Row and Col are only required for advanced cases.</p>
    </div>

</div>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
