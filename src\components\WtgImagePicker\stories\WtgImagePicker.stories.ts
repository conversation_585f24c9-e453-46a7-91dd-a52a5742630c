import WtgImagePicker from '@components/WtgImagePicker/WtgImagePicker.vue';
import { action } from '@storybook/addon-actions';
import { StoryObj } from '@storybook/vue3';
import { ref } from 'vue';

type Story = StoryObj<typeof WtgImagePicker>;

export default {
    title: 'Icons & Images/Image Picker',
    component: WtgImagePicker,
    parameters: {
        docs: {
            description: {
                component: 'ImagePicker is a helper component whose primary purpose is to select and upload images.',
            },
        },
    },
    render: (args) => ({
        components: { WtgImagePicker },
        setup: () => ({ args }),
        template: `<WtgImagePicker v-bind="args"></WtgImagePicker>`,
    }),
} as Story;

export const Default: Story = {
    args: {
        width: '300',
        src: 'https://picsum.photos/710/500?random',
    },
    render: (args) => ({
        components: { WtgImagePicker },
        setup: () => {
            const src = ref<string | undefined>(args?.src);
            const updateSrc = (file: File) => {
                action('imageSelected')(file);
                if (file) {
                    const fileReader = new FileReader();
                    fileReader.onloadend = (): void => {
                        src.value = fileReader.result
                            ? URL.createObjectURL(new Blob([fileReader.result], { type: 'image/jpg' }))
                            : '';
                    };
                    fileReader.readAsArrayBuffer(file);
                } else {
                    src.value = '';
                }
            };
            return {
                args,
                src,
                updateSrc,
            };
        },
        template: `<div class="ma-4">
                        <WtgImagePicker v-bind="args" :src="src" @imageSelected="updateSrc"></WtgImagePicker>
                    </div>`,
    }),
};
