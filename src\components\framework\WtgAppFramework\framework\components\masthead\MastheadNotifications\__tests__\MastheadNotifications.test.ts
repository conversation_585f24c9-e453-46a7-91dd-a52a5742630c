import { WtgFramework } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import MastheadNotifications from '../MastheadNotifications.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('masthead-notifications', () => {
    let application: WtgFramework;

    beforeEach(() => {
        application = reactive(new WtgFramework());
        setApplication(application);
    });

    test('it is called MastheadNotifications', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('MastheadNotifications');
    });

    test('it renders the Alert Hub button with aria-label, aria-haspopup and aria-expanded="false" attributes', async () => {
        const wrapper = await mountComponentAsync();
        const btn = wrapper.findComponent({ name: 'WtgIconButton' });
        expect(btn.attributes('aria-label')).toBe('Toggle Alert Hub');
        expect(btn.attributes('aria-haspopup')).toBe('menu');
        expect(btn.attributes('aria-expanded')).toBe('false');
    });

    test('it renders the button with aria-expanded="true" attribute when the drawer is visible', async () => {
        const wrapper = await mountComponentAsync();
        const btn = wrapper.findComponent({ name: 'WtgIconButton' });
        await btn.trigger('click');

        expect(application.notificationsDrawer.visible).toBe(true);
        expect(btn.attributes('aria-expanded')).toBe('true');
    });

    test('when clicked it toggles the display of the notifications', async () => {
        const wrapper = await mountComponentAsync();
        const btn = wrapper.findComponent({ name: 'WtgIconButton' });
        await btn.trigger('click');

        expect(application.notificationsDrawer.visible).toBe(true);
        await btn.trigger('click');

        expect(application.notificationsDrawer.visible).toBe(false);
    });

    test('it renders a ghost variant of the icon button on mobile', async () => {
        wtgUi.breakpoint.smAndUp = false;
        const wrapper = await mountComponentAsync();
        const btn = wrapper.findComponent({ name: 'WtgIconButton' });
        expect(btn.props().variant).toBe('ghost');
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(MastheadNotifications) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(MastheadNotifications);
    }
});
