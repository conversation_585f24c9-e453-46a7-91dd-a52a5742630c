import WtgChart from '@components/WtgChart/WtgChart.vue';
import WtgProgressCircular from '@components/WtgProgressCircular';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import 'jest-canvas-mock';
import WtgUi from '../../../WtgUi';

const wtgUi = new WtgUi({
    theme: {
        colors: {
            light: {
                controls: {
                    chart: {
                        background: '#371FE1',
                        backdrop: 'rgba(255, 255, 255, 0.75)',
                        border: '#666',
                        grid: 'rgba(0, 0, 0, 0.6)',
                        text: '#666',
                        ticks: 'rgba(0, 0, 0, 0.6)',
                    },
                },
            },
            dark: {
                controls: {
                    chart: {
                        background: '#2387EE',
                        backdrop: 'rgba(0, 0, 0, 0.75)',
                        border: '#FFF',
                        grid: 'rgba(255, 255, 255, 0.7)',
                        text: '#FFF',
                        ticks: 'rgba(255, 255, 255, 0.7)',
                    },
                },
            },
        },
    },
});

enableAutoUnmount(afterEach);

describe('WtgChart', () => {
    test('its name is WtgChart', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgChart');
    });

    test('it should have max width 100% to prevent layout breaking', () => {
        const wrapper = mountComponent();
        expect(wrapper.find('canvas').attributes('style')).toContain('max-width: 100%;');
    });

    test('its has a type property that it passes on to the Chart', () => {
        const wrapper = mountComponent({
            propsData: {
                type: 'line',
            },
        });

        const vm: any = wrapper.vm;
        const chart = vm.nonReactive.chart;
        expect(chart.config.type).toBe('line');
    });

    test('its has an options property that it passes on to the Chart', () => {
        const wrapper = mountComponent({
            propsData: {
                options: {
                    plugins: {
                        title: {
                            text: 'Chart Title',
                        },
                        tooltip: {
                            callbacks: {
                                label: () => {
                                    return 'Label Tooltip';
                                },
                            },
                        },
                    },
                },
            },
        });

        const vm: any = wrapper.vm;
        const chart = vm.nonReactive.chart;
        expect(chart.config.options.plugins.title.text).toBe('Chart Title');
        expect(chart.config.options.plugins.tooltip.callbacks.label()).toBe('Label Tooltip');
    });

    test('its has a data property that it passes on to the Chart', () => {
        const wrapper = mountComponent({
            propsData: {
                data: {
                    datasets: [
                        {
                            label: 'My First Dataset',
                            data: [65, 59, 80, 81, 56, 55, 40],
                        },
                    ],
                },
            },
        });

        const vm: any = wrapper.vm;
        const chart = vm.nonReactive.chart;
        expect(chart.config.data.datasets[0].label).toBe('My First Dataset');
    });

    test('it has Inter font family', () => {
        const wrapper = mountComponent();

        const vm: any = wrapper.vm;
        const chart = vm.nonReactive.chart;
        const options = chart.config.options;

        expect(options.plugins.title.font.family).toBe('Inter');
        expect(options.plugins.legend.labels.font.family).toBe('Inter');
        expect(options.scales.x.ticks.font.family).toBe('Inter');
        expect(options.scales.y.ticks.font.family).toBe('Inter');
    });

    test('it takes its colors from the options if specified', () => {
        const wrapper = mountComponent({
            propsData: {
                data: {
                    datasets: [
                        {
                            backgroundColor: 'dataset background color',
                            borderColor: 'dataset border color',
                            data: [65, 59, 80, 81, 56, 55, 40],
                        },
                    ],
                },
                options: {
                    plugins: {
                        title: {
                            color: 'title color',
                        },
                        legend: {
                            labels: {
                                color: 'legend-labels color',
                            },
                        },
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'scale-x-grid color',
                            },
                            ticks: {
                                color: 'scale-x-ticks color',
                            },
                        },
                        y: {
                            grid: {
                                color: 'scale-y-grid color',
                            },
                            ticks: {
                                color: 'scale-y-ticks color',
                            },
                        },
                    },
                },
            },
        });

        const vm: any = wrapper.vm;
        const chart = vm.nonReactive.chart;
        const options = chart.config.options;
        const dataset = chart.config.data.datasets[0];

        expect(dataset.backgroundColor).toBe('dataset background color');
        expect(dataset.borderColor).toBe('dataset border color');
        expect(options.plugins.title.color).toBe('title color');
        expect(options.plugins.legend.labels.color).toBe('legend-labels color');
        expect(options.scales.x.grid.color).toBe('scale-x-grid color');
        expect(options.scales.x.ticks.color).toBe('scale-x-ticks color');
        expect(options.scales.y.grid.color).toBe('scale-y-grid color');
        expect(options.scales.y.ticks.color).toBe('scale-y-ticks color');
    });

    test('when running in a light theme, it takes its colors from the light theme defaults if unspecified', () => {
        wtgUi.appearance = 'light';

        const wrapper = mountComponent({
            propsData: {
                data: {
                    datasets: [
                        {
                            data: [65, 59, 80, 81, 56, 55, 40],
                        },
                    ],
                },
            },
        });

        const vm: any = wrapper.vm;
        const chart = vm.nonReactive.chart;
        const options = chart.config.options;
        const dataset = chart.config.data.datasets[0];

        expect(dataset.backgroundColor).toBe('#371FE1');
        expect(dataset.borderColor).toBe('#666');
        expect(options.plugins.title.color).toBe('#666');
        expect(options.plugins.legend.labels.color).toBe('#666');
        expect(options.scales.x.grid.color).toBe('rgba(0, 0, 0, 0.6)');
        expect(options.scales.x.ticks.backdropColor).toBe('rgba(255, 255, 255, 0.75)');
        expect(options.scales.x.ticks.color).toBe('rgba(0, 0, 0, 0.6)');
        expect(options.scales.y.grid.color).toBe('rgba(0, 0, 0, 0.6)');
        expect(options.scales.y.ticks.backdropColor).toBe('rgba(255, 255, 255, 0.75)');
        expect(options.scales.y.ticks.color).toBe('rgba(0, 0, 0, 0.6)');
    });

    test('when running in a dark theme, it takes its colors from the dark theme defaults if unspecified', () => {
        wtgUi.appearance = 'dark';

        const wrapper = mountComponent({
            propsData: {
                data: {
                    datasets: [
                        {
                            data: [65, 59, 80, 81, 56, 55, 40],
                        },
                    ],
                },
            },
        });

        const vm: any = wrapper.vm;
        const chart = vm.nonReactive.chart;
        const options = chart.config.options;
        const dataset = chart.config.data.datasets[0];

        expect(dataset.backgroundColor).toBe('#2387EE');
        expect(dataset.borderColor).toBe('#FFF');
        expect(options.plugins.title.color).toBe('#FFF');
        expect(options.plugins.legend.labels.color).toBe('#FFF');
        expect(options.scales.x.grid.color).toBe('rgba(255, 255, 255, 0.7)');
        expect(options.scales.x.ticks.backdropColor).toBe('rgba(0, 0, 0, 0.75)');
        expect(options.scales.x.ticks.color).toBe('rgba(255, 255, 255, 0.7)');
        expect(options.scales.y.grid.color).toBe('rgba(255, 255, 255, 0.7)');
        expect(options.scales.y.ticks.backdropColor).toBe('rgba(0, 0, 0, 0.75)');
        expect(options.scales.y.ticks.color).toBe('rgba(255, 255, 255, 0.7)');
    });

    test('it displays the progress circular component when loading', () => {
        const wrapper = mountComponent({ propsData: { loading: true } });
        const progressCircular = wrapper.findComponent(WtgProgressCircular);

        expect(progressCircular.exists()).toBe(true);
        expect(progressCircular.vm.$props.indeterminate).toBe(true);
    });

    test('it does not display progress circular component when not loading', () => {
        const wrapper = mountComponent({ propsData: { loading: false } });

        expect(wrapper.findComponent(WtgProgressCircular).exists()).toBe(false);
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgChart, {
            propsData,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
