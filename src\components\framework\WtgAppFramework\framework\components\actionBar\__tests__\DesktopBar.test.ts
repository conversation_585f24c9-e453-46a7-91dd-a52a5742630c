import { WtgFrameworkTask } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { nextTick, reactive } from 'vue';
import WtgUi from '../../../../../../../WtgUi';
import DesktopBar from '../DesktopBar.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('desktop-bar', () => {
    let el: HTMLElement;
    let task: WtgFrameworkTask;
    let wrapper: VueWrapper<any>;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        task = reactive(new WtgFrameworkTask());
        task = {
            showFooter: true,
            cancelAction: {
                visible: false,
                caption: 'Cancel',
                onInvoke: jest.fn(),
            },
            saveAction: {
                visible: false,
                caption: 'Save changes',
                label: 'Save',
                onInvoke: jest.fn(),
            },
            saveCloseAction: {
                visible: false,
                caption: 'Save and close',
                label: 'Close',
                onInvoke: jest.fn(),
            },
            genericActions: [],
        } as any;
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is DesktopBar', () => {
        wrapper = mountComponent({ props: { task: task } });
        expect(wrapper.vm.$options.__name).toBe('DesktopBar');
    });

    test('it will initially display no buttons', () => {
        wrapper = mountComponent({ props: { task: task } });
        const primaryActions = wrapper.vm.primaryActions;
        const secondaryActions = wrapper.vm.secondaryActions;
        const defaultActions = wrapper.vm.defaultActions;
        expect(primaryActions.length).toBe(0);
        expect(secondaryActions.length).toBe(0);
        expect(defaultActions.length).toBe(0);
    });

    describe('when handling standard actions', () => {
        test('it will display the cancel action as a primary action if save not visible', async () => {
            wrapper = mountComponent({ props: { task } });
            let primaryActions = wrapper.vm.primaryActions;
            expect(primaryActions.length).toBe(0);

            task.cancelAction.visible = true;
            wrapper = mountComponent({ props: { task } });
            primaryActions = wrapper.vm.primaryActions;
            expect(primaryActions.length).toBe(1);
            expect(primaryActions[0].caption).toBe('Cancel');
            primaryActions[0].click();
            expect(task.cancelAction.onInvoke).toHaveBeenCalledTimes(1);
        });

        test('it will display the save action as a primary action if visible', async () => {
            wrapper = mountComponent({ props: { task } });
            let primaryActions = wrapper.vm.primaryActions;
            expect(primaryActions.length).toBe(0);

            task.saveAction.visible = true;
            wrapper = mountComponent({ props: { task } });
            primaryActions = wrapper.vm.primaryActions;
            expect(primaryActions.length).toBe(1);
            expect(primaryActions[0].caption).toBe('Save changes');
            primaryActions[0].click();
            expect(task.saveAction.onInvoke).toHaveBeenCalledTimes(1);
        });

        test('it will display the other actions as a secondary actions if save is visible', async () => {
            wrapper = mountComponent({ props: { task } });
            let secondaryActions = wrapper.vm.secondaryActions;
            expect(secondaryActions.length).toBe(0);

            task.saveAction.visible = true;
            task.saveCloseAction.visible = true;
            task.cancelAction.visible = true;
            wrapper = mountComponent({ props: { task } });
            secondaryActions = wrapper.vm.secondaryActions;
            expect(secondaryActions.length).toBe(1);
            expect(secondaryActions[0].caption).toBe('Save and close');
            secondaryActions[0].click();
            expect(task.saveCloseAction.onInvoke).toHaveBeenCalledTimes(1);
        });

        test('it will display Cancel action as a destructive action if save is visible', async () => {
            wrapper = mountComponent({ props: { task } });
            let destructiveActions = wrapper.vm.destructiveActions;
            expect(destructiveActions.length).toBe(0);
            task.saveAction.visible = true;
            task.cancelAction.visible = true;
            wrapper = mountComponent({ props: { task } });
            destructiveActions = wrapper.vm.destructiveActions;
            expect(destructiveActions.length).toBe(1);
            expect(destructiveActions[0].caption).toBe('Cancel');
            destructiveActions[0].click();
            expect(task.cancelAction.onInvoke).toHaveBeenCalledTimes(1);
        });
    });

    describe('when handling generic actions', () => {
        let genericActions;

        beforeEach(() => {
            genericActions = [
                {
                    id: 'someguid1',
                    caption: 'Primary Action 1',
                    placement: 'primary',
                    onInvoke: jest.fn(),
                    icon: 's-icon-arrow-down',
                },
                {
                    id: 'someguid2',
                    caption: 'Secondary Action 1',
                    placement: 'secondary',
                    onInvoke: jest.fn(),
                    icon: 's-icon-arrow-up',
                },
                {
                    id: 'someguid3',
                    caption: 'Default Action 1',
                    placement: 'default',
                    onInvoke: jest.fn(),
                    icon: 's-icon-arrow-right',
                },
            ];
            task.genericActions = genericActions;
        });

        test('if the placement is primary it will be part of the primary actions', () => {
            wrapper = mountComponent({ props: { task } });
            const primaryActions = wrapper.vm.primaryActions;
            expect(primaryActions.length).toBe(1);
            expect(primaryActions[0].caption).toBe('Primary Action 1');
            primaryActions[0].click();
            expect(task.genericActions[0].onInvoke).toHaveBeenCalledTimes(1);
        });

        test('if the placement is primary and it has an icon it will display the icon', async () => {
            wrapper = mountComponent({ props: { task } });
            const primaryActions = wrapper.vm.primaryActions;
            expect(primaryActions.length).toBe(1);
            expect(primaryActions[0].icon).toBe('s-icon-arrow-down');
            await nextTick();
            const arrowIconsWrapper = wrapper.findComponent('.s-icon-arrow-down');
            expect(arrowIconsWrapper).toBeDefined();
        });

        test('if the placement is secondary it will be part of the secondary actions', () => {
            wrapper = mountComponent({ props: { task } });
            const secondaryActions = wrapper.vm.secondaryActions;
            expect(secondaryActions.length).toBe(1);
            expect(secondaryActions[0].caption).toBe('Secondary Action 1');
            secondaryActions[0].click();
            expect(task.genericActions[1].onInvoke).toHaveBeenCalledTimes(1);
        });

        test('if the placement is secondary it will be part of the secondary actions', async () => {
            wrapper = mountComponent({ props: { task } });
            const secondaryActions = wrapper.vm.secondaryActions;
            expect(secondaryActions.length).toBe(1);
            expect(secondaryActions[0].icon).toBe('s-icon-arrow-up');
            await nextTick();
            const arrowIconsWrapper = wrapper.findComponent('.s-icon-arrow-up');
            expect(arrowIconsWrapper).toBeDefined();
        });

        test('if the placement is default it will be part of the default actions', () => {
            wrapper = mountComponent({ props: { task } });
            const defaultActions = wrapper.vm.defaultActions;
            expect(defaultActions.length).toBe(1);
            expect(defaultActions[0].caption).toBe('Default Action 1');
            defaultActions[0].click();
            expect(task.genericActions[2].onInvoke).toHaveBeenCalledTimes(1);
        });

        test('if the placement is default and it has an icon prop, it should have an icon', async () => {
            wrapper = mountComponent({ props: { task } });
            const defaultActions = wrapper.vm.defaultActions;
            expect(defaultActions.length).toBe(1);
            expect(defaultActions[0].icon).toBe('s-icon-arrow-right');
            await nextTick();
            const menuBarWrapper = wrapper.findComponent({ name: 'WtgMenuBar' });
            expect(menuBarWrapper).toBeDefined();
            const iconsWrapper = menuBarWrapper.findAllComponents({ name: 'WtgIcon' });
            expect(iconsWrapper).toBeDefined();
            const arrowIconsWrapper = iconsWrapper.filter((iw) => iw.findComponent('.s-icon-arrow-right'));
            expect(arrowIconsWrapper).toHaveLength(1);
        });
    });

    describe('when handling tab orders', () => {
        let genericActions;

        beforeEach(() => {
            genericActions = [
                {
                    id: 'someguid1',
                    caption: 'Primary Action 1',
                    placement: 'primary',
                    onInvoke: jest.fn(),
                },
                {
                    id: 'someguid2',
                    caption: 'Primary Action 2',
                    placement: 'primary',
                    onInvoke: jest.fn(),
                },
                {
                    id: 'someguid3',
                    caption: 'Secondary Action 1',
                    placement: 'secondary',
                    onInvoke: jest.fn(),
                },
                {
                    id: 'someguid4',
                    caption: 'Secondary Action 2',
                    placement: 'secondary',
                    onInvoke: jest.fn(),
                },
                {
                    id: 'someguid5',
                    caption: 'Default Action 1',
                    placement: 'default',
                    onInvoke: jest.fn(),
                },
                {
                    id: 'someguid6',
                    caption: 'Default Action 2',
                    placement: 'default',
                    onInvoke: jest.fn(),
                },
            ];
            task.genericActions = genericActions;
            Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
                value: 1024,
                configurable: true,
            });
        });

        test('it should display the buttons in the correct order', async () => {
            wrapper = mountComponent({ props: { task } });
            await nextTick();
            const btnsWrapper = wrapper.findAllComponents({ name: 'WtgButton' });
            expect(btnsWrapper.length).toBe(6);
            expect(btnsWrapper.at(0)!.text()).toBe('Default Action 1');
            expect(btnsWrapper.at(1)!.text()).toBe('Default Action 2');
            expect(btnsWrapper.at(2)!.text()).toBe('Secondary Action 2');
            expect(btnsWrapper.at(3)!.text()).toBe('Secondary Action 1');
            expect(btnsWrapper.at(4)!.text()).toBe('Primary Action 2');
            expect(btnsWrapper.at(5)!.text()).toBe('Primary Action 1');
        });
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(DesktopBar, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
