import { SearchFieldDisplayMode } from '@components/WtgSearchField';
import CountriesItemProvider from '@components/WtgSearchField/__tests__/CountriesItemProvider';
import CountriesSearchProvider from '@components/WtgSearchField/__tests__/CountriesSearchProvider';
import StatusCellProvider from '@components/WtgSearchField/__tests__/StatusCellProvider';
import { filtersCompare } from '@components/WtgSearchList/common/filters/operations/FilterOperations';
import { quickSearchFind } from '@components/WtgSearchList/common/filters/search';
import { SortDirection } from '@components/WtgSearchList/common/layouts/types';
import { MeasureProvider } from '@components/WtgUnitField/types';
import { ref, watchEffect } from 'vue';

export interface DessertsServiceOptions {
    simulatedItems: number;
    totalItems: number;
    options: {};
    lazyItems?: number;
    loading: boolean;
    items: any[];
    selectedItems: any[];
    itemsPerPage?: number;
    groupedColumns?: any[];
    groupBy?: any[];
    sortBy?: any[];
    search: string;
    layout?: any;
    fields: any[];
    filterFields: any[];
    headers: any[];
    loadItems?: Function;
}

const countryProvider = new CountriesItemProvider();
const countryProviderDescOnly = new CountriesItemProvider({
    displayMode: SearchFieldDisplayMode.DescOnly,
    searchResultsDisplayMode: SearchFieldDisplayMode.DescOnly,
});
const countrySearchProvider = new CountriesSearchProvider();
const statusProvider = new StatusCellProvider();
const measureProvider: MeasureProvider = {
    getUnits: () => ({
        units: ref([
            { value: 'CM', text: 'CM', itemText: 'Centimeters' },
            { value: 'FT', text: 'FT', itemText: 'Feet' },
            { value: 'IN', text: 'IN', itemText: 'Inches' },
            { value: 'KM', text: 'KM', itemText: 'Kilometers' },
            { value: 'M', text: 'M', itemText: 'Meters' },
            { value: 'MI', text: 'MI', itemText: 'Miles' },
            { value: 'MM', text: 'MM', itemText: 'Millimeters' },
            { value: 'YD', text: 'YD', itemText: 'Yards' },
        ]),
        loading: ref(false),
        dispose: () => {},
    }),
    getAllowNegatives: () => false,
};

let nextGuid = 1;
let serverItems = [] as any[];
let serverItemCount = 0;

export const dessertHeaders = [
    { key: 'name', title: 'Dessert (100g serving)', align: 'start' },
    { key: 'rating', title: 'Rating', align: 'start' },
    { key: 'fat', title: 'Fat (g)', align: 'end' },
    { key: 'expiry', title: 'Expiry Date', align: 'end' },
    { key: 'calories', title: 'Calories', align: 'end' },
    { key: 'available', title: 'Available', align: 'center' },
];

const oneLevelActions = [
    {
        id: '1',
        caption: 'Item Action 1',
    },
    { id: '2', caption: 'Item Action 2' },
    {
        id: '3',
        caption: 'Item Action 3',
        icon: 's-icon-placeholder',
    },
];

const threeLevelActions = [
    {
        id: '1',
        caption: 'Item Action 1',
        icon: 's-icon-placeholder',
    },
    { id: '2', caption: 'Item Action 2' },
    {
        id: '3',
        caption: 'Item Action 3',
        icon: 's-icon-placeholder',
        actions: [
            {
                id: '1',
                caption: 'Submenu Action 1',
                icon: 's-icon-placeholder',
            },
            { id: '2', caption: 'Submenu Action 2' },
            {
                id: '3',
                caption: 'Submenu Action 3',
                actions: [
                    {
                        id: '1',
                        caption: 'Subsubmenu Action 1',
                        icon: 's-icon-placeholder',
                    },
                    { id: '2', caption: 'Subsubmenu Action 2' },
                ],
            },
        ],
    },
];

const multiLevelActions = [
    {
        id: '1',
        caption: 'Item Action 1',
        icon: 's-icon-placeholder',
    },
    {
        id: '2',
        caption: 'Item Action 2',
        actions: [
            {
                id: '1',
                caption: 'Submenu Action 1',
            },
        ],
    },
    {
        id: '3',
        caption: 'Item Action 3',
        icon: 's-icon-placeholder',
        actions: [
            {
                id: '1',
                caption: 'Submenu Action 1',
            },
            { id: '2', caption: 'Submenu Action 2' },
            {
                id: '3',
                caption: 'Submenu Action 3',
                actions: [
                    {
                        id: '1',
                        caption: 'Subsubmenu Action 1',
                        icon: 's-icon-placeholder',
                    },
                    { id: '2', caption: 'Subsubmenu Action 2' },
                    {
                        id: '3',
                        caption: 'Subsubmenu Action 3',
                        icon: 's-icon-placeholder',
                        actions: [
                            {
                                id: '1',
                                caption: 'Subsubsubmenu Action 1',
                            },
                            {
                                id: '2',
                                caption: 'Subsubsubmenu Action 2',
                                actions: [
                                    {
                                        id: '1',
                                        caption: 'Subsubsubsubmenu Action 1',
                                        icon: 's-icon-placeholder',
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
    },
];

export const dessertItems = [
    {
        id: nextGuid++,
        name: 'Frozen Yogurt',
        calories: 159,
        fat: 6.0,
        carbs: 24,
        protein: 4.0,
        iron: '1%',
        rating: 2,
        expiry: '2031-08-06',
        available: true,
        country: 'AU',
        status: 'new',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: oneLevelActions,
        timezone: '2025-03-20T11:30:00+03:00',
    },
    {
        id: nextGuid++,
        name: 'Ice cream sandwich',
        calories: 518,
        fat: 9.0,
        carbs: 37,
        protein: 4.3,
        iron: '1%',
        rating: 3,
        expiry: '2032-12-18',
        available: true,
        country: 'AU',
        status: '',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: threeLevelActions,
        timezone: '2027-02-25T10:40:00+05:00',
    },
    {
        id: nextGuid++,
        name: 'Eclair',
        calories: 408,
        fat: 16.0,
        carbs: 23,
        protein: 6.0,
        iron: '7%',
        rating: 2,
        expiry: '2032-08-12',
        available: false,
        country: 'AU',
        status: '',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: multiLevelActions,
        timezone: '2031-08-06T03:20:00+01:00',
    },
    {
        id: nextGuid++,
        name: 'Cupcake',
        calories: 305,
        fat: 3.7,
        carbs: 67,
        protein: 4.3,
        iron: '8%',
        rating: 4,
        expiry: '2032-07-08',
        available: true,
        country: 'NZ',
        status: '',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: multiLevelActions,
        timezone: '2024-01-08T01:00:00.000Z',
    },
    {
        id: nextGuid++,
        name: 'Gingerbread',
        calories: 262,
        fat: 10.0,
        carbs: 49,
        protein: 3.9,
        iron: '16%',
        rating: 3,
        expiry: '2024-12-31',
        available: true,
        country: 'AU',
        status: 'out',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 11, unit: 'IN' },
        actions: multiLevelActions,
        timezone: '2022-07-16T11:30:00+02:00',
    },
    {
        id: nextGuid++,
        name: 'Jelly bean',
        calories: 375,
        fat: 0.0,
        carbs: 94,
        protein: 0.0,
        iron: '0%',
        rating: 3,
        expiry: '2032-09-03',
        available: false,
        country: 'AU',
        status: '',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: multiLevelActions,
        timezone: '2025-03-20T11:30:00+03:00',
    },
    {
        id: nextGuid++,
        name: 'Lollipop',
        calories: 392,
        fat: 0.2,
        carbs: 98,
        protein: 0,
        iron: '2%',
        rating: 1,
        expiry: '2032-11-18',
        available: false,
        country: 'AU',
        status: '',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: multiLevelActions,
        timezone: '2025-03-20T11:30:00+03:00',
    },
    {
        id: nextGuid++,
        name: 'Honeycomb',
        calories: 356,
        fat: 3.2,
        carbs: 87,
        protein: 6.5,
        iron: '45%',
        rating: 2,
        expiry: '2032-07-08',
        available: true,
        country: 'AU',
        status: '',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: multiLevelActions,
        timezone: '2025-03-20T11:30:00+03:00',
    },
    {
        id: nextGuid++,
        name: 'Donut',
        calories: 452,
        fat: 25.0,
        carbs: 51,
        protein: 4.9,
        iron: '22%',
        rating: 5,
        expiry: '2032-03-27',
        available: true,
        country: 'AU',
        status: '',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: multiLevelActions,
        timezone: '2025-03-20T11:30:00+03:00',
    },
    {
        id: nextGuid++,
        name: 'KitKat',
        calories: 237,
        fat: 26.0,
        carbs: 65,
        protein: 7,
        iron: '6%',
        rating: 3,
        expiry: '2032-10-24',
        available: true,
        country: 'AU',
        status: '',
        availableIn: ['AU', 'NZ'],
        unit: { magnitude: 1, unit: 'CM' },
        actions: multiLevelActions,
        timezone: '2025-03-20T11:30:00+03:00',
    },
];

export function loadDessertItems(): any[] {
    return JSON.parse(JSON.stringify(dessertItems));
}
class DessertsService {
    _options: DessertsServiceOptions;

    constructor(options: DessertsServiceOptions) {
        this._options = options;
    }

    getDataFromApi(groupFields?: any[]): any {
        this._options.loading = true;
        return this._fakeApiCall(groupFields).then((data: any) => {
            this._options.items = data.items;
            this._options.totalItems = data.total;
            this._options.loading = false;
            return;
        });
    }
    /**
     * In a real application this would be a call to fetch() or axios.get()
     */
    _fakeApiCall(groupFields?: any[]): any {
        return new Promise((resolve) => {
            const { sortBy, page, itemsPerPage } = this._options as any;
            const groupBy =
                groupFields !== undefined
                    ? groupFields.map((field) => field.key)
                    : this._options.groupBy?.map((field: any) => field.key);
            let total = 0;
            let items = [] as any[];
            if (serverItemCount !== this._options.simulatedItems) {
                serverItemCount = this._options.simulatedItems;
                serverItems = [];
                for (let i = 1; i <= this._options.simulatedItems; i++) {
                    const item = {
                        ...dessertItems[(i - 1) % 5],
                        id: nextGuid++,
                        name: `${dessertItems[(i - 1) % 5].name} ${i}`,
                    };
                    serverItems.push(item);
                }
            }
            serverItems.forEach((item) => {
                if (
                    filtersCompare(
                        this._options.fields,
                        this._options.layout?.filter?.items ?? [],
                        this._options.layout?.filter?.operator ?? 'and',
                        item
                    ) &&
                    quickSearchFind(this._options.fields, this._options.search, item)
                ) {
                    items.push(item);
                }
            });

            total = items.length;
            if (sortBy.length) {
                items.sort((a, b) => {
                    for (const sortColumn of sortBy) {
                        const sortKey = sortColumn.key;
                        const sortOrder = sortColumn.order;
                        const sortA = a[sortKey];
                        const sortB = b[sortKey];
                        if (sortOrder === SortDirection.Descending) {
                            if (sortA < sortB) {
                                return 1;
                            }
                            if (sortA > sortB) {
                                return -1;
                            }
                        } else {
                            if (sortA < sortB) {
                                return -1;
                            }
                            if (sortA > sortB) {
                                return 1;
                            }
                        }
                    }
                    return 0;
                });
            }

            if (groupBy?.length === 1) {
                items = items.sort((a, b) => {
                    const sortA = a[groupBy[0]];
                    const sortB = b[groupBy[0]];

                    if (sortA < sortB) {
                        return -1;
                    }
                    if (sortA > sortB) {
                        return 1;
                    }
                    return 0;
                });
            }

            const itemsForCurrentPage = this._options.lazyItems ?? itemsPerPage;
            if (!groupBy?.length) {
                if (itemsPerPage > 0) {
                    items = items.slice((page - 1) * itemsPerPage, (page - 1) * itemsPerPage + itemsForCurrentPage);
                } else if (itemsForCurrentPage > 0) {
                    items = items.slice(0, itemsForCurrentPage);
                }
            }

            setTimeout(() => {
                resolve({
                    items,
                    total,
                });
            }, 500);
        });
    }
}

export function getItem(id: string): any {
    return serverItems.find((item) => item.id === id);
}

export function setItem(id: string, item: any): Promise<void> {
    return new Promise((resolve) => {
        const index = serverItems.findIndex((item) => item.id.toString() === id);
        serverItems[index] = item;
        setTimeout(() => resolve(undefined), 1000);
    });
}

export function createDessertsService(config?: {
    lazyItems?: number;
    itemsPerPage?: number;
    simulatedItems?: number;
}): DessertsService {
    const options = ref({
        simulatedItems: config?.simulatedItems ?? 100,
        page: 1,
        itemsPerPage: config?.itemsPerPage,
        lazyItems: config?.lazyItems,
        search: '',
        items: [] as any,
        selectedItems: [] as any,
        sortBy: [],
        loading: false,
        totalItems: 0,
        fields: [
            { name: 'name', text: 'Dessert (100g serving)', type: 'string', width: 300 },
            { name: 'fat', text: 'Fat (g)', type: 'number', width: 100, canAggregate: true },
            { name: 'status', text: 'Status', type: 'status', width: 150, provider: statusProvider },
            {
                name: 'country',
                text: 'Country of Origin',
                type: 'search',
                provider: countryProvider,
                initSearchProvider: () => [countrySearchProvider, true],
                width: 300,
            },
            { name: 'expiry', text: 'Expiry Date', type: 'date', width: 150 },
            { name: 'calories', text: 'Calories', type: 'number', width: 150, canAggregate: true },
            { name: 'available', text: 'Available', type: 'boolean', width: 150, align: 'center' },
            {
                name: 'availableIn',
                text: 'Available In Countries',
                type: 'search',
                multiple: true,
                provider: countryProviderDescOnly,
                initSearchProvider: () => [countrySearchProvider, true],
                width: 300,
                sortable: false,
            },
            {
                name: 'unit',
                text: 'Unit',
                type: 'measure',
                decimals: 3,
                measureProvider: measureProvider,
                width: 200,
            },
            {
                name: 'timezone',
                text: 'Release Date',
                type: 'datetime',
                width: 300,
                showTimeZone: true,
            },
        ],
        filterFields: [
            { name: 'name', text: 'Dessert (100g serving)', type: 'string' },
            { name: 'fat', text: 'Fat (g)', type: 'number' },
            { name: 'country', text: 'Country of Origin', type: 'search' },
            { name: 'expiry', text: 'Expiry Date', type: 'date' },
            { name: 'calories', text: 'Calories', type: 'number' },
            { name: 'available', text: 'Available', type: 'boolean' },
            { name: 'availableIn', text: 'Available In Countries', type: 'multiple' },
            { name: 'timezone', text: 'Release Date', type: 'datetime' },
        ],
        headers: dessertHeaders,
        sortDesc: '',
        options: {},
        loadItems: undefined as Function | undefined,

        groupBy: [],
        groupedColumns: [],

        layout: {
            filter: {
                items: [],
                operator: 'and',
            },
            groupedColumns: [],
            //groupedColumns: [{ key: 'name', order: SortDirection.Ascending }],
        },
    });
    const dessertsService = new DessertsService(options.value);

    function loadItems({ page = 1, itemsPerPage = 10, sortBy = [] }, groupFields?: any) {
        options.value.loading = true;
        options.value.page = page;
        options.value.itemsPerPage = itemsPerPage;
        options.value.sortBy = sortBy;
        dessertsService.getDataFromApi(groupFields).then(() => {});
    }

    options.value.loadItems = loadItems;

    watchEffect(() => {
        loadItems(options.value);
    });

    return dessertsService;
}
