import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { Canvas, ColorItem, ColorPalette, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgGoogleMap from './WtgGoogleMap.stories';

<Meta of={WtgGoogleMap} />

<div className="component-header">
    <h1>Google Maps</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=219-21320&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img> With pending updates
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style={{ width: '25%' }}>[PRJ00052736](https://svc-ediprod.wtg.zone/Services/edit/PRJ/PRJ00052736)</td>
            <td style={{ width: '75%' }}>GLOW Maps - maps to work geographically</td>
        </tr>
        <tr>
            <td style={{ width: '33%' }}>
                [PRJ00049489](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/54e1e1c0-c7be-4a44-a012-1abdb2a6a978?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply Maps figma\dev alignment.</td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

<p>
    Google Maps is the default maps type we support but we understand that other map may be required later and please
    reach out if you have a requirement to add a different map type.
</p>

<Canvas className="canvas-preview" of={WtgGoogleMap.Default} />

## Types

### Clusterer

<p>This example uses a clusterer with markers that combines nearby markers into clusters</p>

<Canvas className="canvas-preview" of={WtgGoogleMap.ClusterMap} />

### Polylines

<p>Here is an example showing a flight path by using the polylines property</p>

<Canvas className="canvas-preview" of={WtgGoogleMap.PolyLinesMap} />

### Polygons

<p>This example creates a map with a simple polygon representing the Bermuda Triangle</p>

<Canvas className="canvas-preview" of={WtgGoogleMap.PolygonMap} />

### Shapes

<p>This example creates a map with circles and rectangles to show information to represent capital cities</p>

<Canvas className="canvas-preview" of={WtgGoogleMap.ShapesMap} />

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
