export const ButtonWithSentimentsTemplate = `
<WtgRow>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            :icon="args.icon"
            @click="action">
        </WtgIconButton>
        <WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            sentiment="success"
            :icon="args.icon"
            @click="action">
        </WtgIconButton>
        <WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            sentiment="critical"
            :icon="args.icon"
            @click="action">
        </WtgIconButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            variant="fill"
            sentiment="primary"
            data-testid="testSentimentsIconButton-primary"
            :icon="args.icon"
            @click="action">
        </WtgIconButton>
        <WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            variant="fill"
            sentiment="success"
            :icon="args.icon"
            @click="action">
        </WtgIconButton>
        <WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            variant="fill"
            sentiment="critical"
            :icon="args.icon"
            @click="action">
        </WtgIconButton>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            variant="ghost"
            sentiment="default"
            :icon="args.icon"
            @click="action">
        </WtgIconButton>
        <WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            variant="ghost"
            sentiment="success"
            :icon="args.icon"
            @click="action">
        </WtgIconButton><WtgIconButton 
            v-bind="args"
            aria-label="Aria Name"
            variant="ghost"
            sentiment="critical"
            :icon="args.icon"
            @click="action">
        </WtgIconButton>
    </WtgCol>
</WtgRow>`;
