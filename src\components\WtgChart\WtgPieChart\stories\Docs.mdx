import statusAvailable from '../../../../storybook/assets/statusAvailable.svg';
import statusPlanned from '../../../../storybook/assets/statusPlanned.svg';
import statusDeprecated from '../../../../storybook/assets/statusDeprecated.svg';
import info from '../../../../storybook/assets/info.png';

import { Meta, Title, Description, Story, Canvas, Controls, ArgTypes } from '@storybook/blocks';
import * as WtgPieChart from './WtgPieChart.stories.ts';

<Meta of={WtgPieChart} />

<div className="component-header">
    <h1>Pie Chart</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusPlanned}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img> With pending updates
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style={{ width: '25%' }}>
                [PRJ00047835](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/8f42ea6e-5101-44da-bd46-2ba555cd986d?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply Charts figma\dev alignment.</td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

<Canvas className="canvas-preview" of={WtgPieChart.Default} />

## Quick tips

<div className="component-quicktip">
    🔨 Please bear with us while we're getting this part of our documentation ready.
</div>

## Best practices

<div className="component-quicktip">
    🔨 Please bear with us while we're getting this part of our documentation ready.
</div>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
