import WtgCheckbox from '@components/WtgCheckbox/WtgCheckbox.vue';
import WtgCheckboxGroup from '@components/WtgCheckbox/WtgCheckboxGroup.vue';
import WtgCol from '@components/WtgCol';
import WtgPanel from '@components/WtgPanel';
import WtgRow from '@components/WtgRow';
import { Meta, StoryObj } from '@storybook/vue3';
import { CheckboxGroupTemplate } from './templates/wtg-checkbox-group.stories-template';

type Story = StoryObj<typeof WtgCheckboxGroup>;
const meta: Meta<typeof WtgCheckboxGroup> = {
    title: 'Components/Checkbox/CheckboxGroup',
    component: WtgCheckboxGroup,
    parameters: {
        docs: {
            description: {
                component: 'CheckboxGroup allows users to select multiple options from a group of checkboxes',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=149-5673&mode=design&t=FoGkzhcXiCnlZFCr-0',
        },
        layout: 'centered',
        controls: {
            sort: 'alpha',
        },
    },
    render: (args) => ({
        components: { WtgCheckbox, WtgCheckboxGroup, WtgCol, WtgRow },
        setup: () => ({ args }),
        template: CheckboxGroupTemplate,
    }),
};

export default meta;

export const Default: Story = {
    args: {},
};

export const HorizontalCheckboxGroup: Story = {
    args: {
        horizontal: true,
    },
};

export const Sentiments: Story = {
    render: (args) => ({
        components: { WtgCheckbox, WtgCheckboxGroup, WtgPanel },
        setup: () => ({
            args,
            sentiments: ['success', 'warning', 'critical'],
        }),
        template: `
            <div class="mt-6" max-width="800">
                <WtgPanel v-for="sentiment in sentiments" :key="sentiment" style="margin-bottom: 16px;">
                    <WtgCheckboxGroup
                        v-bind="args"
                        :sentiment="sentiment"
                        description="Assistive text"
                        label="Checkbox group"
                    >
                        <WtgCheckbox label="Option 1" value="one" :sentiment="sentiment" :messages="'Sample ' + sentiment + ' message'"/>
                        <WtgCheckbox label="Option 2" value="two"/>
                        <WtgCheckbox label="Option 3" value="three"/>
                    </WtgCheckboxGroup>
                </WtgPanel>
            </div>
        `,
    }),
};
