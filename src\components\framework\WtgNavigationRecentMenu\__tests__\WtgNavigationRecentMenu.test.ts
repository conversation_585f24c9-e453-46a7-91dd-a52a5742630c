import WtgNavigationRecentMenu from '@components/framework/WtgNavigationRecentMenu/WtgNavigationRecentMenu.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const items = {
    favorites: [
        {
            id: 'favoriteItem1',
            caption: 'Favorite Item 1',
            href: '/',
            favorite: true,
            actions: [
                {
                    id: 'e97ce2d8-6486-4501-b50b-d1001c9a9b3a',
                    caption: 'Caption',
                    href: '/',
                },
            ],
            onRemoveFromFavorites: () => {},
            onAddToFavorites: () => {},
        },
    ],
    recents: [
        {
            id: 'recentItem1',
            caption: 'Recent Item 1',
            href: '/',
            favorite: true,
            actions: [
                {
                    id: 'string',
                    caption: 'string',
                    href: '/',
                },
            ],
            onRemoveFromFavorites: () => {},
            onAddToFavorites: () => {},
        },
    ],
};
const mockClickFavoritesHandler = jest.fn(() => null);
const propsData = {
    items,
    favoritesCaption: 'Favorites Test',
    recentsCaption: 'Recent Test',
    captionClose: 'Close',
    onClick: mockClickFavoritesHandler,
    onDialogOpen: jest.fn(),
};

describe('recent-menu', () => {
    beforeEach(() => {
        wtgUi.breakpoint.mdAndDown = false;
    });

    test('its name is WtgNavigationRecentMenu', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgNavigationRecentMenu');
    });

    test('its title comes from the properties', () => {
        const wrapper = mountComponent(propsData);
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        expect(listItem.text()).toBe('Favorites Test');
    });

    test('it will open the recent list when clicked', async () => {
        const wrapper = mountComponent(propsData);
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        await listItem.trigger('click');

        expect(wrapper.findComponent({ name: 'RecentList' }).exists());
    });

    test('it will invoke onClick method when clicked', async () => {
        const wrapper = mountComponent(propsData);
        const listItem = wrapper.findComponent({ name: 'WtgListItem' });
        await listItem.trigger('click');

        expect(propsData.onClick).toBeCalled();
    });

    describe('when on mobile', () => {
        beforeEach(() => {
            wtgUi.breakpoint.mdAndDown = true;
        });

        afterEach(() => {
            wtgUi.breakpoint.mdAndDown = false;
        });

        test('it when render the recent menu item component', async () => {
            const wrapper = await mountComponent();
            const menuItem = wrapper.findComponent({ name: 'RecentInlineMenu' });
            expect(menuItem.exists()).toBe(true);
        });
    });

    describe('Accessibility', () => {
        test('it renders a list item with a role and aria has popup', async () => {
            const wrapper = await mountComponent();
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            expect(listItem.attributes('role')).toBe('menuitem');
            expect(listItem.attributes('aria-label')).toBe('Favorites');
            expect(listItem.attributes('aria-haspopup')).toBe('menu');
        });
    });

    function mountComponent(propsData = {}) {
        return mount(WtgNavigationRecentMenu, {
            propsData,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
