import { WtgFramework } from '@components/framework/types';
import { WtgHelpDialog } from '@components/framework/WtgHelpDialog';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../WtgUi';
import HelpDialog from '../HelpDialog.vue';

enableAutoUnmount(afterEach);
let wtgUi: WtgUi;

describe('HelpDialog', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        wtgUi = new WtgUi();
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        application = reactive(new WtgFramework());
        application.pageHelp = {
            captions: {
                title: 'Dialog Title',
                close: 'Close',
                loading: 'Loading',
                alwaysOpenHelp: 'Always show',
            },

            visible: false,
            loading: false,
            alwaysOpenHelp: false,

            name: 'My Page',
            text: 'Help Text',

            onAlwaysOpenHelpChanged: jest.fn(),
            onPageHelpClosing: jest.fn(),
            onPageHelpOpening: jest.fn(),
        };
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('it renders the component', () => {
        const wrapper = mountComponent();
        expect(wrapper.exists()).toBe(true);
    });

    test('it renders a WtgHelpDialog', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent(WtgHelpDialog).exists()).toBe(true);
    });

    test('opens the dialog', async () => {
        const wrapper = mountComponent();
        const modal = wrapper.findComponent(WtgHelpDialog);
        expect(modal.props('modelValue')).toBe(false);

        application.openPageHelp();
        await nextTick();

        expect(modal.props('modelValue')).toBe(true);
        expect(application.pageHelp?.onPageHelpOpening).toHaveBeenCalled();
    });

    test('when dialog is closed', async () => {
        application.pageHelp!.visible = true;
        const wrapper = mountComponent();
        const modal = wrapper.findComponent(WtgHelpDialog);
        expect(modal.props('modelValue')).toBe(true);

        modal.vm.$emit('update:modelValue', false);

        expect(application.pageHelp!.visible).toBe(false);
        expect(application.pageHelp?.onPageHelpClosing).toHaveBeenCalled();
    });

    test('it sets correct props on WtgHelpDialog', () => {
        const wrapper = mountComponent();
        const helpDialog = wrapper.findComponent(WtgHelpDialog);
        expect(helpDialog.props('alwaysOpenHelp')).toBe(application.pageHelp!.alwaysOpenHelp);
        expect(helpDialog.props('alwaysOpenHelpCaption')).toBe(application.pageHelp!.captions!.alwaysOpenHelp);
        expect(helpDialog.props('closeTooltip')).toBe(application.pageHelp!.captions!.close);
        expect(helpDialog.props('dialogTitle')).toBe(application.pageHelp!.captions!.title);
        expect(helpDialog.props('helpContent')).toBe(application.pageHelp!.text);
        expect(helpDialog.props('loading')).toBe(application.pageHelp!.loading);
        expect(helpDialog.props('loadingText')).toBe(application.pageHelp!.captions!.loading);
    });

    test('it calls onAlwaysOpenHelpChanged', () => {
        const wrapper = mountComponent();
        const helpDialog = wrapper.findComponent(WtgHelpDialog);
        helpDialog.vm.$emit('update:alwaysOpen', true);
        expect(application.pageHelp!.onAlwaysOpenHelpChanged).toHaveBeenCalledWith(true);

        helpDialog.vm.$emit('update:alwaysOpen', false);
        expect(application.pageHelp!.onAlwaysOpenHelpChanged).toHaveBeenCalledWith(false);
    });

    function mountComponent({ propsData = {}, listeners = {}, slots = { default: h(HelpDialog) } } = {}) {
        const wrapper = mount(VApp, {
            listeners,
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        return wrapper.findComponent(HelpDialog);
    }
});
