export const ButtonWithIconsTemplate = `
<wtg-row>
    <wtg-col style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <wtg-button 
            v-bind="args"
            :leading-icon="args.icon" 
            :trailing-icon="args.icon" 
                @click="action">
            {{args.label}}
        </wtg-button>
        <wtg-button 
            v-bind="args"
            :leading-icon="args.icon" 
                @click="action">
            {{args.label}}
        </wtg-button>
        <wtg-button 
            v-bind="args"
            :trailing-icon="args.icon" 
                @click="action">
            {{args.label}}
        </wtg-button>
    </wtg-col>
    <wtg-col style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <wtg-button 
            v-bind="args"
            variant="fill"
            sentiment="primary"
            :leading-icon="args.icon" 
            :trailing-icon="args.icon" 
            @click="action">
                {{args.label}}
        </wtg-button>
        <wtg-button 
            v-bind="args"
            sentiment="primary"
            variant="fill"
            :leading-icon="args.icon" 
                @click="action">
                {{args.label}}
        </wtg-button>
        <wtg-button 
            v-bind="args"
            sentiment="primary"
            variant="fill"
            :trailing-icon="args.icon" 
                @click="action">
                {{args.label}}
        </wtg-button>
    </wtg-col>
    <wtg-col style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <wtg-button 
            v-bind="args"
            variant="ghost"
            :leading-icon="args.icon" 
            :trailing-icon="args.icon" 
                @click="action">
                {{args.label}}
        </wtg-button>
        <wtg-button 
            v-bind="args"
            variant="ghost"
            :leading-icon="args.icon" 
            @click="action">
                {{args.label}}
        </wtg-button>
        <wtg-button 
            v-bind="args"
            variant="ghost"
            :trailing-icon="args.icon" 
                @click="action">
                {{args.label}}
        </wtg-button>
    </wtg-col>
</wtg-row>`;
