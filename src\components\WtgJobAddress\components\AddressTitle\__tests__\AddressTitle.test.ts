import WtgHyperlink from '@components/WtgHyperlink/WtgHyperlink.vue';
import WtgLabel from '@components/WtgLabel';
import WtgTag from '@components/WtgTag/WtgTag.vue';
import { useWtgUi } from '@composables/global';
import { mount } from '@vue/test-utils';
import AddressTitle from '../AddressTitle.vue';

const wtgUi = useWtgUi();

describe('AddressTitle', () => {
    test('it renders the component', () => {
        const wrapper = mountComponent();
        expect(wrapper.exists()).toBe(true);
    });

    test('it displays the provided header caption when title is provided', () => {
        const wrapper = mountComponent({
            propsData: {
                title: 'My Address',
            },
        });
        const labels = wrapper.findAllComponents(WtgLabel);
        expect(labels.at(0)!.text()).toBe('My Address');
    });

    test('it displays the edit hyperlink with correct content', async () => {
        const wrapper = mountComponent();
        let hyperlink = wrapper.findAllComponents(WtgHyperlink);
        expect(hyperlink.length).toBe(0);
        expect(wrapper.emitted('edit')).toBeUndefined();
        await wrapper.setProps({ editable: true });
        hyperlink = wrapper.findAllComponents(WtgHyperlink);
        expect(hyperlink.length).toBe(1);
        expect(hyperlink.at(0)?.text()).toBe('Edit');
    });

    test('address override status is not visible by default', async () => {
        const wrapper = mountComponent({ propsData: { isCardAddressOverriden: false } });
        const editTag = wrapper.findComponent(WtgTag);
        expect(editTag.exists()).toBe(false);
    });

    test('address override status is visible where isCardAddressOverriden is true', async () => {
        const wrapper = mountComponent({ propsData: { isCardAddressOverriden: true } });
        const editTag = wrapper.findComponent(WtgTag);
        expect(editTag.exists()).toBe(true);
        expect(editTag.props('label')).toBe('Edited');
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(AddressTitle, {
            propsData: {
                ...propsData,
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
