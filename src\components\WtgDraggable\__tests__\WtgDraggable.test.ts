import { WtgDraggable } from '../';
import WtgUi from '../../../WtgUi';
import { mount, enableAutoUnmount } from '@vue/test-utils';
import draggable from 'vuedraggable';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgDraggable', () => {
    test('its name is WtgDraggable', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgDraggable');
    });

    test('it renders a Draggable component', () => {
        const wrapper = mountComponent();
        const draggableComp = wrapper.findComponent(draggable).vm;
        expect(draggableComp).toBeDefined();
    });

    test('it passes all its properties to the draggable component', () => {
        const list1 = [
            { name: '<PERSON>', id: 1 },
            { name: '<PERSON><PERSON>', id: 2 },
            { name: '<PERSON>', id: 3 },
            { name: '<PERSON>', id: 4 },
        ];
        const wrapper = mountComponent({
            props: {
                list: list1,
            },
        });
        const draggableComp = wrapper.findComponent(draggable).vm;
        expect(draggableComp.$props.list).toEqual(list1);
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgDraggable, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
