import WtgCol from '@components/WtgCol';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgRow from '@components/WtgRow';
import { inputArgTypes } from '@composables/input';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import WtgDateTimeField from '..';

type Story = StoryObj<typeof WtgDateTimeField>;
const meta: Meta<typeof WtgDateTimeField> = {
    title: 'Components/Date Time Field',
    component: WtgDateTimeField,
    parameters: {
        docs: {
            description: {
                component:
                    'The Date Time Field captures a single date typically in YYYY-MM-DD 00:00 format. Users can input a date manually or choose from a calendar dropdown, which appears when interacting with the calendar icon and closes after selection.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=383-43226',
        },
        layout: 'centered',
        controls: {
            sort: 'alpha',
        },
    },
    argTypes: {
        ...inputArgTypes,
        modelValue: {
            control: 'object',
        },
    },
    render: (args) => ({
        components: { WtgDateTimeField },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `<WtgDateTimeField
                        v-bind="args"
                        @update:model-value="updateModel"
                        @change="changeAction"
                        @input="inputAction"
                        @focus="focusAction"
                        @blur="blurAction">
                    </WtgDateTimeField>`,
    }),
    decorators: [
        () => ({
            template: `
                <div style="max-width:300px">
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        label: 'Departure',
        modelValue: '2024-01-08T01:00:00.000Z',
    },
};

export const Sentiments: Story = {
    args: {
        modelValue: '2024-01-08T01:00:00.000Z',
    },
    render: (args) => ({
        components: { WtgDateTimeField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
        <WtgRow>
        <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column col-md-3">
            <WtgLayoutGrid>
                <WtgDateTimeField v-bind="args" 
                    label="Default"
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateTimeField>
                <WtgDateTimeField v-bind="args" 
                    label="Success" 
                    sentiment="success" 
                    messages="Sample success message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateTimeField>
                <WtgDateTimeField v-bind="args" 
                    label="Warning" 
                    sentiment="warning" 
                    messages="Sample warning message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateTimeField>
                <WtgDateTimeField v-bind="args" 
                    label="Error" 
                    sentiment="critical" 
                    messages="Sample error message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateTimeField>
                <WtgDateTimeField v-bind="args" 
                    label="Info" 
                    sentiment="info" 
                    messages="Sample info message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateTimeField>
            </WtgLayoutGrid>
        </WtgCol>
    </WtgRow>`,
    }),
};

export const ReadOnly: Story = {
    args: {
        modelValue: '2024-01-08T01:00:00.000Z',
        label: 'Read only',
        readonly: true,
    },
    render: (args) => ({
        components: { WtgDateTimeField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
                <WtgDateTimeField v-bind="args" 
                    label="Read only"
                    :readonly="true"
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateTimeField>`,
    }),
};

export const Disabled: Story = {
    args: {
        modelValue: '2024-01-08T01:00:00.000Z',
        label: 'Disabled',
        disabled: true,
    },
    render: (args) => ({
        components: { WtgDateTimeField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
                <WtgDateTimeField v-bind="args" 
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDateTimeField>`,
    }),
};

export const TimeZoneModelDefined: Story = {
    args: {
        label: 'Departure',
        modelValue: '2025-03-20T11:30:00+03:00',
        showTimeZone: true,
    },
};

export const TimeZoneModelUndefined: Story = {
    args: {
        label: 'Departure',
        modelValue: undefined,
        showTimeZone: true,
    },
};

export const TimeZoneIgnored: Story = {
    args: {
        label: 'Departure',
        modelValue: '2025-03-20T11:30:00+03:00',
        showTimeZone: false,
    },
};

export const TimeZoneDefault: Story = {
    args: {
        label: 'Departure',
        modelValue: '2025-03-20T11:30:00',
        showTimeZone: true,
    },
};

export const Native: Story = {
    args: {
        label: 'Departure',
        modelValue: '2024-01-08T12:00:00.000',
        native: true,
    },
};
