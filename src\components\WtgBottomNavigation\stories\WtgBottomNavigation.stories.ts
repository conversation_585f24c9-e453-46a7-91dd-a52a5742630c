import WtgApp from '@components/WtgApp';
import WtgBottomNavigation from '@components/WtgBottomNavigation';
import WtgButton from '@components/WtgButton';
import WtgIcon from '@components/WtgIcon';
import WtgDivider from '@components/WtgDivider';
import WtgSpacer from '@components/WtgSpacer';
import { Meta, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgBottomNavigation>;

const meta: Meta<typeof WtgBottomNavigation> = {
    title: 'Utilities/Bottom Navigation',
    component: WtgBottomNavigation,
    parameters: {
        docs: {
            description: {
                component: `The bottom navigation component is an alternative to the sidebar. It is primarily used for mobile applications.`,
            },
        },
        layout: 'fullscreen',
        controls: {
            sort: 'alpha',
        },
    },
    render: (args) => ({
        components: { WtgApp, WtgBottomNavigation, WtgButton, WtgIcon, WtgDivider, WtgSpacer },
        setup() {
            return { args };
        },
        template: `
            <WtgBottomNavigation v-bind="args">
                <WtgButton variant="ghost">
                    <div class="d-flex flex-column align-center justify-center">
                        <WtgIcon>s-icon-home</WtgIcon>
                        <span>Home</span>
                    </div>
                </WtgButton>
                <WtgButton variant="ghost">
                    <div class="d-flex flex-column align-center justify-center">
                        <WtgIcon>s-icon-analytics-bars</WtgIcon>
                        <span>Analytics</span>
                    </div>
                </WtgButton>
                <WtgButton variant="ghost">
                    <div class="d-flex flex-column align-center justify-center">
                        <WtgIcon>s-icon-star-filled</WtgIcon>
                        <span>Favorites</span>
                    </div>
                </WtgButton>
            </WtgBottomNavigation>
        `,
    }),
    decorators: [
        () => ({
            components: { WtgApp },
            template: '<WtgApp class="content-embedded-app"><story /></WtgApp>',
        }),
    ],
};

export default meta;
export const Default: Story = {
    args: {},
};
