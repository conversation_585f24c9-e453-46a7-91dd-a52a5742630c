import WtgCol from '@components/WtgCol';
import WtgIconButton from '@components/WtgIconButton/WtgIconButton.vue';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgPanel from '@components/WtgPanel';
import WtgRow from '@components/WtgRow';
import { useSupplyPrefixIconsName } from '@composables/icon';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { expect, within } from '@storybook/test';
import { Meta, StoryObj } from '@storybook/vue3';
import { ButtonSandboxTemplate } from './templates/wtg-button-sandbox.stories-template';
import { ButtonWithSentimentsTemplate } from './templates/wtg-button-with-sentiments.stories-template';
import { ButtonWithVariantsTemplate } from './templates/wtg-button-with-variants.stories-template';
import windowMaximize from '../../../assets/WtgIconButton/maximize-icon.svg';

type Story = StoryObj<typeof WtgIconButton>;
const icons = useSupplyPrefixIconsName();
const meta: Meta<typeof WtgIconButton> = {
    title: 'Components/Icon Button',
    component: WtgIconButton,
    parameters: {
        docs: {
            description: {
                component:
                    'Icon buttons, like buttons, are commonly used to trigger an action and act as a stand in to save space for actions that are frequently used and have a clear and recognizable visual representation.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=59-1790&mode=design&t=FoGkzhcXiCnlZFCr-0',
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgIconButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `<WtgIconButton v-bind="args" aria-label="Aria Name" :icon="args.icon" @click="action" />`,
    }),
    argTypes: {
        icon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
        sentiment: {
            options: ['', 'critical', 'primary', 'success'],
            control: {
                type: 'select',
            },
        },
        variant: {
            options: ['', 'fill', 'ghost'],
            control: {
                type: 'select',
            },
        },
    },
    decorators: [
        () => ({
            template: `
            <div style="display: flex; flex-wrap: wrap;">
                <story/>
            </div>
            `,
        }),
    ],
};
export default meta;

export const Default: Story = {
    args: {
        icon: 's-icon-delete',
    },
};

export const Variants: Story = {
    args: {
        icon: 's-icon-delete',
    },
    render: (args) => ({
        components: { WtgIconButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: ButtonWithVariantsTemplate,
    }),
};

export const Fill: Story = {
    args: {
        icon: 's-icon-delete',
    },
    render: (args) => ({
        components: { WtgIconButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `<WtgIconButton 
        v-bind="args"
        aria-label="Aria Name"
        variant="fill"
        sentiment="primary"
        :icon="args.icon"
        @click="action" />`,
    }),
};

export const Outline: Story = {
    args: {
        icon: 's-icon-delete',
    },
    render: (args) => ({
        components: { WtgIconButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `<WtgIconButton 
        v-bind="args"
        aria-label="Aria Name"
        variant="outline"
        sentiment="default"
        :icon="args.icon"
        @click="action" />`,
    }),
};

export const Ghost: Story = {
    args: {
        icon: 's-icon-delete',
    },
    render: (args) => ({
        components: { WtgIconButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `<WtgIconButton 
        v-bind="args"
        aria-label="Aria Name"
        variant="ghost"
        sentiment="default"
        :icon="args.icon"
        @click="action" />`,
    }),
};

export const Sentiments: Story = {
    name: 'Sentiments',
    args: {
        icon: 's-icon-delete',
    },
    render: (args) => ({
        components: { WtgIconButton, WtgCol, WtgRow },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: ButtonWithSentimentsTemplate,
    }),
    play: async ({ canvasElement, step }) => {
        const canvas = within(canvasElement);
        const focusedButton = canvas.getByTestId('testSentimentsIconButton-primary');
        await step('Focus on button', async () => {
            focusedButton.focus();
            await step('Should show button outline', () => {
                const outline = getComputedStyle(focusedButton).outlineColor;
                expect(outline).toEqual('rgb(55, 30, 225)');
            });
        });
    },
};

export const Disabled: Story = {
    args: {
        icon: 's-icon-delete',
        disabled: true,
    },
    render: (args) => ({
        components: { WtgIconButton },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: `
        <div style="flex: 1 1 100%; display: flex; flex-wrap: wrap; gap: 8px">
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                variant="fill"
                sentiment="primary"
                @click="action">
            </WtgIconButton>
            <WtgIconButton 
                v-bind="args"
                aria-label="Aria Name"
                variant="ghost"
                @click="action">
            </WtgIconButton>
        </div>`,
    }),
};

export const Sandbox: StoryObj = {
    args: { icon: 's-icon-placeholder', label: 'Label' },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgIconButton, WtgCol, WtgRow, WtgPanel, WtgLayoutGrid },
        setup: () => {
            return { args, windowMaximize };
        },
        methods: {
            click: action('click'),
        },
        template: templateWithRtl(ButtonSandboxTemplate),
    }),
};
