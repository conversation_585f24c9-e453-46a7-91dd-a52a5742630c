import { segmentedControlContextKey } from '@composables/segmentedControl';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgIconButton } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgIconButton', () => {
    test('it renders a <button> tag', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('BUTTON');
    });

    test('it renders an <a> tag when the href property is set', () => {
        const wrapper = mountComponent({
            props: {
                href: 'some href',
            },
        });
        expect(wrapper.element.tagName).toBe('A');
    });

    test('it does not render an <a> tag when the href property is set and the control is disabled as <a> tag does not support the disabled attribute', () => {
        const wrapper = mountComponent({
            props: {
                href: 'some href',
                disabled: true,
            },
        });
        expect(wrapper.element.tagName).toBe('BUTTON');
    });

    test('it resets the <wtg-button> sizing defaults as they should not apply to icon button', async () => {
        const wrapper = mountComponent();
        const computedStyle = window.getComputedStyle(wrapper.element);
        expect(computedStyle.minWidth).toBe('');
        expect(computedStyle.maxWidth).toBe('');
    });

    test('it emits a click event when the <button> is clicked', async () => {
        const wrapper = mountComponent();
        const button = wrapper.find('button');
        expect(wrapper.emitted('click')).toBeUndefined();
        await button.trigger('click');
        expect(wrapper.emitted('click')!.length).toBe(1);
        expect(wrapper.emitted('click')![0][0]).toBeInstanceOf(MouseEvent);
    });

    test('it applies correct classes for Icon button that is fill', () => {
        const wrapper = mountComponent({
            props: {
                variant: 'fill',
            },
        });
        expect(wrapper.classes()).toContain('wtg-button');
        expect(wrapper.classes()).toContain('wtg-icon-button');
        expect(wrapper.classes()).toContain('wtg-button--fill');
    });

    test('it applies correct classes for Icon button that is fill and primary ', () => {
        const wrapper = mountComponent({
            props: {
                variant: 'fill',
                sentiment: 'primary',
            },
        });
        expect(wrapper.classes()).toContain('wtg-button');
        expect(wrapper.classes()).toContain('wtg-icon-button');
        expect(wrapper.classes()).toContain('wtg-button--fill');
        expect(wrapper.classes()).toContain('wtg-button--primary');
    });

    test('it applies correct classes for Icon button that is ghost ', () => {
        const wrapper = mountComponent({
            props: {
                variant: 'ghost',
            },
        });
        expect(wrapper.classes()).toContain('wtg-button');
        expect(wrapper.classes()).toContain('wtg-icon-button');
        expect(wrapper.classes()).toContain('wtg-button--ghost');
    });

    describe('when color is set', () => {
        test('it sets the element style', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('text-red');
        });

        test('it sets the icon style when variant is ghost', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'red',
                    variant: 'ghost',
                },
            });
            expect(wrapper.classes()).toContain('text-red');
        });

        test('it sets the icon style when variant is fill', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'red',
                    variant: 'fill',
                },
            });
            expect(wrapper.classes()).toContain('bg-red');
        });
    });

    test('it has tooltip capability mixed in', () => {
        const wrapper = mountComponent({
            props: { tooltip: { content: 'Some tooltip', placement: 'top' } },
        });
        const button = wrapper.findComponent({ name: 'WtgButton' });
        expect(button.vm.tooltipDirective.content).toBe('Some tooltip');
        expect(button.vm.tooltipDirective.placement).toBe('top');
    });

    test('it applies the aria-label from the tooltip when the aria-label is not specified', () => {
        const wrapper = mountComponent({
            props: { tooltip: 'Some string tooltip' },
        });
        const button = wrapper.findComponent({ name: 'WtgButton' });
        expect(button.attributes('aria-label')).toBe('Some string tooltip');
    });

    test('it applies the aria-label from the tooltip options when the aria-label is not specified', () => {
        const wrapper = mountComponent({
            props: { tooltip: { content: 'Some content tooltip', placement: 'top' } },
        });
        const button = wrapper.findComponent({ name: 'WtgButton' });
        expect(button.attributes('aria-label')).toBe('Some content tooltip');
    });

    test('it applies the aria-label from the aria-label prop', () => {
        const wrapper = mountComponent({
            props: {
                ariaLabel: 'button aria-label',
                tooltip: { content: 'Some content tooltip', placement: 'top' },
            },
        });
        const button = wrapper.findComponent({ name: 'WtgButton' });
        expect(button.attributes('aria-label')).toBe('button aria-label');
    });

    test('it toggles an icon button', async () => {
        const mockOnToggle = jest.fn();
        const mockValue = 'mock-value';

        const wrapper = mountComponent({
            props: {
                value: mockValue,
            },
            provide: {
                [segmentedControlContextKey as symbol]: {
                    value: {
                        onToggle: mockOnToggle,
                    },
                },
            },
        });

        const button = wrapper.find('button');
        await button.trigger('click');

        expect(mockOnToggle).toHaveBeenCalledWith(mockValue, true);
    });

    test('it applies the size prop to WtgButton', () => {
        const wrapper = mountComponent({
            props: {
                size: 'l',
            },
        });
        const button = wrapper.findComponent({ name: 'WtgButton' });
        expect(button.props('size')).toBe('l');
    });

    test('it applies the iconSize prop to WtgIcon', () => {
        const wrapper = mountComponent({
            props: {
                iconSize: 'l',
            },
        });
        const icon = wrapper.findComponent({ name: 'WtgIcon' });
        expect(icon.props('size')).toBe('l');
    });

    function mountComponent({ props = {}, provide = {} } = {}) {
        return mount(WtgIconButton as any, {
            props,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
