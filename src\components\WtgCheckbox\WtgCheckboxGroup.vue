<template>
    <div
        ref="root"
        v-floating-vue-tooltip="tooltipDirective"
        :class="computedClass"
        @mouseenter="isHovered = true"
        @mouseleave="isHovered = false"
    >
        <label :id="groupName" class="wtg-checkbox-group-label">
            <slot name="label">
                {{ label }}
            </slot>
        </label>
        <WtgIcon class="wtg-checkbox-group-icon">{{ computedIcon }}</WtgIcon>
        <div v-if="description" class="wtg-checkbox-group-description">{{ description }}</div>
        <div
            :id="groupName"
            role="group"
            :aria-labelledby="groupName"
            :class="horizontal ? 'wtg-checkbox-group-horizontal' : 'wtg-checkbox-group-vertical'"
        >
            <slot />
        </div>
    </div>
</template>

<script setup lang="ts">
import { WtgIcon } from '@components/WtgIcon';
import { AlertLevel, ValidationState, useCurrentNotification } from '@composables/notifications';
import { convertContent, TooltipSentiment, useTooltip } from '@composables/tooltip';
import { computed, getCurrentInstance, PropType, ref } from 'vue';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';

//
// Properties
//
const props = defineProps({
    /**
     * Optional description text displayed below the label.
     */
    description: {
        type: String,
        default: undefined,
    },
    /**
     * The unique id for the checkbox group. If not provided, a unique id is generated.
     */
    id: {
        type: String,
        default: undefined,
    },
    /**
     * The label text for the checkbox group.
     */
    label: {
        type: String,
        default: undefined,
    },
    /**
     * Validation or help messages to display below the group. Can be a string or an array of strings.
     */
    messages: {
        type: [String, Array] as PropType<string | string[]>,
        default: (): string[] => [],
    },
    /**
     * If true, checkboxes are arranged horizontally. Otherwise, they are stacked vertically.
     */
    horizontal: {
        type: Boolean,
        default: false,
    },
    /**
     * The sentiment of the group, affecting color and icon. Can be 'critical', 'warning', or 'success'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'warning' | 'success'>,
        default: undefined,
    },
    /**
     * The validation state object, which may include alert level and validation messages.
     */
    validationState: {
        type: Object as PropType<Readonly<ValidationState>>,
        default: undefined,
    },
    ...makeLayoutGridColumnProps(),
});

//
// State
//
const isHovered = ref(false);
const root = ref<HTMLElement | null>(null);

//
// Composables
//
const instance = getCurrentInstance();
const { createTooltipDirective } = useTooltip();
const { displayCurrentNotification } = useCurrentNotification(root, props.validationState);

useLayoutGridColumn(props);

//
// Computed
//
const groupName = computed(() => {
    return props.id || `checkbox-group-${instance!.uid}`;
});

const computedMessages = computed(() => {
    let messages: string[] = [];
    if (typeof props.messages === 'string') {
        messages.push(props.messages);
    } else if (Array.isArray(props.messages)) {
        messages = messages.concat(props.messages);
    }
    if (props.validationState) {
        messages = messages.concat(props.validationState.messages);
    }
    return messages;
});

const alertLevel = computed(() => {
    return props.validationState?.alertLevel ?? AlertLevel.None;
});

const computedSentiment = computed(() => {
    if (props.sentiment === 'critical' || alertLevel.value === AlertLevel.Error) {
        return 'critical';
    } else if (
        props.sentiment === 'warning' ||
        alertLevel.value === AlertLevel.Warning ||
        alertLevel.value === AlertLevel.MessageError
    ) {
        return 'warning';
    } else if (props.sentiment === 'success') {
        return 'success';
    } else {
        return '';
    }
});

const computedClass = computed(() => {
    return {
        'wtg-checkbox-group': true,
        'wtg-checkbox-group--success': computedSentiment.value === 'success',
        'wtg-checkbox-group--critical': computedSentiment.value === 'critical',
        'wtg-checkbox-group--warning': computedSentiment.value === 'warning',
    };
});

const computedIcon = computed(() => {
    return computedSentiment.value ? `s-icon-status-${computedSentiment.value}` : null;
});

const tooltipDirective = computed(() =>
    createTooltipDirective({
        autoSize: true,
        content: convertContent(computedMessages.value),
        popperClass: 'wtg-tooltip--validation wtg-tooltip--full-width',
        sentiment: computedSentiment.value as TooltipSentiment,
        shown: isHovered.value || displayCurrentNotification.value,
    })
);
</script>

<style lang="scss">
.wtg-checkbox-group {
    .wtg-checkbox-group-label {
        color: var(--s-neutral-txt-default);
        font: var(--s-text-sm-default);
    }

    .wtg-checkbox-group-icon {
        margin-left: var(--s-padding-s);
        width: var(--s-sizing-m);
        height: var(--s-sizing-m);
    }

    .wtg-checkbox-group-description {
        color: var(--s-neutral-txt-weak-default);
        font: var(--s-text-sm-default);
    }

    .wtg-checkbox-group-vertical {
        display: flex;
        flex-direction: column;
    }

    .wtg-checkbox-group-horizontal {
        display: flex;
        flex-direction: row;
        gap: var(--s-padding-xl);
    }

    label ~ .wtg-checkbox-group-vertical,
    label ~ .wtg-checkbox-group-horizontal {
        padding-top: var(--s-padding-m);
    }

    &.wtg-checkbox-group--critical {
        & .wtg-checkbox-group-label {
            color: var(--s-error-txt-default);
        }

        & .wtg-checkbox-group-icon {
            color: var(--s-error-icon-default);
        }
    }

    &.wtg-checkbox-group--warning {
        & .wtg-checkbox-group-label {
            color: var(--s-warning-txt-default);
        }

        & .wtg-checkbox-group-icon {
            color: var(--s-warning-icon-default);
        }
    }

    &.wtg-checkbox-group--success {
        & .wtg-checkbox-group-label {
            color: var(--s-success-txt-default);
        }

        & .wtg-checkbox-group-icon {
            color: var(--s-success-icon-default);
        }
    }
}
</style>
