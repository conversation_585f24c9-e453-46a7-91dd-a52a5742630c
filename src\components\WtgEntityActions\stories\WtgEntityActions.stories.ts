import WtgButton from '@components/WtgButton/WtgButton.vue';
import WtgEntityActions from '@components/WtgEntityActions/WtgEntityActions.vue';
import WtgIcon from '@components/WtgIcon/WtgIcon.vue';
import WtgIconButton from '@components/WtgIconButton/WtgIconButton.vue';
import WtgList from '@components/WtgList/WtgList.vue';
import WtgListItem from '@components/WtgList/WtgListItem.vue';
import WtgPopover from '@components/WtgPopover/WtgPopover.vue';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgEntityActions>;
const meta: Meta<typeof WtgEntityActions> = {
    title: 'Components/Entity Actions',
    component: WtgEntityActions,
    parameters: {
        docs: {
            description: {
                component:
                    'EntityActions are a collection of entity specific actions you can perform based on where you are in the product and what is available on the current page.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=684-32659&mode=design&t=Id65DtVyTBzfOExN-0',
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgEntityActions },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: '<wtg-entity-actions v-bind="args" @click="action"></wtg-entity-actions>',
    }),
    decorators: [
        () => ({
            template: `
            <div style="display:flex;flex-direction:row">
                <story/>
            </div>
            `,
        }),
    ],
};

export default meta;

export const Default: Story = {
    render: (args) => ({
        components: { WtgIcon, WtgEntityActions, WtgButton },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: `
        <wtg-entity-actions v-bind="args" @click="action">
            <template #default>
                <WtgButton variant="ghost" leading-icon="s-icon-workflow">Workflow</WtgButton>
                <WtgButton variant="ghost" leading-icon="s-icon-eDocs">eDocs</WtgButton>
                <WtgButton variant="ghost" leading-icon="s-icon-documentation">Documents</WtgButton>
                <WtgButton variant="ghost" leading-icon="s-icon-related-records">Related items</WtgButton>
                <WtgButton variant="ghost" leading-icon="s-icon-notes">Notes</WtgButton>
                <WtgButton variant="ghost" leading-icon="s-icon-chat">Messages</WtgButton>
                <WtgButton variant="ghost" leading-icon="s-icon-logs">Logs</WtgButton>
            </template>
        </wtg-entity-actions>`,
    }),
};

export const Medium: Story = {
    render: (args) => ({
        components: { WtgIconButton, WtgEntityActions },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: `
        <wtg-entity-actions v-bind="args" @click="action">
            <template #default>
                <WtgIconButton aria-label="Aria Name" variant="ghost" icon="s-icon-workflow"></WtgIconButton> 
                <WtgIconButton aria-label="Aria Name" variant="ghost" icon="s-icon-eDocs"></WtgIconButton> 
                <WtgIconButton aria-label="Aria Name" variant="ghost" icon="s-icon-documentation"></WtgIconButton>  
                <WtgIconButton aria-label="Aria Name" variant="ghost" icon="s-icon-related-records"></WtgIconButton> 
                <WtgIconButton aria-label="Aria Name" variant="ghost" icon="s-icon-notes"></WtgIconButton>   
                <WtgIconButton aria-label="Aria Name" variant="ghost" icon="s-icon-chat"></WtgIconButton>   
                <WtgIconButton aria-label="Aria Name" variant="ghost" icon="s-icon-logs"></WtgIconButton>   
            </template>
        </wtg-entity-actions>`,
    }),
};

export const Small: Story = {
    render: (args) => ({
        components: { WtgIconButton, WtgEntityActions, WtgList, WtgListItem, WtgPopover },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: `
        <wtg-entity-actions v-bind="args" @click="action">
            <template #default>
                <wtg-popover :close-on-content-click=false location="bottom right" nudge-bottom="var(--s-padding-m)">
                    <template #activator="{props}">                                          
                        <WtgIconButton v-bind="props" aria-label="More" variant="ghost" icon="s-icon-menu-kebab"></WtgIconButton>                        
                    </template>
                    <div>
                        <wtg-list>                            
                            <wtg-list-item leading-icon="s-icon-workflow">Workflow</wtg-list-item>
                            <wtg-list-item leading-icon="s-icon-eDocs">eDocs</wtg-list-item>
                            <wtg-list-item leading-icon="s-icon-documentation">Documents</wtg-list-item>
                            <wtg-list-item leading-icon="s-icon-related-records">Related items</wtg-list-item>
                            <wtg-list-item leading-icon="s-icon-notes">Notes</wtg-list-item>
                            <wtg-list-item leading-icon="s-icon-chat">Messages</wtg-list-item>
                            <wtg-list-item leading-icon="s-icon-logs">Logs</wtg-list-item>
                        </wtg-list>
                    </div>
                </wtg-popover>
            </template>
        </wtg-entity-actions>`,
    }),
};
