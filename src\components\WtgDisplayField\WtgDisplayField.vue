<template>
    <div
        v-if="label && value"
        class="d-flex"
        :class="{ 'flex-column': !horizontal, 'align-baseline': horizontal }"
        :style="horizontal ? 'gap: var(--s-spacing-s)' : 'gap: var(--s-spacing-xs)'"
    >
        <WtgLabel class="wtg-display-field__label text-truncate" typography="label" no-wrap>
            {{ label }}
        </WtgLabel>
        <WtgLabel
            :align="align"
            :color="color"
            :font-weight="fontWeight"
            :no-wrap="noWrap"
            :typography="computedTypography"
        >
            {{ displayValue }}
        </WtgLabel>
    </div>
    <WtgLabel
        v-else-if="label"
        :align="align"
        class="wtg-display-field__label text-truncate"
        no-wrap
        typography="text-sm-default"
    >
        {{ label }}
    </WtgLabel>
    <WtgLabel
        v-else
        :align="align"
        :color="color"
        :font-weight="fontWeight"
        :no-wrap="noWrap"
        :typography="computedTypography"
    >
        {{ displayValue }}
    </WtgLabel>
</template>

<script setup lang="ts">
import WtgLabel from '@components/WtgLabel';
import { autoNumericOptionsFromLocale } from '@components/WtgNumberField/utils';
import { MeasureValue } from '@components/WtgUnitField';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import AutoNumeric from 'autonumeric';
import { PropType, computed } from 'vue';
import { DurationDisplayFormat } from '../../language';
import { WtgDisplayFieldTypes } from './types';

//
// Properties
//
const props = defineProps({
    /**
     * The alignment of the text within the display field.
     * Examples include 'start', 'center', or 'end'.
     */
    align: {
        type: String,
        default: undefined,
    },

    /**
     * The color of the text in the display field.
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * The number of decimal places to display for numeric values.
     */
    decimals: {
        type: [Number, String],
        default: 0,
    },

    /**
     * The font weight of the text in the display field.
     * Examples include 'normal', 'bold', or 'lighter'.
     */
    fontWeight: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the label and value will be displayed horizontally.
     */
    horizontal: {
        type: Boolean,
        default: false,
    },

    /**
     * The label text to display in the display field.
     */
    label: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the text will not wrap to the next line.
     */
    noWrap: {
        type: Boolean,
        default: false,
    },

    /**
     * The suffix to display after the value.
     */
    suffix: {
        type: String,
        default: '',
    },

    /**
     * If true, trailing zeroes will be suppressed for numeric values.
     */
    suppressTrailingZeroes: {
        type: Boolean,
        default: false,
    },

    /**
     * The typography style to apply to the text.
     * If no value is set, the typography shall be text-md-strong
     * Examples include 'label', 'text-sm-default', or other design system typography tokens.
     */
    typography: {
        type: String,
        default: '',
    },

    /**
     * If true, seconds will be included in time and duration values.
     */
    useSeconds: {
        type: Boolean,
        default: false,
    },

    /**
     * The value to display in the display field.
     * Can be a string, number, or a `MeasureValue` object.
     */
    value: {
        type: [String, Number, Object] as PropType<string | number | MeasureValue>,
        default: '',
    },

    /**
     * The type of the value to display.
     * Options include 'Date', 'DateTime', 'Time', 'Duration', 'Number', or 'Measure'.
     */
    valueType: {
        type: String as PropType<'date' | 'datetime' | 'duration' | 'measure' | 'number' | '' | 'time'>,
        default: undefined,
    },

    ...makeLayoutGridColumnProps(),
});

//
// Composables
//
const { dateFormatter, dateTimeFormatter, durationFormatter, locale, timeFormatter } = useLocale();
useLayoutGridColumn(props);

//
// Computed
//
const computedTypography = computed(() => {
    return props.typography ? props.typography : 'text-md-strong';
});
const displayValue = computed(() => {
    const { value, useSeconds, suffix } = props;
    let result = value;

    switch (props.valueType) {
        case WtgDisplayFieldTypes.Date:
            result = dateFormatter.value.format(value.toString());
            break;

        case WtgDisplayFieldTypes.DateTime:
            result = dateTimeFormatter.value.format(value.toString(), useSeconds);
            break;

        case WtgDisplayFieldTypes.Time:
            result = timeFormatter.value.format(value.toString(), useSeconds);
            break;

        case WtgDisplayFieldTypes.Duration:
            result = durationFormatter.value.format(value.toString(), DurationDisplayFormat.HoursAndMinutes);
            break;

        case WtgDisplayFieldTypes.Number: {
            result = formatNumericValue(value);
            break;
        }

        case WtgDisplayFieldTypes.Measure: {
            result = formatMeasureValue(value);
            break;
        }
    }

    if (suffix) {
        result += ' ';
        result += suffix;
    }

    return result;
});

//
// Helpers
//
function formatNumericValue(value: number | string | MeasureValue): string {
    const { decimals, suppressTrailingZeroes } = props;

    const curValue = Number(value);
    const curDecimals = Number(decimals);

    if (isFinite(curValue) && isFinite(curDecimals)) {
        const options = autoNumericOptionsFromLocale(locale.value, {
            decimals: curDecimals,
            suppressTrailingZeroes: Boolean(suppressTrailingZeroes),
        });
        return AutoNumeric.format(curValue, options);
    }

    return '';
}

function formatMeasureValue(value: number | string | MeasureValue): string {
    let result = '';
    const measureValue = value as MeasureValue;

    if (measureValue.magnitude !== undefined) {
        result = formatNumericValue(measureValue.magnitude);
    }
    if (result && measureValue.unit) {
        result += ' ';
        result += measureValue.unit;
    }
    return result;
}
</script>

<style lang="scss">
.wtg-display-field__label {
    font: var(--s-text-sm-default);
}
</style>
