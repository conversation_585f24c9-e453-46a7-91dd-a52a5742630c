import * as fs from 'fs';
import fetch from 'node-fetch';
import * as constants from './constants.mjs';

function componentToHex(c) {
    var hex = c.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
}

export function figmaColorToCssColor(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value.a === 1) {
        return (
            '#' +
            componentToHex(Math.floor(value.r * 255)) +
            componentToHex(Math.floor(value.g * 255)) +
            componentToHex(Math.floor(value.b * 255))
        );
    } else {
        return `rgba(${Math.floor(value.r * 255)}, ${Math.floor(value.g * 255)}, ${Math.floor(value.b * 255)}, ${
            Math.round(value.a * 100) / 100
        })`;
    }
}

export function formatName(name) {
    const parts = name
        .toLowerCase()
        .replace('*', '')
        .split('/')
        .filter((name) => name !== 'components');
    return `--s-${parts.join('-')}`;
}

let variables = undefined;

export async function getVariables() {
    if (variables) {
        return Promise.resolve(variables);
    } else {
        return fetchFromFigma('variables/local').then((json) => {
            variables = json.meta.variables;
            return json.meta.variables;
        });
    }
}

export function writeTokens(tokens, file, selector, mediaQuery) {
    if (mediaQuery) {
        const string = Object.entries(tokens)
            .map(([k, v]) => `${k}: ${v}`)
            .sort((a, b) => a.localeCompare(b))
            .join(';\n\t\t');
        fs.writeFile(
            `src/styles/css/${file}`,
            `@media (${mediaQuery}) {\n\t${selector} {\n\t\t${string};\n\t}\n}`,
            (err) => err && console.error(err)
        );
    } else {
        const string = Object.entries(tokens)
            .map(([k, v]) => `${k}: ${v}`)
            .sort((a, b) => a.localeCompare(b))
            .join(';\n\t');
        fs.writeFile(`src/styles/css/${file}`, `${selector} {\n\t${string};\n}`, (err) => err && console.error(err));
    }
}

export function fetchFromFigma(endpoint) {
    return fetch(`https://api.figma.com/v1/files/${constants.fileKey}/${endpoint}`, {
        headers: {
            'X-Figma-Token': constants.token,
        },
    }).then((res) => res.json());
}
