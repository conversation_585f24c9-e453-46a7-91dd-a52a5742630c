export const CardSandboxTemplate = `
<div class="d-flex flex-column ga-2" style="max-width:400px">
    <WtgCard class="d-flex flex-column pa-m ga-1">
        <div class="d-flex mb-2">
            <span class="font-weight-bold">Version ID1234567</span>
            <WtgSpacer />
            <WtgStatus label="Unpublished" />
        </div>
        <span>Butterflies causing disruptions</span>
        <span>Connected: OY20, OY21, OY22</span>
    </WtgCard>
    <WtgCard class="pa-m" max-width="500" color="brand">
        Card with class color
    </WtgCard>
    <WtgCard class="pa-m" max-width="500" color="rgba(55,30,225)">
        Card with style color
    </WtgCard>
    <WtgCard class="pa-m" max-width="500" color="var(--s-error-txt-default)">
        Card with var() color
    </WtgCard>
    <WtgCard class="pa-m" max-width="500" href="https://www.google.com/">
        Card as a link
    </WtgCard>
    <WtgCard class="pa-m" max-width="500" style="margin-top: 10px">
        <WtgStatus label="Completed" sentiment="success" style="position: absolute; top: -10px; right: 10px "/>
        Vuetify 3's VCard sets overflow:hidden, but our WtgCard is compatible with Vuetify 2 behaviour and allows for content to be (partly) outside the bounds of the card.
    </WtgCard>
</div>
`;
