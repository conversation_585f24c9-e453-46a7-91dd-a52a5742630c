# Use Architecture Decision Records (ADRs)

## Status
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

3. Communication: ADRs enhance communication within the team and with stakeholders by providing a reference point for discussions.
4. Onboarding: They are beneficial for onboarding new team members, helping them understand the reasoning behind architectural choices.

Cons:

## Decision

## Status
- [ ] Proposed
- [x] Accepted
- [ ] Rejected
- [ ] Deprecated
- [ ] Superseded

## Consequences

Pros:

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by <PERSON>](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.
