import WtgCard from '@components/WtgCard';
import WtgCol from '@components/WtgCol';
import WtgDraggable from '@components/WtgDraggable/WtgDraggable.vue';
import WtgLabel from '@components/WtgLabel';
import WtgRow from '@components/WtgRow';
import { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';

type Story = StoryObj<typeof WtgDraggable>;
const meta: Meta<typeof WtgDraggable> = {
    title: 'Utilities/Draggable',
    component: WtgDraggable,
    parameters: {
        docs: {
            description: {
                component: 'The Draggable component is used to implement drag and drop functionality in ui.',
            },
        },
        controls: {
            sort: 'alpha',
            exclude: ['$slots'],
        },
    },
    decorators: [
        () => ({
            template: `
                <div style="max-width:400px">
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        list: [
            { name: 'Item: one', id: 0 },
            { name: 'Item: two', id: 1 },
            { name: 'Item: three', id: 2 },
        ],
    },
    render: (args) => ({
        components: { WtgCard, WtgDraggable, WtgRow, WtgCol, WtgLabel },
        setup: () => {
            const list2 = ref(
                args.list
                    ? args.list.map((item) => {
                          return { name: `${item.name} Copy`, id: `${item.id} Copy` };
                      })
                    : []
            );
            return { args, list2 };
        },
        template: `
                <WtgRow>
                    <WtgCol cols="6">
                        <h3>Draggable One</h3>
                        <WtgDraggable
                                :list="args.list"
                                group="items"
                                style="cursor: move;"

                            >
                            <template #header>
                                <WtgLabel>this is a header</WtgLabel>
                            </template>
                            <template #item="{ element }">
                                <WtgCard class="mt-2 pa-m">
                                    {{ element.name }}
                                </WtgCard>
                            </template>
                            <template #footer>
                                <WtgLabel>this is a footer</WtgLabel>
                            </template>
                        </WtgDraggable>
                    </WtgCol>
                    <WtgCol cols="6">
                        <h3>Draggable Two</h3>
                        <WtgDraggable
                                v-model="list2"
                                group="items"
                                item-key="name"
                                style="cursor: move;"
                            >
                            <template #item="{ element }">
                                <WtgCard class="mt-2 pa-m">
                                    {{ element.name }}
                                </WtgCard>
                            </template>
                        </WtgDraggable>
                    </WtgCol> 
                </WtgRow>`,
    }),
};
