<template>
    <div ref="root" v-floating-vue-tooltip="tooltipDirective" :class="computedClass" @click="onClick">
        <div v-if="label" class="wtg-input__label-row">
            <label :for="id" :class="computedLabelClass">{{ label }}</label>
            <WtgIcon
                v-if="info"
                :tooltip="{ content: props.info, placement: TooltipPlacement.Right }"
                class="wtg-input__info"
            >
                s-icon-info-circle
            </WtgIcon>
        </div>
        <div class="wtg-input__content-wrapper">
            <div
                class="wtg-input__content"
                :class="currentNotificationClass"
                @mouseenter="inputHovered = true"
                @mouseleave="inputHovered = false"
            >
                <div class="wtg-input__shadow" />
                <div v-if="hasLeadingSlotContent" class="wtg-input__leading-content">
                    <slot name="leading">
                        <WtgIcon v-if="leadingIcon">{{ leadingIcon }}</WtgIcon>
                    </slot>
                </div>
                <div
                    v-if="!displayOnly && (computedValidationIcon || loading) && alignment === InputAlignment.End"
                    class="wtg-input__leading-content"
                >
                    <WtgLoader v-if="loading"></WtgLoader>
                    <WtgIcon v-if="computedValidationIcon && !loading" class="wtg-input-validation-icon">
                        {{ computedValidationIcon }}
                    </WtgIcon>
                </div>
                <div v-if="!restricted" class="wtg-input__content-slot">
                    <div v-if="!!prefix" class="wtg-input__content-prefix">{{ prefix }}</div>
                    <slot></slot>
                    <div v-if="!!suffix" class="wtg-input__content-suffix">{{ suffix }}</div>
                </div>
                <div v-if="restricted" class="wtg-input__content-slot">
                    <input value="*****" disabled />
                </div>

                <div
                    v-if="!displayOnly && (computedValidationIcon || loading) && alignment !== InputAlignment.End"
                    class="wtg-input__trailing-content"
                    data-testid="input_trailing_slot"
                >
                    <WtgLoader v-if="loading"></WtgLoader>
                    <WtgIcon v-if="computedValidationIcon && !loading" class="wtg-input-validation-icon">
                        {{ computedValidationIcon }}
                    </WtgIcon>
                </div>
                <div
                    v-if="slots.trailing || trailingIcon"
                    class="wtg-input__trailing-icon-content"
                    data-testid="input_trailing-icon_slot"
                >
                    <slot name="trailing">
                        <WtgIcon v-if="trailingIcon && !loading">{{ trailingIcon }}</WtgIcon>
                    </slot>
                </div>
                <div v-if="slots.prompter" class="wtg-input__divider"></div>
                <slot name="prompter" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import WtgIcon from '@components/WtgIcon';
import WtgLoader from '@components/WtgLoader';
import { InputAlignment, makeInputProps, statusIcons } from '@composables/input';
import { useTheme } from '@composables/theme';
import { AlertLevel, useCurrentNotification } from '@composables/notifications';
import { convertContent, TooltipPlacement, TooltipSentiment, useTooltip } from '@composables/tooltip';
import { makeValidationProps, useValidation } from '@composables/validation';
import { computed, ref, useSlots, type PropType } from 'vue';

const { darkMode } = useTheme();

const props = defineProps({
    modelValue: {
        type: null as any as PropType<any>,
        default: null,
    },
    filled: {
        type: Boolean,
        default: false,
    },
    hideMessages: {
        type: Boolean,
        default: false,
    },
    input: {
        type: HTMLElement,
        default: undefined,
    },
    leadingIcon: {
        type: String,
        default: '',
    },
    promptable: {
        type: Boolean,
        default: false,
    },
    trailingIcon: {
        type: String,
        default: '',
    },
    ...makeInputProps(),
    ...makeValidationProps(),

    /**
     * @deprecated Use modelValue instead
     */
    value: {
        type: null as any as PropType<any>,
        default: null,
    },
});

const emit = defineEmits<{
    click: [e: MouseEvent];
}>();

const { isPristine, errorMessages, isValid } = useValidation(props, props.id);

const slots = useSlots();

const alertLevel = computed(() => {
    return props.validationState?.alertLevel ?? AlertLevel.None;
});

const root = ref<HTMLElement | null>(null);
const { displayCurrentNotification } = useCurrentNotification(root, props.validationState);

const computedSentiment = computed(() => {
    if (props.disabled || props.loading) {
        return '';
    } else if (
        props.error ||
        props.errorMessages?.length > 0 ||
        errorMessages.value.length ||
        alertLevel.value === AlertLevel.Error ||
        props.sentiment === 'critical'
    ) {
        return 'critical';
    } else if (
        alertLevel.value === AlertLevel.Warning ||
        alertLevel.value === AlertLevel.MessageError ||
        props.sentiment === 'warning'
    ) {
        return 'warning';
    } else if (alertLevel.value === AlertLevel.Information || props.sentiment === 'info') {
        return 'info';
    } else if (props.sentiment === 'success') {
        return 'success';
    }
    return '';
});

const computedClass = computed(() => ({
    'wtg-input': true,
    'wtg-input--disabled': props.disabled,
    'wtg-input--readonly': !props.disabled && props.readonly,
    'wtg-input--display': props.displayOnly || props.flat,
    'wtg-input--promptable': props.promptable,
    'wtg-input--filled': !darkMode.value && !props.disabled && !props.readonly && props.filled,
    'wtg-input--loading': props.loading,
    'wtg-input--critical': computedSentiment.value === 'critical',
    'wtg-input--warning': computedSentiment.value === 'warning',
    'wtg-input--info': computedSentiment.value === 'info',
    'wtg-input--success': computedSentiment.value === 'success',
    'wtg-input--cell': props.cell,
    'wtg-input--align-end': props.alignment === InputAlignment.End,
    'wtg-input--align-center': props.alignment === InputAlignment.Center,
}));

const computedLabelClass = computed(() => ({
    'wtg-input__label': true,
    'wtg-required': props.required,
}));

const currentNotificationClass = computed(() => ({
    'wtg-input__current-notification': displayCurrentNotification.value,
}));

const computedValidationIcon = computed(() =>
    alertLevel.value === AlertLevel.Error ||
    computedSentiment.value === 'critical' ||
    (!isPristine.value && !isValid.value)
        ? statusIcons.IsInvalidIcon
        : alertLevel.value === AlertLevel.MessageError ||
          alertLevel.value === AlertLevel.Warning ||
          computedSentiment.value === 'warning'
        ? statusIcons.IsWarningIcon
        : computedSentiment.value === 'success'
        ? statusIcons.IsValidIcon
        : alertLevel.value === AlertLevel.Information || computedSentiment.value === 'info'
        ? statusIcons.IsInfoIcon
        : ''
);

const computedMessages = computed(() => {
    const messages = props.errorMessages?.length > 0 ? props.errorMessages : props.messages || undefined;
    let newMessages: string[] = [];
    if (typeof messages === 'string') {
        newMessages.push(messages);
    } else if (Array.isArray(messages)) {
        newMessages = newMessages.concat(messages);
    }
    if (props.validationState) {
        newMessages = newMessages.concat(props.validationState.messages);
    }
    return newMessages;
});

const hasLeadingSlotContent = computed(() => {
    const slot =
        props.leadingIcon ||
        (slots.leading &&
            (typeof slots.leading()[0].children !== 'string' ||
                (slots.leading()[0].children! as string).trim().length > 0));

    return slot;
});

const inputHovered = ref(false);

const displayMessages = computed(() => {
    return (
        !props.disabled &&
        !props.displayOnly &&
        !props.hideMessages &&
        (inputHovered.value || props.focused || displayCurrentNotification.value) &&
        (computedMessages.value.length > 0 || (!isPristine.value && errorMessages.value.length > 0))
    );
});

const { createTooltipDirective } = useTooltip();

const messagesContent = computed(() => {
    let messages = computedMessages.value;
    if (!isPristine.value && errorMessages.value.length > 0) {
        messages = messages.concat(errorMessages.value);
    }
    return convertContent(messages);
});

const tooltipDirective = computed(() =>
    createTooltipDirective({
        autoSize: true,
        content: messagesContent.value,
        popperClass: 'wtg-tooltip--validation wtg-tooltip--full-width',
        sentiment: computedSentiment.value as TooltipSentiment,
        shown: displayMessages.value,
    })
);

function onClick(e: MouseEvent): void {
    if (props.input) {
        props.input?.focus();
    }
    emit('click', e);
}
</script>

<style lang="scss">
.wtg-input.wtg-input--cell {
    & > .wtg-input__content-wrapper > .wtg-input__content {
        border: none !important;
        box-shadow: none !important;
        background: none !important;
        outline: none !important;
        padding: 0;
        & > .wtg-input__shadow {
            display: none;
        }
    }

    &:not(.wtg-search-field) .wtg-input--interactive-element {
        border: none;
    }
}

.wtg-input--align-end {
    input,
    textarea {
        text-align: end;
    }
}

.wtg-input--align-center {
    input,
    textarea {
        text-align: center;
    }
}

.wtg-input {
    display: flex;
    align-content: center;
    justify-content: flex-start;
    flex-direction: column;
    flex: 1 1 auto;
    gap: var(--s-padding-s);
    font: var(--s-text-sm-default);
    max-width: 100%;

    input,
    textarea {
        min-width: 0;
        flex: 1 1;
        width: 100%;
        background-color: transparent;
        border-style: none;
        color: var(--s-neutral-txt-default);
        text-overflow: ellipsis;
        border-radius: 0;
        &:focus {
            outline: none !important;
        }
    }

    .wtg-input__label-row {
        display: flex;
        align-items: flex-start;
        gap: var(--s-padding-xs);
        align-self: stretch;

        .wtg-input__label {
            text-wrap: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            &.wtg-required::after {
                color: var(--s-error-icon-default);
                content: '*';
            }
        }
    }

    .wtg-input__info {
        color: var(--s-info-icon-default);
        font-size: var(--s-sizing-icon-sm);
        padding-inline-start: var(--s-padding-xs);
    }

    > .wtg-input__content-wrapper {
        display: flex;
        position: relative;

        .wtg-input__content {
            display: flex;
            position: relative;
            min-width: 0;
            flex-grow: 1;
            padding: var(--s-padding-s);
            justify-content: flex-end;
            align-items: center;
            gap: var(--s-padding-xs);
            align-self: stretch;
            border-radius: var(--s-radius-s);
            border: 1px solid var(--s-neutral-border-weak-default);
            background: var(--s-neutral-bg-default);
            color: var(--s-neutral-txt-default);

            .wtg-input__divider {
                border-inline-start: 1px solid var(--s-neutral-border-weak-default);
                border-bottom: none;
                border-top: none;
                border-inline-end: none;
                height: 100%;
            }

            & > .wtg-input__shadow {
                border-radius: var(--s-radius-s);
                bottom: 0;
                box-shadow: var(--s-elevation-100);
                content: '';
                left: 0;
                position: absolute;
                right: 0;
                top: 0;
                transition-duration: 0.28s;
                transition-property: box-shadow;
                margin: -1px;
                pointer-events: none;
            }

            .wtg-input__content-slot {
                min-width: 0;
                display: flex;
                flex: 1 1 auto;
                position: relative;
                align-items: center;
                font: var(--s-text-md-default);

                &:focus {
                    outline: none;
                }

                & > .wtg-input__content-prefix {
                    padding-inline-end: var(--s-padding-s);
                }

                & > .wtg-input__content-suffix {
                    padding-inline-start: var(--s-padding-s);
                }
            }

            .wtg-input__content-slot:has(textarea) {
                height: fit-content;
                min-height: var(--s-sizing-l);
            }

            &:hover:not(:has(.wtg-input--interactive-element:hover)) {
                background: var(--s-neutral-bg-weak-hover);
                border: 1px solid var(--s-neutral-border-weak-hover);
            }

            &:focus-within {
                outline-offset: 1px;
                outline: 2px solid var(--s-primary-border-default);
            }
        }
    }

    .wtg-input__trailing-icon {
        display: flex;
    }

    .wtg-input__messages {
        font: var(--s-text-sm-default);
        list-style-type: none;
        margin: 0px;
        padding: 0px;
    }

    &--filled {
        > .wtg-input__content-wrapper {
            > .wtg-input__content {
                border: 1px solid var(--s-neutral-border-active);
            }
        }
    }

    &--disabled {
        pointer-events: none;
        > .wtg-input__label {
            color: var(--s-neutral-txt-disabled);
        }

        > .wtg-input__content-wrapper {
            > .wtg-input__content {
                border: 1px solid var(--s-neutral-border-disabled);
                background: var(--s-neutral-bg-disabled);

                i {
                    color: var(--s-neutral-icon-disabled) !important;
                }
                > .wtg-input__shadow {
                    display: none;
                }

                .wtg-input__content-slot > .wtg-input__content-prefix,
                .wtg-input__content-suffix {
                    color: var(--s-neutral-txt-disabled);
                }
            }
        }

        input,
        textarea {
            color: var(--s-neutral-txt-disabled);
        }
    }

    &--readonly {
        > .wtg-input__content-wrapper {
            > .wtg-input__content {
                background: var(--s-neutral-bg-disabled);
                border: 1px solid var(--s-neutral-border-disabled);
                i {
                    color: var(--s-neutral-icon-disabled) !important;
                }
                > .wtg-input__shadow {
                    display: none;
                }
                &:hover:not(:has(.wtg-input--interactive-element:hover)) {
                    background: var(--s-neutral-bg-disabled);
                    border: 1px solid var(--s-neutral-border-disabled);
                }
            }
        }

        input,
        textarea {
            cursor: default;
        }
    }

    &--display {
        > .wtg-input__content-wrapper {
            > .wtg-input__content {
                border-color: transparent;
                background: transparent;
                gap: 0px;
                padding: var(--s-padding-s) 0px;

                &:hover:not(:has(.wtg-input--interactive-element:hover)) {
                    background: transparent;
                    border-color: transparent;
                }

                &:focus-within {
                    outline-offset: none;
                    outline: none;
                }
                > .wtg-input__shadow {
                    display: none;
                }
            }
        }
    }

    &--loading {
        > .wtg-input__content-wrapper {
            > .wtg-input__content:not(:focus-within) {
                pointer-events: none;
                background: var(--s-neutral-bg-disabled);
                border: 1px solid var(--s-neutral-border-disabled);

                > .wtg-input__shadow {
                    display: none;
                }
            }
        }
    }

    &--success {
        &.wtg-input--readonly {
            > .wtg-input__content-wrapper {
                > .wtg-input__content {
                    border: 1px solid var(--s-neutral-border-disabled);
                    &:hover:not(:has(.wtg-input--interactive-element:hover)),
                    &.wtg-input__current-notification {
                        background: var(--s-neutral-bg-disabled);
                        border: 1px solid var(--s-neutral-border-disabled);
                    }
                }
            }
        }

        > .wtg-input__content-wrapper {
            > .wtg-input__content {
                border: 1px solid var(--s-success-border-default);
                box-shadow: var(--s-elevation-validation-success);

                &:hover:not(:has(.wtg-input--interactive-element:hover)),
                &.wtg-input__current-notification {
                    background: var(--s-success-bg-weak-hover);
                    border: 1px solid var(--s-success-border-hover);
                }

                &:focus-within {
                    border: 1px solid var(--s-success-border-active);
                }

                & > .wtg-input__trailing-content > .wtg-input-validation-icon {
                    color: var(--s-success-icon-default) !important;
                }

                & > .wtg-input__leading-content > .wtg-input-validation-icon {
                    color: var(--s-success-icon-default) !important;
                }
            }
        }
    }

    &--warning {
        &.wtg-input--readonly {
            > .wtg-input__content-wrapper {
                > .wtg-input__content {
                    border: 1px solid var(--s-neutral-border-disabled);
                    &:hover:not(:has(.wtg-input--interactive-element:hover)),
                    &.wtg-input__current-notification {
                        background: var(--s-neutral-bg-disabled);
                        border: 1px solid var(--s-neutral-border-disabled);
                    }
                }
            }
        }

        > .wtg-input__content-wrapper {
            > .wtg-input__content {
                border: 1px solid var(--s-warning-border-default);
                box-shadow: var(--s-elevation-validation-warning);

                &:hover:not(:has(.wtg-input--interactive-element:hover)),
                &.wtg-input__current-notification {
                    background: var(--s-warning-bg-weak-hover);
                    border: 1px solid var(--s-warning-border-hover);
                }

                &:focus-within {
                    border: 1px solid var(--s-warning-border-active);
                }

                & > .wtg-input__trailing-content > .wtg-input-validation-icon {
                    color: var(--s-warning-icon-default) !important;
                }

                & > .wtg-input__leading-content > .wtg-input-validation-icon {
                    color: var(--s-warning-icon-default) !important;
                }
            }
        }
    }

    &--critical {
        &.wtg-input--readonly {
            > .wtg-input__content-wrapper {
                > .wtg-input__content {
                    border: 1px solid var(--s-neutral-border-disabled);
                    &:hover:not(:has(.wtg-input--interactive-element:hover)),
                    &.wtg-input__current-notification {
                        background: var(--s-neutral-bg-disabled);
                        border: 1px solid var(--s-neutral-border-disabled);
                    }
                }
            }
        }

        > .wtg-input__content-wrapper {
            > .wtg-input__content {
                border: 1px solid var(--s-error-border-default);
                box-shadow: var(--s-elevation-validation-error);

                &:hover:not(:has(.wtg-input--interactive-element:hover)),
                &.wtg-input__current-notification {
                    background: var(--s-error-bg-weak-hover);
                    border: 1px solid var(--s-error-border-hover);
                }

                &:focus-within {
                    border: 1px solid var(--s-error-border-active);
                }

                & > .wtg-input__trailing-content > .wtg-input-validation-icon {
                    color: var(--s-error-icon-default) !important;
                }

                & > .wtg-input__leading-content > .wtg-input-validation-icon {
                    color: var(--s-error-icon-default) !important;
                }
            }
        }
    }

    &--info {
        &.wtg-input--readonly {
            > .wtg-input__content-wrapper {
                > .wtg-input__content {
                    border: 1px solid var(--s-neutral-border-disabled);
                    &:hover:not(:has(.wtg-input--interactive-element:hover)),
                    &.wtg-input__current-notification {
                        background: var(--s-neutral-bg-disabled);
                        border: 1px solid var(--s-neutral-border-disabled);
                    }
                }
            }
        }

        > .wtg-input__content-wrapper {
            > .wtg-input__content {
                border: 1px solid var(--s-info-border-default);
                box-shadow: var(--s-elevation-validation-info);

                &:hover:not(:has(.wtg-input--interactive-element:hover)),
                &.wtg-input__current-notification {
                    background: var(--s-info-bg-weak-hover);
                    border: 1px solid var(--s-info-border-hover);
                }

                &:focus-within {
                    border: 1px solid var(--s-info-border-active);
                }

                & > .wtg-input__trailing-content > .wtg-input-validation-icon {
                    color: var(--s-info-icon-default) !important;
                }

                & > .wtg-input__leading-content > .wtg-input-validation-icon {
                    color: var(--s-info-icon-default) !important;
                }
            }
        }
    }

    &--interactive-element {
        display: inline-flex;
        margin-inline-start: calc(var(--s-padding-s) - var(--s-padding-xs));
        justify-content: flex-end;
        align-items: center;
        border-radius: var(--s-radius-xs);
        outline: none;

        &:hover {
            background-color: var(--s-neutral-bg-weak-hover);
        }

        &:active {
            background-color: var(--s-neutral-bg-weak-active);
        }
    }
}
</style>
