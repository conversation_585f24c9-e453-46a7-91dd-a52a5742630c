<template>
    <WtgButton
        :active="active"
        :aria-label="computedAriaLabel"
        :class="computedClass"
        :color="color"
        :disabled="disabled"
        :href="href"
        :loading="loading"
        min-width=""
        max-width=""
        :sentiment="sentiment"
        :target="target"
        :tooltip="tooltip"
        :variant="variant"
        :value="value"
        :size="size"
        @click="onClick"
    >
        <div class="wtg-icon-button__container">
            <slot>
                <WtgIcon class="wtg-icon-button__icon" :size="iconSize">
                    {{ icon }}
                </WtgIcon>
            </slot>
        </div>
    </WtgButton>
</template>

<script setup lang="ts">
import { WtgButton } from '@components/WtgButton';
import { WtgIcon } from '@components/WtgIcon';
import { makeSizeProps } from '@composables/size';
import { makeTooltipProps, useTooltip } from '@composables/tooltip';
import { computed, PropType } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * If true, the button will appear active.
     */
    active: {
        type: Boolean,
        default: false,
    },

    /**
     * The ARIA label for the button, used for accessibility.
     */
    ariaLabel: {
        type: String as PropType<string | undefined>,
        default: undefined,
    },

    /**
     * The color of the button.
     * Can be a predefined color from the design system.
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * Disables the button, making it unclickable and visually indicating its disabled state.
     */
    disabled: {
        type: Boolean,
        default: false,
    },

    /**
     * The URL that the button links to, if used as a hyperlink.
     */
    href: {
        type: String,
        default: undefined,
    },

    /**
     * The name of the icon to display inside the button.
     */
    icon: {
        type: String,
        default: undefined,
    },

    /**
     * If true, a loading indicator will be displayed on the button.
     */
    loading: {
        type: Boolean,
        default: false,
    },

    /**
     * The sentiment or visual style of the button.
     * Options include 'critical', 'primary', or 'success'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'primary' | 'success'>,
        default: undefined,
    },

    /**
     * Specifies where to open the linked document if `href` is provided.
     * Example: '_blank' to open in a new tab.
     */
    target: {
        type: String,
        default: undefined,
    },

    /**
     * The variant of the button.
     * Options include 'fill' or 'ghost'.
     */
    variant: {
        type: String as PropType<'fill' | 'ghost'>,
        default: undefined,
    },

    /**
     * The value associated with the button, used for form submissions or other purposes.
     */
    value: {
        type: [Boolean, Number, String],
        default: undefined,
    },

    /**
     * The size of the icon.
     * Can be one of the predefined sizes: 'xs', 's', 'm', 'l', 'xl', 'xxl'.
     */
    iconSize: {
        type: String as PropType<'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl'>,
        default: undefined,
    },

    ...makeTooltipProps(),
    ...makeSizeProps(),
});

//
// Emits
//
const emit = defineEmits<{
    click: [e: MouseEvent];
}>();

//
// Computed
//
const computedClass = computed(() => ({
    'wtg-icon-button--disabled': props.disabled,
    'wtg-icon-button': true,
}));

const computedAriaLabel = computed(() => {
    const { tooltipDirective } = useTooltip(props);
    const tooltip = tooltipDirective.value ? tooltipDirective.value.content : '';
    return props.ariaLabel || tooltip;
});

//
// Event Handlers
//
function onClick(e: MouseEvent) {
    emit('click', e);
}
</script>

<style lang="scss">
.wtg-icon-button {
    cursor: pointer;
    display: inline-flex;
    padding: var(--s-padding-s);
    justify-content: center;
    align-items: center;
    gap: var(--s-spacing-xs);

    & > .wtg-button__content {
        padding: 0;
    }

    .wtg-icon-button__container {
        display: flex;
        justify-content: center;
        align-items: center;

        > img {
            width: 1em;
            height: 1em;
            font-size: inherit;
            vertical-align: middle;
            fill: currentColor;
            stroke: currentColor;
            flex-shrink: 0;
            pointer-events: none;
        }
    }

    &.wtg-icon-button--disabled {
        pointer-events: none;
        background: var(--s-neutral-bg-disabled);
        border-color: var(--s-neutral-bg-disabled);

        &.wtg-button-primary {
            .wtg-button__shadow {
                box-shadow: none;
            }
        }

        &.wtg-button-default {
            .wtg-button__shadow {
                box-shadow: none;
            }
        }

        .wtg-icon-button__icon {
            color: var(--s-neutral-icon-disabled);
        }
    }

    &--prompter {
        border-start-start-radius: 0px;
        border-end-start-radius: 0px;

        .wtg-button__shadow {
            border-start-start-radius: 0px;
            border-end-start-radius: 0px;
            left: 1px;
        }

        &:focus-within {
            z-index: 1;
        }

        &--embedded {
            border-inline-start: 0px;
        }
    }
}
</style>
