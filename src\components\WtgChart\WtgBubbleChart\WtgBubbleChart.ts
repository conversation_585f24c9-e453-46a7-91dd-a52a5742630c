import WtgChart from '@components/WtgChart/WtgChart.vue';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { BubbleController, Chart, ChartData, ChartOptions, LinearScale, PointElement } from 'chart.js';
import { PropType, VNode, defineComponent, h } from 'vue';

Chart.register(BubbleController, LinearScale, PointElement);

export default defineComponent({
    name: 'WtgBubbleChart',
    props: {
        data: {
            type: Object as PropType<ChartData>,
            default: (): ChartData => {
                return {
                    datasets: [],
                };
            },
        },
        options: {
            type: Object as PropType<ChartOptions>,
            default: (): ChartOptions => {
                return {};
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    setup(props) {
        useLayoutGridColumn(props);
    },
    render(): VNode {
        return h(WtgChart, {
            type: 'bubble',
            data: this.data,
            options: this.options,
            loading: this.loading,
        });
    },
});
