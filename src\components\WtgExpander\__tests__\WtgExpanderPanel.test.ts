import WtgExpander from '@components/WtgExpander/WtgExpander.vue';
import WtgExpanderPanel from '@components/WtgExpander/WtgExpanderPanel.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { h } from 'vue';
import { VExpansionPanel } from 'vuetify/components/VExpansionPanel';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgExpanderPanel', () => {
    test('it renders a VExpansionPanel component', () => {
        const wrapper = mountComponent();
        const expanderPanel = wrapper.findComponent(VExpansionPanel);
        expect(expanderPanel.exists()).toBe(true);
        expect(expanderPanel.classes()).toContain('wtg-expander-panel');
    });

    test('it passes the props to the VExpansionPanel component', () => {
        const wrapper = mountComponent({ props: { disabled: true } });
        const expanderPanel = wrapper.findComponent(VExpansionPanel);
        expect(expanderPanel.props().disabled).toBe(true);
    });

    function mountComponent({ props = {} } = {}) {
        const wrapper = mount(WtgExpander, {
            props,
            global: {
                plugins: [wtgUi],
            },
            slots: {
                default: h(WtgExpanderPanel, props),
            },
        });
        return wrapper.findComponent({ name: 'WtgExpanderPanel' });
    }
});
