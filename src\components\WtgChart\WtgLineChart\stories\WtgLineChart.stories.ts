import { Meta, StoryObj } from '@storybook/vue3';
import { Wtg<PERSON>ine<PERSON>hart } from '../..';

type Story = StoryObj<typeof WtgLineChart>;
const meta: Meta<typeof WtgLineChart> = {
    title: 'Data viz/Line Chart',
    component: WtgLineChart,
    parameters: {
        docs: {
            description: {
                component:
                    'Line graphs are used to track changes over short and long periods of time. When smaller changes exist, line graphs are better to use than bar graphs. Line graphs can also be used to compare changes over the same period of time for more than one group.',
            },
        },
    },
    render: (args) => ({
        components: { WtgLineChart },
        setup: () => ({ args }),
        template: `<wtg-line-chart v-bind="args"/>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        data: {
            labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
            datasets: [
                {
                    label: 'My First Dataset',
                    data: [65, 59, 80, 81, 56, 55, 40],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.5)',
                },
                {
                    label: 'My Second Dataset',
                    data: [81, 56, 55, 40, 65, 59, 80],
                    borderColor: 'rgb(192, 192, 75)',
                    backgroundColor: 'rgba(192, 192, 75, 0.5)',
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
            },
        },
        loading: false,
    },
};
