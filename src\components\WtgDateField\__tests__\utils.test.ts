import { useWtgUi } from '@composables/global';
import timezoneMock from 'timezone-mock';
import { WtgDateFormatter } from '../types';
import {
    convertDateToInputDate,
    convertInputDateToDate,
    formatDate,
    formatMonthYearDisplayValue,
    formatMonthYearParsedValue,
    parseDate,
} from '../utils';

describe('convertInputDateToDate Etc/GMT+12', () => {
    beforeAll(() => {
        timezoneMock.register('Etc/GMT+12');
    });

    afterAll(() => {
        timezoneMock.unregister();
    });

    test.each([
        ['2020-01-01', '2020-01-01T12:00:00.000Z'],
        ['2020-12-08', '2020-12-08T12:00:00.000Z'],
        ['2020-02-29', '2020-02-29T12:00:00.000Z'],
    ])("it converts string '%s' to date '%s'", (input, isoStringOutput) =>
        expect(convertInputDateToDate(input).toISOString()).toEqual(isoStringOutput)
    );

    it('handles timezone correctly around midnight', () => {
        const input = '2020-01-01';
        const date = convertInputDateToDate(input);
        expect(date.toISOString()).toEqual('2020-01-01T12:00:00.000Z');
    });
});

describe('convertInputDateToDate Etc/GMT-12', () => {
    beforeAll(() => {
        timezoneMock.register('Etc/GMT-12');
    });

    afterAll(() => {
        timezoneMock.unregister();
    });

    test.each([
        ['2020-01-01', '2019-12-31T12:00:00.000Z'],
        ['2020-12-08', '2020-12-07T12:00:00.000Z'],
        ['2020-02-29', '2020-02-28T12:00:00.000Z'],
    ])("it converts string '%s' to date '%s'", (input, isoStringOutput) =>
        expect(convertInputDateToDate(input).toISOString()).toEqual(isoStringOutput)
    );

    it('handles timezone correctly around midnight', () => {
        const input = '2020-01-01';
        const date = convertInputDateToDate(input);
        expect(date.toISOString()).toEqual('2019-12-31T12:00:00.000Z');
    });
});

describe('convertInputDateToDate DST handling', () => {
    beforeAll(() => {
        timezoneMock.register('US/Eastern');
    });

    afterAll(() => {
        timezoneMock.unregister();
    });

    test.each([
        ['2020-03-08', '2020-03-08T05:00:00.000Z'],
        ['2020-11-01', '2020-11-01T04:00:00.000Z'],
        ['2020-01-01', '2020-01-01T05:00:00.000Z'],
    ])("it handles DST transition for '%s' to date '%s'", (input, isoStringOutput) =>
        expect(convertInputDateToDate(input).toISOString()).toEqual(isoStringOutput)
    );
});

describe('convertDateToInputDate', () => {
    test.each([
        [new Date('2020-01-01'), '2020-01-01'],
        [new Date('2020-12-08'), '2020-12-08'],
        [new Date('12/08/2020'), '2020-12-08'],
    ])("it converts string date '%s' to string '%s'", (input, inputDateString) =>
        expect(convertDateToInputDate(input)).toEqual(inputDateString)
    );
});

describe('formatMonthYearDisplayValue', () => {
    test.each([
        ['2020-01-01', '01-2020'],
        ['2020-12-08', '12-2020'],
        ['2020-12', '12-2020'],
        ['2020', ''],
    ])("it converts date string '%s' to display value '%s'", (input, expectedDisplayValue) =>
        expect(formatMonthYearDisplayValue(input)).toEqual(expectedDisplayValue)
    );
});

describe('formatMonthYearParsedValue', () => {
    test.each([
        ['2020-01-01', '2020-01'],
        ['2020-12-08', '2020-12'],
        ['2020-12', '2020-12'],
        ['2020', '2020'],
    ])("it converts parsed date string '%s' to display value '%s'", (input, expectedDisplayValue) =>
        expect(formatMonthYearParsedValue(input)).toEqual(expectedDisplayValue)
    );
});

describe('formatDate', () => {
    useWtgUi();

    test.each<[string, string, undefined | WtgDateFormatter]>([
        ['2020-01-01', '01-Jan-20', undefined],
        ['2020-12-08', '08-Dec-20', undefined],
        ['12-2020', '12-2020', undefined],
        ['2020', '2020', undefined],
        ['2020-01-01', '1984', { formatDate: () => '1984', parseDate: (val) => val, today: () => '2021-01-01' }],
        ['2020-12-08', '1984', { formatDate: () => '1984', parseDate: (val) => val, today: () => '2021-01-01' }],
        ['12-2020', '1984', { formatDate: () => '1984', parseDate: (val) => val, today: () => '2021-01-01' }],
        ['2020', '1984', { formatDate: () => '1984', parseDate: (val) => val, today: () => '2021-01-01' }],
    ])("it formats date '%s' to '%s' using default or provided formatter", (input, formattedDate, formatter) =>
        expect(formatDate(input, formatter)).toEqual(formattedDate)
    );
});

describe('parseDate', () => {
    useWtgUi();

    test.each<[string, string, undefined | WtgDateFormatter]>([
        ['01-Jan-20', '2020-01-01', undefined],
        ['08-Dec-20', '2020-12-08', undefined],
        ['12-2020', '2020-12-01', undefined],
        ['2020', '2020-01-01', undefined],
        ['2020-01-01', '1984', { parseDate: () => '1984', formatDate: (val) => val, today: () => '2021-01-01' }],
        ['2020-12-08', '1984', { parseDate: () => '1984', formatDate: (val) => val, today: () => '2021-01-01' }],
        ['12-2020', '1984', { parseDate: () => '1984', formatDate: (val) => val, today: () => '2021-01-01' }],
        ['2020', '1984', { parseDate: () => '1984', formatDate: (val) => val, today: () => '2021-01-01' }],
    ])("it parses date '%s' to '%s' using default or provided formatter", (input, formattedDate, formatter) =>
        expect(parseDate(input, formatter)).toEqual(formattedDate)
    );
});
