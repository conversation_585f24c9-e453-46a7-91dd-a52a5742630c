import WtgExpander from '@components/WtgExpander/WtgExpander.vue';
import WtgExpanderPanel from '@components/WtgExpander/WtgExpanderPanel.vue';
import WtgExpanderPanelContent from '@components/WtgExpander/WtgExpanderPanelContent.vue';
import WtgExpanderPanelHeader from '@components/WtgExpander/WtgExpanderPanelHeader.vue';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { Meta, StoryObj } from '@storybook/vue3';
import { ExpanderSandboxTemplate } from './templates/wtg-expander-sandbox.stories-template';

type Story = StoryObj<typeof WtgExpander>;
const meta: Meta<typeof WtgExpander> = {
    title: 'Components/Expander',
    component: WtgExpander,
    parameters: {
        docs: {
            description: {
                component:
                    'Expander can be used for hiding complex entity related information in order to assist the scan-ability of a page and keep your layout clean.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/g6kTPiwXZrzyIZb4i41RYl/[CargoWise]-SUPPLY---Components?node-id=18220-102615&m=dev',
        },
    },
    render: (args) => ({
        components: { WtgExpander, WtgExpanderPanel, WtgExpanderPanelHeader, WtgExpanderPanelContent },
        setup: () => ({ args }),
        data: () => {
            return {};
        },
        template: `<WtgExpander v-bind="args">
                    <WtgExpanderPanel>
                        <WtgExpanderPanelHeader title="Title" description="Description" />
                        <WtgExpanderPanelContent>
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                        </WtgExpanderPanelContent>
                    </WtgExpanderPanel>
                    <WtgExpanderPanel>
                        <WtgExpanderPanelHeader title="Title" description="Description" />
                        <WtgExpanderPanelContent>
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                        </WtgExpanderPanelContent>
                    </WtgExpanderPanel>
                    <WtgExpanderPanel>
                        <WtgExpanderPanelHeader title="Title" description="Description" />
                        <WtgExpanderPanelContent>
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                        </WtgExpanderPanelContent>
                    </WtgExpanderPanel>
                    <WtgExpanderPanel disabled>
                        <WtgExpanderPanelHeader>This is title</WtgExpanderPanelHeader>
                        <WtgExpanderPanelContent>
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                        </WtgExpanderPanelContent>
                    </WtgExpanderPanel>
                </WtgExpander>`,
    }),
    argTypes: {
        mobile: {
            control: 'boolean',
        },
    },
};

export default meta;
export const Default: Story = {};

export const Sandbox: StoryObj = {
    args: {},
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgExpander, WtgExpanderPanel, WtgExpanderPanelHeader, WtgExpanderPanelContent },
        setup: () => ({ args }),
        data: () => {
            return {};
        },
        template: templateWithRtl(ExpanderSandboxTemplate),
    }),
};
