<template>
    <draggable v-bind="{ ...props }" item-key="id" @update:model-value="onUpdateModelValue">
        <template v-if="$slots['header']" #header>
            <slot name="header" />
        </template>
        <template v-if="$slots['item']" #item="slotData">
            <div><slot name="item" v-bind="slotData" /></div>
        </template>
        <template v-if="$slots['footer']" #footer>
            <slot name="footer" />
        </template>
    </draggable>
</template>

<script setup lang="ts">
import { PropType, ref, watchEffect } from 'vue';
import draggable from 'vuedraggable';

//
// Properties
//
const props = defineProps({
    /**
     * Optional clone function to control how items are copied during drag operations.
     * By default, it returns the original item.
     */
    clone: {
        type: Function,
        default: (original: any): any => {
            return original;
        },
    },

    /**
     * Optional object to bind additional attributes or event listeners to the underlying tag/component.
     */
    componentData: {
        type: Object,
        default: undefined,
    },

    /**
     * Property name on each item that uniquely identifies it. Required for draggable to track changes.
     */
    itemKey: {
        type: String,
        default: undefined,
    },

    /**
     * The source array used for rendering draggable items.
     * Required if v-model is not used (for read-only lists).
     */
    list: {
        type: Array as PropType<Record<string, unknown>[]>,
        default: undefined,
    },

    /**
     * The two-way bound array of items.
     * This is the preferred approach for full drag-and-drop support.
     */
    modelValue: {
        type: Array as PropType<Record<string, unknown>[]>,
        default: undefined,
    },

    /**
     * Optional custom function to control whether an item move is allowed.
     * Called with context about the drag source and target.
     */
    move: {
        type: Function,
        default: undefined,
    },

    /**
     * The HTML tag or component used to render the draggable container.
     * Defaults to a <div> if not specified.
     */
    tag: {
        type: String,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    'update:modelValue': [value: Record<string, unknown>[]];
}>();

//
// Slots
//
defineSlots<{
    /**
     * Optional header slot content.
     */
    header?: () => unknown;

    /**
     * Renders each draggable item.
     * Receives an object with `{ element, index }`.
     */
    item?: (slotProps: { element: Record<string, unknown>; index: number }) => unknown;

    /**
     * Optional footer slot content.
     */
    footer?: () => unknown;
}>();

//
// State
//
const internalValue = ref<Record<string, unknown>[]>([]);

//
// Watchers
//
watchEffect(() => (internalValue.value = props.modelValue ? props.modelValue : []));

//
// Event Handlers
//
function onUpdateModelValue(value: Record<string, unknown>[]) {
    internalValue.value = value;
    emit('update:modelValue', value);
}
</script>
