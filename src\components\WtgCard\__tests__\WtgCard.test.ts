import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { WtgRow } from '@components/WtgRow';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { VCol, VRow } from 'vuetify/components/VGrid';
import { WtgCard } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgCard', () => {
    test('it renders a <div> component', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('DIV');
    });

    test('it adds the wtg-card class to allow application styling to be added', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('wtg-card');
    });

    test('it renders as a VCard to ensure we are backwards compatible with Vue2', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('v-card');
    });

    test('it passes its props to the base WtgCard', () => {
        const wrapper = mountComponent({
            propsData: {
                exact: true,
                href: 'card href',
                link: true,
                replace: true,
                to: 'card to',
            },
        });
        const props = wrapper.findComponent({ name: 'VCard' }).props();
        expect(props.exact).toBe(true);
        expect(props.href).toBe('card href');
        expect(props.link).toBe(true);
        expect(props.replace).toBe(true);
        expect(props.to).toBe('card to');
    });

    test('it defaults the link property to undefined to ensure card click works our of the box', () => {
        const wrapper = mountComponent();
        expect(wrapper.props('link')).toBeUndefined();
    });

    test('it passes the default slot to the <div>', () => {
        const wrapper = mountComponent();
        expect(wrapper.text()).toBe('Some Content');
    });

    test('it passes measurable styles to div element', () => {
        const [height, minHeight, minWidth, maxHeight, maxWidth, width] = [
            '100px',
            '50px',
            '25px',
            '200px',
            '250px',
            '150px',
        ];
        const wrapper = mountComponent({
            propsData: {
                height,
                minHeight,
                minWidth,
                maxHeight,
                maxWidth,
                width,
            },
        });
        expect((wrapper.element as HTMLElement).style.height).toBe(height);
        expect((wrapper.element as HTMLElement).style.minHeight).toBe(minHeight);
        expect((wrapper.element as HTMLElement).style.minWidth).toBe(minWidth);
        expect((wrapper.element as HTMLElement).style.maxHeight).toBe(maxHeight);
        expect((wrapper.element as HTMLElement).style.maxWidth).toBe(maxWidth);
        expect((wrapper.element as HTMLElement).style.width).toBe(width);
    });

    test('it applies the wtg-grid-layout when the layout type is grid', async () => {
        const wrapper = mountComponent({ propsData: { layout: 'grid' } });
        expect(wrapper.html()).toContain('v-col v-col-12');
    });

    test('it wraps the slot elements inside a column', async () => {
        const wrapper = mountComponent({
            propsData: {
                layout: 'grid',
            },
            slots: {
                default: '<span>One</span><span>Two</span><span>Three</span><span data-wtg-layout-grid-ignore></span>',
            },
        });
        const cols = wrapper.findAllComponents(VCol);

        expect(cols.length).toBe(3);
        expect(cols.at(0)?.text()).toBe('One');
        expect(cols.at(1)?.text()).toBe('Two');
        expect(cols.at(2)?.text()).toBe('Three');
    });

    test('when the layout property is set to FLEX, it renders a div to hold the content', () => {
        const wrapper = mountComponent({
            propsData: {
                layout: 'flex',
                caption: 'Some caption',
            },
            slots: {
                default: '<span id="text">Some text</span>',
            },
        });
        const div = wrapper.find('div#content');
        expect(div.exists()).toBe(true);
    });

    test('when the layout property is set to GRID, it renders a layout grid', () => {
        const wrapper = mountComponent({
            propsData: { layout: 'grid' },
        });
        const row = wrapper.findComponent(WtgRow);
        expect(row.exists()).toBe(true);
        expect(row.props('fillAvailable')).toBe(false);
    });

    test('when the layout property is set to GRID_FILL, it renders a layout grid', () => {
        const wrapper = mountComponent({
            propsData: { layout: 'grid-fill' },
        });
        const row = wrapper.findComponent(WtgRow);
        expect(row.exists()).toBe(true);
        expect(row.props('fillAvailable')).toBe(true);
    });

    test('it passes its properties to the layout grid', async () => {
        const wrapper = mountComponent({
            propsData: { layout: 'grid' },
        });
        const row = wrapper.findComponent(VRow);
        expect(row.props('noGutters')).toBe(false);
        await wrapper.setProps({ noGutters: true });
        expect(row.props('noGutters')).toBe(true);
    });

    test('when the layout property is set to FLEX, it renders a div AND it applies the d-flex class', () => {
        const wrapper = mountComponent({
            propsData: {
                layout: 'flex',
            },
        });
        const div = wrapper.find('div#content');
        expect(div.exists()).toBe(true);
        expect(div.classes()).toContain('d-flex');
    });

    test('when the layout property is set to FLEX, it also applies the classes for the flex-directive properties', () => {
        const wrapper = mountComponent({
            propsData: {
                layout: 'flex',
                flexDirection: 'flex-column-reverse',
                flexAlign: 'align-center',
                flexJustify: 'justify-end',
            },
        });
        const div = wrapper.find('div#content');
        expect(div.classes()).toContain('flex-column-reverse');
        expect(div.classes()).toContain('align-center');
        expect(div.classes()).toContain('justify-end');
    });

    test('when the layout property is NOT set to FLEX, it does NOT apply the classes for the flex-directive properties', () => {
        const wrapper = mountComponent({
            propsData: {
                layout: 'grid',
                flexDirection: 'flex-column-reverse',
                flexAlign: 'align-center',
                flexJustify: 'justify-end',
            },
        });
        const div = wrapper.find('div#content');
        expect(div.classes()).not.toContain('flex-column-reverse');
        expect(div.classes()).not.toContain('align-center');
        expect(div.classes()).not.toContain('justify-end');
    });

    test('when layout=fill, it renders a div AND it applies the wtg-fill classes', () => {
        const wrapper = mountComponent({
            propsData: {
                layout: 'fill',
                height: '100%',
            },
        });
        expect(wrapper.classes()).toContain('wtg-fill');
        const div = wrapper.find('div#content');
        expect(div.exists()).toBe(true);
        expect(div.classes()).toContain('wtg-fill');
        expect(div.classes()).toContain('wtg-h-100');
    });

    test('when the fillAvailable property is set and layout is fill, it renders a div AND it applies the wtg-fill classes', () => {
        const wrapper = mountComponent({
            propsData: {
                layout: 'fill',
                fillAvailable: true,
            },
        });
        expect(wrapper.classes()).toContain('wtg-fill');
        expect(wrapper.classes()).toContain('wtg-fill-available');
        const div = wrapper.find('div#content');
        expect(div.exists()).toBe(true);
        expect(div.classes()).toContain('wtg-h-100');
    });

    describe('when color is set', () => {
        test('it sets the element style from valid css style prop colors', () => {
            const wrapper = mountComponent({
                propsData: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('bg-red');
        });

        test('it sets the element style from vars', () => {
            const wrapper = mountComponent({
                propsData: {
                    color: 'var(--s-error-txt-default)',
                },
            });
            expect((wrapper.vm as any).computedStyle.background).toBe('var(--s-error-txt-default)');
            expect((wrapper.vm as any).computedStyle.color).toBe('var(--s-primary-txt-inv-default)');
        });
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    function mountComponent({ propsData = {}, slots = { default: '<span>Some Content</span>' }, provide = {} } = {}) {
        return mount(WtgCard, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
