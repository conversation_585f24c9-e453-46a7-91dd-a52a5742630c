import WtgChart from '@components/WtgChart/WtgChart.vue';
import WtgPieChart from '@components/WtgChart/WtgPieChart/WtgPieChart';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import 'jest-canvas-mock';
import WtgUi from '../../../../WtgUi';

const wtgUi = new WtgUi({
    theme: {
        colors: {
            light: {
                controls: {
                    chart: {
                        background: '#371FE1',
                        backdrop: 'rgba(255, 255, 255, 0.75)',
                        border: '#666',
                        grid: 'rgba(0, 0, 0, 0.6)',
                        text: '#666',
                        ticks: 'rgba(0, 0, 0, 0.6)',
                    },
                },
            },
            dark: {
                controls: {
                    chart: {
                        background: '#2387EE',
                        backdrop: 'rgba(0, 0, 0, 0.75)',
                        border: '#FFF',
                        grid: 'rgba(255, 255, 255, 0.7)',
                        text: '#FFF',
                        ticks: 'rgba(255, 255, 255, 0.7)',
                    },
                },
            },
        },
    },
});

enableAutoUnmount(afterEach);

describe('WtgPieChart', () => {
    test('its name is WtgPieChart', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('WtgPieChart');
    });

    test('it renders a WtgChart of type pie', () => {
        const wrapper = mountComponent();
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('type')).toBe('pie');
    });

    test('it passes its options on to the WtgChart component to render', () => {
        const options = {
            plugins: {
                title: {
                    text: 'A pie chart',
                },
            },
        };
        const wrapper = mountComponent({
            propsData: {
                options,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('options')).toStrictEqual(options);
    });

    test('it passes its data on to the WtgChart component to render', () => {
        const data = {
            labels: ['Red', 'Orange', 'Yellow'],
            datasets: [
                {
                    label: 'Some dataset',
                    data: [5, 7, 12],
                    backgroundColor: ['#F4433680', '#FF980080', '#FFEB3B80'],
                },
            ],
        };
        const wrapper = mountComponent({
            propsData: {
                data,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('data')).toStrictEqual(data);
    });

    test('it passes loading flag to the WtgChart component', () => {
        const wrapper = mountComponent({
            propsData: {
                loading: true,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('loading')).toBe(true);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    function mountComponent({ propsData = {}, provide = {} } = {}) {
        return mount(WtgPieChart, {
            propsData,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
