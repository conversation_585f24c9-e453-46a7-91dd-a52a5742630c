let loading: Promise<void>;

const loadMapsAsync = (apiKey: string, libraries?: string, language?: string): Promise<void> => {
    if ((window as any).google?.maps) {
        return Promise.resolve();
    }

    if (!loading) {
        loading = new Promise((resolve): void => {
            const header: HTMLElement = document.head;
            const apiScriptTag: HTMLScriptElement = document.createElement('script');
            const modulesQueryParm = libraries ? `libraries=${libraries}&` : '';
            const languageQueryParm = language ? `language=${language}&` : '';
            const apiKeyQueryParm = apiKey ? `key=${apiKey}&` : '';
            const mapQuery = `https://maps.googleapis.com/maps/api/js?${modulesQueryParm}${languageQueryParm}${apiKeyQueryParm}v=weekly&callback=wtgInitGoogleMap`;
            apiScriptTag.src = mapQuery;
            apiScriptTag.async = true;
            apiScriptTag.defer = true;

            header.appendChild(apiScriptTag);

            (window as any).wtgInitGoogleMap = (): void => {
                resolve();
            };
        });
    }

    return loading;
};

export default loadMapsAsync;
