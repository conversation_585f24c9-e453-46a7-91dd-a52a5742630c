<template>
    <WtgPopover>
        <template #activator="args">
            <div v-if="action.visible">
                <WtgIconButton
                    v-if="isTabletOrMobile"
                    variant="ghost"
                    icon="s-icon-workflow"
                    :aria-label="action.caption"
                    aria-haspopup="menu"
                    :aria-expanded="open ? 'true' : 'false'"
                    :tooltip="action.caption"
                    v-bind="args.props"
                />
                <WtgButton
                    v-else
                    variant="ghost"
                    leading-icon="s-icon-workflow"
                    aria-haspopup="menu"
                    :aria-expanded="open ? 'true' : 'false'"
                    v-bind="args.props"
                >
                    {{ action.caption }}
                </WtgButton>
            </div>
        </template>
        <template #default>
            <WtgList>
                <WtgListItem v-for="(item, index) in action.menuItems" :key="index" @click="item.onInvoke">
                    {{ item.caption }}
                </WtgListItem>
            </WtgList>
        </template>
    </WtgPopover>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgIconButton from '@components/WtgIconButton';
import { WtgList, WtgListItem } from '@components/WtgList';
import WtgPopover from '@components/WtgPopover';
import { WtgFrameworkTask, WtgFrameworkTaskWorkflowAction } from '@components/framework/types';
import { useFramework } from '@composables/framework';
import { computed, PropType, ref } from 'vue';

const props = defineProps({
    task: { type: Object as PropType<WtgFrameworkTask>, default: undefined },
});

const { isTabletOrMobile } = useFramework();

const open = ref(false);

const action = computed((): WtgFrameworkTaskWorkflowAction => {
    return (
        props.task?.showWorkflowActions ?? {
            visible: false,
            caption: 'Workflows',
            menuItems: [],
        }
    );
});
</script>
