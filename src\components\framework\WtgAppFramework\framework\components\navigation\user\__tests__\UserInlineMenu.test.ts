import { WtgFramework } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import UserInlineMenu from '../UserInlineMenu.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('user-menu-item', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.user = {
            name: 'Test User',
            image: {
                image: "https://localhost/Glow/Global/GlbStaffPhotos(guid'99dd9437-ae1c-41f3-ba78-3a1998bfacb2')/TU_ProfilePhoto",
            },
            onLogOff: jest.fn(),
        } as any;
        application.captions = {
            manageAccount: 'Manage my account',
            changePassword: 'Change Password',
            changeBranchDepartment: 'Change branch/department',
            impersonateContactUser: 'Impersonate Contact User',
            configure: 'Configure',
            theme: 'Theme',
            darkMode: 'Dark Mode',
            lightMode: 'Light Mode',
            mutedMode: 'Muted Mode',
            logOff: 'Logoff',
        } as any;
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is UserInlineMenu', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('UserInlineMenu');
    });

    test('it renders a list group with the users name & profile picture', async () => {
        const wrapper = await mountComponentAsync();
        const listGroup = wrapper.findComponent({ name: 'WtgListGroup' });
        const avatar = listGroup.findComponent({ name: 'WtgAvatar' });
        const title = listGroup.find('.wtg-list-item__container');
        expect(avatar.exists()).toBe(true);
        expect(title.exists()).toBe(true);
        expect(avatar.vm.$props.alt).toBe(application.user.name);
        expect(title.text()).toBe(application.user.name);
        expect(avatar.vm.$props.image).toBe(application.user.image.image);
    });

    describe('when no user profile image available', () => {
        beforeEach(() => {
            application.user = {
                name: 'Test User',
                image: {
                    image: '',
                    fallbackImage: 's-icon-user',
                },
                onLogOff: jest.fn(),
            } as any;
        });

        test('renders fallbackImage as an icon', async () => {
            const wrapper = await mountComponentAsync();
            const listGroup = wrapper.findComponent({ name: 'WtgListGroup' });
            expect(listGroup.exists()).toBe(true);
            const avatar = listGroup.findComponent({ name: 'WtgAvatar' });
            expect(avatar.exists()).toBe(false);
            const image = listGroup.findComponent({ name: 'WtgIcon' });
            expect(image.exists()).toBe(true);
        });
    });

    describe('when open and no user handlers exist', () => {
        let wrapper: any;

        beforeEach(async () => {
            wrapper = await mountComponentAsync();
            application.navDrawer.visible = true;
            const listGroup = wrapper.findComponent({ name: 'WtgListGroup' });
            const listTitle = listGroup.find('.wtg-list-item__container');
            await listTitle.trigger('click');
        });

        test('it will not display user items without a handler', () => {
            expect(wrapper.html()).not.toContain(application.captions.manageAccount);
            expect(wrapper.html()).not.toContain(application.captions.changePassword);
            expect(wrapper.html()).not.toContain(application.captions.impersonateContactUser);
        });

        test('it will display the appearance toggle & log off user item and call user onLogOff', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(userMenuItems.length).toBe(3);
            expect(userMenuItems.at(1).find('.wtg-list-item__container').text()).toBe(application.captions.darkMode);
            expect(userMenuItems.at(2).find('.wtg-list-item__container').text()).toBe(application.captions.logOff);
        });

        test('it will toggle the appearance mode when the option is clicked', async () => {
            expect(wtgUi.dark).toBe(false);
            expect(wtgUi.appearance).toBe('light');
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(1).trigger('click');

            expect(wtgUi.dark).toBe(true);
            expect(wtgUi.appearance).toBe('dark');
            await userMenuItems.at(1).trigger('click');

            expect(wtgUi.dark).toBe(false);
            expect(wtgUi.appearance).toBe('muted');
            await userMenuItems.at(1).trigger('click');

            expect(wtgUi.dark).toBe(false);
            expect(wtgUi.appearance).toBe('light');
        });

        test('it will display a log off user item and call user onLogOff', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(2).trigger('click');

            expect(application.user.onLogOff).toHaveBeenCalledTimes(1);
        });
    });

    describe('when open and user handlers exist', () => {
        let wrapper: any;

        beforeEach(async () => {
            application.user.onChangePassword = jest.fn();
            application.user.onProfile = jest.fn();
            application.user.onChangeBranchDepartment = jest.fn();
            application.user.onImpersonateContactUser = jest.fn();
            wrapper = await mountComponentAsync();
            const listGroup = wrapper.findComponent({ name: 'WtgListGroup' });
            const listTitle = listGroup.find('.wtg-list-item__container');
            await listTitle.trigger('click');

            const userMenuItems = listGroup.findAll('.wtg-list-item__container');
            expect(userMenuItems.length).toBe(7);
            await userMenuItems.at(6).trigger('click');
        });

        test('it will display user items with a handler', async () => {
            const userMenuItems = wrapper.findAll('.wtg-list-item__container');
            expect(userMenuItems.at(1).text()).toBe(application.captions.manageAccount);
            expect(userMenuItems.at(2).text()).toBe(application.captions.changePassword);
            expect(userMenuItems.at(3).text()).toBe(application.captions.changeBranchDepartment);
            expect(userMenuItems.at(4).text()).toBe(application.captions.impersonateContactUser);
        });

        test('it will call user onProfile when my profile is clicked', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(1).trigger('click');

            expect(application.user.onProfile).toHaveBeenCalledTimes(1);
        });

        test('it will call user changePassword when change password is clicked', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(2).trigger('click');

            expect(application.user.onChangePassword).toHaveBeenCalledTimes(1);
        });

        test('it will call user changeBranchDepartment when change branch department is clicked', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(3).trigger('click');

            expect(application.user.onChangeBranchDepartment).toHaveBeenCalledTimes(1);
        });

        test('it will call user impersonateContactUser when impersonate contact user is clicked', async () => {
            const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await userMenuItems.at(4).trigger('click');

            expect(application.user.onImpersonateContactUser).toHaveBeenCalledTimes(1);
        });

        describe('Accessibility', () => {
            test('it renders the menu with aria properties', () => {
                const userMenuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                expect(userMenuItems.at(1).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(1).attributes('aria-label')).toBe(application.captions.manageAccount);
                expect(userMenuItems.at(2).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(2).attributes('aria-label')).toBe(application.captions.changePassword);
                expect(userMenuItems.at(3).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(3).attributes('aria-label')).toBe(application.captions.changeBranchDepartment);
                expect(userMenuItems.at(4).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(4).attributes('aria-label')).toBe(application.captions.impersonateContactUser);
                expect(userMenuItems.at(5).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(5).attributes('aria-label')).toBe(application.captions.darkMode);
                expect(userMenuItems.at(6).attributes('role')).toBe('menuitem');
                expect(userMenuItems.at(6).attributes('aria-label')).toBe(application.captions.logOff);
            });
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(UserInlineMenu) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(UserInlineMenu);
    }
});
