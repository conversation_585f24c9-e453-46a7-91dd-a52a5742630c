import { PropType } from 'vue';

interface ModalProps {
    maxWidth?: number | string;
}

declare const block: readonly ['top', 'bottom'];
declare const inline: readonly ['start', 'end', 'left', 'right'];
type Tblock = (typeof block)[number];
type Tinline = (typeof inline)[number];
type Anchor =
    | Tblock
    | Tinline
    | 'center'
    | 'center center'
    | `${Tblock} ${Tinline | 'center'}`
    | `${Tinline} ${Tblock | 'center'}`;

export const modalProps = (defaults?: ModalProps) => {
    return {
        /**
         * Applies position: absolute to the content element.
         */
        absolute: {
            type: Boolean,
            default: false,
        },

        /**
         * If true, the modal content will be rendered eagerly (immediately).
         */
        eager: {
            type: Boolean,
            default: false,
        },

        /**
         * The height of the modal.
         * Can be a number (interpreted as pixels) or a string (e.g., '50%', '400px').
         */
        height: {
            type: [String, Number],
            default: undefined,
        },

        /**
         * If true, the modal will occupy the entire screen.
         */
        fullscreen: {
            type: <PERSON>olean,
            default: undefined,
        },

        /**
         * Specifies the anchor point for positioning the component, using directional cues to align it either horizontally, vertically, or both.
         */
        location: {
            type: String as PropType<Anchor>,
            default: 'bottom',
        },

        /**
         * The maximum height of the modal.
         * Can be a number (interpreted as pixels) or a string (e.g., '50%', '400px').
         */
        maxHeight: {
            type: [String, Number],
            default: undefined,
        },

        /**
         * The maximum width of the modal.
         * Can be a number (interpreted as pixels) or a string (e.g., '50%', '400px').
         */
        maxWidth: {
            type: [String, Number],
            default: defaults?.maxWidth,
        },

        /**
         * The minimum height of the modal.
         * Can be a number (interpreted as pixels) or a string (e.g., '50%', '400px').
         */
        minHeight: {
            type: [String, Number],
            default: undefined,
        },

        /**
         * The minimum width of the modal.
         * Can be a number (interpreted as pixels) or a string (e.g., '50%', '400px').
         */
        minWidth: {
            type: [String, Number],
            default: undefined,
        },

        /**
         * The current visibility state of the modal, used for two-way binding.
         */
        modelValue: {
            type: Boolean,
            default: undefined,
        },

        /**
         * If true, the modal will not close when clicking outside of it.
         */
        persistent: {
            type: Boolean,
            default: false,
        },

        /**
         * Tab focus will return to the first child of the dialog by default. Disable this when using external tools that require focus such as TinyMCE or vue-clipboard.
         */
        retainFocus: {
            type: Boolean,
            default: undefined,
        },

        /**
         * If true, the dialog will be scrollable, expects a designated height.
         */
        scrollable: {
            type: Boolean,
            default: false,
        },

        /**
         * The size of the modal.
         * Can be one of the predefined sizes: 'm', 'l', 'xl' or empty for content sizing.
         * Defaults to ''.
         * @experimental This is an experimental feature.
         */
        size: {
            type: String as PropType<'m' | 'l' | 'xl' | ''>,
            default: '',
        },

        /**
         * The title text displayed in the modal header.
         */
        title: {
            type: String,
            default: undefined,
        },

        /**
         * The transition effect to use when showing or hiding the modal.
         */
        transition: {
            type: String,
            default: 'dialog-transition',
        },

        /**
         * The width of the modal.
         * Can be a number (interpreted as pixels) or a string (e.g., '50%', '400px').
         */
        width: {
            type: [String, Number],
            default: undefined,
        },

        /**
         * The visibility state of the modal.
         * @deprecated Use `modelValue` instead.
         */
        value: {
            type: Boolean,
            default: undefined,
        },
    };
};
