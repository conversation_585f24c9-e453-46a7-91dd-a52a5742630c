export const AvatarSandboxTemplate = `
<WtgRow>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials=""></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="xxl"></WtgAvatar>
    </WtgCol>    
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="xl"></WtgAvatar>
    </WtgCol>    
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="l"></WtgAvatar>
    </WtgCol>    
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="m"></WtgAvatar>
    </WtgCol>    
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" :disabled="true"></WtgAvatar>
    </WtgCol>
</WtgRow>
<WtgRow>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :icon="args.icon" image="" initials=""></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :icon="args.icon" image="" initials="" size="xxl"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :icon="args.icon" image="" initials="" size="xl"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :icon="args.icon" image="" initials="" size="l"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :icon="args.icon" image="" initials="" size="m"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :icon="args.icon" image="" initials="" :disabled="true"></WtgAvatar>
    </WtgCol>
</WtgRow>
<WtgRow>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :initials="args.initials" image="" icon=""></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :initials="args.initials" image="" icon="" size="xxl"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :initials="args.initials" image="" icon="" size="xl"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :initials="args.initials" image="" icon="" size="l"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" initials="M" image="" icon="" size="m"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" :initials="args.initials" image="" icon="" :disabled="true"></WtgAvatar>
    </WtgCol>
</WtgRow>
<WtgRow>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" image="https://picsum.photos/id/0/5000/3333.jpg" icon="" initials=""></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" image="https://picsum.photos/id/0/5000/3333.jpg" icon="" initials="" size="xxl"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" image="https://picsum.photos/id/0/5000/3333.jpg" icon="" initials="" size="xl"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" image="https://picsum.photos/id/0/5000/3333.jpg" icon="" initials="" size="l"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" image="https://picsum.photos/id/0/5000/3333.jpg" icon="" initials="" size="m"></WtgAvatar>
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin: 8px;" class="d-flex flex-column">
        <WtgAvatar v-bind="args" image="https://picsum.photos/id/0/5000/3333.jpg" icon="" initials="" :disabled="true"></WtgAvatar>
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials=""  notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="l" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="m" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" disabled notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials=""  notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="l" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="m" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" disabled notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials=""  notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="l" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" size="m" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" :image="args.image" icon="" initials="" disabled notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials=""  notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="l" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="m" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" disabled notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials=""  notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="l" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="m" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" disabled notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials=""  notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="l" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" size="m" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" :icon="args.icon" initials="" disabled notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials"  notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="l" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="m" notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" disabled notification-icon="s-icon-placeholder" notification-sentiment="primary"></WtgAvatar>    
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials"  notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="l" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="m" notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" disabled notification-icon="s-icon-placeholder" notification-sentiment="success"></WtgAvatar>    
    </WtgCol>
</WtgRow>

<WtgRow>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials"  notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="xxl" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="xl" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="l" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" size="m" notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
    <WtgCol style="max-width: fit-content; margin:8px;" class="d-flex flex-column">        
        <WtgAvatar v-bind="args" image="" icon="" :initials="args.initials" disabled notification-icon="s-icon-placeholder" notification-sentiment="critical"></WtgAvatar>    
    </WtgCol>
</WtgRow>

`;
