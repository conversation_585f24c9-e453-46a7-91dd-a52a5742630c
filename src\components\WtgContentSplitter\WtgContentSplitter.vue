<template>
    <div :class="computedClass" role="presentation">
        <span class="wtg-content-splitter__content-wrapper">
            <slot />
        </span>
    </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * Specifies the alignment of the content within the splitter.
     * Options include:
     * - `'start'`: Aligns the content to the start.
     * - `'center'`: Centers the content.
     * - `'end'`: Aligns the content to the end.
     */
    justify: {
        type: String as PropType<'start' | 'center' | 'end'>,
        default: 'center',
    },

    /**
     * Specifies the style of the splitter line.
     * Options include:
     * - `'solid'`: Displays a solid line.
     * - `'dashed'`: Displays a dashed line.
     */
    variant: {
        type: String as PropType<'solid' | 'dashed'>,
        default: 'solid',
    },
});

//
// Computed
//
const computedClass = computed(() => [
    'wtg-content-splitter',
    `wtg-content-splitter--${props.variant}`,
    `wtg-content-splitter--${props.justify}`,
]);
</script>

<style lang="scss">
.wtg-content-splitter {
    display: flex;
    flex-direction: row;
    align-items: center;
    &::before,
    &::after {
        flex: 1 1 0;
        content: '';
        border-color: var(--s-neutral-txt-default);
        border-width: 0px;
        border-block-start-width: thin;
        border-inline-start-width: thin;
        opacity: 0.12;
    }
}

.wtg-content-splitter--solid {
    &::before,
    &::after {
        border-style: solid;
    }
}

.wtg-content-splitter--dashed {
    &::before,
    &::after {
        border-style: dashed;
    }
}

.wtg-content-splitter--start::before {
    display: none;
}

.wtg-content-splitter--end::after {
    display: none;
}

.wtg-content-splitter__content-wrapper {
    font: var(--s-title-sm-default);
    padding-inline: var(--s-spacing-m);
}
</style>
