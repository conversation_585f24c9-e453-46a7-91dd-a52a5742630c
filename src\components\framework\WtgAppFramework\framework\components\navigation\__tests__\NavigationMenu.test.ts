import { WtgFramework, WtgFrameworkMenuItem, WtgFrameworkMenuItemType } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../WtgUi';
import NavigationMenu from '../NavigationMenu.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('navigation-menu', () => {
    let el: HTMLElement;
    let application: WtgFramework;
    const menu: WtgFrameworkMenuItem[] = [
        {
            action: WtgFrameworkMenuItemType.Link,
            caption: 'Test Link',
            icon: 'mdi-icon-1',
            href: '#/page/bc1cd66d-fb7b-444b-bc9b-3e9f16b81ee3',
            to: '#/page/bc1cd66d-fb7b-444b-bc9b-3e9f16b81ee3',
            id: 'ad4b0edc-e9dd-40dc-a2bf-19e8f54875b5',
            active: false,
        },
        {
            action: WtgFrameworkMenuItemType.Menu,
            caption: 'Test Menu',
            icon: 'mdi-icon-2',
            id: '5258a62e-da0a-458e-8a8c-1197e9e2318d',
            active: false,
            items: [
                {
                    action: WtgFrameworkMenuItemType.Link,
                    caption: 'Test Level 2 Link',
                    icon: 'mdi-icon-3',
                    href: '#/page/5ba46f99-dd52-45d6-934b-3477cfdf263a',
                    to: '#/page/5ba46f99-dd52-45d6-934b-3477cfdf263a',
                    id: '960e0928-2715-49fd-bc7f-3939e3f6b338',
                    active: false,
                },
                {
                    action: WtgFrameworkMenuItemType.Menu,
                    caption: 'Test Level 2 Menu',
                    id: '7e9eee7b-9002-41fb-9bed-f8c43b923460',
                    active: false,
                    items: [
                        {
                            action: WtgFrameworkMenuItemType.Link,
                            caption: 'Test Level 3 Menu Link',
                            icon: 'mdi-icon-4',
                            href: '#/page/7b671dd3-e629-485e-9ca8-697e15dbde82',
                            to: '#/page/7b671dd3-e629-485e-9ca8-697e15dbde82',
                            id: '1b6928b0-44b6-4dff-9a33-510396a713c3',
                            active: false,
                        },
                    ],
                },
            ],
        },
    ];

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is NavigationMenu', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('NavigationMenu');
    });

    test('it renders a wtg-list', async () => {
        const wrapper = await mountComponentAsync();
        const list = wrapper.findComponent({ name: 'WtgList' });
        expect(list.exists()).toBe(true);
    });

    describe('when rendering menu items', () => {
        let wrapper: any;

        beforeEach(async () => {
            application.menu = menu;
            await nextTick();
            wrapper = await mountComponentAsync();
        });

        test('it renders a list item for each menu item', () => {
            const listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(listItems.length).toBe(5);
        });

        test('it renders the list item with the menu caption & href properties', () => {
            const listItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(0);
            expect(listItem.text()).toBe('Test Link');
            expect(listItem.vm.$props.href).toBe('#/page/bc1cd66d-fb7b-444b-bc9b-3e9f16b81ee3');
            expect(listItem.vm.$props.to).toBe('#/page/bc1cd66d-fb7b-444b-bc9b-3e9f16b81ee3');
        });

        test('it emits a menu-item-click when the menu item is clicked', async () => {
            const listItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(0);
            await listItem.trigger('click');

            expect(wrapper.emitted('item-click').length).toBe(1);
        });

        test('it renders a list group when an item has items', () => {
            const listGroup = wrapper.findAllComponents({ name: 'WtgListGroup' });
            expect(listGroup.length).toBe(2);
        });

        test('it sets the menu caption as the list group title', () => {
            const listGroupHeader = wrapper
                .findAllComponents({ name: 'WtgListGroup' })
                .at(0)
                .find('.wtg-list-item__container');
            expect(listGroupHeader.text()).toBe('Test Menu');
        });

        test('it will show an icon for top level menu items', async () => {
            const topLevelItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(0);
            const topLevelGroup = wrapper.findAllComponents({ name: 'WtgListGroup' }).at(0);
            expect(topLevelItem.findComponent({ name: 'WtgIcon' }).exists()).toBe(true);
            expect(topLevelGroup.findComponent({ name: 'WtgIcon' }).exists()).toBe(true);
        });

        test('the icon will as defined in the menu item', () => {
            const topLevelItemIcon = wrapper
                .findAllComponents({ name: 'WtgListItem' })
                .at(0)
                .findComponent({ name: 'WtgIcon' });
            const topLevelGroup = wrapper.findAllComponents({ name: 'WtgListGroup' }).at(0);
            expect(topLevelItemIcon.html()).toContain('mdi-icon-1');
            expect(topLevelGroup.html()).toContain('mdi-icon-2');
        });

        test('it emits an item clicked when a list item is clicked', async () => {
            const item = wrapper.findAllComponents({ name: 'WtgListItem' }).at(0);
            await item.trigger('click');

            expect(wrapper.emitted('item-click').length).toBe(1);
        });

        test('it emits an item clicked when a list group is clicked', async () => {
            const groupHeader = wrapper.findAllComponents({ name: 'WtgListGroup' }).at(0);
            await groupHeader.trigger('click');

            expect(wrapper.emitted('group-click').length).toBe(1);
        });
    });

    describe('when a menu item is active', () => {
        let wrapper: any;
        beforeEach(async () => {
            const menuGroup = menu[1].items;
            let menuSubGroup;
            menu[1].active = true;
            if (menuGroup) {
                menuSubGroup = menuGroup[1].items;
                menuGroup[1].active = true;
            }
            if (menuSubGroup) menuSubGroup[0].active = true;
            application.menu = menu;
            await nextTick();
            wrapper = await mountComponentAsync();
        });

        test('it will open a list group', () => {
            const topLevelGroup = wrapper.findAllComponents({ name: 'WtgListGroup' }).at(0);
            const subLevelGroup = wrapper.findAllComponents({ name: 'WtgListGroup' }).at(1);
            expect(topLevelGroup.vm.$props.modelValue).toBe(true);
            expect(subLevelGroup.vm.$props.modelValue).toBe(true);
        });
    });

    describe('Accessibility', () => {
        let wrapper: any;

        beforeEach(async () => {
            application.menu = menu;
            await nextTick();
            wrapper = await mountComponentAsync();
        });

        test('it renders list items with the role and aria-label attributes', () => {
            const listItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(0);
            expect(listItem.attributes('role')).toBe('menuitem');
            expect(listItem.attributes('aria-label')).toBe('Test Link');
        });

        test('it renders list groups with the role, aria-haspopup and aria-label attributes', () => {
            const listGroup = wrapper.findAllComponents({ name: 'WtgListGroup' }).at(0);
            expect(listGroup.attributes('role')).toBe('menuitem');
            expect(listGroup.attributes('aria-haspopup')).toBe('menu');
            expect(listGroup.attributes('aria-label')).toBe('Test Menu');
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(NavigationMenu) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
            stubs: ['router-link'],
        });
        await flushPromises();
        return wrapper.findComponent(NavigationMenu);
    }
});
