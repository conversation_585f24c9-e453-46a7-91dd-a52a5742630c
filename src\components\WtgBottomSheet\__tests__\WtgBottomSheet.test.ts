import WtgBottomSheet from '@components/WtgBottomSheet/WtgBottomSheet.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import { VBottomSheet } from 'vuetify/components';
import WtgUi from '../../../WtgUi';

const wtgUi = new WtgUi();
enableAutoUnmount(afterEach);

describe('WtgBottomSheet', () => {
    test('it renders a VBottomSheet component', () => {
        const wrapper = mountComponent();
        const container = wrapper.findComponent(VBottomSheet);
        expect(container.exists()).toBe(true);
    });

    test('it passes its props correctly', () => {
        const wrapper = mountComponent({
            props: {
                modelValue: true,
                persistent: true,
            },
        });

        expect(wrapper.props('modelValue')).toBe(true);
        expect(wrapper.props('persistent')).toBe(true);
    });

    test('it closes when clicking outside if persistent is false', async () => {
        const wrapper = mountComponent({
            props: { modelValue: true, persistent: false },
        });

        await wrapper.vm.$emit('update:modelValue', false);
        await nextTick();

        expect(wrapper.emitted('update:modelValue')).toBeTruthy();
        expect(wrapper.emitted('update:modelValue')![0]).toEqual([false]);
    });

    test('it renders the default slot', async () => {
        const wrapper = mountComponent({
            slots: {
                default: '<div class="test-content">Sheet Content</div>',
            },
            props: { modelValue: true },
        });

        expect(wrapper.html()).not.toBe('');
        expect(document.querySelector('.test-content')).not.toBeNull();
        expect(document.querySelector('.test-content')!.textContent).toBe('Sheet Content');
    });

    test('it closes when modelValue is set to false', async () => {
        const wrapper = mountComponent({ props: { modelValue: true } });

        expect(wrapper.props('modelValue')).toBe(true);
        await wrapper.setProps({ modelValue: false });
        await nextTick();

        expect(wrapper.props('modelValue')).toBe(false);
    });

    test('it listens to the update:modelValue event on inner VBottomSheet component, which is used to communicate the visibility state through v-model', async () => {
        const wrapper = mountComponent();
        await wrapper.findComponent({ name: 'VBottomSheet' }).vm.$emit('update:modelValue', true);

        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')?.[0][0]).toBe(true);
    });

    test('it does not close when persistent is true', async () => {
        const wrapper = mountComponent({
            props: { modelValue: true, persistent: true },
        });

        await wrapper.trigger('click');

        expect(wrapper.props('modelValue')).toBe(true);
        expect(wrapper.emitted('update:modelValue')).toBeFalsy();
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgBottomSheet, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
