<template>
    <WtgNavigationDrawer
        v-model="model"
        class="wtg-drawer"
        color="transparent"
        location="right"
        disable-resize-watcher
        :order="order"
        :width="internalWidth"
        :scrim="false"
        temporary
        touchless
    >
        <div
            id="splitter"
            class="wtg-drawer-splitter"
            style="position: absolute; top: 0px; left: 0px; height: 100%; width: 8px"
            @mousedown="onDown"
            @touchstart.prevent="onTouchDown"
        />
        <div ref="drawer" class="wtg-drawer__container">
            <div class="wtg-drawer__header">
                <div class="wtg-drawer__header__container">
                    <div class="wtg-drawer__title__container">
                        <WtgLabel typography="title-md-default">{{ title }}</WtgLabel>
                        <WtgStatus v-if="statusLabel" :label="statusLabel" :sentiment="statusSentiment" />
                    </div>
                    <div class="wtg-drawer__controls">
                        <slot name="toolbar" />
                    </div>
                    <WtgIconButton
                        v-if="props.dismissible"
                        :aria-label="ariaLabelClose"
                        :tooltip="ariaLabelClose"
                        variant="ghost"
                        icon="s-icon-close"
                        @click="model = false"
                    />
                </div>
                <div v-if="slots.tabs" class="wtg-drawer__tab__group">
                    <slot name="tabs" />
                </div>
            </div>
            <div class="wtg-drawer__content">
                <slot />
            </div>
            <div class="wtg-drawer__action__bar">
                <slot name="actions" />
            </div>
        </div>
    </WtgNavigationDrawer>
</template>

<script setup lang="ts">
import { WtgFrameworkTaskStatus } from '@components/framework/types';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import WtgNavigationDrawer from '@components/WtgNavigationDrawer';
import WtgStatus from '@components/WtgStatus';
import { StatusSentiment } from '@components/WtgStatus/types';
import { useWtgUi } from '@composables/global';
import { useLocale } from '@composables/locale';
import { computed, onMounted, PropType, ref, useSlots, watch } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * Determines if the drawer can be dismissed by the user.
     */
    dismissible: {
        type: Boolean,
        default: false,
    },
    /**
     * Adjust the order of the drawer in relation to its registration order.
     */
    order: {
        type: [String, Number],
        default: 0,
    },
    /**
     * The status object to display in the drawer.
     * Includes properties like label and sentiment.
     */
    status: {
        type: Object as PropType<WtgFrameworkTaskStatus | undefined>,
        default: undefined,
    },
    /**
     * The title of the drawer.
     */
    title: {
        type: String,
        default: '',
    },
    /**
     * The width of the drawer, can be a string (e.g., '350px') or a number (e.g., 350).
     */
    width: {
        type: [String, Number],
        default: 350,
    },
});
const model = defineModel<boolean | undefined>({ default: undefined });

//
// Emits
//
const emit = defineEmits<{
    'update:width': [value: number | string];
}>();

//
// Slots
//
const slots = useSlots();

//
// State
//
const drawer = ref<HTMLElement>();
const internalWidth = ref(props.width);
const resizeDiv = ref<HTMLDivElement | undefined>(undefined);
const offsetX = ref(0);
const startX = ref(0);
const prevClick = ref(-1);

//
// Composables
//
const wtgUI = useWtgUi();
const { formatCaption } = useLocale();

//
// Computed
//
const ariaLabelClose = computed(() => formatCaption('dialog.cancel'));
const statusLabel = computed(() => props.status?.label);
const statusSentiment = computed(() => props.status?.sentiment as StatusSentiment);

//
// Watchers
//
watch(internalWidth, () => {
    emit('update:width', internalWidth.value);
});

watch(model, (isOpen) => {
    if (drawer.value) {
        drawer.value.inert = !isOpen;
    }
});

//
// Event Handlers
//
function onTouchDown(e: TouchEvent): void {
    onDown(e as unknown as MouseEvent);
}

function onDown(e: MouseEvent): void {
    const now = new Date().getTime();
    const doubleClick = now - prevClick.value < 500;
    prevClick.value = now;

    if (doubleClick) {
        internalWidth.value = props.width;
        prevClick.value = 0;
        reset();
    } else {
        const rc = drawer.value!.getBoundingClientRect();
        const rcRoot = document.body!.getBoundingClientRect();
        offsetX.value = 0;
        startX.value = e.pageX;
        internalWidth.value = rc.width;

        resizeDiv.value = document.createElement('div');
        resizeDiv.value.classList.add('wtg-drawer-splitter-active');
        resizeDiv.value.style.position = 'absolute';
        resizeDiv.value.style.left = rc.left + 'px';
        resizeDiv.value.style.top = rc.top - rcRoot.top + 'px';
        resizeDiv.value.style.width = '4px';
        resizeDiv.value.style.height = rc.height + 'px';
        resizeDiv.value.style.background = wtgUI.dark ? 'rgba(255, 255, 255, 0.54)' : 'rgba(0, 0, 0, 0.54)';

        document.body.appendChild(resizeDiv.value);

        document.addEventListener('mousemove', documentMouseMove);
        document.addEventListener('mouseup', documentMouseUp);
    }
    e.preventDefault();
    e.stopPropagation();
}

//
// Helpers
//
function reset(): void {
    if (resizeDiv.value) {
        document.removeEventListener('mousemove', documentMouseMove);
        document.removeEventListener('mouseup', documentMouseUp);
        document.body.removeChild(resizeDiv.value);
        resizeDiv.value = undefined;
    }
}

function documentMouseUp(e: MouseEvent): void {
    if (resizeDiv.value) {
        if (offsetX.value !== 0) {
            if (typeof internalWidth.value === 'number') {
                internalWidth.value -= offsetX.value;
            }
        }
        reset();
        e.preventDefault();
        e.stopPropagation();
    }
}

function documentMouseMove(e: MouseEvent): void {
    if (e.buttons === 0) {
        prevClick.value = 0;
        reset();
    } else if (resizeDiv.value) {
        offsetX.value = e.pageX - startX.value;
        resizeDiv.value.style.transform = `translateX(${offsetX.value}px)`;
    }
}

//
// Lifecycle
//
onMounted(() => {
    if (drawer.value) {
        drawer.value.inert = !model.value;
    }
});
</script>

<style lang="scss">
.wtg-drawer {
    display: flex;
    padding: var(--s-padding-null);
    flex-direction: column;
    align-items: flex-start;
    border-radius: var(--s-radius-null);
    box-shadow: var(--s-elevation-100);
    background-color: var(--s-neutral-canvas-default);

    > .v-navigation-drawer__content {
        width: 100%;
    }

    .wtg-drawer__container {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1 0 0;
        align-self: stretch;
        border-radius: var(--s-radius-null);

        .wtg-drawer__header {
            display: flex;
            padding: 0px var(--s-padding-xl);
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            align-self: stretch;

            border-radius: var(--s-radius-null);
            border-bottom: 1px solid var(--s-neutral-border-weak-default);
            border-left: 1px solid var(--s-neutral-border-weak-default);
            background: var(--s-neutral-bg-default);

            box-shadow: var(--s-elevation-100);
            color: var(--s-neutral-txt-default);

            .wtg-drawer__header__container {
                display: flex;
                height: 52px;
                padding: var(--s-padding-l) 0px;
                align-items: center;
                gap: var(--s-spacing-m);
                align-self: stretch;

                border-radius: var(--s-radius-null);

                .wtg-drawer__title__container {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    flex: 1 0 0;
                    border-radius: var(--s-radius-null);
                }

                .wtg-drawer__controls {
                    display: flex;
                    justify-content: center;
                    align-items: flex-start;
                    gap: var(--s-spacing-s);
                    border-radius: var(--s-radius-null);
                }
            }

            .wtg-drawer__tab__group {
                display: flex;
                height: 36px;
                align-items: flex-end;
                gap: var(--s-padding-xl);
                align-self: stretch;

                border-top: 1px solid var(--s-neutral-border-weak-default);
            }
        }

        .wtg-drawer__content {
            flex: 1 0 0;
            align-self: stretch;
            overflow: auto;

            border-radius: var(--s-radius-null);
            border-left: 1px solid var(--s-neutral-border-weak-default);
            background: var(--s-neutral-canvas-default);
        }

        .wtg-drawer__action__bar {
            display: flex;
            padding: var(--s-padding-xl);
            justify-content: flex-end;
            align-items: center;
            gap: var(--s-spacing-m);
            align-self: stretch;

            border-radius: var(--s-radius-null);
            border-top: 1px solid var(--s-neutral-border-weak-default);
            border-left: 1px solid var(--s-neutral-border-weak-default);
            background: var(--s-neutral-bg-default);
            color: var(--s-neutral-txt-default);
        }
    }
}
.wtg-drawer-splitter {
    opacity: 0;
    cursor: ew-resize;
    position: absolute;
    right: 0px;
    height: 16px;
    top: calc(50% - 8px);
    width: 8px;
    user-select: none;
}
th:hover .wtg-drawer-splitter {
    opacity: 1;
}
.theme--light .wtg-drawer-splitter {
    background-color: rgba(0, 0, 0, 0.12);
}
.theme--dark .wtg-drawer-splitter {
    background-color: rgba(255, 255, 255, 0.12);
}
.wtg-drawer-splitter-active {
    z-index: 10000;
}
</style>
