# Use Storybook for Developing Supply Components

## Status
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

Storybook is a development environment tool that is used as a playground for UI components. It allows us, the developers, to create and test components in isolation. It runs outside of the app, too, so project dependencies won't affect the behaviour of components.

## Decision

## Status
- [ ] Proposed
- [x] Accepted
- [ ] Rejected
- [ ] Deprecated
- [ ] Superseded

Storybook is a powerful tool commonly used for building and testing UI components in isolation. When combined with Vue.js, it offers several benefits that can enhance the development process. 

Integrating Storybook with Vue.js can significantly streamline the development process by offering a visual, isolated environment for building and testing UI components. It encourages reusability, improves collaboration, and ensures high-quality, consistent user interfaces. The added features, such as interactive testing, documentation generation, and the vast ecosystem of Storybook addons, make it an invaluable tool for any Vue.js development workflow.

## Consequences

Pros:
1. Component development in Isolation: Storybook allows you to develop Vue components in isolation, without needing to worry about the entire app's dependencies. This makes it easier to focus solely on the design and functionality of individual components.
2. Faster Iteration: You can iterate on components quickly since changes are reflected immediately in Storybook. This speeds up the development process, as you don’t need to navigate the whole app to test components.
3. Design-Dev Collaboration: Designers and developers can collaborate more effectively. Designers can review components in Storybook without needing to run the entire app, making feedback more focused and precise.
4. Live Previews: Storybook allows for live previews of components, making it easier for non-developers (like designers or stakeholders) to see how a component behaves or looks without running a full application.
5. Visual Regression Testing ensuring visual Consistency: With Storybook, you can implement tools for visual regression testing (like Chromatic), which helps you catch unexpected visual changes when UI components are modified. This ensures that your UI remains consistent across updates.
6. Snapshot Testing: Storybook integrates with snapshot testing tools to automatically compare the rendered output of components against a baseline, ensuring that no unintended changes are introduced.
7. Documentation and Auto-Generated Docs: Storybook can automatically generate documentation for each component, displaying props, events, and usage examples. This makes it easier to document your components without needing to write additional markdown or documentation by hand.
8. Component Stories: You can create "stories" for each component, which describe different states and variations. These stories serve as live examples of how components should be used, making onboarding for new developers easier.
9. Component Interactive Testing: Storybook enables interactive testing of UI components in isolation, which can help ensure the behavior is correct before integrating it into the main application.
10. Integration with Testing Libraries: Storybook integrates with Vue testing tools like Jest, Cypress, and Testing Library, allowing you to write and run tests directly within the Storybook environment.
11. Component Reusability: By developing components in isolation and seeing their behavior in Storybook, you can easily reuse them across your projects without worrying about conflicts or dependencies.
12. Improved Code Quality allowing Focus on Component Design: Storybook encourages developers to think about component design, accessibility, and usability early in the process, leading to higher-quality components.
13. Consistent UI Patterns: By using Storybook, it’s easier to define and follow UI patterns and standards, leading to a more consistent and cohesive user interface throughout the application.

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.
