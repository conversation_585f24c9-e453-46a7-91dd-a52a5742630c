<template>
    <div class="d-flex flex-grow-1 flex-shrink-1" style="min-width: 0px">
        <WtgSpacer class="d-flex flex-shrink-1" style="position: relative; min-width: 100px">
            <WtgMenuBar
                :actions="defaultActions"
                :disabled="isProcessing"
                button-variant="ghost"
                hide-more-caption
                more-icon="s-icon-menu-meatballs"
                top
                nudge-top="4px"
            />
        </WtgSpacer>
        <div class="d-flex">
            <WtgButton
                v-for="action in destructiveActions"
                :key="action.id"
                class="ml-1"
                max-height="30"
                :disabled="isProcessing"
                outlined
                text
                :trailing-icon="action.icon"
                @click="action.click"
            >
                {{ action.caption }}
            </WtgButton>
            <WtgDivider v-if="showDivider" vertical class="divider ml-4 mr-3" />
            <WtgButton
                v-for="action in secondaryActions"
                :key="action.id"
                class="ml-1"
                max-height="30"
                :disabled="isProcessing"
                outlined
                text
                :trailing-icon="action.icon"
                @click="action.click"
            >
                {{ action.caption }}
            </WtgButton>
            <WtgButton
                v-for="action in primaryActions"
                :key="action.id"
                variant="fill"
                sentiment="primary"
                max-height="30"
                class="ml-1"
                :disabled="isProcessing"
                :trailing-icon="action.icon"
                @click="action.click"
            >
                {{ action.caption }}
            </WtgButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    WtgFrameworkTask,
    WtgFrameworkTaskGenericAction,
    WtgFrameworkTaskGenericActionPlacement,
    WtgFrameworkTaskStandardAction,
} from '@components/framework/types';
import { WtgButton } from '@components/WtgButton';
import { WtgDivider } from '@components/WtgDivider';
import { WtgActionItemData } from '@components/WtgMenu';
import WtgMenuBar from '@components/WtgMenuBar';
import { WtgSpacer } from '@components/WtgSpacer';
import { computed, PropType } from 'vue';

let nextID = 1;

const props = defineProps({
    task: {
        type: Object as PropType<WtgFrameworkTask>,
        default: (): WtgFrameworkTask => new WtgFrameworkTask(),
    },
});

const cancelAction = computed((): WtgFrameworkTaskStandardAction => {
    return props.task.cancelAction;
});

const saveAction = computed((): WtgFrameworkTaskStandardAction => {
    return props.task!.saveAction;
});

const saveCloseAction = computed((): WtgFrameworkTaskStandardAction => {
    return props.task!.saveCloseAction;
});

const genericActions = computed((): WtgFrameworkTaskGenericAction[] => {
    return props.task.genericActions;
});

const isProcessing = computed((): boolean | undefined => {
    return saveAction.value.loading || saveCloseAction.value.loading || cancelAction.value.loading;
});

const defaultActions = computed((): WtgActionItemData[] => {
    const itemActions: WtgActionItemData[] = [];
    const categoryActions: WtgActionItemData[] = [];
    const categories: Record<string, any> = {};
    const filteredActions = genericActions.value.filter(
        (action) => action.placement === WtgFrameworkTaskGenericActionPlacement.Default
    );
    filteredActions.forEach((action) => {
        if (action.category) {
            if (categories[action.category]) {
                categories[action.category].actions.push(mapGenericAction(action));
            } else {
                categories[action.category] = mapGenericCategory(action);
                itemActions.push(categories[action.category]);
                categories[action.category].actions.push(mapGenericAction(action));
            }
        } else {
            itemActions.push(mapGenericAction(action));
        }
    });
    return [...itemActions, ...categoryActions];
});

const primaryActions = computed((): WtgActionItemData[] => {
    const actions: WtgActionItemData[] = [];
    if (saveAction.value?.visible) {
        actions.push(mapStandardAction(saveAction.value));
    } else {
        if (cancelAction.value?.visible) {
            actions.push(mapStandardAction(cancelAction.value));
        }
    }
    const filteredActions = genericActions.value
        ?.filter((action) => action.placement === WtgFrameworkTaskGenericActionPlacement.Primary)
        .map(mapGenericAction);
    return [...actions, ...filteredActions].reverse();
});

const allSecondaryActions = computed((): WtgActionItemData[] => {
    const actions: WtgActionItemData[] = [];
    if (saveAction.value?.visible) {
        if (saveCloseAction.value?.visible) {
            actions.push(mapStandardAction(saveCloseAction.value));
        }
        if (cancelAction.value?.visible) {
            actions.push(mapStandardAction(cancelAction.value));
        }
    }
    const filteredActions = genericActions.value
        ?.filter((action) => action.placement === WtgFrameworkTaskGenericActionPlacement.Secondary)
        .map(mapGenericAction);
    return [...actions, ...filteredActions].reverse();
});

const secondaryActions = computed((): WtgActionItemData[] => {
    const filteredActions = allSecondaryActions.value.filter((action) => action.caption !== 'Cancel');
    return filteredActions;
});

const destructiveActions = computed((): WtgActionItemData[] => {
    const destructiveSecondaryActions = allSecondaryActions.value.filter((action) => action.caption === 'Cancel');
    return destructiveSecondaryActions;
});

const showDivider = computed((): boolean => {
    return (
        destructiveActions.value.length > 0 && (primaryActions.value?.length > 0 || secondaryActions.value?.length > 0)
    );
});

function mapGenericAction(action: WtgFrameworkTaskGenericAction): WtgActionItemData {
    return {
        caption: action.caption,
        id: action.id,
        icon: action.icon,
        click: (): void => action.onInvoke(),
    };
}

function mapGenericCategory(action: WtgFrameworkTaskGenericAction): WtgActionItemData {
    return {
        caption: action.category ?? '',
        id: '' + nextID++,
        actions: [],
    };
}

function mapStandardAction(action: WtgFrameworkTaskStandardAction): WtgActionItemData {
    return {
        caption: action.caption,
        id: '' + nextID++,
        click: (): void => action.onInvoke(),
    };
}
</script>
