import WtgButton from '@components/WtgButton';
import WtgIconButton from '@components/WtgIconButton';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgDropdownButton from '..';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgDropdownButton', () => {
    test('it renders the left with correct slot', () => {
        const wrapper = mountComponent();
        const button = wrapper.findComponent(WtgButton);
        expect(button.text()).toBe('My Button');
    });

    test('it renders the left button with placeholder leading icon', () => {
        const wrapper = mountComponent({
            propsData: {
                leadingIcon: 's-icon-placeholder',
            },
        });
        const button = wrapper.findComponent(WtgButton);
        expect(button.props('leadingIcon')).toBe('s-icon-placeholder');
    });

    test('it emits a click event when the left button is clicked', async () => {
        const wrapper = mountComponent();
        const button = wrapper.findComponent(WtgButton);
        expect(wrapper.emitted('click')).toBeUndefined();
        await button.trigger('click');
        expect(wrapper.emitted('click')!.length).toBe(1);
        expect(wrapper.emitted('click')![0][0]).toBeInstanceOf(MouseEvent);
    });

    test('it passes its props to WtgButton', () => {
        const wrapper = mountComponent({
            propsData: {
                variant: 'primary',
                disabled: true,
            },
        });
        const button = wrapper.findComponent(WtgButton);
        expect(button.props('variant')).toBe('primary');
        expect(button.props('disabled')).toBe(true);
    });

    test('it has a WtgIconButton with caret switch button', () => {
        const wrapper = mountComponent();
        const dropdownBtn = wrapper.findComponent(WtgIconButton);
        expect(dropdownBtn.props('icon')).toBe('s-icon-caret-switch');
    });

    test('it passes its props to WtgIconButton', () => {
        const wrapper = mountComponent({
            propsData: {
                variant: 'primary',
                disabled: true,
            },
        });
        const dropdownBtn = wrapper.findComponent(WtgIconButton);
        expect(dropdownBtn.props('variant')).toBe('primary');
        expect(dropdownBtn.props('disabled')).toBe(true);
    });

    test('it applies the correct classes when dropdown button is active', async () => {
        const wrapper = mountComponent({
            propsData: {
                variant: 'fill',
                sentiment: 'primary',
            },
        });
        const dropdownBtn = wrapper.findComponent(WtgIconButton);
        expect(dropdownBtn.findComponent(WtgButton).props('active')).toBe(false);
        await dropdownBtn.trigger('click');
        expect(dropdownBtn.findComponent(WtgButton).props('active')).toBe(true);
    });

    test('it renders a WtgPopover', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent({ name: 'WtgPopover' }).exists()).toBe(true);
    });

    test('it passes location: top right to WtgPopover when openPosition is set to top', () => {
        const wrapper = mountComponent({ propsData: { openPosition: 'top' } });
        expect(wrapper.findComponent({ name: 'WtgPopover' }).props('location')).toBe('top right');
    });

    test('it passes location: bottom right to WtgPopover when openPosition is set to bottom', () => {
        const wrapper = mountComponent({ propsData: { openPosition: 'bottom' } });
        expect(wrapper.findComponent({ name: 'WtgPopover' }).props('location')).toBe('bottom right');
    });

    test('it passes the popoverMaxHeight prop to the WtgPopover', () => {
        const wrapper = mountComponent({ propsData: { popoverMaxHeight: 100 } });
        expect(wrapper.findComponent({ name: 'WtgPopover' }).props('maxHeight')).toBe(100);
    });

    test('it has tooltip capability mixed in', () => {
        const wrapper = mountComponent({
            propsData: { tooltip: { content: 'Some tooltip', placement: 'top' } },
        });

        const vm: any = wrapper.vm;
        expect(vm.tooltipDirective.content).toBe('Some tooltip');
        expect(vm.tooltipDirective.placement).toBe('top');
    });

    test('it does not render icon button when isSplitButton flag is false', () => {
        const wrapper = mountComponent({
            propsData: { isSplitButton: false },
        });
        const iconButton = wrapper.findAllComponents(WtgIconButton);
        expect(iconButton.length).toBe(0);
    });

    test('it sets the aria label on the icon button', () => {
        const wrapper = mountComponent({ propsData: { dropdownAria: 'aria label' } });
        expect(wrapper.findComponent({ name: 'WtgIconButton' }).attributes('aria-label')).toBe('aria label');
    });

    describe('when sentiment is set', () => {
        test('it applies the wtg-button-success class if sentiment is success', () => {
            const wrapper = mountComponent({
                propsData: {
                    sentiment: 'success',
                },
            });
            const button = wrapper.findComponent(WtgButton);
            expect(button.classes()).toContain('wtg-button--success');
        });

        test('it applies the wtg-button-critical class if sentiment is critical', () => {
            const wrapper = mountComponent({
                propsData: {
                    sentiment: 'critical',
                },
            });
            const button = wrapper.findComponent(WtgButton);
            expect(button.classes()).toContain('wtg-button--critical');
        });
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount(WtgDropdownButton, {
            propsData,
            slots: {
                default: 'My Button',
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
