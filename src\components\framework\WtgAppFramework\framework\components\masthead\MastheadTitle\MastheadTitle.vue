<template>
    <template v-if="isMobile">
        <div v-if="mobileTitle || (!isStatusHidden && currentStatus)" class="d-flex flex-column overflow-hidden">
            <span
                v-if="mobileTitle"
                class="text-truncate text-title-sm-default wtg-masthead--mobile__caption"
                data-testid="mobile-masthead-caption"
            >
                {{ mobileTitle }}
            </span>
            <span
                v-if="!isStatusHidden && currentStatus"
                class="text-truncate text-xs-default"
                data-testid="mobile-masthead-status"
            >
                {{ currentStatus?.label }}
            </span>
        </div>
    </template>
    <template v-else>
        <div class="d-flex" :aria-label="ariaLabels.breadcrumbs" role="navigation">
            <div v-if="showPortalTitle" :style="portalTitleStyle">
                <a :href="portalHref" class="text-decoration-none" style="color: inherit">
                    {{ portalTitle }}
                </a>
            </div>
            <div v-if="showPageName" class="d-flex">
                <div v-if="showPortalTitle" class="px-2">/</div>
                <div :style="pageNameStyle">
                    {{ pageName }}
                </div>
            </div>
        </div>
    </template>
</template>

<script setup lang="ts">
import { WtgFrameworkAriaLabels, WtgFrameworkTask } from '@components/framework/types';
import { useApplication, useTaskStatus } from '@composables/application';
import { useFramework } from '@composables/framework';
import { computed, StyleValue } from 'vue';

const application = useApplication();
const { isMobile } = useFramework();

const ariaLabels = computed((): WtgFrameworkAriaLabels => {
    return application.ariaLabels;
});

const currentTask = computed((): WtgFrameworkTask => {
    return application.currentTask ?? new WtgFrameworkTask();
});

const atHomePage = computed((): boolean => {
    const currentMenuItem = application.menu.find((item) => item.active && item.home);
    return currentMenuItem ? true : false;
});

const pageName = computed((): string | undefined => {
    return application.currentTask ? currentTask.value.title : application.pageHelp?.name;
});

const pageNameStyle = computed((): StyleValue => {
    return {
        color: isMobile.value ? undefined : 'var(--s-primary-txt-default)',
        fontSize: 'var(--s-fontsize-300)',
        fontStyle: 'normal',
        fontWeight: isMobile.value ? 'var(--s-fontweight-600)' : 'var(--s-fontweight-400)',
        lineHeight: 'var(--s-fontsize-500)',
    };
});

const portalTitle = computed((): string => {
    return application.title;
});

const portalTitleStyle = computed((): StyleValue => {
    return {
        color: isMobile.value ? undefined : 'var(--s-primary-txt-default)',
        fontSize: 'var(--s-fontsize-300)',
        fontStyle: 'normal',
        fontWeight: 'var(--s-fontweight-600)',
        lineHeight: 'var(--s-fontsize-500)',
    };
});

const portalHref = computed((): string => {
    return application.href;
});

const showPageName = computed((): boolean => {
    return !atHomePage.value && !!pageName.value;
});

const showPortalTitle = computed((): boolean => {
    return (!isMobile.value || atHomePage.value) && !!portalTitle.value;
});

const mobileTitle = computed((): string | undefined => {
    if (currentTask.value?.entityName) {
        return currentTask.value?.entityName;
    }

    if (atHomePage.value) {
        return portalTitle.value;
    }

    return pageName.value;
});

const { currentStatus, isStatusHidden } = useTaskStatus(currentTask);
</script>
