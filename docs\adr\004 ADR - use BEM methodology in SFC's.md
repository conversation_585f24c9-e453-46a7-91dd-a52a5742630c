# Use BEM Methodology in SFCs

## Status
**Status**: Accepted  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

BEM stands for Block Element Modifier. The main idea behind it is to speed up the development process, and ease the teamwork of developers by arranging CSS classes into independent modules. If you ever saw a class name like header__form--search, that is BEM in action.

## Decision

## Status
- [ ] Proposed
- [x] Accepted
- [ ] Rejected
- [ ] Deprecated
- [ ] Superseded

Using BEM (Block, Element, Modifier) methodology in Vue components offers several benefits, especially in terms of code maintainability, readability, and scalability

## Consequences

Pros:
1. Clear Naming Conventions. BEM enforces a strict naming convention for CSS classes, which improves the readability of the code. This consistency makes it easier for developers to understand the structure and purpose of elements, even without deep knowledge of the codebase.
2. Component Reusability. BEM helps in creating reusable components by making the CSS class names modular. By adhering to the Block and Element structure, you can easily re-style and modify components without affecting others. In Vue, this means that you can create self-contained components with predictable CSS classes, which makes them portable and easy to use across different parts of an application.
3. Avoiding Global Styles Conflicts. One of the biggest issues in CSS is style leakage where styles defined for one component affect others, especially in larger applications. By using BEM, each component has a unique block class prefix, which significantly reduces the chance of style conflicts.
4. Scalability. As applications grow, managing styles becomes more difficult. BEM scales well in large applications by organizing CSS into clear blocks and elements, which makes it easier to maintain as the codebase expands.
5. Maintainability. With BEM, the structure of the styles is self-descriptive. This clarity makes maintaining the application easier because it’s obvious which parts of the code are responsible for which parts of the UI.
6. Easier CSS Debugging. Because of the clear naming conventions and hierarchical structure of BEM, debugging styles becomes easier. When a bug arises, developers can quickly identify which block or element is causing the issue and trace the styles more effectively.
7. Improved Collaboration. Since BEM provides a well-defined structure for class names, it helps standardize how styles are written across the team. This consistency fosters better collaboration, as everyone will follow the same naming convention, reducing misunderstandings or conflicts in code.
8. Support for Responsive and State-based Styles. BEM makes it easy to modify styles based on states or responsiveness. For example, if you need to add a "disabled" state to a button, the class button--disabled follows naturally from BEM conventions, making it clear what modification is being applied.

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.
