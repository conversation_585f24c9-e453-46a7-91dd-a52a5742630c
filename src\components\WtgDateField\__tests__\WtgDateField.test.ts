import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount, type VueWrapper } from '@vue/test-utils';
import WtgDateField from '..';
import WtgUi from '../../../WtgUi';
import * as utils from '../utils';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const datePickerSelectors = {
    monthViewButton:
        'button[class="v-btn v-theme--light v-btn--density-default v-btn--rounded v-btn--size-default v-btn--variant-text v-date-picker-controls__month-btn"]',
    yearViewButton:
        'button[class="v-btn v-btn--icon v-theme--light v-btn--density-comfortable v-btn--size-default v-btn--variant-text v-date-picker-controls__mode-btn"]',
    monthPrevNextButton:
        'button[class="v-btn v-btn--icon v-theme--light v-btn--density-default v-btn--size-default v-btn--variant-text"]',
    daysContainer: '.v-date-picker-month__days',
    yearOptionButton:
        'button[class="v-btn v-theme--light v-btn--density-default v-btn--rounded v-btn--size-default v-btn--variant-text"]',
    yearsContainer: '.v-date-picker-years__content',
    monthOptionButton:
        'button[class="v-btn v-theme--light v-btn--density-default v-btn--rounded v-btn--size-default v-btn--variant-text"]',
    monthsContainer: '.v-date-picker-months__content',
};

describe('WtgDateField', () => {
    beforeEach(() => {
        jest.useFakeTimers().setSystemTime(new Date('2020-01-19'));
    });

    afterEach(() => jest.resetAllMocks());

    test('it passes its props to the base WtgInput', () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
                displayOnly: true,
                flat: true,
                id: '111',
                label: 'My Label',
                leadingIcon: 'icon1',
                loading: true,
                messages: 'message',
                placeholder: 'placeholder',
                readonly: true,
                required: true,
                restricted: true,
                sentiment: 'primary',
            },
        });
        const props = wrapper.findComponent({ name: 'WtgInput' }).props();
        expect(props.disabled).toBe(true);
        expect(props.displayOnly).toBe(true);
        expect(props.flat).toBe(true);
        expect(props.filled).toBe(false);
        expect(props.hideMessages).toBe(false);
        expect(props.id).toBe('111');
        expect(props.label).toBe('My Label');
        expect(props.leadingIcon).toBe('icon1');
        expect(props.loading).toBe(true);
        expect(props.messages).toBe('message');
        expect(props.placeholder).toBe('placeholder');
        expect(props.readonly).toBe(true);
        expect(props.required).toBe(true);
        expect(props.restricted).toBe(true);
        expect(props.sentiment).toBe('primary');
    });

    test('it passes attributes to the base WtgInput element', () => {
        const wrapper = mountComponent({
            props: {
                dataTestId: 'my test id',
            },
        });
        expect(wrapper.attributes('datatestid')).toBe('my test id');
    });

    test('it updates the base WtgInput filled property when the input has content', async () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        input.setValue('20-05-2018');
        await wrapper.vm.$nextTick();

        const props = wrapper.findComponent({ name: 'WtgInput' }).props();
        expect(props.filled).toBe(true);
    });

    test('it renders input field component with date picker button', () => {
        const wrapper = mountComponent();

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        expect(wrapper.find('input').exists()).toBe(true);
        expect(wrapper.find('input').attributes().disabled).toBeUndefined();

        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.exists()).toBe(true);
    });

    test('it ensures the embedded button has the correct role and is focusable', () => {
        const wrapper = mountComponent();

        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.attributes('role')).toBe('button');
        expect(prompter.attributes('tabindex')).toBe('-1');
    });

    test("it has the autocomplete attribute set to 'off'", () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        expect(input.attributes('autocomplete')).toBe('off');
    });

    test('it user can set value of the field by typing (emitting date value)', async () => {
        const wrapper = mountComponent();

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        const input = wrapper.find('input');
        expect(input.exists()).toBe(true);

        await input.setValue('20-05-2018');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2018-05-20', true]);
        expect(input.element.value).toBe('20-May-18');
    });

    test.each(['returnValue', ' returnValue', 'returnValue ', ' returnValue '])(
        'it trims final value %p (emitting date value)',
        async (date) => {
            jest.spyOn(utils, 'parseDate').mockReturnValue(date);
            const wrapper = mountComponent();

            const input = wrapper.find('input');

            await input.setValue(date);
            await wrapper.vm.$nextTick();

            expect(wrapper.emitted('update:modelValue')![0]).toEqual(['returnValue', true]);
            expect(input.element.value).toBe('returnValue');
        }
    );

    test.each(['parseDateValue', ' parseDateValue', 'parseDateValue ', ' parseDateValue '])(
        'it trims user input %p when passing to parser',
        async (date) => {
            const parseDateSpy = jest.spyOn(utils, 'parseDate');
            const wrapper = mountComponent();

            const input = wrapper.find('input');

            await input.setValue(date);
            await wrapper.vm.$nextTick();

            expect(parseDateSpy).toBeCalledWith('parseDateValue', undefined);
        }
    );

    test('it opens date picker when user clicks trigger button (opening to current year + month when value is unset)', async () => {
        const wrapper = mountComponent();

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        expect(wrapper.find('input').exists()).toBe(true);

        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await wrapper.vm.$nextTick();

        expect(document.querySelector('[data-testid="date_picker"]')).not.toBeNull();
        expect(document.querySelector(datePickerSelectors.monthViewButton)).not.toBeNull();
        expect(document.querySelector(datePickerSelectors.monthViewButton)?.textContent).toBe('January 2020');

        expect(document.querySelector(datePickerSelectors.daysContainer)).not.toBeNull();
        const ninethDay = document.querySelectorAll('.v-date-picker-month__day-btn')![10];
        expect(ninethDay.innerHTML).toContain('>9<');
        expect(document.querySelector(datePickerSelectors.yearsContainer)).toBeNull();
        expect(document.querySelectorAll(datePickerSelectors.yearOptionButton).length).toBe(0);
        expect(document.querySelector(datePickerSelectors.monthsContainer)).toBeNull();
        expect(document.querySelectorAll(datePickerSelectors.monthOptionButton).length).toBe(0);
    });

    test('it opens date picker to date which is set in the input field', async () => {
        const wrapper = mountComponent();

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        expect(wrapper.find('input').exists()).toBe(true);

        const input = wrapper.find('input');
        expect(input.exists()).toBe(true);

        await input.setValue('20-05-2018');
        await wrapper.vm.$nextTick();

        expect(input.element.value).toBe('20-May-18');

        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await wrapper.vm.$nextTick();

        expect(document.querySelector('[data-testid="date_picker"]')).not.toBeNull();
        expect(document.querySelector(datePickerSelectors.monthViewButton)).not.toBeNull();
        expect(document.querySelector(datePickerSelectors.monthViewButton)?.textContent).toBe('May 2018');
    });

    test('it sets hideMessages to true when date picker opens', async () => {
        const wrapper = mountComponent();
        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        expect(wrapper.findComponent({ name: 'WtgInput' }).props().hideMessages).toBe(true);
    });

    test('date picker cannot be opened when input is disabled', async () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        const input = wrapper.find('input');
        expect(input.exists()).toBe(true);
        expect(input.attributes().disabled).toBeDefined();

        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.exists()).toBe(true);
        expect(input.attributes().disabled).toBeDefined();
    });

    test('it renders month + year only component when prop is provided', async () => {
        const wrapper = mountComponent({
            props: {
                monthYearOnly: true,
            },
        });

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        const input = wrapper.find('input');
        expect(input.exists()).toBe(true);

        await input.setValue('20-05-2018');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2018-05', true]);
        expect(input.element.value).toBe('05-2018');

        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await wrapper.vm.$nextTick();

        expect(document.querySelector('[data-testid="date_picker"]')).not.toBeNull();
        expect(document.querySelector(datePickerSelectors.monthViewButton)).not.toBeNull();
        expect(document.querySelector(datePickerSelectors.monthViewButton)?.textContent).toBe('May 2018');

        expect(document.querySelector(datePickerSelectors.monthsContainer)).not.toBeNull();
        expect(document.querySelector(datePickerSelectors.monthsContainer)!.children.length).toBe(12);
        expect(document.querySelectorAll(datePickerSelectors.monthOptionButton).length).toBe(11);

        expect(document.querySelector(datePickerSelectors.yearsContainer)).toBeNull();
        expect(document.querySelector(datePickerSelectors.daysContainer)).toBeNull();
    });

    test('it sets input value when user selects date (year + month + day) within date picker', async () => {
        window.HTMLElement.prototype.scrollIntoView = jest.fn();
        const wrapper = mountComponent();

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        expect(wrapper.find('input').exists()).toBe(true);

        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await wrapper.vm.$nextTick();

        expect(document.querySelector('[data-testid="date_picker"]')).not.toBeNull();
        expect(document.querySelector(datePickerSelectors.monthViewButton)).not.toBeNull();

        expect(document.querySelector(datePickerSelectors.daysContainer)).not.toBeNull();

        (document.querySelector(datePickerSelectors.yearViewButton) as HTMLButtonElement)!.click();
        await wrapper.vm.$nextTick();

        expect(document.querySelector(datePickerSelectors.daysContainer)).toBeNull();
        expect(document.querySelector(datePickerSelectors.yearsContainer)).not.toBeNull();

        const y2k = document.querySelectorAll(datePickerSelectors.yearOptionButton)[80];
        expect(y2k.textContent).toBe('2000');

        (y2k as HTMLButtonElement)!.click();
        await wrapper.vm.$nextTick();

        (document.querySelector(datePickerSelectors.monthViewButton) as HTMLButtonElement)!.click();
        await wrapper.vm.$nextTick();

        expect(document.querySelector(datePickerSelectors.daysContainer)).toBeNull();
        expect(document.querySelector(datePickerSelectors.monthOptionButton)).not.toBeNull();

        const august = document.querySelectorAll(datePickerSelectors.monthOptionButton)[6];
        expect(august.textContent).toBe('Aug');

        (august as HTMLButtonElement)!.click();
        await wrapper.vm.$nextTick();

        expect(document.querySelector(datePickerSelectors.daysContainer)).not.toBeNull();

        const ninethDay = document.querySelectorAll('.v-date-picker-month__day-btn')[9];
        expect(ninethDay.textContent).toBe('9');

        (ninethDay as HTMLButtonElement)!.click();
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toEqual(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2000-08-09', true]);
        expect(wrapper.findComponent({ name: 'VMenu' }).vm.modelValue).toBe(false);

        expect(wrapper.find('input').element.value).toBe('09-Aug-00');
    });

    test('it renders with custom date formatter', async () => {
        const wrapper = mountComponent({
            props: {
                formatter: {
                    formatDate: () => 'formatted date',
                    parseDate: () => 'parsed date',
                },
            },
        });

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        expect(wrapper.find('input').exists()).toBe(true);

        const input = wrapper.find('input');
        expect(input.exists()).toBe(true);

        await input.setValue('20-05-2018');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['parsed date', true]);
        expect(input.element.value).toBe('formatted date');
    });

    test('it renders with custom date formatter when user input a invalid date', async () => {
        const invalidDate = 'Invalid date';
        const mockFormatDate = jest.fn().mockReturnValue('formatted date');
        const wrapper = mountComponent({
            props: {
                formatter: {
                    formatDate: mockFormatDate,
                    parseDate: (input: string) => {
                        if (input === invalidDate) {
                            return null;
                        } else {
                            return 'parsed date';
                        }
                    },
                },
            },
        });

        mockFormatDate.mockClear();
        const input = wrapper.find('input');
        input.setValue(invalidDate);
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual([invalidDate, false]);
        expect(mockFormatDate).not.toHaveBeenCalled();
    });

    test('it sets the correct today with custom date formatter', async () => {
        const wrapper = mountComponent({
            props: {
                formatter: {
                    formatDate: () => 'formatted date',
                    parseDate: () => 'parsed date',
                    today: () => '2025-01-21',
                },
            },
        });

        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');
        await wrapper.vm.$nextTick();

        (document.querySelector('[data-testid="set_today"]') as HTMLButtonElement).click();
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2025-01-21', true]);
    });

    test('it sets the correct captions for the current language and updates them if the language changes', async () => {
        const wrapper = mountComponent();
        const button = wrapper.findComponent({ name: 'WtgIcon' });
        expect(button.attributes('aria-label')).toBe('Select Date');

        wtgUi.language.current = 'nl';
        await wrapper.vm.$nextTick();

        expect(button.attributes('aria-label')).toBe('Selecteer Datum');
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('when disabled, it sets the disabled attribute on the input field', async () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });
        expect(wrapper.find('input').attributes('disabled')).toBeDefined();

        await wrapper.setProps({ disabled: false });
        expect(wrapper.find('input').attributes('disabled')).toBeUndefined();
    });

    test('it passes the aria* properties to the input to ensure fields with a hidden label can still meet accessibility requirements', async () => {
        const wrapper = mountComponent({
            props: {
                ariaLabel: 'Aria label',
                ariaLabelledby: 'Aria labelledby',
            },
        });
        expect(wrapper.find('input').attributes('aria-label')).toBe('Aria label');
        expect(wrapper.find('input').attributes('aria-labelledby')).toBe('Aria labelledby');
    });

    test('when readonly, it sets the readonly attribute on the input field', async () => {
        const wrapper = mountComponent({
            props: {
                readonly: true,
            },
        });
        expect(wrapper.find('input').attributes('readonly')).toBeDefined();

        await wrapper.setProps({ readonly: false });
        expect(wrapper.find('input').attributes('readonly')).toBeUndefined();
    });

    test('it has a (deprecated) inputId property that gets applied if no id is specified to aid the GLOW VUE 3 migration', async () => {
        const wrapper = mountComponent();
        await wrapper.setProps({ inputId: 'id1' });
        expect(wrapper.find('input').attributes('id')).toBe('id1');

        await wrapper.setProps({ id: 'id2' });
        expect(wrapper.find('input').attributes('id')).toBe('id2');
    });

    test('when native, it renders the date field with the native date picker', async () => {
        const wrapper = mountComponent({
            props: {
                native: true,
            },
        });

        expect(wrapper.find('input').attributes('type')).toBe('date');
        await wrapper.setProps({ native: false });
        expect(wrapper.find('input').attributes('type')).toBe('text');
    });

    test('when native and readonly, the picker is not displayed on interaction', async () => {
        const showPickerSpy = jest.fn();
        HTMLInputElement.prototype.showPicker = showPickerSpy;

        const wrapper = mountComponent({
            props: {
                native: true,
                readonly: true,
            },
        });

        const input = wrapper.find('input');
        await input.trigger('click');
        expect(showPickerSpy).not.toHaveBeenCalled();
    });

    test('it ensures consistent modelValue values when used as native:true or false', async () => {
        const wrapper = mountComponent();
        wtgUi.language.current = 'en';
        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);
        const input = wrapper.find('input');
        expect(input.exists()).toBe(true);

        await input.setValue('20-05-2018');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')![0][0]).toEqual('2018-05-20');
        expect(input.element.value).toBe('20-May-18');

        await input.setValue('');
        await wrapper.vm.$nextTick();
        await wrapper.setProps({ native: true });

        await input.setValue('2018-05-20');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(3);
        expect(wrapper.emitted('update:modelValue')![2][0]).toEqual('2018-05-20');
        expect(input.element.value).toBe('2018-05-20');
    });

    test('it emits update:menu event when open and close date picker', async () => {
        const wrapper = mountComponent();
        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:menu')?.length).toBe(1);
        expect(wrapper.emitted('update:menu')![0][0]).toEqual(true);

        await prompter.trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:menu')?.length).toBe(2);
        expect(wrapper.emitted('update:menu')![1][0]).toEqual(false);
    });

    describe('Deprecated props', () => {
        test('internalValue is set to an empty string when neither modelValue nor value is provided', () => {
            const wrapper = mountComponent();
            const input = wrapper.find('input');
            expect(input.element.value).toBe('');
        });

        test('internalValue updates when modelValue prop changes', async () => {
            const initialModelValue = '2018-05-20';
            const newModelValue = '2018-06-20';
            const wrapper = mountComponent({
                props: {
                    native: true,
                    modelValue: initialModelValue,
                },
            });
            wtgUi.language.current = 'en';
            await wrapper.vm.$nextTick();

            const input = wrapper.find('input');
            expect(input.element.value).toBe(initialModelValue);

            await wrapper.setProps({ modelValue: newModelValue });
            expect(input.element.value).toBe(newModelValue);
        });

        test('internalValue updates when value prop changes', async () => {
            const initialValue = '2025-02-20';
            const newValue = '2025-03-20';
            const wrapper = mountComponent({
                props: {
                    native: true,
                    inputValue: initialValue,
                },
            });
            wtgUi.language.current = 'en';
            await wrapper.vm.$nextTick();

            const input = wrapper.find('input');
            expect(input.element.value).toBe(initialValue);

            await wrapper.setProps({ inputValue: newValue });
            expect(input.element.value).toBe(newValue);
        });

        test('emits update:modelValue, model-compat:input, and change events on updateValue', async () => {
            const wrapper = mountComponent();
            const newValue = '2025-02-20';
            const input = wrapper.find('input');

            await input.setValue(newValue);

            expect(wrapper.emitted('update:modelValue')).toBeTruthy();
            expect(wrapper.emitted('update:modelValue')?.[0]).toEqual([newValue, true]);

            expect(wrapper.emitted('model-compat:input')).toBeTruthy();
            expect(wrapper.emitted('model-compat:input')?.[0]).toEqual([newValue, true]);

            expect(wrapper.emitted('change')).toBeTruthy();
            expect(wrapper.emitted('change')?.[0]).toEqual([newValue, true]);
        });
    });

    test('it opens the prompter dialog when F4 is pressed and promptable', async () => {
        const wrapper = mountComponent();
        const popover = wrapper.findComponent({ name: 'WtgPopover' });
        expect(popover.props('modelValue')).toBe(false);
        const input = wrapper.find('input');
        input.trigger('click');
        await input.trigger('keydown.F4');
        expect(popover.props('modelValue')).toBe(true);
    });

    describe('when framework is mobile', () => {
        let wrapper: VueWrapper;

        beforeEach(() => {
            const wtgUi = new WtgUi({ framework: 'mobile' });
            wrapper = mountComponent({}, wtgUi);
        });

        test('when mobile, it defaults to the native time picker', async () => {
            expect(wrapper.find('input').attributes('type')).toBe('date');
        });
    });

    function mountComponent({ props = {}, provide = {} } = {}, localWtgUi?: WtgUi) {
        return mount(WtgDateField, {
            props,
            global: {
                plugins: [localWtgUi ?? wtgUi],
                provide: {
                    ...provide,
                    darkMode: false,
                },
            },
        });
    }
});
