import WtgChart from '@components/WtgChart/WtgChart.vue';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import {
    CategoryScale,
    Chart,
    ChartData,
    ChartOptions,
    LinearScale,
    LineController,
    LineElement,
    PointElement,
} from 'chart.js';
import { defineComponent, h, PropType, VNode } from 'vue';

Chart.register(CategoryScale, LinearScale, LineController, LineElement, PointElement);

export default defineComponent({
    name: 'WtgLineChart',
    props: {
        data: {
            type: Object as PropType<ChartData>,
            default: (): ChartData => {
                return {
                    datasets: [],
                };
            },
        },
        options: {
            type: Object as PropType<ChartOptions>,
            default: (): ChartOptions => {
                return {};
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    setup(props) {
        useLayoutGridColumn(props);
    },
    render(): VNode {
        return h(WtgChart, {
            type: 'line',
            data: this.data,
            options: this.options,
            loading: this.loading,
        });
    },
});
