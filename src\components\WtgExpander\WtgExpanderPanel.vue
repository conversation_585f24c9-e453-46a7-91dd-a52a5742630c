<template>
    <VExpansionPanel class="wtg-expander-panel">
        <slot />
    </VExpansionPanel>
</template>

<script setup lang="ts">
import { VExpansionPanel } from 'vuetify/components/VExpansionPanel';
</script>

<style lang="scss">
.wtg-expander-panel {
    border: 1px solid var(--s-neutral-border-weak-default);
    background: var(--s-neutral-bg-default);
    color: var(--s-neutral-txt-default);
    box-shadow: var(--s-elevation-100);
    border-radius: var(--s-radius-m);
    gap: var(--s-spacing-m);
}
</style>
