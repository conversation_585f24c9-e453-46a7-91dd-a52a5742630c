<!-- eslint-disable vue/multi-word-component-names -->
<template>
    <VProgressLinear
        v-show="application.loading"
        id="wtg-loading-indicator"
        absolute
        indeterminate
        :style="{ 'z-index': 2000, top: !isMobile || application.hideAppBar ? 0 : `${mobileMastheadHeight}px` }"
    />
    <Navigation />
    <Masthead />
    <ActionBar />
    <WtgMain name="main">
        <slot />
    </WtgMain>
    <AboutDialog />
    <HelpDialog />
    <!-- <recent-dialog /> -->
    <ToastStacker />
</template>

<script setup lang="ts">
import WtgMain from '@components/WtgMain';
import { useApplication } from '@composables/application';
import { mobileMastheadHeight, useFramework } from '@composables/framework';
import { VProgressLinear } from 'vuetify/components/VProgressLinear';
import { AboutDialog, ActionBar, HelpDialog, Masthead, Navigation, ToastStacker } from './components';

const application = useApplication();
const { isMobile } = useFramework();
</script>
