import WtgContainer from '@components/WtgContainer';
import { createDessertsService } from '@components/WtgDataTable/stories/DessertsService';
import WtgDataTableServer from '@components/WtgDataTable/WtgDataTableServer';
import WtgIcon from '@components/WtgIcon';
import WtgNumberField from '@components/WtgNumberField';
import WtgPanel, { WtgPanelHeader } from '@components/WtgPanel';
import WtgSpacer from '@components/WtgSpacer';
import WtgStatus from '@components/WtgStatus';
import WtgTextField from '@components/WtgTextField';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import { Meta, StoryContext, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgDataTableServer>;
const meta: Meta<typeof WtgDataTableServer> = {
    title: 'Components/Data Table/Variants/Data Table Server',
    component: WtgDataTableServer,
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: [
                'headers',
                'items',
                'itemsLength',
                'loading',
                'itemValue',
                'search',
                'itemsPerPage',
                'itemsPerPageOptions',
                'page',
                'selectionMode',
            ],
        },
        docs: {
            description: {
                component: 'The data-table component is designed for displaying and searching information.',
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: { WtgPanel, WtgDataTableServer },
        setup: () => ({ args }),
        template: `<div />`,
    }),
    argTypes: {
        density: {
            control: 'select',
            options: ['default', 'comfortable', 'compact'],
        },
        editableStyling: {
            control: 'boolean',
        },
        fillAvailable: {
            control: 'boolean',
        },
        fixedHeader: {
            control: 'boolean',
        },
        hideDefaultFooter: {
            control: 'boolean',
        },
        mobile: {
            control: 'boolean',
        },
        mobileBreakpoint: {
            control: 'select',
            options: ['', 'xs', 'sm', 'md', 'lg', 'xl', 'xxl'],
        },
        noDataText: {
            control: 'text',
        },
    },
};

function createProps(storyContext: StoryContext): string {
    const props = [];
    for (const arg in storyContext.args) {
        props.push(`${arg}="${storyContext.args[arg] + ''}"`);
    }
    return props.sort().join(' ');
}

const dessertsService = createDessertsService({ itemsPerPage: 10 });

export default meta;

function snippetContent(args1: string): string {
    return `
            <div class="d-flex align-center mb-2 ga-2">
                <WtgPanelHeader id="server-label" class="mb-0">Server side table simulated items</WtgPanelHeader>
                <WtgNumberField aria-labelledby="server-label" v-model="options.simulatedItems" style="max-width: 200px"/>
                <WtgSpacer/>
                <WtgPanelHeader id="options-label" class="mb-0">Search</WtgPanelHeader>
                <WtgTextField aria-labelledby="options-label" v-model="options.search" style="max-width: 200px"/>
            </div>    
            <WtgDataTableServer
                ${args1}
                v-model:items-per-page="options.itemsPerPage"
                :headers="options.headers"
                :items-length="options.totalItems"
                :items="options.items"
                :loading="options.loading"
                :search="options.search"
                @update:options="options.loadItems($event)">
                    <template #item.available="{ item }">
                        <WtgStatus v-if="item.available" sentiment="success" label="Available"/>
                    </template>
                    <template #item.rating="{ item }">
                        <WtgIcon v-for="index in item.rating" :key="index" color="text-amber">s-icon-star-filled</WtgIcon>
                    </template>
            </WtgDataTableServer>`;
}

export const Default: Story = {
    parameters: {
        ...getChromaticParameters(),
        docs: {
            source: {
                transform: (source: string, storyContext: StoryContext) => `
<template>
  <WtgContainer color="canvas" layout="fill">
      <WtgPanel class="mb-2">
          <WtgCallout
            description="The data table server component is meant to be used for very large datasets, where it would be inefficient to load all the data into the client. It supports sorting, filtering, pagination, and selection like a standard data table, but all the logic must be handled externally by your backend or database."
            sentiment="info"
            title="Data table server"
          />
      </WtgPanel>             
      <WtgPanel fill-available layout="fill">   
      ${snippetContent(`${createProps(storyContext)} `)}
      </WtgPanel>             
  </WtgContainer>
</template>

<script setup lang="ts">
import {WtgContainer, WtgIcon, WtgNumberField, WtgTextField, WtgPanelHeader, WtgPanel, WtgSpacer, WtgCallout, WtgDataTableServer} from '@wtg/wtg-components';
import { computed } from 'vue';
import { createDessertsService } from './DessertsData/DessertsService';

const dessertsService = createDessertsService({itemsPerPage: 10});

const options = computed(() => {
    return dessertsService._options;
});
</script>`,
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: {
            WtgContainer,
            WtgIcon,
            WtgDataTableServer,
            WtgPanel,
            WtgPanelHeader,
            WtgNumberField,
            WtgTextField,
            WtgSpacer,
            WtgStatus,
        },
        setup: () => {
            return {
                args,
                options: dessertsService._options,
            };
        },
        template: `<WtgContainer color="canvas" layout="fill"> <WtgPanel fill-available layout="fill"> ${snippetContent(
            `v-bind="args" `
        )}</WtgPanel></WtgContainer>`,
    }),
};
