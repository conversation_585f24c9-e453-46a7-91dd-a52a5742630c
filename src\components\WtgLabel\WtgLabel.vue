<template>
    <a v-if="isHref" :href="props.href">
        <component :is="typographyTag" :class="labelClasses" :style="computedStyle">
            <slot />
        </component>
    </a>
    <component :is="typographyTag" v-else :class="labelClasses" :style="computedStyle">
        <slot />
    </component>
</template>

<script setup lang="ts">
import { useColor } from '@composables/color';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { computed, useAttrs } from 'vue';
import { createTypographyClasses } from './types';

const attrs = useAttrs();
const supportedDisplays = ['block', 'inline'];
const supportedFontWeights = ['black', 'bold', 'medium', 'regular', 'light', 'thin'];
const props = defineProps({
    /**
     * The text alignment of the label.
     * Examples include 'start', 'center', or 'end'.
     */
    align: {
        type: String,
        default: undefined,
    },

    /**
     * The color of the label text.
     * Can be a predefined color from the design system.
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * The display style of the label.
     * Options include 'block' or 'inline'.
     */
    display: {
        type: String,
        default: undefined,
    },

    /**
     * The font weight of the label text.
     * Options include 'black', 'bold', 'medium', 'regular', 'light', or 'thin'.
     */
    fontWeight: {
        type: String,
        default: undefined,
    },

    /**
     * The URL that the label links to, if used as a hyperlink.
     */
    href: {
        type: String,
        default: '',
    },

    /**
     * If true, the label text will be italicized.
     */
    italic: {
        type: Boolean,
        default: false,
    },

    /**
     * If true, the label text will not wrap to the next line.
     */
    noWrap: {
        type: Boolean,
        default: false,
    },

    /**
     * The typography style to apply to the label.
     * Examples include 'h1', 'body', 'label', or other design system typography tokens.
     */
    typography: {
        type: String,
        default: '',
    },

    ...makeLayoutGridColumnProps(),
});

const { colorClasses, colorStyles } = useColor(props);

const isHref = computed(() => {
    return props.href && props.href.length > 0;
});

const labelClasses = computed(() => {
    const result = ['wtg-label'];
    let typography = props.typography;

    if (attrs.onClick) {
        result.push('wtg-cursor-pointer');
    }

    if (props.align) {
        for (const align of props.align.split(' ')) {
            result.push('text-' + align);
        }
    }

    if (props.display && supportedDisplays.includes(props.display)) {
        result.push('d-' + props.display);
    }

    if (props.fontWeight && supportedFontWeights.includes(props.fontWeight)) {
        result.push('font-weight-' + props.fontWeight);
    }

    if (props.italic) {
        result.push('font-italic');
    }

    if (props.noWrap) {
        result.push('wtg-label-nowrap');
    }

    switch (typography) {
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
        case 'display-large':
        case 'display':
        case 'display-small':
        case 'title-large':
        case 'title':
        case 'title-small':
        case 'body':
        case 'body-strong':
        case 'body-link':
        case 'button':
        case 'numeric':
        case 'numeric-strong':
        case 'numeric-link':
        case 'label':
        case 'label-strong':
        case 'placeholder':
        case 'placeholder-numeric':
        case 'microcopy-numeric':
            result.push(`wtg-typography-${typography}`);
            break;
        default: {
            const typographyClass = createTypographyClasses().find(
                (typographyClass) => typographyClass.text === typography
            );
            result.push(typographyClass ? typographyClass.value : typography);
            break;
        }
    }

    return [...result, ...colorClasses.value];
});

const typographyTag = computed(() => {
    let result = props.typography;

    switch (props.typography) {
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
            result = props.typography;
            break;
        default:
            result = 'span';
            break;
    }

    return result;
});

const computedStyle = computed(() => {
    return colorStyles.value;
});

useLayoutGridColumn(props);
</script>
<style lang="scss">
.wtg-label-nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
