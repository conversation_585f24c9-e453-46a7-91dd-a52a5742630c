import { WtgApp } from '../';
import WtgUi from '../../../WtgUi';
import { mount, enableAutoUnmount } from '@vue/test-utils';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgApp', () => {
    test('its name is WtgApp', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgApp');
    });

    test('it applies the wtg-app class', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('wtg-app');
    });

    test('it renders a VApp component', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('v-application');
    });

    test('it passes the default slot to the VApp component', () => {
        const wrapper = mountComponent({
            slots: {
                default: '<span id="text">Some text</span>',
            },
        });
        expect(wrapper.find('#text').text()).toBe('Some text');
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
