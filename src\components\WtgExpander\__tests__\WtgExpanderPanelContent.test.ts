import WtgExpander from '@components/WtgExpander/WtgExpander.vue';
import WtgExpanderPanel from '@components/WtgExpander/WtgExpanderPanel.vue';
import WtgExpanderPanelContent from '@components/WtgExpander/WtgExpanderPanelContent.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { VExpansionPanelText } from 'vuetify/components/VExpansionPanel';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgExpanderPanelContent', () => {
    test('it renders a VExpansionPanelText component', () => {
        const wrapper = mountComponent();
        const tabs = wrapper.findComponent(VExpansionPanelText);
        expect(tabs.exists()).toBe(true);
    });

    function mountComponent({ props = {} } = {}) {
        const component = {
            components: { WtgExpanderPanel, WtgExpanderPanelContent, WtgExpander },
            template:
                '<wtg-expander v-model="panel"><wtg-expander-panel><wtg-expander-panel-content>ONE</wtg-expander-panel-content></wtg-expander-panel></wtg-expander>',
            data: () => {
                return {
                    panel: 1,
                };
            },
        };

        const wrapper = mount(component, {
            props,
            global: {
                plugins: [wtgUi],
            },
        });
        return wrapper.findComponent({ name: 'WtgExpanderPanelContent' });
    }
});
