import { WtgFramework } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../WtgUi';
import NavigationFooter from '../NavigationFooter.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('navigation-footer', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.clickFavoritesHandler = jest.fn();
        application.dialogs = {
            recentItems: {
                open: jest.fn(),
            },
        } as any;
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is NavigationFooter', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('NavigationFooter');
    });

    test('it renders the recent navigation footer item', async () => {
        const wrapper = await mountComponentAsync();
        const item = wrapper.findComponent({ name: 'WtgNavigationRecentMenu' });
        expect(item.exists()).toBe(true);
    });

    test('it renders the configure menu item', async () => {
        const wrapper = await mountComponentAsync();
        const item = wrapper.findComponent({ name: 'ConfigureMenu' });
        expect(item.exists()).toBe(true);
    });

    test('it renders the help menu item', async () => {
        const wrapper = await mountComponentAsync();
        const item = wrapper.findComponent({ name: 'HelpMenu' });
        expect(item.exists()).toBe(true);
    });

    test('it renders the user menu item', async () => {
        const wrapper = await mountComponentAsync();
        const item = wrapper.findComponent({ name: 'UserMenu' });
        expect(item.exists()).toBe(true);
    });

    test('it should call the application favorites handler when clickFavoritesHandler is called', async () => {
        const wrapper: any = await mountComponentAsync();
        wrapper.vm.clickFavoritesHandler();
        expect(application.clickFavoritesHandler).toHaveBeenCalledTimes(1);
    });

    test('it should emit item-click and call the recent dialog open handler when onRecentDialogOpen is called', async () => {
        const wrapper: any = await mountComponentAsync();
        wrapper.vm.onRecentDialogOpen();
        expect(wrapper.emitted('item-click')?.length).toBe(1);
        expect(application.dialogs.recentItems.open).toHaveBeenCalledTimes(1);
    });

    describe('Accessibility', () => {
        test('it renders a lists with a role and aria has popup', async () => {
            window.innerWidth = 412;
            const wrapper = await mountComponentAsync();
            const lists = wrapper.findAllComponents({ name: 'WtgList' });
            expect(lists.at(0)?.attributes('role')).toBe('menu');
            expect(lists.at(1)?.attributes('role')).toBe('menu');
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(NavigationFooter) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
            stubs: ['router-link'],
        });
        await flushPromises();
        return wrapper.findComponent(NavigationFooter);
    }
});
