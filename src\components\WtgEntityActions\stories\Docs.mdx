import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgEntityActions from './WtgEntityActions.stories.ts';

<Meta of={WtgEntityActions} />

<div className="component-header">
    <h1>Entity actions</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=82-41390&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">Entity actions are a predefined set of actions available for entities.</p>

This component is a sub-component of the application layout components. For more information on how to use this component and its role within the [application layout](/docs/utilities-app--docs), please refer to the application layout documentation.

## API

<Canvas className="canvas-preview" of={WtgEntityActions.Default} />
<Controls of={WtgEntityActions.Default} sort={'alpha'} />

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
