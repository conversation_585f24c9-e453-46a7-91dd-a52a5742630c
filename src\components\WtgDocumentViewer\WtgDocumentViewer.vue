<template>
    <div ref="viewerWrap" class="d-flex flex-column wtg-document-viewer">
        <slot />
        <WtgPanel class="document-panel">
            <div class="document-header">
                <WtgLabel typography="title-md-default">
                    {{ internalDocumentName }}
                </WtgLabel>
                <WtgSpacer />
                <WtgIconButton
                    v-if="!isUnsupportedFile && enableDocumentInfo"
                    icon="s-icon-info-circle"
                    variant="ghost"
                    :tooltip="formatCaption('documentViewer.info')"
                    @click="onInfo"
                />
                <WtgIconButton
                    v-if="!isUnsupportedFile && enablePrint"
                    icon="s-icon-print"
                    variant="ghost"
                    :tooltip="formatCaption('documentViewer.print')"
                    @click="onPrint"
                />
                <WtgIconButton
                    v-if="!isUnsupportedFile && enableDownload"
                    icon="s-icon-download-file"
                    variant="ghost"
                    :tooltip="formatCaption('documentViewer.download')"
                    @click="onDownload"
                />
            </div>
            <div class="document-body">
                <DocumentViewerControls
                    v-if="!isUnsupportedFile && enableControls"
                    :is-loading="loading"
                    :enable-thumbnails="shouldShowThumbnails"
                    :enable-pagination="enablePagination && !isImage"
                    :enable-rotate="enableRotate"
                    :enable-zoom="enableZoom"
                    :page-count="pageCount"
                    :zoom-percentage="zoomPercentage"
                    :scaling-mode="scaleConfig.scaleMode"
                    :rotation-degree="rotationDegree"
                    :active-page-number="activePageNumber"
                    @thumbnails="onThumbnails"
                    @update-rotate="handleUpdateRotate"
                    @update-zoom="handleUpdateZoom"
                    @update-scale="handleUpdateScaleModeOnly"
                    @active-page-updated="handleUpdateActivePageNumber"
                />
                <div v-if="showDocumentInfo" class="document-info">
                    <slot name="document-info" />
                </div>
                <div v-else class="d-flex">
                    <WtgProgressCircular v-if="loading" indeterminate color="primary" class="loading" />
                    <div v-if="shouldShowThumbnails" :class="showThumbnails ? 'thumbnails' : 'thumbnails close'">
                        <DocumentViewerPdfDocument
                            v-if="fileType === 'pdf'"
                            :pdf-source="documentSource"
                            :doc-viewer-wrap-width="160"
                            :page-preview-mode="true"
                            :active-page-number="activePageNumber"
                            @active-page-updated="handleUpdateActivePageNumber"
                        />
                    </div>
                    <div class="scroll-wrap">
                        <div v-if="!loading" class="document-wrap">
                            <DocumentViewerPdfDocument
                                v-if="fileType === 'pdf'"
                                :pdf-source="documentSource"
                                :doc-viewer-wrap-width="viewerWrapWidth"
                                :doc-viewer-wrap-height="viewerWrapHeight"
                                :page-preview-mode="false"
                                :zoom-percentage="zoomPercentage"
                                :rotation-degree="rotationDegree"
                                :scale-config="scaleConfig"
                                :active-page-number="activePageNumber"
                                @page-count-updated="handleUpdatePageCount"
                                @zoom-percentage-updated="handleUpdateZoom"
                                @active-page-updated="handleUpdateActivePageNumber"
                                @update-scale="handleUpdateScale"
                            >
                                <template v-for="page in pageCount" #[`pdf-page-${page}`]="{ pdfPageData }">
                                    <slot :name="`pdf-page-${page}`" :pdf-page-data="pdfPageData"></slot>
                                </template>
                            </DocumentViewerPdfDocument>

                            <DocumentViewerImage
                                v-else-if="isImage"
                                :image-source="documentSource"
                                :file-type="fileType"
                                :image-rotation="rotationDegree"
                                :zoom-percentage="zoomPercentage"
                                :scale-config="scaleConfig"
                                :image-viewer-wrap-width="viewerWrapWidth"
                                :image-viewer-wrap-height="viewerWrapHeight"
                                @zoom-percentage-updated="handleUpdateZoom"
                                @update-scale="handleUpdateScale"
                            >
                                <template #default="{ imageData }">
                                    <slot name="image" :image-data="imageData"></slot>
                                </template>
                            </DocumentViewerImage>
                            <WtgEmptyState
                                v-else
                                class="unsupported-document"
                                variant="default"
                                header="Document not loaded"
                                bodycopy="Either the file was not found or the document format is not supported"
                                type="noData"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </WtgPanel>
    </div>
</template>

<script setup lang="ts">
import WtgEmptyState from '@components/WtgEmptyState';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import WtgPanel from '@components/WtgPanel';
import WtgProgressCircular from '@components/WtgProgressCircular';
import WtgSpacer from '@components/WtgSpacer';
import { useLocale } from '@composables/locale';
import { computed, onMounted, PropType, ref, watch } from 'vue';
import DocumentViewerControls from './components/controls/DocumentViewerControls.vue';
import DocumentViewerImage from './components/imageViewer/DocumentViewerImage.vue';
import DocumentViewerPdfDocument from './components/pdfViewer/DocumentViewerPdfDocument.vue';
import { Scale, ScalingMode, UrlProvider } from './types';

//
// Properties
//
const props = defineProps({
    /**
     * The name of the document being viewed.
     */
    documentName: {
        type: String,
        default: '',
    },

    /**
     * The source of the document, provided as an ArrayBuffer.
     */
    documentSource: {
        type: ArrayBuffer,
        default: undefined,
    },

    /**
     * If true, the document viewer controls (e.g., zoom, rotate) will be enabled.
     */
    enableControls: {
        type: Boolean,
        default: true,
    },

    /**
     * If true, the download button will be displayed.
     */
    enableDownload: {
        type: Boolean,
        default: true,
    },

    /**
     * If true, pagination controls will be enabled for multi-page documents.
     */
    enablePagination: {
        type: Boolean,
        default: true,
    },

    /**
     * If true, the print button will be displayed.
     */
    enablePrint: {
        type: Boolean,
        default: true,
    },

    /**
     * If true, the rotate button will be displayed.
     */
    enableRotate: {
        type: Boolean,
        default: true,
    },

    /**
     * If true, thumbnails for multi-page documents will be displayed.
     */
    enableThumbnail: {
        type: Boolean,
        default: true,
    },

    /**
     * If true, zoom controls will be enabled.
     */
    enableZoom: {
        type: Boolean,
        default: true,
    },

    /**
     * If true, the document information button will be displayed.
     */
    enableDocumentInfo: {
        type: Boolean,
        default: true,
    },

    /**
     * If true, a loading indicator will be displayed while the document is loading.
     */
    loading: {
        type: Boolean,
        default: false,
    },

    /**
     * An object providing URLs for downloading and printing the document.
     */
    urlProvider: {
        type: Object as PropType<UrlProvider>,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    'page-count-updated': [number];
}>();

//
// State
//
const viewerWrapWidth = ref(0);
const viewerWrapHeight = ref(0);
const viewerWrap = ref<HTMLDivElement | null>(null);
const showDocumentInfo = ref(false);
const showThumbnails = ref(false);

const pageCount = ref(0);
const activePageNumber = ref(1);
const zoomPercentage = ref('');
const scaleConfig = ref<Scale>({
    scaleMode: ScalingMode.FitToScreen,
    scaleValue: 0,
    resetScale: false,
});
const rotationDegree = ref(0);

//
// Composables
//
const { formatCaption } = useLocale();

//
// Computed
//
const fileType = computed((): string => {
    if (props.documentSource === undefined) return 'unsupported';

    const uint8Array = new Uint8Array(props.documentSource);

    // PDF signature
    if (uint8Array[0] === 0x25 && uint8Array[1] === 0x50 && uint8Array[2] === 0x44 && uint8Array[3] === 0x46) {
        return 'pdf';
    }
    //jpg signature
    if (uint8Array[0] === 0xff && uint8Array[1] === 0xd8 && uint8Array[2] === 0xff) {
        return 'jpeg';
    }
    // PNG signature
    if (uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4e && uint8Array[3] === 0x47) {
        return 'png';
    }
    // GIF signature
    if (uint8Array[0] === 0x47 && uint8Array[1] === 0x49 && uint8Array[2] === 0x46 && uint8Array[3] === 0x38) {
        return 'gif';
    }
    // BMP signature
    if (uint8Array[0] === 0x42 && uint8Array[1] === 0x4d) {
        return 'bmp';
    }
    // TIFF signature
    if (
        (uint8Array[0] === 0x49 && uint8Array[1] === 0x49 && uint8Array[2] === 0x2a && uint8Array[3] === 0x00) ||
        (uint8Array[0] === 0x4d && uint8Array[1] === 0x4d && uint8Array[2] === 0x00 && uint8Array[3] === 0x2a)
    ) {
        return 'tiff';
    }
    // WEBP signature
    if (
        uint8Array[0] === 0x52 &&
        uint8Array[1] === 0x49 &&
        uint8Array[2] === 0x46 &&
        uint8Array[3] === 0x46 &&
        uint8Array[8] === 0x57 &&
        uint8Array[9] === 0x45 &&
        uint8Array[10] === 0x42 &&
        uint8Array[11] === 0x50
    ) {
        return 'webp';
    }
    // DIB signature (check for BITMAPINFOHEADER size of 40 bytes)
    const headerSize = (uint8Array[3] << 24) | (uint8Array[2] << 16) | (uint8Array[1] << 8) | uint8Array[0];
    if (headerSize === 40) {
        return 'dib';
    }
    return 'unsupported';
});

const shouldShowThumbnails = computed((): boolean => {
    return !props.loading && props.enableThumbnail && fileType.value === 'pdf';
});

const isImage = computed((): boolean => {
    return ['jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'dib'].includes(fileType.value);
});

const isUnsupportedFile = computed((): boolean => {
    return fileType.value === 'unsupported';
});

const internalDocumentName = computed((): string => {
    return isUnsupportedFile.value ? 'Document viewer' : props.documentName;
});

//
// Watchers
//
watch(
    () => props.loading,
    (newVal) => {
        if (!newVal) {
            updateViewerWrapDimensions();
        }
    }
);

//
// Event Handlers
//
function onInfo() {
    showDocumentInfo.value = !showDocumentInfo.value;
}

function onThumbnails() {
    showThumbnails.value = !showThumbnails.value;
}

function onDownload() {
    const { urlProvider } = props;

    if (urlProvider && urlProvider.downloadUrl) {
        try {
            window.open(urlProvider.downloadUrl);
        } catch (error) {
            console.error('Invalid URL:', error);
        }
    } else {
        console.warn('Download URL is not provided');
    }
}

function onPrint() {
    const { urlProvider } = props;

    if (urlProvider && urlProvider.printUrl) {
        try {
            const printWindow = window.open(urlProvider.printUrl, '_blank');

            if (printWindow) {
                printWindow.onload = () => {
                    printWindow.print();
                };
            } else {
                console.error('Failed to open print window');
            }
        } catch (error) {
            console.error('Invalid URL:', error);
        }
    } else {
        console.warn('Print URL is not provided');
    }
}

//
// Helpers
//
function updateViewerWrapDimensions() {
    if (viewerWrap.value) {
        viewerWrapWidth.value = viewerWrap.value.clientWidth;
        viewerWrapHeight.value = viewerWrap.value.clientHeight;
    }
}

function handleUpdateRotate(rotation: number) {
    rotationDegree.value = rotation;
}

function handleUpdateScale(newScaleConfig: Scale) {
    scaleConfig.value.resetScale = false;
    scaleConfig.value.scaleMode = newScaleConfig.scaleMode;
    scaleConfig.value.scaleValue = newScaleConfig.scaleValue;
}

function handleUpdateScaleModeOnly(newScaleMode: ScalingMode) {
    scaleConfig.value.scaleMode = newScaleMode;
    scaleConfig.value.resetScale = true;
}

function handleUpdateZoom(newZoomValue: string) {
    zoomPercentage.value = newZoomValue;
}

function handleUpdatePageCount(newPageCount: number) {
    pageCount.value = newPageCount;
    emit('page-count-updated', pageCount.value);
}

function handleUpdateActivePageNumber(newActivePageNumber: number) {
    activePageNumber.value = newActivePageNumber;
}

//
// Lifecycle
//
onMounted(() => {
    updateViewerWrapDimensions();
});
</script>

<style lang="scss">
.wtg-document-viewer {
    position: relative;
    height: fit-content;

    .document-panel {
        padding: 0;
        overflow: hidden;
    }

    .document-header {
        display: flex;
        align-items: center;
        padding: 10px;
        color: #333;
        background: #fff;
        position: absolute;
        top: 1px;
        right: 1px;
        left: 1px;
        border-radius: 6px 6px 0 0;
        border-bottom: 1px solid rgba(107, 107, 104, 0.5);
        min-height: 50px;

        .document-file-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .document-info {
        display: flex;
        padding: var(--s-padding-xl);
        flex-direction: column;
        flex: 1 0 0;
        align-self: stretch;
        min-height: calc(100vh - 282px);
    }

    .document-body {
        display: flex;
        flex-direction: column;
    }

    .thumbnails,
    .scroll-wrap {
        overflow: auto;
        flex: 1;
        height: calc(100vh - 300px);
        opacity: 1;
        transition: opacity 0.25s ease-in-out;
        scrollbar-width: thin;
    }

    .thumbnails {
        max-width: 140px;
        padding: 15px;
        border-right: 1px solid var(--s-neutral-border-weak-default);
        transition: all 0.2s ease-in-out;

        &.close {
            max-width: 0;
            padding: 15px 0;
            border-right: none;
        }
    }

    .scroll-wrap {
        padding: 15px;
    }

    .loading {
        position: absolute;
        top: calc(50% - 25px);
        margin: 0 50% 0 calc(50% - 25px);
        width: 50px;
    }

    .document-wrap {
        position: relative;
        min-width: fit-content;
        height: fit-content;

        .unsupported-document {
            margin: 10% 0 10px 50%;
            transform: translateX(-50%);
        }
    }
}
</style>
