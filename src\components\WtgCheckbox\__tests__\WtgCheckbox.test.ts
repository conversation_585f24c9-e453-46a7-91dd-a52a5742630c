import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import * as notifications from '@composables/notifications';
import * as tooltip from '@composables/tooltip/tooltip';
import { AlertLevel } from '@composables/notifications';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { computed, ComputedRef, defineComponent, nextTick, Ref, ref } from 'vue';
import { WtgCheckbox } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

jest.mock('@composables/notifications');

describe('WtgCheckbox', () => {
    beforeEach(() => {
        jest.spyOn(notifications, 'useCurrentNotification').mockReturnValue({
            currentNotification: ref({}),
            displayCurrentNotification: computed(() => false),
        });
    });

    test('it sets the checkbox label to the label attribute passed in ', () => {
        const wrapper = mountComponent({
            props: {
                label: 'My Checkbox',
            },
        });
        const checkboxlabel = wrapper.find('label');
        expect(checkboxlabel.html()).toContain('My Checkbox');
    });

    test('it shows the s-icon-checkbox-off* icons when the checkbox is un-checked', async () => {
        const wrapper = mountComponent();

        const icon = wrapper.find('i');
        expect(icon.classes()).toContain('s-icon-checkbox-off');
        await wrapper.setProps({ readonly: true });
        expect(icon.classes()).toContain('s-icon-checkbox-off-readonly');
        await wrapper.setProps({ readonly: false, disabled: true });
        expect(icon.classes()).toContain('s-icon-checkbox-off-disabled');
    });

    test('it shows the s-icon-checkbox-on* icons when the checkbox is checked', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: true,
            },
        });

        const icon = wrapper.find('i');
        expect(icon.classes()).toContain('s-icon-checkbox-on');
        await wrapper.setProps({ readonly: true });
        expect(icon.classes()).toContain('s-icon-checkbox-on-readonly');
        await wrapper.setProps({ readonly: false, disabled: true });
        expect(icon.classes()).toContain('s-icon-checkbox-on-disabled');
    });

    test('it shows the s-icon-checkbox-indeterminate* icons when the checkbox is indeterminate', async () => {
        const wrapper = mountComponent({
            props: {
                indeterminate: true,
            },
        });

        const icon = wrapper.find('i');
        expect(icon.classes()).toContain('s-icon-checkbox-indeterminate');
        await wrapper.setProps({ readonly: true });
        expect(icon.classes()).toContain('s-icon-checkbox-indeterminate-readonly');
        await wrapper.setProps({ readonly: false, disabled: true });
        expect(icon.classes()).toContain('s-icon-checkbox-indeterminate-disabled');
    });

    test('it renders the messages list passed in the messages property', () => {
        const wrapper = mountComponent({
            props: {
                messages: ['this is a test message'],
            },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<ol><li>this is a test message</li></ol>');
    });

    test('it renders the message passed into the messages property when it is a string', () => {
        const wrapper = mountComponent({
            props: {
                messages: 'this is a test message',
            },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<ol><li>this is a test message</li></ol>');
    });

    test('it renders the validation state', () => {
        const wrapper = mountComponent({
            props: {
                validationState: {
                    alertLevel: AlertLevel.Warning,
                    messages: ['this is a validation message'],
                },
            },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        expect(checkbox.classes()).toContain('wtg-checkbox--warning');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<ol><li>this is a validation message</li></ol>');
    });

    test('it merges validationState and messages if both were to be used simultaniously', () => {
        const wrapper = mountComponent({
            props: {
                messages: 'this is a test message',
                validationState: {
                    alertLevel: AlertLevel.Warning,
                    messages: ['this is a validation message'],
                },
            },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        expect(checkbox.classes()).toContain('wtg-checkbox--warning');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<li>this is a test message</li>');
        expect(vm.tooltipDirective.content).toContain('<li>this is a validation message</li>');
    });

    test('it should emit the update:model-value event when the input change event is fired', async () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
        await input.setValue(true);
        expect(wrapper.emitted('update:modelValue')!.length).toBe(1);
        expect(![0][0]).toBe(true);
        await input.setValue(false);
        expect(wrapper.emitted('update:modelValue')!.length).toBe(2);
        expect(wrapper.emitted('update:modelValue')![1][0]).toBe(false);
    });

    test('it should not emit the update:model-value when disabled', async () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });
        const input = wrapper.find('input');
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
        await input.setValue(true);
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
    });

    test('when readonly, it prevents the user from changing the value', async () => {
        const wrapper = mountComponent({
            props: {
                readonly: true,
            },
        });
        const input = wrapper.find('input');
        let e = { preventDefault: jest.fn() };
        await input.trigger('click', e);
        expect(e.preventDefault).toHaveBeenCalled();

        await wrapper.setProps({ readonly: false });
        e = { preventDefault: jest.fn() };
        await input.trigger('click', e);
        expect(e.preventDefault).not.toHaveBeenCalled();
    });

    test('it sets the individual checkboxes when used with the multiple prop', async () => {
        const component = defineComponent({
            components: { WtgCheckbox },
            data: () => {
                return {
                    selectedOptions: [],
                    options: ['Option1', 'Option2', 'Option3'],
                };
            },
            template: `
                <div>
                    <wtg-checkbox
                        v-for="option in options"
                        :key="option"
                        v-model="selectedOptions"
                        multiple
                        :label="option"
                        :value="option"
                    />
                </div>
            `,
        });
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });

        const checkboxes = wrapper.findAllComponents(WtgCheckbox);
        const checkBoxOptionOne = checkboxes.at(0);
        const checkBoxOptionTwo = checkboxes.at(1);
        const checkBoxOptionThree = checkboxes.at(2);

        await checkBoxOptionOne?.find('input').setValue(true);
        await checkBoxOptionTwo?.find('input').setValue(true);
        await checkBoxOptionThree?.find('input').setValue(true);

        expect(wrapper.vm.selectedOptions).toEqual(['Option1', 'Option2', 'Option3']);
    });

    test('it implements v-model through the value property and the change event', async () => {
        const component = defineComponent({
            components: { WtgCheckbox },
            data: () => {
                return {
                    checked: true,
                };
            },
            template: '<wtg-checkbox v-model="checked" label="my check" />',
        });
        const wrapper = mount(component, {
            wtgUi,
            global: {
                plugins: [wtgUi],
            },
        });
        const checkbox = wrapper.findComponent(WtgCheckbox);
        expect(checkbox.props('modelValue')).toBe(true);

        const input = checkbox.find('input');
        await input.setValue(false);
        expect(wrapper.vm.$data.checked).toBe(false);
        expect(checkbox.props('modelValue')).toBe(false);
    });

    test('it has tooltip capability mixed in', () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: { tooltip: { content: 'Some tooltip', placement: 'top' } },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        expect(wrapper.vm.tooltipDirective.content).toBe('Some tooltip');
        expect(wrapper.vm.tooltipDirective.placement).toBe('top');
        expect(wrapper.vm.tooltipDirective.shown).toBe(true);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('it renders the restricted icon instead of checkbox when the display is restricted', () => {
        const wrapper = mountComponent({
            props: {
                restricted: true,
            },
        });
        expect(wrapper.find('i').html()).toContain(
            '<i aria-hidden="true" class="wtg-icon s-icon-hide wtg-checkbox__icon">'
        );
    });

    test('it applies the required class when the display is required', async () => {
        const wrapper = mountComponent();
        expect(wrapper.find('.wtg-required').exists()).toBe(false);

        await wrapper.setProps({ required: true });
        expect(wrapper.find('.wtg-required').exists()).toBe(true);
    });

    test("it always renders the image tag as the next element after the input tag because that's what the CSS is targeting to make focus state work", async () => {
        const wrapper = mountComponent();
        expect((wrapper.find('input').element!.nextSibling as HTMLElement)!.tagName).toBe('I');

        await wrapper.setProps({ modelValue: true });
        expect((wrapper.find('input').element!.nextSibling as HTMLElement)!.tagName).toBe('I');

        await wrapper.setProps({ modelValue: false });
        expect((wrapper.find('input').element!.nextSibling as HTMLElement)!.tagName).toBe('I');
    });

    test('it has a (deprecated) inputId property that gets applied if no id is specified to aid the GLOW VUE 3 migration', async () => {
        const wrapper = mountComponent();
        await wrapper.setProps({ inputId: 'id1' });
        expect(wrapper.find('input').attributes('id')).toBe('id1');

        await wrapper.setProps({ id: 'id2' });
        expect(wrapper.find('input').attributes('id')).toBe('id2');
    });

    test('it has (deprecated) VALUE property and INPUT event that allows it to be backwards compatible with the V-MODEL handling of the VUE 2 implementation', async () => {
        const onChange = jest.fn();
        const wrapper = mountComponent({
            props: {
                inputValue: true,
            },
            attrs: {
                onChange,
            },
        });

        const icon = wrapper.find('i');
        expect(icon.classes()).toContain('s-icon-checkbox-on');

        const input = wrapper.find('input');
        await input.setValue(false);
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenCalledWith(false);
        expect(icon.classes()).toContain('s-icon-checkbox-off');

        await wrapper.setProps({ modelValue: true });
        expect(icon.classes()).toContain('s-icon-checkbox-on');
    });

    test('it does not render the label when no label content is available', () => {
        const wrapper = mountComponent();

        const label = wrapper.find('label');
        expect(label.exists()).toBe(false);
    });

    describe('when multiple is true', () => {
        test('it updates internalValue based on internalValueMultiple', async () => {
            const wrapper = mountComponent({
                props: {
                    multiple: true,
                    value: 'Option1',
                    inputValue: ['Option1', 'Option2'],
                },
            });
            let checkbox = wrapper.find('.wtg-checkbox--selected');
            expect(checkbox.exists()).toBe(true);

            await wrapper.setProps({ inputValue: ['Option2'] });
            checkbox = wrapper.find('.wtg-checkbox--selected');
            expect(checkbox.exists()).toBe(false);
        });

        test('it should update icon when checkbox is checked', async () => {
            const wrapper = mountComponent({
                props: {
                    multiple: true,
                    value: 'Option1',
                    inputValue: ['Option1'],
                },
            });
            let icon = wrapper.find('i');
            expect(icon.classes()).toContain('s-icon-checkbox-on');

            await wrapper.setProps({ inputValue: [] });
            icon = wrapper.find('i');
            expect(icon.classes()).not.toContain('s-icon-checkbox-on');
        });
    });

    describe('when error or errorMessages are set', () => {
        test('it applies the correct style when error is set to true', () => {
            const wrapper = mountComponent({ props: { error: true, messages: ['Test error message.'] } });
            const checkbox = wrapper.find('.wtg-checkbox');
            expect(checkbox.classes()).toContain('wtg-checkbox--critical');
            checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('<ol><li>Test error message.</li></ol>');
        });

        test('it applies the correct style when error is changed', async () => {
            const wrapper = mountComponent({ props: { error: true, messages: ['Test error message.'] } });
            await wrapper.setProps({ error: false, messages: [] });
            const checkbox = wrapper.find('.wtg-checkbox');
            expect(checkbox.classes()).not.toContain('wtg-checkbox--critical');
            checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective).toBe(undefined);
        });

        test('it applies the correct style when errorMessages is set', () => {
            const wrapper = mountComponent({ props: { errorMessages: ['Test error message.'] } });
            const checkbox = wrapper.find('.wtg-checkbox');
            expect(checkbox.classes()).toContain('wtg-checkbox--critical');
            checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('<ol><li>Test error message.</li></ol>');
        });

        test('it applies the correct style when errorMessages is set', async () => {
            const wrapper = mountComponent({ props: { errorMessages: ['Test error message.'] } });
            await wrapper.setProps({ errorMessages: ['Test error message2.'] });
            const checkbox = wrapper.find('.wtg-checkbox');
            expect(checkbox.classes()).toContain('wtg-checkbox--critical');
            checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('<ol><li>Test error message2.</li></ol>');
        });

        test('it applies the correct style when sentiment and messages are set', () => {
            const wrapper = mountComponent({
                props: { sentiment: 'warning', messages: ['Test warning message.'] },
            });
            const checkbox = wrapper.find('.wtg-checkbox');
            expect(checkbox.classes()).toContain('wtg-checkbox--warning');
            checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
            const vm = wrapper.vm as any;
            expect(vm.tooltipDirective.shown).toBe(true);
            expect(vm.tooltipDirective.content).toContain('<ol><li>Test warning message.</li></ol>');
        });
    });

    test('it has a tabindex prop that it passes to the input element', () => {
        const wrapper = mountComponent({
            props: {
                tabindex: 5,
            },
        });
        const input = wrapper.find('input');
        expect(input.attributes('tabindex')).toBe('5');
    });

    describe('useCurrentNotification', () => {
        let mockdisplayCurrentNotificationRef: Ref<boolean>;
        let mockdisplayCurrentNotification: ComputedRef<boolean>;
        let createTooltipDirectiveSpy: jest.SpyInstance;

        beforeEach(() => {
            mockdisplayCurrentNotificationRef = ref(false);
            mockdisplayCurrentNotification = computed(() => mockdisplayCurrentNotificationRef.value);

            jest.spyOn(notifications, 'useCurrentNotification').mockReturnValue({
                currentNotification: ref({}),
                displayCurrentNotification: mockdisplayCurrentNotification,
            });

            jest.spyOn(tooltip, 'useTooltip').mockImplementationOnce((props) => {
                const { useTooltip } = jest.requireActual('@composables/tooltip/tooltip');
                const result = useTooltip(props) as ReturnType<typeof tooltip.useTooltip>;
                createTooltipDirectiveSpy = jest.spyOn(result, 'createTooltipDirective');
                return result;
            });
        });

        test('it should have root ref for useCurrentNotification', () => {
            const wrapper = mountComponent();
            expect(wrapper.vm.$refs.root).not.toBeUndefined();
        });

        test('it should change the background color to the hover state and display message when displayCurrentNotification is true', async () => {
            const wrapper = mountComponent({
                props: {
                    messages: ['this is a test message'],
                },
            });
            mockdisplayCurrentNotificationRef.value = true;
            await nextTick();
            const background = wrapper.find('.wtg-checkbox__current-notification');
            expect(background.exists()).toBe(true);
            expect(createTooltipDirectiveSpy.mock.calls.at(-1)[0].shown).toBe(true);
            expect(createTooltipDirectiveSpy.mock.calls.at(-1)[0].content).toContain('this is a test message');
        });
    });

    function mountComponent({ attrs = {}, props = {}, provide = {} } = {}) {
        return mount(WtgCheckbox, {
            attrs,
            props,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
