import WtgButton from '@components/WtgButton';
import WtgDivider from '@components/WtgDivider';
import WtgEmptyState from '@components/WtgEmptyState';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgPanel from '@components/WtgPanel';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { EmptyStateSandboxTemplate } from './templates/wtg-empty-state-sandbox.stories-template';

type Story = StoryObj<typeof WtgEmptyState>;
const meta: Meta<typeof WtgEmptyState> = {
    title: 'Components/Empty State',
    component: WtgEmptyState,
    parameters: {
        docs: {
            description: {
                component:
                    'Empty states are moments in the user journey where there is no data to be displayed to the user. It is very important to understand that the empty state has a high priority in the UX design process.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/g6kTPiwXZrzyIZb4i41RYl/[CargoWise]-SUPPLY---Components?node-id=13055-29163&t=rWBAqQuxVW5WOxlr-0',
        },
        layout: 'centered',
    },
    argTypes: {
        variant: {
            options: ['default', 'condensed'],
            control: {
                type: 'select',
            },
        },
        type: {
            options: ['firstUse', 'userCleared', 'missingPage', 'error', 'restricted', 'noData', ''],
            control: {
                type: 'select',
            },
        },
    },
    decorators: [
        () => ({
            template: `
            <div style="display:flex;flex-direction:row">
                <story/>
            </div>
            `,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        variant: 'default',
        type: 'firstUse',
        header: 'Empty State Header',
        bodycopy: 'Empty State Bodycopy',
    },
    render: (args) => ({
        components: { WtgEmptyState, WtgButton },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: `<WtgEmptyState v-bind="args" @click="action">
                        <WtgButton @click="onClick" leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                                                                                      
                                Label                                                   
                        </WtgButton>
                    </WtgEmptyState>`,
    }),
};
export const Condensed: Story = {
    args: {
        variant: 'condensed',
        type: 'firstUse',
        header: 'Empty State Header',
        bodycopy: 'Empty State Bodycopy',
    },
    render: (args) => ({
        components: { WtgEmptyState, WtgButton },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: `<WtgEmptyState v-bind="args" @click="action">
                            <WtgButton @click="onClick" leading-icon='s-icon-placeholder' trailing-icon='s-icon-placeholder'>                                
                                    Label                                
                            </WtgButton>
                    </WtgEmptyState>`,
    }),
};
export const Sandbox: Story = {
    args: {
        type: 'firstUse',
        header: 'Empty State Header',
        bodycopy: 'Empty State Bodycopy',
    },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgEmptyState, WtgButton, WtgDivider, WtgLayoutGrid, WtgPanel },
        setup: () => ({ args }),
        methods: {},
        template: templateWithRtl(EmptyStateSandboxTemplate),
    }),
};
