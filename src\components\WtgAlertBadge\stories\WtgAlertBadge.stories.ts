import { WtgAlertBadge, WtgButton, WtgCol, WtgIconButton, WtgLabel, WtgLayoutGrid, WtgRow } from '@components';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { Meta, StoryObj } from '@storybook/vue3';
import { AlertBadgeBehaviorsTemplate } from './templates/wtg-alert-badge-sandbox.stories-template';

type Story = StoryObj<typeof WtgAlertBadge>;
const meta: Meta<typeof WtgAlertBadge> = {
    title: 'Components/Alert Badge',
    component: WtgAlertBadge,
    parameters: {
        docs: {
            description: {
                component: 'Alert badge components are attached to other components to indicate their status.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/g6kTPiwXZrzyIZb4i41RYl/[CargoWise]-SUPPLY---Components?node-id=188-15929&t=ImRcVD4KcWdl5sXs-0',
        },
        layout: 'centered',
    },
    args: {
        variant: 'info',
    },
    render: (args) => ({
        components: { WtgAlertBadge, WtgIconButton },
        setup: () => ({ args }),
        template: '<WtgAlertBadge v-bind="args"><wtg-icon-button :icon="args.icon"></wtg-icon-button></WtgAlertBadge>',
    }),
    argTypes: {
        variant: {
            options: ['error', 'warning', 'success', 'info'],
            control: {
                type: 'select',
            },
        },
    },
};

export default meta;

export const error: Story = {
    args: {
        variant: 'error',
    },
    render: (args) => ({
        components: { WtgAlertBadge, WtgButton },
        setup: () => ({ args }),
        template: `<WtgAlertBadge v-bind="args">
            <WtgButton aria-label="Aria Name">Button</WtgButton>
        </WtgAlertBadge>`,
    }),
};

export const info: Story = {
    args: {
        variant: 'info',
    },
    render: (args) => ({
        components: { WtgAlertBadge, WtgButton },
        setup: () => ({ args }),
        template: `<WtgAlertBadge v-bind="args">
            <WtgButton aria-label="Aria Name">Button</WtgButton>
        </WtgAlertBadge>`,
    }),
};

export const success: Story = {
    args: {
        variant: 'success',
    },
    render: (args) => ({
        components: { WtgAlertBadge, WtgButton },
        setup: () => ({ args }),
        template: `<WtgAlertBadge v-bind="args">
            <WtgButton aria-label="Aria Name">Button</WtgButton>
        </WtgAlertBadge>`,
    }),
};

export const warning: Story = {
    args: {
        variant: 'warning',
    },
    render: (args) => ({
        components: { WtgAlertBadge, WtgButton },
        setup: () => ({ args }),
        template: `<WtgAlertBadge v-bind="args">
            <WtgButton aria-label="Aria Name">Button</WtgButton>
        </WtgAlertBadge>`,
    }),
};

export const sandbox: Story = {
    args: {},
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgAlertBadge, WtgButton, WtgIconButton, WtgLabel, WtgRow, WtgCol, WtgLayoutGrid },
        setup: () => ({ args }),
        template: templateWithRtl(AlertBadgeBehaviorsTemplate),
    }),
};
