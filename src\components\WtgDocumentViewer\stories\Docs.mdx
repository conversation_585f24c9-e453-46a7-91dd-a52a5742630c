import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusInProgress from '../../../storybook/assets/statusInProgress.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import documentViewerAnatomy from '../../../assets/WtgDocumentViewer/PDF-viewer-anatomy.png';
import * as WtgDocumentViewer from './WtgDocumentViewer.stories.ts';

<Meta of={WtgDocumentViewer} />

<div className="component-header">
    <h1>Document Viewer</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=228-88020&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>With pending updates
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                [PRJ00051784](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/b5bcabcc-6ee8-4b0e-b748-63d2ece5cb6f?lang=en-gb)
            </td>
            <td>Replace WtgDocumentPreview with WtgDocumentViewer</td>
        </tr>
    </tbody>
</table>

## Overview

The PDF viewer or document viewer lets users preview multi-page documents and images.

<div className="banner-warning">

    <div>
        <h4>⚠️ Please note</h4>
        The Document Viewer component is a lightweight viewer intended for displaying simple document types. It is often confused with the more advanced PDF viewer provided by the GLOW framework, which includes additional features such as highlighting and metadata support.
            The key difference is architectural: the GLOW component is tightly integrated with the eDocs system and relies on specific database tables and structures to enable its enhanced functionality. As a result, it is only available within GLOW-based applications. The Supply component remains intentionally decoupled to support broader use cases without requiring eDocs integration.

    </div>

</div>

## API

<Canvas className="canvas-preview" of={WtgDocumentViewer.Default} />
<Controls of={WtgDocumentViewer.Default} sort={'alpha'} />

## Anatomy

<div className="d-flex flex-column align-center">
    <img srcSet={`${documentViewerAnatomy} 3x`} alt="anatomy of document viewer" />
</div>

<ol className="anatomy-list">
    <li>
        <strong>Document name:</strong> Name of the document/image being viewed.
    </li>
    <li>
        <strong>Pagination:</strong> Allows for page navigation, displays current page, and total pages. 
    </li>
    <li>
         <strong>Zoom: </strong> Zoom in and out of the document using the plus/minus icons. As well as adjust the scaling properties of the document using the select field.
    </li>

     <li>
         <strong>Rotate </strong> Rotates current page.
    </li>

     <li>
         <strong>Document information: </strong> Shows document metadata.
    </li>
     <li>
         <strong>Print: </strong> Triggers print options.
    </li>
     <li>
         <strong>Download:</strong> Downloads document.
    </li>
     <li>
         <strong>Page thumbnail toggle:  </strong> Show and hide the page thumbnail view of the document pages
    </li>
     <li>
         <strong>Page thumbnail: </strong> Preview of a page.
    </li>

</ol>

## Behavior

### Page thumbnail view

Page thumbnails can be viewed in a side drawer using the page thumbnail toggle. It allows for page navigation and will support bulk actions in the future.

### Zoom and scaling

Users can zoom in and out in set increments of 25% using the plus and minus buttons. Additional scaling options are available in the select field and are displayed as percentage values.

## Related components

-   [File area](/docs/components-file-area--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
