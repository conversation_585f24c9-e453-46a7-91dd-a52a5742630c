<template>
    <div v-floating-vue-tooltip="tooltipDirective">
        <WtgPopover
            :location="`${openPosition} right`"
            :max-height="popoverMaxHeight"
            :nudge-top="openPosition === 'top' ? '4px' : undefined"
            :nudge-bottom="openPosition === 'bottom' ? '4px' : undefined"
            :close-on-content-click="closePopoverOnContentClick"
        >
            <template #activator="{ props: activatorProps }: { props: Record<string, any> }">
                <WtgIconButton
                    v-bind="activatorProps"
                    :active="isOpen"
                    :aria-label="dropdownAria ?? formatCaption('dropdownButton.toggleMenu')"
                    :color="color"
                    :disabled="disabled"
                    :icon="icon"
                    :icon-size="iconSize"
                    :loading="loading"
                    :sentiment="sentiment"
                    :tooltip="tooltip"
                    :size="size"
                    :variant="variant"
                    @click="onDropdownButtonClicked"
                >
                </WtgIconButton>
            </template>
            <slot name="popover" />
        </WtgPopover>
    </div>
</template>

<script setup lang="ts">
import { WtgIconButton } from '@components/WtgIconButton';
import { WtgPopover } from '@components/WtgPopover';
import { useLocale } from '@composables/locale';
import { makeSizeProps } from '@composables/size';
import { makeTooltipProps, useTooltip } from '@composables/tooltip';
import { PropType } from 'vue';

const props = defineProps({
    /**
     * If true, the popover will close when its content is clicked.
     */
    closePopoverOnContentClick: {
        type: Boolean,
        default: true,
    },

    /**
     * The color of the button.
     * Can be a predefined color from the design system.
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * Disables the icon button, making it unclickable and visually indicating its disabled state.
     */
    disabled: {
        type: Boolean,
        default: false,
    },

    /**
     * The ARIA label for the dropdown button.
     */
    dropdownAria: {
        type: String,
        default: undefined,
    },

    /**
     * The name of the icon to display inside the button.
     */
    icon: {
        type: String,
        default: undefined,
    },

    /**
     * The size of the icon.
     * Can be one of the predefined sizes: 'xs', 's', 'm', 'l', 'xl', 'xxl'.
     */
    iconSize: {
        type: String as PropType<'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl'>,
        default: undefined,
    },

    /**
     * If true, a loading indicator will be displayed on the button.
     */
    loading: {
        type: Boolean,
        default: false,
    },

    /**
     * The position where the dropdown popover will open.
     * Options include 'top' or 'bottom'.
     */
    openPosition: {
        type: String as PropType<'top' | 'bottom'>,
        default: 'bottom',
    },

    /**
     * The maximum height of the dropdown popover, in pixels.
     */
    popoverMaxHeight: {
        type: Number,
        default: undefined,
    },

    /**
     * The sentiment or visual style of the button.
     * Options include 'critical', 'primary', or 'success'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'primary' | 'success'>,
        default: undefined,
    },

    /**
     * The variant of the button.
     * Options include 'fill' for a solid background button or 'ghost' for a transparent button with a border.
     */
    variant: {
        type: String as PropType<'fill' | 'ghost'>,
        default: undefined,
    },
    ...makeSizeProps(),
    ...makeTooltipProps(),
});

const { formatCaption } = useLocale();

const isOpen = defineModel<boolean>({ default: undefined });

const emit = defineEmits<{
    'dropdown-click': [e: MouseEvent];
}>();

const { tooltipDirective } = useTooltip(props);

const onDropdownButtonClicked = (e: MouseEvent) => {
    emit('dropdown-click', e);
};
</script>
