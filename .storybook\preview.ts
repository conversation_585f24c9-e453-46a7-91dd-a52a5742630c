/** @type { import('@storybook/vue').Preview } */
import { setup } from '@storybook/vue3';
import { watch } from 'vue';
import { useRtl } from 'vuetify';
import WtgUi from '../src';
import DocsContainer from '../src/storybook/docsContainer/docsContainer';
import DocumentationTemplate from '../src/storybook/documentation/documentation-template.mdx';
import './storybook.scss';
import supplyTheme from './supplyTheme';

const wtgUi = new WtgUi();

setup((app) => {
    app.use(wtgUi);
});

const preview = {
    decorators: [
        (story, context) => {
            watch(
                () => context.globals.locale,
                (newLocale) => (wtgUi.language.current = newLocale),
                { immediate: true }
            );
            watch(
                () => context.globals.theme,
                (newTheme) => (wtgUi.appearance = newTheme),
                { immediate: true }
            );
            return {
                components: { story },
                setup: () => {
                    const { isRtl } = useRtl();
                    return { isRtl };
                },
                template: `<div :style="{'direction': isRtl ? 'rtl' : 'ltr'}"><story /></div>`,
            };
        },
    ],

    parameters: {
        backgrounds: {
            disable: true,
        },
        options: {
            storySort: {
                order: [
                    'Intro',
                    'About',
                    'Channels',
                    'Roadmap',
                    'Changelog',
                    ['Versions', ['2.0', '1.2', '1.1']],
                    'Getting started',
                    [
                        'Design',
                        ['*', 'Low-code guidelines'],
                        'Engineering',
                        [
                            'Overview',
                            "FAQ's",
                            'Contributing',
                            'Custom components',
                            'Component checklist',
                            'Resources',
                            'Platform Builder components',
                            'Features not supported in Vue 2',
                            'Figma to code guidelines',
                            'Applications',
                            [
                                'Overview',
                                'Web applications',
                                'Mobile applications',
                                'GLOW Portal in Platform Builder',
                                'GLOW Portal in Platform Builder with custom code',
                                'GLOW Portals in Vue',
                                'Adding Supply components to existing Vue applications',
                            ],
                            'Vue 2 Upgrade guide',
                            ['Overview', 'Migration strategy', 'Component changes'],
                        ],
                    ],
                    'Coding Assistant',
                    'Foundations',
                    'Guidelines',
                    [
                        'Content',
                        'Accessibility',
                        ['Getting started', 'Criteria and tips'],
                        'Localization',
                        'Grid system',
                    ],
                    'Design Tokens',
                    [
                        'Overview',
                        'Color',
                        ['Overview', 'Tokens'],
                        'Typography',
                        'Space',
                        'Object styles',
                        'Utility classes',
                    ],
                    'Components',
                    ['*', 'Experimental'],
                    'Icons & Images',
                    ['Icon contribution'],
                    'Data viz',
                    ['Overview'],
                    'Utilities',
                    ['Overview', 'Grid'],
                    'Composables',
                    ['Overview', 'useDisplay', 'useFramework', 'useTheme'],
                    'Patterns',
                    ['Overview', 'Enterprise', ['Overview', '*']],
                    'Framework',
                    ['*', 'Dialogs', 'Pages', 'Frameworks'],
                    '*',
                ],
                locales: 'en-US',
                method: 'alphabetical',
            },
        },
        chromatic: {
            disableSnapshot: true,
        },
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/,
            },
        },
        docs: {
            theme: supplyTheme,
            toc: {
                contentsSelector: '.sbdocs-content',
                headingSelector: 'h2, h3',
                disable: false,
                unsafeTocbotOptions: {
                    orderedList: false,
                },
            },
            page: DocumentationTemplate,
            container: DocsContainer,
        },
        viewport: {
            viewports: {
                mobile: {
                    name: 'mobile',
                    styles: { width: '360px', height: '640px' },
                    type: 'mobile',
                },
                tablet: {
                    name: 'tablet',
                    styles: { width: '950px', height: '768px' },
                    type: 'tablet',
                },
                desktop: {
                    name: 'desktop',
                    styles: { width: '1920px', height: '1080px' },
                    type: 'desktop',
                },
            },
        },
        a11y: {
            test: 'todo',
        },
    },
};

export const globalTypes = {
    theme: {
        description: 'Theme',
        defaultValue: 'light',
        toolbar: {
            icon: 'paintbrush',
            items: ['light', 'dark', 'muted'],
        },
    },
    locale: {
        description: 'Internationalization locale',
        defaultValue: 'en-au',
        toolbar: {
            icon: 'globe',
            items: ['en-au', 'en-us', 'en-gb', 'ko', 'fr', 'nl', 'ar'],
        },
    },
};

export default preview;
