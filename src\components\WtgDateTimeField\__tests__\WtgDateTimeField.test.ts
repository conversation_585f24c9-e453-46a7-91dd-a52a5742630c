import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { DOMWrapper, enableAutoUnmount, mount, type VueWrapper } from '@vue/test-utils';
import WtgDateTimeField from '..';
import WtgUi from '../../../WtgUi';
import { WtgDateTimeFormatter } from '../types';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

window.HTMLElement.prototype.scrollIntoView = jest.fn();

describe('WtgDateTimeField', () => {
    beforeEach(() => {
        jest.useFakeTimers().setSystemTime(new Date('2020-01-19'));
    });

    test('its name is WtgDateTimeField', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgDateTimeField');
    });

    test('it passes its props to the base WtgInput', () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
                displayOnly: true,
                flat: true,
                id: '111',
                label: 'My Label',
                leadingIcon: 'icon1',
                loading: true,
                messages: 'message',
                placeholder: 'placeholder',
                readonly: true,
                required: true,
                restricted: true,
                sentiment: 'primary',
            },
        });
        const props = wrapper.findComponent({ name: 'WtgInput' }).props();
        expect(props.disabled).toBe(true);
        expect(props.displayOnly).toBe(true);
        expect(props.filled).toBe(false);
        expect(props.flat).toBe(true);
        expect(props.hideMessages).toBe(false);
        expect(props.id).toBe('111');
        expect(props.label).toBe('My Label');
        expect(props.leadingIcon).toBe('icon1');
        expect(props.loading).toBe(true);
        expect(props.messages).toBe('message');
        expect(props.placeholder).toBe('placeholder');
        expect(props.readonly).toBe(true);
        expect(props.required).toBe(true);
        expect(props.restricted).toBe(true);
        expect(props.sentiment).toBe('primary');
    });

    test('it passes attributes to the base WtgInput element', () => {
        const wrapper = mountComponent({
            props: {
                dataTestId: 'my test id',
            },
        });
        expect(wrapper.attributes('datatestid')).toBe('my test id');
    });

    test("it has the autocomplete attribute set to 'off'", () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        expect(input.attributes('autocomplete')).toBe('off');
    });

    test('it updates the base WtgInput filled property when the input has content', async () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        input.setValue('20-05-2018');
        await wrapper.vm.$nextTick();

        const props = wrapper.findComponent({ name: 'WtgInput' }).props();
        expect(props.filled).toBe(true);
    });

    test('it sets the display value and internal value ref based on the value passed in as prop', () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                modelValue: '2024-01-09T01:00:00.000Z',
            },
        });

        expect(wrapper.vm.internalValue).toBe('2024-01-09T01:00:00.000Z');
        expect(wrapper.vm.nativeInputValue).toBe('09-Jan-24 12:00');
    });

    test('it formats the display value and internal value ref with a default formatter when a blur event is triggered', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                modelValue: '2024-01-09T01:00:00.000Z',
            },
        });

        const input = wrapper.find('input');
        input.setValue('09-01-24 12:00');
        await input.trigger('change');
        expect(wrapper.vm.internalValue).toBe('2024-01-09T01:00:00.000Z');
        expect(wrapper.vm.nativeInputValue).toBe('09-Jan-24 12:00');
    });

    test('it formats the display value and internal value ref correctly when useSecond props is true', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: {
                modelValue: '2024-01-09T01:00:00.000Z',
                useSeconds: true,
            },
        });

        const input = wrapper.find('input');
        input.setValue('09-01-24 12:00');
        await input.trigger('change');
        expect(wrapper.vm.internalValue).toBe('2024-01-09T01:00:00.000Z');
        expect(wrapper.vm.nativeInputValue).toBe('09-Jan-24 12:00:00');
    });

    test('it emits a change event with correct internal value to allow 2-way binding with data from parent', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: '2024-01-09T01:00:00.000Z',
            },
        });

        const input = wrapper.find('input');
        input.setValue('09-Jan-24 13:00');
        await input.trigger('change');
        expect(wrapper.emitted('update:modelValue')).toBeTruthy();
        expect(wrapper.emitted('update:modelValue')![0]).toStrictEqual(['2024-01-09T02:00:00.000Z', true]);
    });

    test.each(['inputValue', ' inputValue', 'inputValue ', ' inputValue '])(
        'it passes trimmed version of user input to parser %p',
        async (datetime) => {
            const mockParseDate = jest.fn();
            const wrapper = mountComponent({
                props: {
                    formatter: {
                        formatDate: jest.fn().mockReturnValue('a value'),
                        parseDate: mockParseDate,
                    },
                    modelValue: '2024-01-09T01:00:00.000Z',
                },
            });

            const input = wrapper.find('input');
            input.setValue(datetime);
            await input.trigger('change');
            expect(mockParseDate).toBeCalledWith('inputValue', false);
        }
    );

    test.each(['returnValue', ' returnValue', 'returnValue ', ' returnValue '])(
        'it trims emitted value %p',
        async (datetime) => {
            const mockParseDate = jest.fn().mockReturnValue(datetime);
            const wrapper = mountComponent({
                props: {
                    formatter: {
                        formatDate: jest.fn().mockReturnValue('a value'),
                        parseDate: mockParseDate,
                    },
                    modelValue: '2024-01-09T01:00:00.000Z',
                },
            });

            const input = wrapper.find('input');
            input.setValue(datetime);
            await input.trigger('change');
            expect(wrapper.emitted('update:modelValue')![0]).toStrictEqual(['returnValue', true]);
        }
    );

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('it ensures the embedded button has the correct role and is focusable', () => {
        const wrapper = mountComponent();

        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.attributes('role')).toBe('button');
        expect(prompter.attributes('tabindex')).toBe('-1');
    });

    test('it opens date time picker when user clicks calendar button', async () => {
        const wrapper = mountComponent();
        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        expect(new DOMWrapper(document.body).find('.wtg-date-time-picker-container').exists()).toBe(true);
    });

    test('it sets hideMessages to true when date time picker opens', async () => {
        const wrapper = mountComponent();
        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        expect(wrapper.findComponent({ name: 'WtgInput' }).props().hideMessages).toBe(true);
    });

    test('it show input field date time in date time picker', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: '2020-01-08T01:00:00.000Z',
            },
        });

        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        const documentWrapper = new DOMWrapper(document.body);
        expect(documentWrapper.find('.v-date-picker-month__day--selected').text()).toBe('8');
        expect(documentWrapper.find('.v-date-picker-controls__month-btn').text()).toBe('January 2020');

        const selectedTimeValueItems = documentWrapper.findAll('.wtg-time-picker__selected-values-item');
        expect(selectedTimeValueItems[0].text()).toBe('12');
        expect(selectedTimeValueItems[1].text()).toBe('00');
    });

    test('when WtgDateTimeField modelValue is a valid date, WtgDateTimePicker modelValue is computed correctly', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: '2020-01-08T01:00:00.000Z',
            },
        });

        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        const picker = wrapper.findComponent({ name: 'WtgDateTimePicker' });
        expect(picker.exists()).toBeTruthy();
        expect(picker.props('modelValue')).toBe('2020-01-08 12:00:00');
    });

    test('when WtgDateTimeField modelValue is an invalid date, WtgDateTimePicker modelValue is empty', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: '2020-01-08T25:00:00.000Z',
            },
        });

        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        const picker = wrapper.findComponent({ name: 'WtgDateTimePicker' });
        expect(picker.exists()).toBeTruthy();
        expect(picker.props('modelValue')).toBe('');
    });

    test('when WtgDateTimeField modelValue is empty, WtgDateTimePicker modelValue is empty', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: '',
            },
        });

        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        const picker = wrapper.findComponent({ name: 'WtgDateTimePicker' });
        expect(picker.exists()).toBeTruthy();
        expect(picker.props('modelValue')).toBe('');
    });

    test('it should select the date time in date time picker', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: '2020-01-08T01:00:00.000Z',
            },
        });

        const prompter = wrapper.find('.wtg-input--interactive-element');
        await prompter.trigger('click');

        const documentWrapper = new DOMWrapper(document.body);

        const day = documentWrapper.find('[data-v-date="2020-01-15"]');
        await day.find('button').trigger('click');

        const selectors = documentWrapper.findAll('.wtg-time-picker__selector');
        const hrsSelector = selectors.at(0);
        const minsSelector = selectors.at(1);

        await hrsSelector?.findAll('.wtg-time-picker__selector-value').at(4)?.trigger('click');
        await minsSelector?.findAll('.wtg-time-picker__selector-value').at(55)?.trigger('click');

        expect(wrapper.emitted()).toHaveProperty('update:modelValue', [
            ['2020-01-15T01:00:00.000Z', true],
            ['2020-01-14T17:00:00.000Z', true],
            ['2020-01-14T17:55:00.000Z', true],
        ]);
    });

    test('it sets the aria label on the prompter', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent({ name: 'WtgIcon' }).attributes('aria-label')).toBe('Select Date');
    });

    test('when disabled, it sets the disabled attribute on the input field', async () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });
        expect(wrapper.find('input').attributes('disabled')).toBeDefined();

        await wrapper.setProps({ disabled: false });
        expect(wrapper.find('input').attributes('disabled')).toBeUndefined();
    });

    test('it passes the aria* properties to the input to ensure fields with a hidden label can still meet accessibility requirements', async () => {
        const wrapper = mountComponent({
            props: {
                ariaLabel: 'Aria label',
                ariaLabelledby: 'Aria labelledby',
            },
        });
        expect(wrapper.find('input').attributes('aria-label')).toBe('Aria label');
        expect(wrapper.find('input').attributes('aria-labelledby')).toBe('Aria labelledby');
    });

    test('when readonly, it sets the readonly attribute on the input field', async () => {
        const wrapper = mountComponent({
            props: {
                readonly: true,
            },
        });
        expect(wrapper.find('input').attributes('readonly')).toBeDefined();

        await wrapper.setProps({ readonly: false });
        expect(wrapper.find('input').attributes('readonly')).toBeUndefined();
    });

    describe('when given a formatter', () => {
        let wrapper: VueWrapper;
        const dateTime = new Date('2021-01-05 12:01:00').toISOString();

        beforeEach(() => {
            const formatter: WtgDateTimeFormatter = {
                formatDate(dateTime: string): string {
                    return new Date(dateTime).toDateString();
                },
                parseDate(dateTime: string): string {
                    return new Date(dateTime).toISOString();
                },
                values(dateTime: string) {
                    const date = new Date(dateTime);
                    return {
                        year: date.getFullYear(),
                        month: date.getMonth() + 1,
                        day: date.getDay(),
                        hour: date.getHours(),
                        minutes: date.getMinutes(),
                        seconds: date.getSeconds(),
                        isValid: true,
                    };
                },
                today(showTimeZone?: boolean): string {
                    return showTimeZone ? '2021-01-05T12:01:00+03:00' : '2021-01-05T12:01:00';
                },
            };
            wrapper = mountComponent({
                props: {
                    modelValue: dateTime,
                    formatter,
                },
            });
        });

        test('it uses the formatter to create the display value', () => {
            const input = wrapper.find('input');
            expect(input.element.value).toBe(new Date('2021-01-05 12:01:00').toDateString());
        });

        test('when the input changes and the field loses focus, it uses the formatter to parse the display value back into an ISO date string', async () => {
            const input = wrapper.find('input');
            input.setValue('2020/12/31 12:20:10');
            await input.trigger('change');

            const emitted = wrapper.emitted('update:modelValue');
            expect(emitted?.[0]).toEqual([new Date('2020/12/31 12:20:10').toISOString(), true]);
        });

        test('it sets the correct today', async () => {
            jest.useFakeTimers().setSystemTime(new Date('2025-01-21 12:00:00'));
            const wrapper = mountComponent({
                props: {
                    formatter: {
                        formatDate: () => 'formatted date',
                        parseDate: (date: string) => date,
                        values(dateTime: string) {
                            const date = new Date(dateTime);
                            return {
                                year: date.getFullYear(),
                                month: date.getMonth() + 1,
                                day: date.getDay(),
                                hour: date.getHours(),
                                minutes: date.getMinutes(),
                                seconds: date.getSeconds(),
                                isValid: true,
                            };
                        },
                        today(showTimeZone?: boolean): string {
                            return showTimeZone ? '2025-01-21T12:00+03:00' : '2025-01-21T12:00';
                        },
                    },
                },
            });

            const prompter = wrapper.find('.wtg-input--interactive-element');
            await prompter.trigger('click');
            await wrapper.vm.$nextTick();

            const documentWrapper = new DOMWrapper(document.body);
            const todayButton = documentWrapper.find('.wtg-date-time-picker-today');
            await todayButton.trigger('click');
            await wrapper.vm.$nextTick();

            expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
            expect(wrapper.emitted('update:modelValue')![0]).toEqual(['2025-01-21 12:00', true]);
        });

        test('it renders with custom date formatter when user input a invalid date', async () => {
            const invalidDate = 'Invalid date';
            const mockFormatDate = jest.fn().mockReturnValue('formatted date');
            const wrapper = mountComponent({
                props: {
                    formatter: {
                        formatDate: mockFormatDate,
                        parseDate: (input: string) => {
                            if (input === invalidDate) {
                                return null;
                            } else {
                                return 'parsed date';
                            }
                        },
                    },
                },
            });

            mockFormatDate.mockClear();
            const input = wrapper.find('input');
            input.setValue(invalidDate);
            await wrapper.vm.$nextTick();

            expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
            expect(wrapper.emitted('update:modelValue')![0]).toEqual([invalidDate, false]);
            expect(mockFormatDate).not.toHaveBeenCalled();
        });
    });

    describe('timezone functionality', () => {
        test('it excludes timezone in picker value when showTimeZone is false', async () => {
            const wrapper = mountComponent({
                props: {
                    modelValue: '2025-03-20T11:30:00+03:00',
                    showTimeZone: false,
                },
            });

            const prompter = wrapper.find('.wtg-input--interactive-element');
            await prompter.trigger('click');

            const picker = wrapper.findComponent({ name: 'WtgDateTimePicker' });
            expect(picker.exists()).toBeTruthy();
            expect(picker.props('modelValue')).toBe('2025-03-20 19:30:00');
        });

        test('it includes timezone in picker value when showTimeZone is false', async () => {
            const wrapper = mountComponent({
                props: {
                    modelValue: '2025-03-20T11:30:00+03:00',
                    showTimeZone: true,
                },
            });

            const prompter = wrapper.find('.wtg-input--interactive-element');
            await prompter.trigger('click');

            const picker = wrapper.findComponent({ name: 'WtgDateTimePicker' });
            expect(picker.exists()).toBeTruthy();
            expect(picker.props('modelValue')).toBe('2025-03-20 11:30:00 +03:00');
        });

        test('it passes showTimeZone to formatter functions', async () => {
            const mockFormatDate = jest.fn().mockReturnValue('formatted date');
            const mockParseDate = jest.fn().mockReturnValue('parsed date');
            const mockValues = jest.fn().mockReturnValue({
                year: 2025,
                month: 3,
                day: 20,
                hour: 11,
                minutes: 30,
                seconds: 0,
                isValid: true,
                timeZone: '+03:00',
            });

            const wrapper = mountComponent({
                props: {
                    modelValue: '2025-03-20T11:30:00+03:00',
                    showTimeZone: true,
                    formatter: {
                        formatDate: mockFormatDate,
                        parseDate: mockParseDate,
                        values: mockValues,
                    },
                },
            });

            const prompter = wrapper.find('.wtg-input--interactive-element');
            await prompter.trigger('click');

            expect(mockFormatDate).toHaveBeenCalledWith('2025-03-20T11:30:00+03:00', true);
            expect(mockValues).toHaveBeenCalledWith('2025-03-20T11:30:00+03:00', true);

            const input = wrapper.find('input');
            input.setValue('2025-03-20 11:30:00 +03:00');
            await input.trigger('input');
            await input.trigger('blur');
            await wrapper.vm.$nextTick();
            expect(mockParseDate).toHaveBeenCalledWith('2025-03-20 11:30:00 +03:00', true);
        });
    });

    test('it has a (deprecated) inputId property that gets applied if no id is specified to aid the GLOW VUE 3 migration', async () => {
        const wrapper = mountComponent();
        await wrapper.setProps({ inputId: 'id1' });
        expect(wrapper.find('input').attributes('id')).toBe('id1');

        await wrapper.setProps({ id: 'id2' });
        expect(wrapper.find('input').attributes('id')).toBe('id2');
    });

    test('when native, it renders the date field with the native datetime picker', async () => {
        const wrapper = mountComponent({
            props: {
                native: true,
            },
        });
        expect(wrapper.find('input').attributes('type')).toBe('datetime-local');
        await wrapper.setProps({ native: false });
        expect(wrapper.find('input').attributes('type')).toBe('text');
    });

    test('when native and readonly, the picker is not displayed on interaction', async () => {
        const showPickerSpy = jest.fn();
        HTMLInputElement.prototype.showPicker = showPickerSpy;

        const wrapper = mountComponent({
            props: {
                native: true,
                readonly: true,
            },
        });

        const input = wrapper.find('input');
        await input.trigger('click');
        expect(showPickerSpy).not.toHaveBeenCalled();
    });

    test('it ensures consistent modelValue values when used as native:true or false', async () => {
        const wrapper = mountComponent();
        wtgUi.language.current = 'en';

        expect(wrapper.find('div[class="wtg-input__content"]').exists()).toBe(true);

        const input = wrapper.find('input');
        expect(input.exists()).toBe(true);

        await input.setValue('17-Jan-20 20:05');
        await input.trigger('change');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')).toBeTruthy();
        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')![0][0]).toEqual('2020-01-17T09:05:00.000Z');
        expect(input.element.value).toBe('17-Jan-20 20:05');

        await input.setValue('');
        await input.trigger('change');
        await wrapper.vm.$nextTick();

        await wrapper.setProps({ native: true });
        await wrapper.vm.$nextTick();

        expect(input.attributes('type')).toBe('datetime-local');

        await input.setValue('2020-01-19T20:05');
        await input.trigger('change');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('update:modelValue')?.length).toBe(3);
        expect(wrapper.emitted('update:modelValue')![0][0]).toEqual('2020-01-17T09:05:00.000Z');
        expect(input.element.value).toBe('2020-01-19T20:05');

        await wrapper.setProps({ native: false });
        await wrapper.vm.$nextTick();

        expect(input.attributes('type')).toBe('text');

        expect(input.element.value).toBe('2020-01-19T20:05');
    });

    test('it emits update:menu event when open and close date time picker', async () => {
        const wrapper = mountComponent();
        const prompter = wrapper.find('.wtg-input--interactive-element');
        expect(prompter.exists()).toBe(true);
        await prompter.trigger('click');

        expect(wrapper.emitted('update:menu')?.length).toBe(1);
        expect(wrapper.emitted('update:menu')![0][0]).toEqual(true);

        await prompter.trigger('click');

        expect(wrapper.emitted('update:menu')?.length).toBe(2);
        expect(wrapper.emitted('update:menu')![1][0]).toEqual(false);
    });

    test('it opens the prompter dialog when F4 is pressed and promptable', async () => {
        const wrapper = mountComponent();
        const popover = wrapper.findComponent({ name: 'WtgPopover' });
        expect(popover.props('modelValue')).toBe(false);
        const input = wrapper.find('input');
        input.trigger('click');
        await input.trigger('keydown.F4');
        expect(popover.props('modelValue')).toBe(true);
    });

    describe('when framework is mobile', () => {
        let wrapper: VueWrapper;

        beforeEach(() => {
            const wtgUi = new WtgUi({ framework: 'mobile' });
            wrapper = mountComponent({}, wtgUi);
        });

        test('when mobile, it defaults to the native time picker', async () => {
            expect(wrapper.find('input').attributes('type')).toBe('datetime-local');
        });
    });

    function mountComponent({ props = {}, slots = {}, provide = {} } = {}, localWtgUi?: WtgUi) {
        return mount(WtgDateTimeField, {
            props,
            slots,
            global: {
                plugins: [localWtgUi ?? wtgUi],
                provide,
            },
        });
    }
});
