import { WtgApp } from '@components/WtgApp';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { VAppBar } from 'vuetify/components/VAppBar';
import { WtgAppBar } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgAppBar', () => {
    test('it renders a VAppBar component', async () => {
        const wrapper = await mountComponentAsync();
        const appBar = wrapper.findComponent(VAppBar);
        expect(appBar.exists()).toBe(true);
    });

    test('passes the properties to VAppBar', async () => {
        const wrapper = await mountComponentAsync({
            props: {
                extensionHeight: 104,
                flat: true,
                height: 52,
                modelValue: true,
                order: 1,
                scrollBehavior: 'elevate',
            },
        });
        const appBarProps = wrapper.findComponent({ name: 'VAppBar' }).props();
        expect(appBarProps.extensionHeight).toBe(104);
        expect(appBarProps.flat).toBe(true);
        expect(appBarProps.height).toBe(52);
        expect(appBarProps.modelValue).toBe(true);
        expect(appBarProps.order).toBe(1);
        expect(appBarProps.scrollBehavior).toBe('elevate');
    });

    test('it applies the wtg-app-bar classes', async () => {
        const wrapper = await mountComponentAsync();
        const appBar = wrapper.findComponent(WtgAppBar);
        expect(appBar.classes()).toContain('wtg-app-bar');
    });

    test('it passes the default slot content to the VAppBar component', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                default: () => '<div>Some Text</div>',
            },
        });
        expect(wrapper.html()).toContain('Some Text');
    });

    test('it passes the append slot content to the VNavigationDrawer component', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                append: () => '<div>Append Text</div>',
            },
        });
        expect(wrapper.html()).toContain('Append Text');
    });

    test('it passes the prepend slot content to the VNavigationDrawer component', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                prepend: () => '<div>Prepend Text</div>',
            },
        });
        expect(wrapper.html()).toContain('Prepend Text');
    });

    async function mountComponentAsync({ props = {}, slots = {} } = {}) {
        const wrapper = mount(
            {
                template:
                    '<wtg-app><wtg-app-bar v-bind="$attrs"><template v-for="(_, name) in $slots" v-slot:[name]="slotData"><slot :name="name" v-bind="slotData" /></template></wtg-app-bar></wtg-app>',
                components: { WtgApp, WtgAppBar },
            },
            {
                propsData: {
                    ...props,
                },
                slots,
                global: {
                    plugins: [wtgUi],
                },
            }
        );
        await flushPromises();
        return wrapper;
    }
});
