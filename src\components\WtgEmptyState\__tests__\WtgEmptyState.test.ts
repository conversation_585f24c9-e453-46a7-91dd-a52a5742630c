import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgEmptyState from '..';
import WtgUi from '../../../WtgUi';
enableAutoUnmount(afterEach);
const wtgUi = new WtgUi();

describe('WtgEmptyState', () => {
    test('renders the WtgEmptyState component', () => {
        const wrapper = mountComponent({});
        expect(wrapper.element.tagName).toBe('DIV');
        expect(wrapper.classes('wtg-empty-state')).toBe(true);
    });
    test('it renders the default variant when no variant is provided', () => {
        const wrapper = mountComponent({});
        expect(wrapper.classes('wtg-empty-state_default')).toBe(true);
    });
    test('it renders the condensed variant when condensed variant is provided', () => {
        const wrapper = mountComponent({
            propsData: { variant: 'condensed', header: 'Empty State', bodycopy: 'Body Copy' },
        });
        expect(wrapper.classes('wtg-empty-state_condensed')).toBe(true);
    });
    test('if there is no microcopy provided it should not be rendered', () => {
        const wrapper = mountComponent({
            propsData: {
                header: 'Empty State',
                bodycopy: 'Body Copy',
            },
        });
        expect(wrapper.find('.wtg-empty-state__microcopy').exists()).toBe(false);
    });
    test('should render provided text props', () => {
        const customHeader = 'Custom Header';
        const customBodycopy = 'Custom bodycopy';
        const customMicrocopy = 'Custom microcopy';
        const wrapper = mountComponent({
            propsData: {
                header: customHeader,
                bodycopy: customBodycopy,
                microcopy: customMicrocopy,
            },
        });
        expect(wrapper.find('.wtg-empty-state__header').text()).toBe(customHeader);
        expect(wrapper.find('.wtg-empty-state__bodycopy').text()).toBe(customBodycopy);
        expect(wrapper.find('.wtg-empty-state__microcopy').text()).toBe(customMicrocopy);
    });

    test('should display button from slot when provided', () => {
        const testButton = '<button>Click me</button>';
        const wrapper = mountComponent({
            slots: {
                default: testButton,
            },
            propsData: { header: 'Empty State', bodycopy: 'Body Copy' },
        });
        const buttonElement = wrapper.find('button');
        expect(buttonElement.exists()).toBe(true);
        expect(buttonElement.text()).toBe('Click me');
    });

    describe('Header typography', () => {
        test('should render medium title in condensed mode', () => {
            const wrapper = mountComponent({
                propsData: { variant: 'condensed' },
            });

            const header = wrapper.findComponent({ name: 'WtgLabel' });
            expect(header.props('typography')).toBe('title-md-default');
        });

        test('should render medium title if type is not specified', () => {
            const wrapper = mountComponent({
                propsData: { type: '' },
            });

            const header = wrapper.findComponent({ name: 'WtgLabel' });
            expect(header.props('typography')).toBe('title-md-default');
        });

        test('should render large title when type is specified', () => {
            const wrapper = mountComponent({
                propsData: { type: 'noData' },
            });

            const header = wrapper.findComponent({ name: 'WtgLabel' });
            expect(header.props('typography')).toBe('title-lg-default');
        });
    });
});
describe('WtgEmptyState Image', () => {
    test.each<{ type: string; variant: string; expectedAlt: string }>([
        { type: 'firstUse', variant: 'condensed', expectedAlt: 'Empty state first use' },
        { type: 'firstUse', variant: 'default', expectedAlt: 'Empty state first use' },
        { type: 'userCleared', variant: 'condensed', expectedAlt: 'Empty state user cleared' },
        { type: 'userCleared', variant: 'default', expectedAlt: 'Empty state user cleared' },
        { type: 'missingPage', variant: 'condensed', expectedAlt: 'Empty state missing page' },
        { type: 'missingPage', variant: 'default', expectedAlt: 'Empty state missing page' },
        { type: 'error', variant: 'condensed', expectedAlt: 'Empty state error' },
        { type: 'error', variant: 'default', expectedAlt: 'Empty state error' },
        { type: 'restricted', variant: 'condensed', expectedAlt: 'Empty state restricted' },
        { type: 'restricted', variant: 'default', expectedAlt: 'Empty state restricted' },
        { type: 'noData', variant: 'condensed', expectedAlt: 'Empty state no data' },
        { type: 'noData', variant: 'default', expectedAlt: 'Empty state no data' },
    ])(
        'should render correct image to the component for different type options that passed from props',
        ({ type, variant, expectedAlt }) => {
            const wrapper = mountComponent({
                propsData: {
                    type,
                    variant,
                    header: 'Empty State',
                    bodycopy: 'Body Copy',
                },
            });
            const image = wrapper.findComponent({ name: 'WtgImage' });
            expect(image.props('alt')).toContain(expectedAlt);
        }
    );
});
function mountComponent({ propsData = {}, slots = {} } = {}) {
    const defaultProps = {
        header: 'Empty State',
        bodycopy: 'Body Copy',
    };

    return mount(WtgEmptyState, {
        propsData: { ...defaultProps, ...propsData },
        slots,
        global: {
            plugins: [wtgUi],
        },
    });
}
