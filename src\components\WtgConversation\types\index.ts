import { SearchFieldItem } from '@components/WtgSearchField/types';

export interface WtgImageWithFallback {
    image: string;
    fallbackImage: string;
}

export interface DataProviderConfiguration {
    valueField: string;
    textField: string;
}

export interface FollowerUserType {
    type: number;
    caption: string;
}

export interface Conversation {
    id: string;
    name: string;
    type: string;
    followerUserTypes: FollowerUserType[];
    messages: Record<string, ConversationMessage[]>;
    provider: ConversationProvider;
    followers: ConversationFollower[];
    following: boolean;
    loadingMessages: boolean;
}

export interface ConversationFollower {
    id: string;
    name: string;
    icon: string;
    photo?: WtgImageWithFallback;
}

export enum ConversationMessageType {
    user = 'user',
    staff = 'staff',
    system = 'system',
}

export interface ConversationMessage {
    id: string;
    body: string;
    isFromSelf: boolean;
    sender: string;
    time: string;
    score: number;
    loadingDislikeScore: boolean;
    loadingLikeScore: boolean;
    type: ConversationMessageType;
}

export interface PotentialFollowerItem {
    label: string;
    chips?: Array<{
        label: string;
        color: string;
        textColor?: string;
    }>;
    secondaryText?: string;
    actionText?: string;
    value: string;
    text: string;
}

export interface PotentialFollowersResponseType {
    items: SearchFieldItem[];
    total: number;
}

export type AddFollowersErrorResponse = Record<number, string[]>;

export interface ConversationProvider {
    likeMessageAsync(message: ConversationMessage): Promise<number>;
    dislikeMessageAsync(message: ConversationMessage): Promise<number>;
    sendMessageAsync(conversation: Conversation, body: string): Promise<void>;
    followConversationAsync(conversation: Conversation): Promise<void>;
    unfollowConversationAsync(conversation: Conversation): Promise<void>;
    sendUnsentMessageAsync(conversation: Conversation, body: string): Promise<void>;
    removeFollowerFromConversationAsync(follower: ConversationFollower, conversation: Conversation): Promise<void>;
    getPotentialFollowersAsync(
        conversation: Conversation,
        userType: number,
        searchText: string,
        skip: number
    ): Promise<PotentialFollowersResponseType>;
    addFollowersAsync(
        conversation: Conversation,
        requestData: Record<number, string[]>
    ): Promise<AddFollowersErrorResponse>;
}

export function defaultConversation(): Conversation {
    return {
        id: '',
        name: '',
        type: '',
        messages: {},
        followers: [],
        following: false,
        loadingMessages: false,
        followerUserTypes: [],
        provider: {
            likeMessageAsync(message: ConversationMessage): Promise<number> {
                const score = message.score + 1;
                return Promise.resolve(score);
            },
            dislikeMessageAsync(message: ConversationMessage): Promise<number> {
                const score = message.score - 1;
                return Promise.resolve(score);
            },
            sendMessageAsync(conversation: Conversation, body: string): Promise<void> {
                const date = new Date();
                conversation.messages[date.toLocaleDateString()] = [
                    {
                        id: '',
                        body,
                        type: ConversationMessageType.user,
                        isFromSelf: true,
                        sender: '',
                        time: date.toLocaleString('default', {
                            hour: 'numeric',
                            minute: 'numeric',
                        }),
                        score: 0,
                        loadingDislikeScore: false,
                        loadingLikeScore: false,
                    },
                ];
                return Promise.resolve();
            },
            followConversationAsync(conversation: Conversation): Promise<void> {
                conversation.following = true;
                return Promise.resolve();
            },
            unfollowConversationAsync(conversation: Conversation): Promise<void> {
                conversation.following = false;
                return Promise.resolve();
            },
            sendUnsentMessageAsync(): Promise<void> {
                return Promise.resolve();
            },
            removeFollowerFromConversationAsync(): Promise<void> {
                return Promise.resolve();
            },
            getPotentialFollowersAsync(): Promise<PotentialFollowersResponseType> {
                return Promise.resolve({
                    items: [],
                    total: 0,
                });
            },
            addFollowersAsync(): Promise<AddFollowersErrorResponse> {
                return Promise.resolve({});
            },
        },
    };
}
