import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { AlertLevel } from '@composables/notifications';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { defineComponent } from 'vue';
import { WtgSimpleCheckbox } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgSimpleCheckbox', () => {
    test('it sets the checkbox label to the label attribute passed in ', () => {
        const wrapper = mountComponent({
            props: {
                label: 'My Checkbox',
            },
        });
        const checkboxlabel = wrapper.find('label');
        expect(checkboxlabel.html()).toContain('My Checkbox');
    });

    test('it shows the s-icon-checkbox-off* icons when the checkbox is un-checked', async () => {
        const wrapper = mountComponent();

        const icon = wrapper.find('i');
        expect(icon.classes()).toContain('s-icon-checkbox-off');
        await wrapper.setProps({ readonly: true });
        expect(icon.classes()).toContain('s-icon-checkbox-off-readonly');
        await wrapper.setProps({ readonly: false, disabled: true });
        expect(icon.classes()).toContain('s-icon-checkbox-off-disabled');
    });

    test('it shows the s-icon-checkbox-on* icons when the checkbox is checked', async () => {
        const wrapper = mountComponent({
            props: {
                modelValue: true,
            },
        });

        const icon = wrapper.find('i');
        expect(icon.classes()).toContain('s-icon-checkbox-on');
        await wrapper.setProps({ readonly: true });
        expect(icon.classes()).toContain('s-icon-checkbox-on-readonly');
        await wrapper.setProps({ readonly: false, disabled: true });
        expect(icon.classes()).toContain('s-icon-checkbox-on-disabled');
    });

    test('it renders the messages list passed in the messages property', () => {
        const wrapper = mountComponent({
            props: {
                messages: ['this is a test message'],
            },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<ol><li>this is a test message</li></ol>');
    });

    test('it renders the message passed into the messages property when it is a string', () => {
        const wrapper = mountComponent({
            props: {
                messages: 'this is a test message',
            },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<ol><li>this is a test message</li></ol>');
    });

    test('it renders the validation state', () => {
        const wrapper = mountComponent({
            props: {
                validationState: {
                    alertLevel: AlertLevel.Warning,
                    messages: ['this is a validation message'],
                },
            },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        expect(checkbox.classes()).toContain('wtg-checkbox--warning');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<ol><li>this is a validation message</li></ol>');
    });

    test('it merges validationState and messages if both were to be used simultaniously', () => {
        const wrapper = mountComponent({
            props: {
                messages: 'this is a test message',
                validationState: {
                    alertLevel: AlertLevel.Warning,
                    messages: ['this is a validation message'],
                },
            },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        expect(checkbox.classes()).toContain('wtg-checkbox--warning');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        const vm = wrapper.vm as any;
        expect(vm.tooltipDirective.shown).toBe(true);
        expect(vm.tooltipDirective.content).toContain('<li>this is a test message</li>');
        expect(vm.tooltipDirective.content).toContain('<li>this is a validation message</li>');
    });

    test('it should emit the update:model-value event when the checkbox is clicked', async () => {
        const wrapper = mountComponent();
        const input = wrapper.find('input');
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
        await input.trigger('click');
        expect(wrapper.emitted('update:modelValue')!.length).toBe(1);
        expect(![0][0]).toBe(true);
    });

    test('it should not emit the update:model-value when disabled', async () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
            },
        });
        const input = wrapper.find('input');
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
        await input.trigger('click');
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
    });

    test('it should not emit the update:model-value when readonly', async () => {
        const wrapper = mountComponent({
            props: {
                readonly: true,
            },
        });
        const input = wrapper.find('input');
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
        await input.trigger('click');
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();
    });

    test('it implements v-model through the value property and the change event', async () => {
        const component = defineComponent({
            components: { WtgSimpleCheckbox },
            data: () => {
                return {
                    checked: true,
                };
            },
            template: '<wtg-simple-checkbox v-model="checked" label="my check" />',
        });
        const wrapper = mount(component, {
            wtgUi,
            global: {
                plugins: [wtgUi],
            },
        });
        const checkbox = wrapper.findComponent(WtgSimpleCheckbox);
        expect(checkbox.props('modelValue')).toBe(true);

        const input = checkbox.find('input');
        await input.trigger('click');
        expect(wrapper.vm.$data.checked).toBe(false);
        expect(checkbox.props('modelValue')).toBe(false);
    });

    test('it has tooltip capability mixed in', () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: { tooltip: { content: 'Some tooltip', placement: 'top' } },
        });
        const checkbox = wrapper.find('.wtg-checkbox');
        checkbox.find('.wtg-checkbox__selection-control').trigger('mouseenter');
        expect(wrapper.vm.tooltipDirective.content).toBe('Some tooltip');
        expect(wrapper.vm.tooltipDirective.placement).toBe('top');
        expect(wrapper.vm.tooltipDirective.shown).toBe(true);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    test('it renders the restricted icon instead of checkbox when the display is restricted', () => {
        const wrapper = mountComponent({
            props: {
                restricted: true,
            },
        });
        expect(wrapper.find('i').html()).toContain(
            '<i aria-hidden="true" class="wtg-icon s-icon-hide wtg-checkbox__icon">'
        );
    });

    test("it always renders the image tag as the next element after the input tag because that's what the CSS is targeting to make focus state work", async () => {
        const wrapper = mountComponent();
        expect((wrapper.find('input').element!.nextSibling as HTMLElement)!.tagName).toBe('I');

        await wrapper.setProps({ modelValue: true });
        expect((wrapper.find('input').element!.nextSibling as HTMLElement)!.tagName).toBe('I');

        await wrapper.setProps({ modelValue: false });
        expect((wrapper.find('input').element!.nextSibling as HTMLElement)!.tagName).toBe('I');
    });

    test('it has (deprecated) VALUE property and INPUT event that allows it to be backwards compatible with the V-MODEL handling of the VUE 2 implementation', async () => {
        const onChange = jest.fn();
        const wrapper = mountComponent({
            props: {
                inputValue: true,
            },
            attrs: {
                onChange,
            },
        });

        const icon = wrapper.find('i');
        expect(icon.classes()).toContain('s-icon-checkbox-on');

        const input = wrapper.find('input');
        await input.trigger('click');
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenCalledWith(false);
    });

    test('it does not render the label when no label content is available', () => {
        const wrapper = mountComponent();

        const label = wrapper.find('label');
        expect(label.exists()).toBe(false);
    });

    test('it does not change the checkbox checked state on mouse click', async () => {
        const checkbox = mountComponent({ props: { modelValue: false } });
        expect(checkbox.props('modelValue')).toBe(false);

        const input = checkbox.find('input');
        await input.trigger('click');
        expect(input.element.checked).toBe(false);
        expect(checkbox.props('modelValue')).toBe(false);
    });

    function mountComponent({ attrs = {}, props = {}, provide = {} } = {}) {
        return mount(WtgSimpleCheckbox, {
            attrs,
            props,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
