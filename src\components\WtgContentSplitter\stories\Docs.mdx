import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import contentSplitterAlignment from '../../../assets/WtgContentSplitter/content-splitter-alignment.png';
import contentSplitterAnatomy from '../../../assets/WtgContentSplitter/content-splitter-anatomy.png';
import * as WtgContentSplitter from './WtgContentSplitter.stories.ts';

<Meta of={WtgContentSplitter} />

<div className="component-header">
    <h1>Content splitter</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img>[Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components-%5BSUPPLY%5D?node-id=79-4404&t=UwMYyqvaNhokPqyF-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    The content splitter is a non-interactive component that separates content while including a text label for
    contextual information.
</p>

## API

<Canvas className="canvas-preview" of={WtgContentSplitter.Default} />
<Controls of={WtgContentSplitter.Default} sort={'alpha'} />

## Anatomy

<div className="d-flex flex-column align-center">
    <img srcSet={`${contentSplitterAnatomy} 3x`} alt="anatomy of content splitter component" />
</div>

<ol className="anatomy-list">
    <li>
        <strong>Leading divider:</strong> A non-interactive line that visually separates content before the label.
    </li>
    <li>
        <strong>Label:</strong> A text element that provides contextual information about the content split.
    </li>
    <li>
         <strong>Trialing divider:</strong> A continuation of the divider after the label, maintaining visual balance.
    </li>

</ol>

## How to use

### ✅ Do

-   Use alignment of text consistently across panels, screens and flows.
-   Use the content splitter sparingly across designs to split large groups of content into smaller groups.

### ❌ Don't

-   Mix up the flow of content by utilizing different label alignments (start, center, end) within the same panels or screens.
-   Stack or overuse the content splitter pattern where better components could be utilized.

## Behavior

### Alignment

The text label can be positioned in three ways to provide flexibility in structuring content while enhancing visual hierarchy:

-   Floated left with the divider extending to the right.
-   Centered with dividers on both sides.
-   Floated right with the divider extending to the left.

<img srcSet={`${contentSplitterAlignment} 3x`} alt="anatomy of content splitter component" />

### Solid vs dashed

Content splitter supports both dashed and solid variants to control visual emphasis. Like the [divider](/docs/utilities-divider--docs), there is no universal rule for choosing between dashed and solid dividers. Dashed dividers are typically used to create a softer separation within a section, whereas solid dividers suggest a more definitive boundary.

### Orientation and layout

-   The `orientation` prop obeys [writing-mode](https://developer.mozilla.org/en-US/docs/Web/CSS/writing-mode), like flex and grid layouts.
-   This component's sizing will behave like a normal block or inline element, so you may have to give it size depending on how you use it.

## Content guidelines

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview).

-   Keep label text concise and direct, always have more divider than text.

## Accessibility

Although it looks similar to [Divider](/docs/utilities-divider--docs), the connector is purely aesthetic,
particularly for accessibility purposes. Since connector's role is `presentational`, only the provided content will be be shown to accessibility tools.

## Related components

-   [Divider](/docs/utilities-divider--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
