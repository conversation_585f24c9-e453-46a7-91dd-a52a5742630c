<template>
    <div :class="pagePreviewMode ? 'page-preview' : ''">
        <DocumentViewerPdfPage
            v-for="(page, index) in pages"
            :key="index"
            :page-number="page"
            :pdf-document="pdfDocument"
            :pdf-scale="internalScaleConfig.scaleValue"
            :pdf-rotation="rotationDegree"
            :page-preview-mode="pagePreviewMode"
            :active-page="activePage"
            @active-page-updated="handleActivePageUpdate"
        >
            <template #default="{ pdfPageData }">
                <slot :name="`pdf-page-${page}`" :pdf-page-data="pdfPageData"></slot>
            </template>
        </DocumentViewerPdfPage>
    </div>
</template>

<script setup lang="ts">
import { PDFDocumentProxy } from 'pdfjs-dist';
import { onMounted, PropType, ref, toRaw, watch } from 'vue';
import { Scale, ScalingMode } from '../../types';
import DocumentViewerPdfPage from './DocumentViewerPdfPage.vue';

const props = defineProps({
    activePageNumber: { type: Number, default: 0 },
    docViewerWrapHeight: { type: Number, default: 0 },
    docViewerWrapWidth: { type: Number, default: 0 },
    pagePreviewMode: { type: Boolean, default: false },
    pdfSource: { type: ArrayBuffer, default: [] },
    rotationDegree: { type: Number, default: 0 },
    scaleConfig: { type: Object as PropType<Scale>, default: undefined },
    zoomPercentage: { type: String, default: '100%' },
});

const emit = defineEmits<{
    'page-count-updated': [number];
    'zoom-percentage-updated': [string];
    'update-scale': [Scale];
    'active-page-updated': [number];
}>();

const activePage = ref(1);
const pdfDocument = ref<PDFDocumentProxy>();
const pageCount = ref(1);
const pages = ref<number[]>([]);
const internalScaleConfig = ref<Scale>({
    scaleMode: ScalingMode.FitToScreen,
    scaleValue: 0,
    resetScale: false,
});
const internalZoomPercentage = ref(props.zoomPercentage);

watch(
    () => props.zoomPercentage,
    (newZoomPercentage: string) => {
        if (!props.pagePreviewMode) {
            if (internalZoomPercentage.value !== newZoomPercentage) {
                if (
                    parseInt(internalZoomPercentage.value.replace('%', ''), 10) <
                    parseInt(newZoomPercentage.replace('%', ''), 10)
                ) {
                    internalScaleConfig.value.scaleValue += 0.2;
                } else {
                    internalScaleConfig.value.scaleValue = Math.max(0.2, internalScaleConfig.value.scaleValue - 0.2);
                }
                emit('update-scale', internalScaleConfig.value);
                internalZoomPercentage.value = newZoomPercentage;
            }
        }
    }
);

watch(
    () => props.activePageNumber,
    (newActivePageNumber: number) => {
        activePage.value = newActivePageNumber;
        if (!props.pagePreviewMode) {
            const targetPage = document.querySelectorAll(
                `[data-preview-mode='false'][data-page='${newActivePageNumber}']`
            )[0];
            if (targetPage) targetPage.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
);

watch(
    () => props.scaleConfig,
    async (newScaleConfig: Scale | undefined) => {
        if (newScaleConfig) {
            if (props.pagePreviewMode) return;

            if (newScaleConfig.scaleMode !== internalScaleConfig.value.scaleMode || newScaleConfig.resetScale) {
                await updateScalingMode(newScaleConfig.scaleMode);
                updateInternalZoomPercentage();
            }
        }
    },
    { deep: true }
);

const handleActivePageUpdate = (newActivePageNumber: number) => {
    activePage.value = newActivePageNumber;
    emit('active-page-updated', newActivePageNumber);
};

const getFirstPageViewPort = async () => {
    if (pdfDocument.value) {
        const rawPdfDoc = toRaw(pdfDocument.value);
        const firstPage = await rawPdfDoc.getPage(1);
        return firstPage.getViewport({ scale: 1 });
    }
    return null;
};

const fitToScreen = async () => {
    const viewport = await getFirstPageViewPort();
    if (viewport) {
        const containerWidth = props.docViewerWrapWidth;
        const containerHeight = props.docViewerWrapHeight;
        const pageWidth = viewport.width;
        const pageHeight = viewport.height;

        const scaleX = containerWidth / pageWidth;
        const scaleY = containerHeight / pageHeight;

        internalScaleConfig.value.scaleValue = Math.max(0.1, Math.min(scaleX, scaleY));
    }
};

const fitHorizontally = async () => {
    const viewport = await getFirstPageViewPort();
    if (viewport) {
        const scaleOffset = 50; // panel padding + scrollbar
        const containerDivWidth = props.docViewerWrapWidth - scaleOffset;

        internalScaleConfig.value.scaleValue = containerDivWidth / viewport.width;
    }
};

const fitVertically = async () => {
    const viewport = await getFirstPageViewPort();
    if (viewport) {
        const scaleOffset = 125; // control headers
        const containerDivHeight = props.docViewerWrapHeight - scaleOffset;

        internalScaleConfig.value.scaleValue = containerDivHeight / viewport.height;
    }
};

const updateScalingMode = async (newScalingMode: ScalingMode) => {
    switch (newScalingMode) {
        case ScalingMode.FitHorizontally:
            await fitHorizontally();
            break;
        case ScalingMode.FitVertically:
            await fitVertically();
            break;
        case ScalingMode.ActualSize:
            internalScaleConfig.value.scaleValue = 1;
            break;
        case ScalingMode.FitToScreen:
            await fitToScreen();
            break;
    }
    internalScaleConfig.value.scaleMode = newScalingMode;
    emit('update-scale', internalScaleConfig.value);
};

const loadPdf = async () => {
    const pdfjsLib = await loadPdfJsAsync();
    const copyOfPdfSource = props.pdfSource.slice(0);
    const loadingTask = pdfjsLib.getDocument({ data: copyOfPdfSource });
    pdfDocument.value = (await loadingTask.promise) as PDFDocumentProxy;
    pageCount.value = pdfDocument.value!.numPages;

    emit('page-count-updated', pageCount.value);

    if (props.pagePreviewMode) {
        await fitHorizontally();
    } else if (props.scaleConfig && props.scaleConfig.scaleValue !== 0) {
        //not the initial load
        internalScaleConfig.value.scaleMode = props.scaleConfig!.scaleMode;
        internalScaleConfig.value.scaleValue = props.scaleConfig!.scaleValue;
        internalZoomPercentage.value = `${Math.round(internalScaleConfig.value.scaleValue * 100)}%`;
    } else {
        await updateScalingMode(props.scaleConfig?.scaleMode || ScalingMode.FitToScreen);
        updateInternalZoomPercentage();
    }
    updatePages();
};

const updatePages = () => {
    pages.value = [];

    for (let pageNum = 1; pageNum <= pageCount.value; pageNum++) {
        pages.value.push(pageNum);
    }
};

const updateInternalZoomPercentage = () => {
    internalZoomPercentage.value = `${Math.round(internalScaleConfig.value.scaleValue * 100)}%`;
    emit('zoom-percentage-updated', internalZoomPercentage.value);
};

onMounted(() => {
    loadPdf();
});
</script>

<script lang="ts">
type PdfJsLib = typeof import('pdfjs-dist');
let pdfjsLib: PdfJsLib;

async function loadPdfJsAsync(): Promise<PdfJsLib> {
    if (pdfjsLib) return pdfjsLib;

    // @ts-ignore
    pdfjsLib = await import('pdfjs-dist/webpack.mjs');
    return pdfjsLib;
}
</script>
