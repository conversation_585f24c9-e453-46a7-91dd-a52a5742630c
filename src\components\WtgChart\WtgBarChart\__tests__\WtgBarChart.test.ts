import WtgBarChart from '@components/WtgChart/WtgBarChart/WtgBarChart';
import WtgChart from '@components/WtgChart/WtgChart.vue';
import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import 'jest-canvas-mock';
import WtgUi from '../../../../WtgUi';

const wtgUi = new WtgUi({
    theme: {
        colors: {
            light: {
                controls: {
                    chart: {
                        background: '#371FE1',
                        backdrop: 'rgba(255, 255, 255, 0.75)',
                        border: '#666',
                        grid: 'rgba(0, 0, 0, 0.6)',
                        text: '#666',
                        ticks: 'rgba(0, 0, 0, 0.6)',
                    },
                },
            },
            dark: {
                controls: {
                    chart: {
                        background: '#2387EE',
                        backdrop: 'rgba(0, 0, 0, 0.75)',
                        border: '#FFF',
                        grid: 'rgba(255, 255, 255, 0.7)',
                        text: '#FFF',
                        ticks: 'rgba(255, 255, 255, 0.7)',
                    },
                },
            },
        },
    },
});

enableAutoUnmount(afterEach);

describe('WtgBarChart', () => {
    test('its name is WtgBarChart', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('WtgBarChart');
    });

    test('it renders a WtgChart of type bar', () => {
        const wrapper = mountComponent();
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('type')).toBe('bar');
    });

    test('it passes its options on to the WtgChart component to render', () => {
        const options = {
            plugins: {
                title: {
                    text: 'Half yearly sales',
                },
            },
        };
        const wrapper = mountComponent({
            propsData: {
                options,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('options')).toStrictEqual(options);
    });

    test('it passes its data on to the WtgChart component to render', () => {
        const data = {
            labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
            datasets: [
                {
                    label: 'Sales',
                    data: [65, 59, 80, 81, 56, 55, 40],
                },
            ],
        };
        const wrapper = mountComponent({
            propsData: {
                data,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('data')).toStrictEqual(data);
    });

    test('it passes loading flag to the WtgChart component', () => {
        const wrapper = mountComponent({
            propsData: {
                loading: true,
            },
        });
        const chart = wrapper.findComponent(WtgChart);
        expect(chart.props('loading')).toBe(true);
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            propsData: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    function mountComponent({ propsData = {}, provide = {} } = {}) {
        return mount(WtgBarChart, {
            propsData,
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
