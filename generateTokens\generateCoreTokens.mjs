import * as utils from './utils.mjs';

const lightModeKey = '9:6';
const darkModeKey = '77:0';
const desktopKey = '68:0';
const mobileKey = '68:3';
const defaultKey = '1:0';
const mutedKey = '9161:0';

export default function generateCoreTokens() {
    utils.getVariables().then((variables) => {
        const tokens = extractTokensFromVariables(variables);

        utils.writeTokens(tokens.light, 'supply-light-core.css', ':root, .wtg-theme-provider-light');
        utils.writeTokens(
            tokens.muted,
            'supply-muted-core.css',
            ':root[data-theme="theme-muted"], .wtg-theme-provider-muted'
        );
        utils.writeTokens(
            tokens.dark,
            'supply-dark-core.css',
            ':root[data-theme="theme-dark"], .wtg-theme-provider-dark'
        );
        utils.writeTokens(tokens.mobile, 'supply-mobile-core.css', ':root');
        utils.writeTokens(tokens.desktop, 'supply-desktop-core.css', ':root', 'min-width: 960px');
        utils.writeTokens(tokens.core, 'supply-core.css', ':root');
    });
}

function extractTokensFromVariables(variables) {
    const tokens = { light: {}, dark: {}, muted: {}, mobile: {}, desktop: {}, core: {} };
    for (const [, variable] of Object.entries(variables)) {
        const tokensForVariable = extractTokensFromVariable(variable, variables);
        const name = utils.formatName(variable.name);
        if (tokensForVariable.light !== undefined) {
            tokens.light[name] = tokensForVariable.light;
        }
        if (tokensForVariable.dark !== undefined) {
            tokens.dark[name] = tokensForVariable.dark;
        }
        if (tokensForVariable.muted !== undefined) {
            tokens.muted[name] = tokensForVariable.muted;
        }
        if (tokensForVariable.mobile !== undefined) {
            tokens.mobile[name] = tokensForVariable.mobile;
        }
        if (tokensForVariable.desktop !== undefined) {
            tokens.desktop[name] = tokensForVariable.desktop;
        }
        if (tokensForVariable.core !== undefined) {
            tokens.core[name] = tokensForVariable.core;
        }
    }
    return tokens;
}

function extractTokensFromVariable(variable, variables) {
    const lightToken = extractToken(variable, lightModeKey, variables);
    const darkToken = extractToken(variable, darkModeKey, variables);
    const mutedToken = extractToken(variable, mutedKey, variables);
    let desktopToken = extractToken(variable, desktopKey, variables);
    let mobileToken = extractToken(variable, mobileKey, variables);
    let coreToken = extractToken(variable, defaultKey, variables);
    if (coreToken !== undefined) {
        if (variable.resolvedType === 'FLOAT') {
            coreToken = coreToken + 'px';
        } else if (variable.resolvedType === 'COLOR') {
            coreToken = utils.figmaColorToCssColor(coreToken);
        }

        return {
            core: coreToken,
        };
    }

    if (variable.hiddenFromPublishing) {
        return {};
    }

    if (desktopToken && variable.resolvedType === 'FLOAT') {
        if (!variable.name.includes('fontWeight')) {
            desktopToken += 'px';
        }
    }

    if (mobileToken && variable.resolvedType === 'FLOAT') {
        if (!variable.name.includes('fontWeight')) {
            mobileToken += 'px';
        }
    }
    return {
        light: utils.figmaColorToCssColor(lightToken),
        dark: utils.figmaColorToCssColor(darkToken),
        muted: utils.figmaColorToCssColor(mutedToken),
        desktop: desktopToken !== undefined && variable.resolvedType === 'FLOAT' ? desktopToken : undefined,
        mobile: mobileToken !== undefined && variable.resolvedType === 'FLOAT' ? mobileToken : undefined,
        core: coreToken,
    };
}

function extractToken(variable, modeKey, variables) {
    const figmaValue = variable.valuesByMode[modeKey];
    if (figmaValue === undefined) {
        return undefined;
    }
    const resolvedFigmaValue = resolveValue(figmaValue, variables);
    if (resolvedFigmaValue === undefined) {
        return undefined;
    }
    return resolvedFigmaValue;
}

function resolveValue(valueByMode, variables) {
    if (valueByMode.type !== 'VARIABLE_ALIAS') {
        return valueByMode;
    }
    return variables[valueByMode.id]?.valuesByMode[defaultKey];
}
