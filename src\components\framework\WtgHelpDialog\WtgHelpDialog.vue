<!-- eslint-disable vue/no-v-html -->
<template>
    <WtgDialog v-model="model" :width="width" persistent>
        <WtgForm>
            <div class="wtg-modal__title">
                <WtgLabel typography="title-md-default">
                    {{ dialogTitle }}
                </WtgLabel>
                <WtgSpacer />
                <WtgIconButton variant="ghost" icon="s-icon-close" :tooltip="closeTooltip" @click="onCloseClick" />
            </div>
            <div class="wtg-modal__content-canvas">
                <WtgPanel layout="grid">
                    <div>
                        <WtgLabel typography="title-md-default">{{ title }}</WtgLabel>
                    </div>
                    <slot v-if="!loading && slots.default" />
                    <div v-else-if="!loading" v-html="helpContent" />
                    <div v-else>
                        <div class="d-flex justify-center">
                            <WtgLabel>{{ loadingText }}</WtgLabel>
                        </div>
                        <WtgProgressLinear indeterminate class="mt-4" />
                    </div>
                    <div v-if="!slots.default" style="min-height: 50vh; overflow: auto"></div>
                </WtgPanel>
            </div>
            <div class="wtg-modal__actions">
                <WtgCheckbox
                    :model-value="alwaysOpenHelp"
                    :label="alwaysOpenHelpCaption"
                    @update:model-value="onAlwaysOpenHelpChanged"
                />
                <WtgSpacer />
                <WtgButton :min-width="88" text outlined sentiment="primary" variant="fill" @click="onCloseClick">
                    {{ formatCaption('dialog.close') }}
                </WtgButton>
            </div>
        </WtgForm>
    </WtgDialog>
</template>

<script setup lang="ts">
import WtgButton from '@components/WtgButton';
import WtgCheckbox from '@components/WtgCheckbox';
import WtgDialog from '@components/WtgDialog';
import WtgForm from '@components/WtgForm';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import WtgPanel from '@components/WtgPanel';
import WtgProgressLinear from '@components/WtgProgressLinear';
import WtgSpacer from '@components/WtgSpacer';
import { useFramework } from '@composables/framework';
import { useLocale } from '@composables/locale';
import { computed, useSlots } from 'vue';

defineProps({
    alwaysOpenHelp: {
        type: Boolean,
        default: false,
    },
    alwaysOpenHelpCaption: {
        type: String,
        default: '',
    },
    closeTooltip: {
        type: String,
        default: 'Close',
    },
    dialogTitle: {
        type: String,
        default: '',
    },
    helpContent: {
        type: String,
        default: '',
    },
    loading: {
        type: Boolean,
        default: false,
    },
    loadingText: {
        type: String,
        default: '',
    },
    title: {
        type: String,
        default: '',
    },
});

const { isTabletOrMobile } = useFramework();
const slots = useSlots();

const width = computed((): string | undefined => {
    return isTabletOrMobile.value ? '60vw' : '30vw';
});

const emit = defineEmits<{
    'update:alwaysOpen': [value: boolean];
}>();

const onAlwaysOpenHelpChanged = (value: boolean) => emit('update:alwaysOpen', value);

const model = defineModel<boolean>({ default: false });

const onCloseClick = (): void => {
    model.value = false;
};

const { formatCaption } = useLocale();
</script>
