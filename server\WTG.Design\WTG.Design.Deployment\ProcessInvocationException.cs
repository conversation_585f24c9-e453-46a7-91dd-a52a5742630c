﻿using System;
using System.Runtime.Serialization;

namespace WTG.Design.Deployment
{
	[Serializable]
	public class ProcessInvocationException : Exception
	{
		public ProcessInvocationException()
		{
		}

		public ProcessInvocationException(string message)
			: base(message)
		{
		}

		public ProcessInvocationException(string message, Exception inner)
			: base(message, inner)
		{
		}

		protected ProcessInvocationException(SerializationInfo info, StreamingContext context)
			: base(info, context)
		{
		}
	}
}
