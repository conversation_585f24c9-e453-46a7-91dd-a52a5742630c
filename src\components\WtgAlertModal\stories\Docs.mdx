import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import alertFlowChart from '../../../storybook/assets/alertModal/alertFlowChart.png';
import alertModalAnatomy from '../../../storybook/assets/alertModal/alertModalAnatomy.png';
import image from '../../../storybook/assets/component-button-decisiontree.svg';
import * as WtgAlertModal from './WtgAlertModal.stories.ts';

<Meta of={WtgAlertModal} />

<div className="component-header">
    <h1><PERSON><PERSON></h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=224-25601&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    An alert modal is a high-attention alert used to communicate critical information, warnings, or required user
    actions.
</p>

## API

<Canvas className="canvas-preview" of={WtgAlertModal.Default} />
<Controls of={WtgAlertModal.Default} sort={'alpha'} />

## How to use

### Summary table

Alert modals are part of the alerts family. Use the summary table below to help decide which alert component or pattern to use.

<table width="100%" className="component-summary-table">
    <thead>
        <tr>
            <th>Component/Pattern</th>
            <th>Priority</th>
            <th>Best used for</th>
            <th>Available sentiments</th>
        </tr>
    </thead>
    <tbody>
        <tr>
        <td>
                <p>[Callout](/docs/components-callout--docs)</p> 
            </td>
            <td>
                <p>Low</p>
            </td>
            <td>
                <p>Highlighting important contextual information on a page or specific section of a page. </p>
            </td>
            <td>
                <p>Success, info, warning, critical</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>[Toast](/docs/components-toast--docs)</p>
            </td>
            <td>
                <p>Low-medium</p>
            </td>
            <td>
                <p>Providing feedback after a user action and enables users to act on the toast content, if applicable.</p>
            </td>
            <td>
                <p>Success, info, warning, critical</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>[Field validation](/docs/components-text-field--docs)</p>
            </td>
            <td>
                <p>Medium-high</p>
            </td>
            <td>
                <p>Indicating errors or warnings on specific field/s along with actionable feedback. </p>
            </td>
            <td>
                <p>Success, warning, critical</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>[Alert modal](/docs/components-alert-modal--docs)</p>
            </td>
            <td>
                <p>High</p>
            </td>
            <td>
                <p>Providing important information that blocks a user from proceeding until a decision has been made.</p>
            </td>
            <td>
                <p>Success, warning, critical</p>
            </td>

        </tr>
    </tbody>

</table>

## Sentiments

<Canvas className="canvas-preview" of={WtgAlertModal.Sentiments} />

## Content guidelines

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview).

<p>
    Alert messaging should be short and to the point, and be clear in what has occurred to trigger the Alert. It then
    needs to tell the user what they need to know or do about it. This applies to the alert modal.
</p>
<p>
    Alerts are usually split into a header, description, and actions. Try to answer the following questions in each
    section directly and succinctly:
</p>

<p class="ml-6">
    <b>Header:</b> Say what happened.
</p>

<p class="ml-6">
    <b>Description:</b> Why did it happen, reassure what actions did occur or how to fix them, and provide a clear
    explanation of the way out of the alert state.
</p>

<p class="ml-6">
    <b>Actions:</b> Provide clear actions to either action or acknowledge the alert as well as a clear way back if
    additional changes are required.
</p>

<img class="documentation" src={alertModalAnatomy} />

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
