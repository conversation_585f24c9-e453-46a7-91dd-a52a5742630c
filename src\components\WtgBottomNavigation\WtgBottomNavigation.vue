<template>
    <VBottomNavigation v-model="internalValue" @update:model-value="onUpdateModelValue">
        <slot />
    </VBottomNavigation>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { VBottomNavigation } from 'vuetify/components/VBottomNavigation';

//
// Properties
//
const props = defineProps({
    /**
     * Controls the visibility of the bottom sheet.
     * Use v-model to bind this prop.
     */
    modelValue: {
        type: Boolean,
        default: false,
    },
    /**
     * If true, the bottom sheet cannot be dismissed by clicking outside or pressing ESC.
     */
    persistent: {
        type: <PERSON>olean,
        default: false,
    },
});

//
// Emits
//
const emit = defineEmits<{
    'update:modelValue': [value: boolean];
    confirm: [];
}>();

//
// State
//
const internalValue = ref(false);

//
// Watchers
//
watchEffect(() => {
    internalValue.value = props.modelValue ?? false;
});

//
// Event handlers
//
function onUpdateModelValue(state: boolean) {
    internalValue.value = state;
    emit('update:modelValue', state);
}
</script>
