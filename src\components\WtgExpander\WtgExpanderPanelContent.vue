<template>
    <VExpansionPanelText class="wtg-expansion-panel-content">
        <slot />
    </VExpansionPanelText>
</template>

<script setup lang="ts">
import { VExpansionPanelText } from 'vuetify/components/VExpansionPanel';
</script>

<style lang="scss">
.wtg-expansion-panel-content {
    .v-expansion-panel-text__wrapper {
        padding-left: var(--s-padding-xl);
        padding-right: var(--s-padding-xl);
    }
}
</style>
