import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import info from '../../../storybook/assets/info.png';

import { Meta, Title, Description, Story, Canvas, Controls, ArgTypes } from '@storybook/blocks';
import * as WtgIcon from './WtgIcon.stories.ts';

<Meta of={WtgIcon} />

<div className="component-header">
    <h1>Icon</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th>
                <img className="status-chip" src={statusAvailable}></img>
            </th>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

<Canvas className="canvas-preview" of={WtgIcon.Default} />
<Controls of={WtgIcon.Default} sort={'alpha'} />

## Search icons

Can’t find what you’re looking? See our [contribution process](?path=/docs/icons-images-icon-contribution--overview).

<Story of={WtgIcon.IconSearch} />

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
