import { WtgFrameworkTaskGenericAction } from '@components/framework/types';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import taskAction from '../TaskAction.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('task-action', () => {
    let action: WtgFrameworkTaskGenericAction;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        action = {
            id: 'someguid1',
            caption: 'Caption 1',
            placement: '',
            icon: '$action1',
            onInvoke: jest.fn(),
        };
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is TaskAction', () => {
        const wrapper = mountComponent({ propsData: { action } });
        expect(wrapper.vm.$options.__name).toBe('TaskAction');
    });

    describe('when rendering the task action', () => {
        test('it sets the label based on the action caption', async () => {
            wtgUi.breakpoint.mdAndDown = false;
            const wrapper = mountComponent({ propsData: { action } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(wrapper.text()).toContain('Caption 1');

            await wrapper.setProps({ action: { ...action, caption: 'Caption 2' } });
            expect(button.text()).toContain('Caption 2');
        });

        test('it sets the leading icon based on the action icon', async () => {
            wtgUi.breakpoint.mdAndDown = false;
            const wrapper = mountComponent({ propsData: { action } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.props().leadingIcon).toBe('$action1');

            await wrapper.setProps({ action: { ...action, icon: '$action2' } });
            expect(button.props().leadingIcon).toBe('$action2');
        });

        test('it calls the action onInvoke when clicked', async () => {
            const wrapper = mountComponent({ propsData: { action } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            expect(action.onInvoke).toHaveBeenCalledTimes(1);
        });

        describe('when on a tablet', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = true;
            });

            test('its sets the aria label based on the action caption', async () => {
                const wrapper = mountComponent({ propsData: { action } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.attributes('aria-label')).toBe('Caption 1');

                await wrapper.setProps({ action: { ...action, caption: 'Caption 2' } });
                expect(button.attributes('aria-label')).toBe('Caption 2');
            });

            test('it sets the icon for the icon button based on the action', () => {
                const wrapper = mountComponent({ propsData: { action } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.vm.$props.icon).toBe('$action1');
            });

            test('its sets the tooltip based on the action caption', async () => {
                const wrapper = mountComponent({ propsData: { action } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.vm.$props.tooltip).toBe('Caption 1');

                await wrapper.setProps({ action: { ...action, caption: 'Caption 2' } });
                expect(button.vm.$props.tooltip).toBe('Caption 2');
            });
        });

        describe('when on a desktop', () => {
            beforeEach(() => {
                wtgUi.breakpoint.lgAndUp = true;
            });

            test('it sets the button as a ghost button', () => {
                const wrapper = mountComponent({ propsData: { action } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.vm.$props.variant).toBe('ghost');
            });
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(taskAction, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
