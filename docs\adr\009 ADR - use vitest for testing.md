# Use Vitest for unit testing

## Status
**Status**: Proposed  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

Our current unit testing setup uses Jest alongside `@vue/test-utils` and `@testing-library/vue`. While functional, these tools were primarily designed for Webpack-based Vue 2 environments.

Now that our design system is built with Vue 3 and maturing we are considering migrating to Vitest. Vitest offers a better-aligned, modern alternative. [Vitest](https://vitest.dev) is a fast unit testing framework built by the Vite team, with first-class support for Vite-powered projects.

It uses the same configuration, plugins, and module resolution as Vite, making it simpler to integrate and maintain. Vitest also offers a Jest-like API, allowing for an easier transition path.

## Decision

We propose adopting Vitest as the default testing framework for the Supply component library.

- Replace Jest with Vitest for new unit tests.
- Integrate Vitest into the Vite-based dev environment.
- Ensure compatibility with `@testing-library/vue` and `@vue/test-utils`.
- Use Vitest’s built-in support for mocking, spies, coverage, and snapshot testing.
- Retain any legacy test framework temporarily for existing code until phased out.

## Consequences

### Benefits:
- Blazing fast execution: Vitest uses Vite under the hood for near-instant test runs and watch mode.
- Vue 3 and Composition API-first: Designed for the Vue 3 ecosystem.
- Unified config: Uses Vite’s config and plugins, reducing duplication and confusion.
- Built-in coverage, mocking, and snapshot support: No need for third-party layers.
- Jest-compatible API: Easy to migrate and familiar for teams already using Jest.
- Improved DX: Supports watch mode, filtered test runs, and better debug output.

### Trade-offs / Risks:
- Migration effort: Requires rewriting or adapting some existing tests.
- Tooling ecosystem maturity: While growing rapidly, Vitest is newer and may have gaps for rare edge cases.
- CI adjustments: May require updates to test runners and reporting tools used in your pipelines.

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.

---

### Best Practice Notes (Team Adoption Guidance)

If you haven’t adopted Vitest yet, here’s a good phased approach:

1. Trial in isolation – Set up Vitest in a small component package or new feature branch.
2. Dual-run some tests – Run a few key tests with both Jest and Vitest to validate parity.
3. Migrate test-by-test – Move one domain or folder of components to Vitest at a time.
4. CI integration – Ensure test coverage, watch mode, and exit codes work in your pipelines.
5. Update dev docs – Document how to run and write tests using Vitest going forward.
