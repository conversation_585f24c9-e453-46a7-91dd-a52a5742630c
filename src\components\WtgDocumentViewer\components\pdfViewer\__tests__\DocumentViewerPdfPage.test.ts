import DocumentViewerPdfPage from '@components/WtgDocumentViewer/components/pdfViewer/DocumentViewerPdfPage.vue';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { PDFDocumentProxy } from 'pdfjs-dist';
import { nextTick } from 'vue';
import WtgUi from '../../../../../WtgUi';

const wtgUi = new WtgUi();
enableAutoUnmount(afterEach);

jest.mock('pdfjs-dist', () => ({
    PDFDocumentProxy: jest.fn(),
    PDFPageProxy: jest.fn().mockImplementation(() => ({
        getViewport: jest.fn(({ scale, rotation }) => {
            const baseWidth = 800;
            const baseHeight = 600;
            const rotated = rotation % 180 !== 0;
            return {
                width: rotated ? baseHeight * scale : baseWidth * scale,
                height: rotated ? baseWidth * scale : baseHeight * scale,
            };
        }),
        render: jest.fn().mockResolvedValue({
            promise: Promise.resolve(),
            cancel: jest.fn(),
        }),
    })),
}));

describe('Document Viewer Pdf Page', () => {
    let wrapper: any;
    let mockPdfDocument: Partial<PDFDocumentProxy>;

    beforeEach(() => {
        mockPdfDocument = {
            getPage: jest.fn().mockResolvedValue({
                getViewport: jest.fn(({ scale, rotation }) => {
                    const baseWidth = 800;
                    const baseHeight = 600;
                    const rotated = rotation % 180 !== 0;
                    return {
                        width: rotated ? baseHeight * scale : baseWidth * scale,
                        height: rotated ? baseWidth * scale : baseHeight * scale,
                    };
                }),
                render: jest.fn(() => {
                    return {
                        promise: Promise.resolve(),
                        cancel: jest.fn(() => {}),
                    };
                }),
            }),
        };

        wrapper = mountComponent({
            props: {
                pageNumber: 1,
                pdfDocument: mockPdfDocument,
                pdfScale: 1,
                pdfRotation: 0,
                pagePreviewMode: false,
                activePage: 1,
            },
            slots: {
                default: `<div class="highlight-slot">
                            Page Width: {{ pdfPageData.pageWidth }},
                            Page Height: {{ pdfPageData.pageHeight }},
                            Page Number: {{ pdfPageData.pageNumber }}
                        </div>`,
            },
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('renders the component and displays the canvas', () => {
        expect(wrapper.find('canvas').exists()).toBe(true);
    });

    it('has a default slot with highlight data', async () => {
        const slotContent = wrapper.find('.highlight-slot');
        expect(slotContent.text()).toContain('Page Width: 0, Page Height: 0, Page Number: 1');

        wrapper.vm.pageWrapWidth = 180;
        await nextTick();

        expect(slotContent.text()).toContain('Page Width: 180, Page Height: 0, Page Number: 1');

        wrapper.vm.pageWrapHeight = 90;
        await nextTick();

        expect(slotContent.text()).toContain('Page Width: 180, Page Height: 90, Page Number: 1');

        await wrapper.setProps({ pageNumber: 2 });
        await nextTick();

        expect(slotContent.text()).toContain('Page Width: 180, Page Height: 90, Page Number: 2');
    });

    it('calls pdfDocument.getPage with the correct page number', async () => {
        await wrapper.vm.renderPage();
        expect(mockPdfDocument.getPage).toHaveBeenCalledWith(1);
    });

    it('displays page number when in preview mode', async () => {
        expect(wrapper.props('pagePreviewMode')).toBe(false);
        expect(wrapper.findAllComponents({ name: 'WtgLabel' })).toHaveLength(0);

        await wrapper.setProps({ pagePreviewMode: true });
        await nextTick();

        const pageNumberLabel = wrapper.findAllComponents({ name: 'WtgLabel' }).at(0);
        expect(pageNumberLabel!.text()).toBe('1');
    });

    it('re-renders the page when pdfScale is updated', async () => {
        const initialCanvasWidth = wrapper.vm.pdfCanvas.width;
        const initialCanvasHeight = wrapper.vm.pdfCanvas.height;

        await wrapper.setProps({ pdfScale: 1.5 });
        await nextTick();
        expect(initialCanvasWidth).not.toEqual(wrapper.vm.pdfCanvas.width);
        expect(initialCanvasHeight).not.toEqual(wrapper.vm.pdfCanvas.height);
    });

    it('re-renders the page when pdfRotation is updated', async () => {
        const initialCanvasWidth = wrapper.vm.pdfCanvas.width;
        const initialCanvasHeight = wrapper.vm.pdfCanvas.height;

        await wrapper.setProps({ pdfRotation: 90 });
        await nextTick();

        expect(initialCanvasWidth).not.toEqual(wrapper.vm.pdfCanvas.width);
        expect(initialCanvasHeight).not.toEqual(wrapper.vm.pdfCanvas.height);
    });

    it('emits event when page is clicked in preview mode', async () => {
        await wrapper.setProps({
            pagePreviewMode: true,
        });

        wrapper.trigger('click');

        expect(wrapper.emitted()['active-page-updated']?.length).toBe(1);
        expect(wrapper.emitted()['active-page-updated']![0][0]).toStrictEqual(1);
    });

    it('calculates page dimensions when page is rendered', async () => {
        expect(wrapper.vm.pageWrapWidth).toBe(0);
        expect(wrapper.vm.pageWrapHeight).toBe(0);

        const mockClientWidth = jest.spyOn(wrapper.vm.$refs.pageRef, 'clientWidth', 'get').mockReturnValue(500);
        const mockClientHeight = jest.spyOn(wrapper.vm.$refs.pageRef, 'clientHeight', 'get').mockReturnValue(300);

        await wrapper.setProps({ pdfScale: 1.5 });
        await flushPromises();

        expect(wrapper.vm.pageWrapWidth).toBe(500);
        expect(wrapper.vm.pageWrapHeight).toBe(300);

        mockClientWidth.mockRestore();
        mockClientHeight.mockRestore();
    });

    it('should not throw error when page has been destroyed', () => {
        wrapper.unmount();
        expect(() => wrapper.vm.getPageDimensions()).not.toThrow();
    });
});

function mountComponent({ props = {}, slots = {} } = {}) {
    return mount(DocumentViewerPdfPage as any, {
        props,
        slots,
        global: {
            plugins: [wtgUi],
        },
    });
}
