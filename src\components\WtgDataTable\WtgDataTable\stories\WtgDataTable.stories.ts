import WtgContainer from '@components/WtgContainer';
import { createDessertsService } from '@components/WtgDataTable/stories/DessertsService';
import WtgDataTable from '@components/WtgDataTable/WtgDataTable';
import WtgIcon from '@components/WtgIcon';
import WtgNumberField from '@components/WtgNumberField';
import WtgPanel, { WtgPanelHeader } from '@components/WtgPanel';
import WtgSpacer from '@components/WtgSpacer';
import WtgStatus from '@components/WtgStatus';
import WtgTextField from '@components/WtgTextField';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import { Meta, StoryContext, StoryObj } from '@storybook/vue3';

type Story = StoryObj<typeof WtgDataTable>;
const meta: Meta<typeof WtgDataTable> = {
    title: 'Components/Data Table/Variants/Data Table',
    component: WtgDataTable,
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: [
                'headers',
                'items',
                'itemsLength',
                'loading',
                'itemValue',
                'search',
                'itemsPerPage',
                'itemsPerPageOptions',
                'page',
                'selectionMode',
            ],
        },
        docs: {
            description: {
                component: 'The data-table component is designed for displaying and searching information.',
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: { WtgPanel, WtgDataTable },
        setup: () => ({ args }),
        template: `<div />`,
    }),
    argTypes: {
        density: {
            control: 'select',
            options: ['default', 'comfortable', 'compact'],
        },
        editableStyling: {
            control: 'boolean',
        },
        fillAvailable: {
            control: 'boolean',
        },
        fixedHeader: {
            control: 'boolean',
        },
        hideDefaultFooter: {
            control: 'boolean',
        },
        hideDefaultHeader: {
            control: 'boolean',
        },
        mobile: {
            control: 'boolean',
        },
        mobileBreakpoint: {
            control: 'select',
            options: ['', 'xs', 'sm', 'md', 'lg', 'xl', 'xxl'],
        },
        noDataText: {
            control: 'text',
        },
    },
};

function createProps(storyContext: StoryContext): string {
    const props = [];
    for (const arg in storyContext.args) {
        props.push(`${arg}="${storyContext.args[arg] + ''}"`);
    }
    return props.sort().join(' ');
}

const dessertsService = createDessertsService({ itemsPerPage: 10 });

export default meta;

function snippetContent(args1: string): string {
    return `
            <div class="d-flex align-center mb-2 ga-2">
                <WtgPanelHeader class="mb-0">Desserts</WtgPanelHeader>
                <WtgSpacer/>
                <WtgPanelHeader id="search" class="mb-0">Search</WtgPanelHeader>
                <WtgTextField aria-labelledby="search" v-model="options.search" style="max-width: 200px"/>
            </div>    
            <WtgDataTable
                ${args1}
                :headers="options.headers"
                :items="options.items"
                :search="options.search"
                @update:options="options.loadItems($event)">
                    <template #item.available="{ item }">
                        <WtgStatus v-if="item.available" sentiment="success" label="Available"/>
                    </template>
                    <template #item.rating="{ item }">
                        <WtgIcon v-for="index in item.rating" :key="index" color="text-amber">s-icon-star-filled</WtgIcon>
                    </template>
            </WtgDataTable>`;
}

export const Default: Story = {
    parameters: {
        ...getChromaticParameters(),
        docs: {
            source: {
                transform: (source: string, storyContext: StoryContext) => `
<template>
  <WtgContainer color="canvas" layout="fill">
      <WtgPanel class="mb-2">
          <WtgCallout
            description="The data table component is used for displaying tabular data. Features include sorting, searching, pagination, grouping, and row selection. This simple data table presumes that the entire data set is available locally."
            sentiment="info"
            title="Data table"
          />
      </WtgPanel>             
      <WtgPanel fill-available layout="fill">   
      ${snippetContent(`${createProps(storyContext)} `)}
      </WtgPanel>             
  </WtgContainer>
</template>

<script setup lang="ts">
import {
    SortDirection,
    quickSearchFind,
    filtersCompare,
    WtgContainer,
    WtgIcon,
    WtgTextField,
    WtgPanelHeader,
    WtgPanel,
    WtgSpacer,
    WtgStatus,
    WtgCallout,
    WtgDataTable,
} from '@wtg/wtg-components';
import { ref } from 'vue';

const baseDesserts = [
    { id: 1, name: 'Frozen Yogurt', calories: 159, fat: 6.0, rating: 2, expiry: '2031-08-06', available: false },
    { id: 2, name: 'Jelly bean', calories: 375, fat: 0.0, rating: 3, expiry: '2032-12-18', available: true },
    { id: 3, name: 'KitKat', calories: 518, fat: 26.0, rating: 2, expiry: '2032-08-12', available: false },
    { id: 4, name: 'Eclair', calories: 262, fat: 16.0, rating: 4, expiry: '2032-07-08', available: true },
    { id: 5, name: 'Gingerbread', calories: 356, fat: 16.0, rating: 3, expiry: '2032-06-20', available: true },
    { id: 6, name: 'Ice cream sandwich', calories: 237, fat: 9.0, rating: 3, expiry: '2032-09-03', available: false },
    { id: 7, name: 'Lollipop', calories: 392, fat: 0.2, rating: 1, expiry: '2032-11-18', available: false },
    { id: 8, name: 'Cupcake', calories: 305, fat: 3.7, rating: 2, expiry: '2032-07-08', available: true },
    { id: 9, name: 'Honeycomb', calories: 408, fat: 3.2, rating: 5, expiry: '2032-03-27', available: true },
    { id: 10, name: 'Donut', calories: 452, fat: 25.0, rating: 3, expiry: '2032-10-24', available: true },
];

const fields = [
    { name: 'name', text: 'Dessert (100g serving)', type: 'string', align: 'start' },
    { name: 'rating', text: 'Rating', align: 'start' },
    { name: 'fat', text: 'Fat (g)', type: 'number', align: 'end' },
    { name: 'expiry', text: 'Expiry Date', type: 'date', align: 'end' },
    { name: 'calories', text: 'Calories', type: 'number', align: 'end' },
    { name: 'available', text: 'Available', type: 'boolean', align: 'end' },
];

const generateItems = () => {
    return Array.from({ length: 50 }, (_, i) => {
        const baseDessertItem = baseDesserts[i % 5];
        return {
            ...baseDessertItem,
            id: i + 1,
            name: baseDessertItem.name + ' ' + (i + 1),
            calories: Math.round(Math.random() * (1200 - 100) + 100),
            fat: Math.round(Math.random() * (10 - 0) + 0),
        };
    });
};

function sortItems(items, sortBy) {
    return items.sort((a, b) => {
        for (const { key, order } of sortBy) {
            const sortA = a[key];
            const sortB = b[key];
            if (sortA < sortB) return order === SortDirection.descending ? 1 : -1;
            if (sortA > sortB) return order === SortDirection.descending ? -1 : 1;
        }
        return 0;
    });
}

const baseItems = generateItems();

const options = ref({
    search: '',
    items: [] as any,
    sortBy: [],
    headers: fields.map((field) => {
        return { key: field.name, title: field.text, align: field.align };
    }),
    sortDesc: '',
    options: {},

    loadItems: ({ sortBy = [] }) => {
        options.value.sortBy = sortBy;
        let items = [] as any[];

        baseItems.forEach((item) => {
            if (filtersCompare(fields, [], 'and', item) && quickSearchFind(fields, options.value.search, item)) {
                items.push(item);
            }
        });

        sortItems(items, sortBy);

        options.value.items = items;
    },
});
</script>`,
            },
        },
        layout: 'fullscreen',
    },
    render: (args) => ({
        components: {
            WtgContainer,
            WtgIcon,
            WtgDataTable,
            WtgPanel,
            WtgPanelHeader,
            WtgNumberField,
            WtgTextField,
            WtgSpacer,
            WtgStatus,
        },
        setup: () => {
            return {
                args,
                options: dessertsService._options,
            };
        },
        template: `<WtgContainer color="canvas" layout="fill"> <WtgPanel fill-available layout="fill"> ${snippetContent(
            `v-bind="args" `
        )}</WtgPanel></WtgContainer>`,
    }),
};
