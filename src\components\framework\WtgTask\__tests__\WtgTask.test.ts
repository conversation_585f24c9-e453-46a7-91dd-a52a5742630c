import { WtgFramework, WtgFrameworkTask } from '@components/framework/types';
import WtgTask from '@components/framework/WtgTask/WtgTask.vue';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../../WtgUi';

const wtgUi = new WtgUi();

enableAutoUnmount(afterEach);

describe('WtgTask', () => {
    let application: WtgFramework;
    let task: Record<string, any>;

    beforeEach(() => {
        application = new WtgFramework();
        setApplication(application);

        task = {};
    });

    test('its name is WtgTask', () => {
        const wrapper = mountComponent({
            propsData: {
                task,
            },
        });
        expect(wrapper.vm.$options.name).toBe('WtgTask');
    });

    test('it assign the application currentTask state when creating and destroying', async () => {
        const task = new WtgFrameworkTask();
        const component = {
            components: { WtgTask },
            template: '<wtg-task :task="task" />',
            data: () => {
                return {
                    task,
                };
            },
        };
        expect(application.currentTask).toBe(null);
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });

        expect(application.currentTask).toStrictEqual(task);
        wrapper.unmount();
        expect(application.currentTask).toBe(null);
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount<any>(WtgTask as any, {
            propsData,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
