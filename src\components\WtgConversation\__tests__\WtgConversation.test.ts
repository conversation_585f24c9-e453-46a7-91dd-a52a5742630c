import WtgConversation from '@components/WtgConversation/WtgConversation.vue';
import { VueWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../WtgUi';
import { ConversationFollower, ConversationMessage } from '../types';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi({
    language: {
        captionProvider: (languageCode: string, key: string, params?: (string | number)[]) => `C:${key}${params}`,
    },
});

const conversation: any = {
    name: 'Shipment 123',
    followers: [] as ConversationFollower[],
    following: false,
    followerUserTypes: [
        { type: 1, caption: 'Staff' },
        { type: 2, caption: 'Contact' },
        { type: 3, caption: 'Group' },
        { type: 4, caption: 'Organization' },
    ],
    loadingMessages: false,
    messages: {
        '31-Oct-2022': [
            {
                id: 'pk-1',
                body: 'message 1',
                score: 0,
                loadingLikeScore: false,
            } as ConversationMessage,
            {
                id: 'pk-2',
                body: 'message 2',
                score: 0,
            } as ConversationMessage,
        ],
        Today: [
            {
                id: 'pk-3',
                body: 'message 3',
                score: 0,
                loadingLikeScore: false,
            } as ConversationMessage,
            {
                id: 'pk-4',
                body: 'message 4',
                score: 0,
            } as ConversationMessage,
        ],
    },
    provider: {
        dislikeMessageAsync: jest.fn(),
        followConversationAsync: jest.fn(),
        likeMessageAsync: jest.fn(),
        sendMessageAsync: jest.fn(),
        unfollowConversationAsync: jest.fn(),
        getPotentialFollowersAsync: jest.fn(),
    },
};

describe('WtgConversation', () => {
    let likeMessageResolve: any, likeMessageReject: any, dislikeMessageResolve: any, dislikeMessageReject: any;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        conversation.provider.likeMessageAsync.mockReturnValue(
            new Promise((resolve, reject) => {
                likeMessageResolve = resolve;
                likeMessageReject = reject;
            })
        );
        conversation.provider.dislikeMessageAsync.mockReturnValue(
            new Promise((resolve, reject) => {
                dislikeMessageResolve = resolve;
                dislikeMessageReject = reject;
            })
        );
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is WtgConversation', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.name).toBe('WtgConversation');
    });

    test('it has a correct properties', () => {
        const wrapper = mountComponent({
            propsData: {
                readonly: true,
                height: 100,
                width: 101,
                minWidth: 103,
                maxWidth: 104,
                minHeight: 105,
                maxHeight: 106,
            },
        });
        expect(wrapper.props('conversation')!.name).toBe('Shipment 123');
        expect(wrapper.props('readonly')).toBe(true);
        expect(wrapper.props('height')).toBe(100);
        expect(wrapper.props('width')).toBe(101);
        expect(wrapper.props('minWidth')).toBe(103);
        expect(wrapper.props('minHeight')).toBe(105);
        expect(wrapper.props('maxWidth')).toBe(104);
        expect(wrapper.props('maxHeight')).toBe(106);
    });

    test('placeholder text is present', () => {
        const wrapper = mountComponent();
        const textArea = wrapper.findAllComponents({ name: 'wtg-text-area' }).at(0);
        expect(textArea!.props('placeholder')).toBe('C:conversation.placeholder');
    });

    describe('conversation message', () => {
        let messages: Array<VueWrapper<any>>, wrapper: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent();
            messages = wrapper.findAll('div').at(0)!.findAllComponents({ name: 'WtgConversationMessage' });
        });

        test('is created for each message', () => {
            expect(messages.length).toBe(4);
        });

        test("Today is shown for today's message", () => {
            const todaysLabel = wrapper.findAll('div').at(0)!.findAllComponents({ name: 'wtg-label' }).at(2);

            expect(todaysLabel.element.innerHTML.trim()).toBe('Today');
        });

        describe('when like button clicked', () => {
            let message1: ConversationMessage;

            beforeEach(() => {
                message1 = conversation.messages['31-Oct-2022'][0];
            });

            test('it calls conversation provider likeMessageAsync', () => {
                messages.at(0)!.vm.$emit('like-click', message1);
                expect(conversation.provider.likeMessageAsync).toHaveBeenCalledWith(message1);
            });

            test('loads like score', () => {
                messages.at(0)!.vm.$emit('like-click', message1);
                expect(message1.loadingLikeScore).toBe(true);
            });

            test('stops loading like score once likeMessageAsync resolved', async () => {
                messages.at(0)!.vm.$emit('like-click', message1);
                await likeMessageResolve();
                expect(message1.loadingLikeScore).toBe(false);
            });

            test('stops loading like score if likeMessageAsync rejected', async () => {
                const error = new Error();
                await likeMessageReject(error);
                await expect(() => wrapper.vm.onLikeClick(message1)).rejects.toThrowError(error);
                expect(message1.loadingLikeScore).toBe(false);
            });
        });

        describe('when dislike button clicked', () => {
            let message1: ConversationMessage;

            beforeEach(() => {
                message1 = conversation.messages['31-Oct-2022'][0];
            });

            test('it calls conversation provider dislikeMessageAsync', () => {
                messages.at(0)!.vm.$emit('dislike-click', message1);
                expect(conversation.provider.dislikeMessageAsync).toHaveBeenCalledWith(message1);
            });

            test('loads dislike score', () => {
                messages.at(0)!.vm.$emit('dislike-click', message1);
                expect(message1.loadingDislikeScore).toBe(true);
            });

            test('stops loading dislike score once dislikeMessageAsync resolved', async () => {
                messages.at(0)!.vm.$emit('dislike-click', message1);
                await dislikeMessageResolve();
                expect(message1.loadingDislikeScore).toBe(false);
            });

            test('stops loading like score if dislikeMessageAsync rejected', async () => {
                const error = new Error();
                await dislikeMessageReject(error);
                await expect(() => wrapper.vm.onDislikeClick(message1)).rejects.toThrowError(error);
                expect(message1.loadingDislikeScore).toBe(false);
            });
        });
    });

    test('it disables wtg-text-area if the readonly property is true', async () => {
        const wrapper = mountComponent();
        const textarea = wrapper.findComponent({ name: 'wtg-text-area' });
        expect(textarea.props('readonly')).toBe(false);

        await wrapper.setProps({ readonly: true });
        expect(textarea.props('readonly')).toBe(true);
    });

    describe('it disabled send button', () => {
        let wrapper: VueWrapper, sendButton: VueWrapper<any>;

        beforeEach(() => {
            wrapper = mountComponent();
            sendButton = wrapper.findAllComponents({ name: 'wtg-button' }).at(1)!;
        });

        test('if the readonly property is true', async () => {
            const textarea = wrapper.findComponent({ name: 'wtg-text-area' });
            await textarea.find('textarea').setValue('blah');
            expect(sendButton!.props('disabled')).toBe(false);

            await wrapper.setProps({ readonly: true });
            expect(sendButton!.props('disabled')).toBe(true);
        });

        test('if there is no value in the textarea', async () => {
            expect(sendButton.props('disabled')).toBe(true);
        });

        test('if there is only whitespace value in the textarea', async () => {
            const textarea = wrapper.findComponent({ name: 'wtg-text-area' });
            await textarea.find('textarea').setValue('     ');
            expect(sendButton.props('disabled')).toBe(true);
        });
    });

    describe('when send button clicked', () => {
        let sendMessageResolve: any, sendMessageReject: any, sendButton: VueWrapper<any>, wrapper: VueWrapper<any>;

        beforeEach(async () => {
            conversation.provider.sendMessageAsync.mockReturnValue(
                new Promise((resolve, reject) => {
                    sendMessageResolve = resolve;
                    sendMessageReject = reject;
                })
            );

            wrapper = mountComponent();
            sendButton = wrapper.findAllComponents({ name: 'wtg-button' }).at(1)!;
            const textarea = wrapper.findComponent({ name: 'wtg-text-area' });
            await textarea.find('textarea').setValue('blah');
        });

        test('it calls conversation provider sendMessageAsync', async () => {
            await sendButton.trigger('click');
            await nextTick();

            expect(conversation.provider.sendMessageAsync).toHaveBeenCalledWith(conversation, 'blah');
        });

        test('button is loading', async () => {
            await sendButton.trigger('click');
            await nextTick();

            expect(sendButton.props('loading')).toBe(true);
        });

        test('once sendMessageAsync resolved stops loading send', async () => {
            sendMessageResolve();
            await sendButton.trigger('click');
            await nextTick();

            expect(sendButton.props('loading')).toBe(false);
        });

        test('stops loading once sendMessageAsync rejected', async () => {
            const error = new Error();
            sendMessageReject(error);

            await expect(wrapper.vm.onSendClick).rejects.toThrowError(error);
            expect(sendButton.props('loading')).toBe(false);
        });
    });

    test('progress bar is shown when loading messages', () => {
        conversation.loadingMessages = true;

        const wrapper = mountComponent();
        const progressBar = wrapper.findComponent({ name: 'wtg-progress-linear' });
        expect(progressBar.exists()).toBe(true);
    });

    test('progress bar is NOT shown when NOT loading messages', () => {
        conversation.loadingMessages = false;

        const wrapper = mountComponent();
        const progressBar = wrapper.findComponent({ name: 'wtg-progress-linear' });
        expect(progressBar.exists()).toBe(false);
    });

    describe('when follow button clicked', () => {
        let followSwitch: VueWrapper<any>, wrapper: VueWrapper<any>;
        beforeEach(() => {
            conversation.following = false;
            wrapper = mountComponent();
            followSwitch = wrapper.findAllComponents({ name: 'WtgSwitch' }).at(0)!;
            expect(followSwitch.text()).toBe('C:conversation.follow');
        });

        test('it calls conversation provider followConversationAsync', async () => {
            followSwitch.find('input')['setChecked'](true);
            await nextTick();

            expect(conversation.provider.followConversationAsync).toHaveBeenCalledWith(conversation);
        });
    });

    describe('when unfollow button clicked', () => {
        let unfollowSwitch: VueWrapper<any>, wrapper: VueWrapper<any>;
        beforeEach(() => {
            conversation.following = true;
            wrapper = mountComponent();
            unfollowSwitch = wrapper.findAllComponents({ name: 'WtgSwitch' }).at(0)!;
            expect(unfollowSwitch.text()).toBe('C:conversation.following');
        });

        test('it calls conversation provider unfollowConversationAsync', async () => {
            unfollowSwitch.find('input')['setChecked'](false);
            await nextTick();

            expect(conversation.provider.unfollowConversationAsync).toHaveBeenCalledWith(conversation);
        });
    });

    describe('when no followers', () => {
        let wrapper: VueWrapper<any>, addFollowersButton: VueWrapper<any> | undefined;

        beforeEach(() => {
            conversation.followers = [];

            wrapper = mountComponent();
            addFollowersButton = wrapper.findAllComponents({ name: 'wtg-button' }).at(0);
        });

        test('button shown', () => {
            expect(addFollowersButton?.text()).toBe('C:conversation.addFollowers');
            expect(addFollowersButton?.props('variant')).toBe('ghost');
        });

        test('shows add followers dialog when add followers button clicked', async () => {
            const dialog = wrapper.findComponent({ name: 'add-followers-dialog' });
            let dialogProps = dialog.props();
            expect(dialogProps.value).toBe(false);
            expect(dialogProps.conversation).toEqual(conversation);

            addFollowersButton?.trigger('click');
            await nextTick();

            dialogProps = dialog.props();
            expect(dialogProps.value).toBe(true);
            expect(dialogProps.conversation).toEqual(conversation);
        });
    });

    describe('when has followers', () => {
        let wrapper: VueWrapper<any>, followButton: VueWrapper<any>;

        beforeEach(() => {
            conversation.followers = [
                {
                    id: 'pk-1',
                    name: 'Uno',
                    photo: { image: 'img.jpg', fallbackImage: 'default.jpg' },
                    icon: '',
                },
                {
                    id: 'pk-2',
                    name: 'Dos',
                    icon: 'mdi-ico-1',
                },
                {
                    id: 'pk-3',
                    name: 'Uno',
                    photo: { image: 'error', fallbackImage: 'default.jpg' },
                    icon: '',
                },
            ];

            wrapper = mountComponent();
            followButton = wrapper.findAllComponents({ name: 'wtg-button' }).at(0)!;
        });

        test('button shown', () => {
            expect(followButton.text()).toBe('C:conversation.followers');
            expect(followButton.props('variant')).toBe('ghost');
        });

        test('displays list when button clicked', async () => {
            const menuItem = wrapper.findComponent({ name: 'wtg-popover' });
            let list = menuItem.findComponent({ name: 'wtg-list' });
            expect(list.exists()).toBe(false);

            followButton.trigger('click');
            await nextTick();

            list = menuItem.findComponent({ name: 'wtg-list' });
            const listItems = list.findAllComponents({ name: 'wtg-list-item' });
            expect(listItems.length).toBe(4);

            const addFollowersListItem = listItems.at(0);
            expect(addFollowersListItem?.text()).toBe('C:conversation.addFollowers');

            const listItem1 = listItems.at(1)!;
            expect(listItem1.text()).toBe('Uno');
            const avatar1 = listItem1.findComponent({ name: 'wtg-avatar' });
            const img1 = avatar1.findComponent({ name: 'wtg-image' });
            expect(img1.vm.src).toBe('img.jpg');
            const icon1 = avatar1.findComponent({ name: 'wtg-icon' });
            expect(icon1.exists()).toBe(false);

            const listItem2 = listItems.at(2)!;
            expect(listItem2.text()).toBe('Dos');
            const avatar2 = listItem2.findComponent({ name: 'wtg-avatar' });
            const img2 = avatar2.findComponent({ name: 'wtg-image' });
            expect(img2.exists()).toBe(false);
            const icon2 = avatar2.findComponent({ name: 'wtg-icon' });
            expect(icon2.element.className).toContain('mdi-ico-1');

            const listItem3 = listItems.at(3)!;
            expect(listItem3.text()).toBe('Uno');
            const avatar3 = listItem3.findComponent({ name: 'wtg-avatar' });
            const img3 = avatar3.findComponent({ name: 'wtg-image' });
            const vImg = img3.findComponent({ name: 'VImg' });

            vImg.vm.$emit('error');
            await nextTick();

            expect(vImg.vm.src).toBe('default.jpg');
            const icon3 = avatar1.findComponent({ name: 'wtg-icon' });
            expect(icon3.exists()).toBe(false);
        });

        test('shows remove follower dialog when close icon clicked', async () => {
            const menuItem = wrapper.findComponent({ name: 'wtg-popover' });

            followButton.trigger('click');
            await nextTick();

            const list = menuItem.findComponent({ name: 'wtg-list' });
            const listItems = list.findAllComponents({ name: 'wtg-list-item' });
            const listItem1 = listItems.at(1)!;
            const closeIcon = listItem1.findComponent({ name: 'wtg-icon' });
            expect(closeIcon.exists()).toBe(true);

            const dialog = wrapper.findComponent({ name: 'remove-follower-dialog' });
            let dialogProps = dialog.props();
            expect(dialogProps.value).toBe(false);
            expect(dialogProps.conversation).toEqual(conversation);
            expect(dialogProps.follower).toBeUndefined();

            closeIcon.trigger('click');
            await nextTick();

            expect(list.isVisible()).toBe(false);
            dialogProps = dialog.props();
            expect(dialogProps.value).toBe(true);
            expect(dialogProps.conversation).toEqual(conversation);
            expect(dialogProps.follower).toEqual(conversation.followers[0]);
        });

        test('shows add followers dialog when add followers item clicked', async () => {
            const menuItem = wrapper.findComponent({ name: 'wtg-popover' });

            followButton.trigger('click');
            await nextTick();

            const list = menuItem.findComponent({ name: 'wtg-list' });
            const listItems = list.findAllComponents({ name: 'wtg-list-item' });
            const listItem1 = listItems.at(0)!;

            const dialog = wrapper.findComponent({ name: 'add-followers-dialog' });
            let dialogProps = dialog.props();
            expect(dialogProps.value).toBe(false);
            expect(dialogProps.conversation).toEqual(conversation);

            listItem1.trigger('click');
            await nextTick();

            expect(list.isVisible()).toBe(false);
            dialogProps = dialog.props();
            expect(dialogProps.value).toBe(true);
            expect(dialogProps.conversation).toEqual(conversation);
        });
    });

    test('sets textarea placeholder', async () => {
        const wrapper = mountComponent();
        const textarea = wrapper.findComponent({ name: 'wtg-text-area' });

        expect(textarea.props('placeholder')).toBe('C:conversation.placeholder');
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgConversation, {
            propsData: {
                conversation,
                ...propsData,
            },
            global: {
                plugins: [wtgUi],
            },
            slots,
        });
    }
});
