import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgCheckbox from './WtgCheckbox.stories.ts';
import * as WtgCheckboxGroup from './WtgCheckboxGroup.stories.ts';

<Meta of={WtgCheckbox} />

<div className="component-header">
    <h1>Checkbox</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-3819&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img> With pending updates
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style={{ width: '33%' }}>[PRJ00052236](https://svc-ediprod.wtg.zone/Services/edit/PRJ/PRJ00052236)</td>
            <td style={{ width: '75%' }}>Supply Check\Radio\Switch figma\dev alignment.</td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">Checkboxes allow users to select one or many options from a list.</p>

## API

<Canvas className="canvas-preview" of={WtgCheckbox.Default} />
<Controls of={WtgCheckbox.Default} sort={'alpha'} />

## How to use

### Summary table

The checkbox has similar functionality with other components. Use the information below to help decide which to use.

<table width="100%" className="component-summary-table">
    <thead>
        <tr>
            <th></th>
            <th>[Switch](/docs/components-switch--docs)</th>
            <th>[Single checkbox](/docs/components-checkbox--docs)</th>
            <th>[Radio group](/docs/components-radio--docs)</th>
            <th>[Checkbox group](/docs/components-checkbox--docs)</th>
            <th>[Segmented control](/docs/components-segmented-control--docs)</th>
        </tr>
    </thead>
    <tbody>
        <tr>
        <td>
                <p>**Best used for**</p> 
            </td>
            <td>
                <p>Turning an option on/off or to show/hide an element in the interface.</p>
            </td>
            <td>
                <p>Indicating agreement or consent, revealing/hiding additional fields or options in a form.</p>
            </td>
            <td>
                <p>Making a single selection from several options.</p>
            </td>
            <td>
                <p>Making multiple selections from several options. </p>
            </td>
            <td>
                <p>Making a single selection from 2-4 succinct options, typically to switch between different views.</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>**Number of options**</p>
            </td>
            <td>
                <p>1</p>
            </td>
            <td>
                <p>1</p>
            </td>
            <td>
                <p>Multiple</p>
            </td>
            <td>
                <p>Multiple</p>
            </td>
            <td>
                <p>Ideally 2-4, max 5</p>
            </td>
        </tr>

         <tr>
        <td>
                <p>**Number of selections**</p>
            </td>
            <td>
                <p>2 (on/off)</p>
            </td>
            <td>
                <p>2 (checked/unchecked)</p>
            </td>
            <td>
                <p>1</p>
            </td>
            <td>
                <p>0-All</p>
            </td>
            <td>
                <p>1</p>
            </td>

        </tr>

         <tr>
        <td>
                <p>**Has default selection**</p>
            </td>
            <td>
                <p>Yes</p>
            </td>
            <td>
                <p>Yes</p>
            </td>
            <td>
                <p>Yes</p>
            </td>
            <td>
                <p>No</p>
            </td>
            <td>
                <p>Yes</p>
            </td>

        </tr>

             <tr>
        <td>
                <p>**Choice type**</p>
            </td>
            <td>
                <p>Mutually exclusive</p>
            </td>
            <td>
                <p>Mutually exclusive</p>
            </td>
            <td>
                <p>Mutually exclusive</p>
            </td>
            <td>
                <p>Independent of each other</p>
            </td>
            <td>
                <p>Mutually exclusive</p>
            </td>

        </tr>
    </tbody>

</table>

## Variants

### Single checkbox

<p>Use a single checkbox for confirming a choice or collecting consent e.g. "I agree to the terms and conditions."</p>

<Canvas className="canvas-preview" of={WtgCheckbox.Default} sourceState={'none'} />

### Checkbox group

<p>
    Use a checkbox group when users need to select multiple related options. Unlike [radio
    buttons](/docs/components-radio--docs), checkboxes allow selecting any combination of options. Consider keeping
    groups under 7-10 options to avoid cognitive overload.
</p>

<Canvas className="canvas-preview" of={WtgCheckboxGroup.Default} sourceState={'none'} />

## Content guidelines

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview).

-   Use clear, concise labels. lengthy labels can cause line breaks resulting in misalignment of checkboxes and making it hard to scan.
-   Checkbox groups should have a clear group label to provide context.

## Related components

-   [Radio buttons](/docs/components-radio--docs)
-   [Switch](/docs/components-switch--docs)
-   [Segmented control](/docs/components-segmented-control--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
