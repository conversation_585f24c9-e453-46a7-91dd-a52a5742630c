﻿using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Dat.Integration;

namespace WTG.Design.Deployment
{
	public sealed class StreamingProcessInvoker : IStreamingProcessInvoker
	{
		public StreamingProcessInvoker(ITaskLogger logger)
		{
			this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
		}

		readonly ITaskLogger logger;

		public async Task<int> ExecuteProcessAsync(string path, string[] arguments)
		{
			var psi = new ProcessStartInfo
			{
				UseShellExecute = false,

				FileName = path,
				Arguments = string.Join(" ", arguments.Select(a => a.IndexOf(' ') >= 0 ? $"\"{a}\"" : a)),

				RedirectStandardOutput = true,
				RedirectStandardError = true,
			};

			using (logger.RecordTask($"Running: {path} {psi.Arguments}"))
			using (var process = new Process())
			{
				process.StartInfo = psi;

				process.OutputDataReceived += (sender, e) => RecordOutput(e.Data);
				process.ErrorDataReceived += (sender, e) => RecordOutput(e.Data);

				if (!process.Start())
				{
					throw new ProcessInvocationException($"Failed to start process \"{path}\".");
				}

				process.BeginOutputReadLine();
				process.BeginErrorReadLine();

				var tcs = new TaskCompletionSource<object>();
				process.Exited += (sender, e) => tcs.TrySetResult(null);
				process.EnableRaisingEvents = true;

				await tcs.Task.ConfigureAwait(false);
				process.WaitForExit(); // Flush I/O

				if (process.ExitCode != 0)
				{
					throw new InvalidOperationException($"{path} failed with exit code {process.ExitCode}.");
				}

				return process.ExitCode;
			}
		}

		void RecordOutput(string line)
		{
			if (!string.IsNullOrWhiteSpace(line))
			{
				logger.RecordInfo(line);
			}
		}
	}
}
