import WtgApp from '@components/WtgApp';
import WtgDrawer from '@components/WtgDrawer/WtgDrawer.vue';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel/WtgLabel.vue';
import WtgNavigationDrawer from '@components/WtgNavigationDrawer';
import WtgStatus from '@components/WtgStatus/WtgStatus.vue';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
// eslint-disable-next-line
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgDrawer', () => {
    test('its name is WtgDrawer', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('WtgDrawer');
    });

    test('it renders a WtgNavigationDrawer', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.findComponent(WtgNavigationDrawer).exists()).toBe(true);
    });

    test('it applies the required classes for supply', async () => {
        const wrapper = await mountComponentAsync();
        const drawer = wrapper.findComponent(WtgNavigationDrawer);
        expect(drawer.html()).toContain('wtg-drawer');
    });

    test('it is attached to the right edge of the screen', async () => {
        const wrapper = await mountComponentAsync();
        const drawer = wrapper.findComponent({ name: 'VNavigationDrawer' });
        expect(drawer.props('location')).toBe('right');
    });

    test('it overlays the content (but it does not show a scrim)', async () => {
        const wrapper = await mountComponentAsync();
        const drawer = wrapper.findComponent({ name: 'VNavigationDrawer' });
        expect(drawer.props('temporary')).toBe(true);
        expect(drawer.props('scrim')).toBe(false);
    });

    test('it can not be brought into view by swiping from the right, only programmatically', async () => {
        const wrapper = await mountComponentAsync();
        const drawer = wrapper.findComponent({ name: 'VNavigationDrawer' });
        expect(drawer.props('touchless')).toBe(true);
    });

    test('it passes the toolbar slot content to be rendered', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                toolbar: '<div>Toolbar Content</div>',
            },
        });

        expect(wrapper.html()).toContain('Toolbar Content');
    });

    test('it does not render tabs slot parent when no tabs are present', async () => {
        const wrapper = await mountComponentAsync();

        expect(wrapper.find('.wtg-drawer__tab__group').exists()).toBe(false);
    });

    test('it passes the tab group slot content to be rendered', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                tabs: '<div>Tab Group Content</div>',
            },
        });

        expect(wrapper.find('.wtg-drawer__tab__group').exists()).toBe(true);
        expect(wrapper.html()).toContain('Tab Group Content');
    });

    test('it passes the default slot content to be rendered', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                default: '<div>Content</div>',
            },
        });

        expect(wrapper.html()).toContain('Content');
    });

    test('it passes the actions slot content to be rendered', async () => {
        const wrapper = await mountComponentAsync({
            slots: {
                actions: '<div>Actions Content</div>',
            },
        });

        expect(wrapper.html()).toContain('Actions Content');
    });

    test('it renders a WtgLabel which shows title', async () => {
        const wrapper = await mountComponentAsync({
            props: {
                title: 'My Title',
            },
        });

        const label = wrapper.findComponent(WtgLabel);
        expect(label.text()).toBe('My Title');
    });

    test('it renders a WtgStatus which shows status', async () => {
        const currentStatus = {
            code: 'TEST',
            label: 'Active',
            sentiment: undefined,
        };
        const wrapper = await mountComponentAsync({
            props: {
                status: currentStatus,
            },
        });

        const status = wrapper.findComponent(WtgStatus);
        expect(status.props('label')).toBe(currentStatus.label);
    });

    test('when status is empty, it does not render a WtgStatus', async () => {
        const currentStatus = {
            code: '',
            label: '',
            sentiment: undefined,
        };
        const wrapper = await mountComponentAsync({
            props: {
                status: currentStatus,
            },
        });

        const status = wrapper.findComponent(WtgStatus);
        expect(status.exists()).toBeFalsy();
    });

    test('it has a close button which emits update:modelValue event when clicked', async () => {
        const wrapper = await mountComponentAsync({
            props: {
                dismissible: true,
            },
        });

        const closeButton = wrapper.findComponent(WtgIconButton);
        expect(closeButton.props('icon')).toBe('s-icon-close');
        expect(wrapper.emitted('update:modelValue')).toBeUndefined();

        await closeButton.trigger('click');

        expect(wrapper.emitted('update:modelValue')!.length).toBe(1);
    });

    test('when not dismissible, it does not render a close button', async () => {
        const wrapper = await mountComponentAsync({
            props: {
                dismissible: false,
            },
        });

        const closeButton = wrapper.findComponent(WtgIconButton);
        expect(closeButton.exists()).toBeFalsy();
    });

    test('when not visible, it applies the inert attribute to its content, (to release focus and be ignored by screen readers/playwright)', async () => {
        const wrapper = await mountComponentAsync({
            props: {
                modelValue: false,
            },
        });

        const container = wrapper.find<HTMLElement>('.wtg-drawer__container');
        expect(container.element.inert).toBe(true);
    });

    test('when visible, it does not apply the inert attribute to its content', async () => {
        const wrapper = await mountComponentAsync({
            props: {
                modelValue: true,
            },
        });

        const container = wrapper.find<HTMLElement>('.wtg-drawer__container');
        expect(container.element.inert).toBe(false);
    });

    describe('width', () => {
        test.each([250, 'auto'])('it configures the nav drawer with %s as width', async (inputWidth) => {
            const wrapper = await mountComponentAsync({
                props: {
                    width: inputWidth,
                },
            });

            const navigationDrawer = wrapper.findComponent(WtgNavigationDrawer);
            expect(navigationDrawer.props('width')).toBe(inputWidth);
        });
        test.each([250, 'auto'])('it updates the width when splitter is dragged', async (inputWidth) => {
            const wrapper = await mountComponentAsync({
                props: {
                    width: inputWidth,
                },
            });

            const navigationDrawer = wrapper.findComponent(WtgNavigationDrawer);
            expect(navigationDrawer.props('width')).toBe(inputWidth);

            const initialWidth = typeof inputWidth === 'number' ? inputWidth : 250;

            HTMLElement.prototype.getBoundingClientRect = jest.fn().mockImplementation(() => {
                return { width: initialWidth };
            });

            const mouseMoveEvent = new MouseEvent('mousemove', { buttons: 1 });
            const mouseUpEvent = new MouseEvent('mouseup');
            (mouseMoveEvent as any).pageX = 820;

            const splitter = wrapper.find('#splitter');
            await splitter.trigger('mousedown', { pageX: 800 });
            document.dispatchEvent(mouseMoveEvent);
            document.dispatchEvent(mouseUpEvent);

            await flushPromises();

            expect(navigationDrawer.props('width')).toBe(initialWidth - 20);

            const updateWidthEvents = wrapper.emitted('update:width');
            expect(updateWidthEvents!.length).toBeGreaterThan(0);

            const latestUpdateWidthEvent = updateWidthEvents!.pop();
            expect(latestUpdateWidthEvent![0]).toBe(initialWidth - 20);
        });
        test.each([250, 'auto'])(
            'it resets the width to the configured value when double clicking the splitter',
            async (inputWidth) => {
                const wrapper = await mountComponentAsync({
                    props: {
                        width: inputWidth,
                    },
                });

                const navigationDrawer = wrapper.findComponent(WtgNavigationDrawer);
                expect(navigationDrawer.props('width')).toBe(inputWidth);

                const mouseMoveEvent = new MouseEvent('mousemove', { buttons: 1 });
                const mouseUpEvent = new MouseEvent('mouseup');
                (mouseMoveEvent as any).pageX = 820;

                const splitter = wrapper.find('#splitter');
                await splitter.trigger('mousedown', { pageX: 800 });
                document.dispatchEvent(mouseMoveEvent);
                document.dispatchEvent(mouseUpEvent);
                await flushPromises();
                await splitter.trigger('mousedown');
                await flushPromises();

                expect(navigationDrawer.props('width')).toBe(inputWidth);

                const updateWidthEvents = wrapper.emitted('update:width');
                expect(updateWidthEvents!.length).toBeGreaterThan(0);

                const latestUpdateWidthEvent = updateWidthEvents!.pop();
                expect(latestUpdateWidthEvent![0]).toBe(inputWidth);
            }
        );
    });

    async function mountComponentAsync({ props = {}, slots = {} } = {}) {
        const wrapper = mount(
            {
                template:
                    '<wtg-app><wtg-drawer v-bind="$attrs"><template v-for="(_, name) in $slots" v-slot:[name]="slotData"><slot :name="name" v-bind="slotData" /></template></wtg-drawer></wtg-app>',
                components: { WtgApp, WtgDrawer },
            },
            {
                propsData: {
                    ...props,
                },
                slots,
                global: {
                    plugins: [wtgUi],
                },
            }
        );
        await flushPromises();
        return wrapper.findComponent(WtgDrawer);
    }
});
