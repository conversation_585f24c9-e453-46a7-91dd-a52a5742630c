import { Cluster, ClusterStats, De<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@googlemaps/markerclusterer';
import ColorVariant from '../../../theme/colors/ColorVariant';

export class WtgGoogleMapRenderer extends DefaultRenderer {
    constructor(themeColors?: ColorVariant | undefined) {
        super();
        this._themeColors = themeColors;
        if (
            this._themeColors &&
            typeof this._themeColors.primary === 'string' &&
            this._themeColors.primary.length > 0
        ) {
            const svg =
                window.btoa(`<svg fill="${this._themeColors?.primary}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240">
                     <circle cx="120" cy="120" opacity=".6" r="70" />
                     <circle cx="120" cy="120" opacity=".3" r="90" />
                     <circle cx="120" cy="120" opacity=".2" r="110" />
                     <circle cx="120" cy="120" opacity=".1" r="130" />
                     </svg>`);

            this._iconUrl = `data:image/svg+xml;base64,${svg}`;
        }
    }

    private readonly _themeColors: ColorVariant | undefined;
    private readonly _iconUrl: string | undefined;

    render(cluster: Cluster, stats: ClusterStats, map: any): Marker {
        if (!this._iconUrl) {
            return super.render(cluster, stats, map);
        }
        const newMarker = new (window as any).google.maps.Marker();
        newMarker.setPosition(cluster.position);
        newMarker.setIcon({
            url: this._iconUrl,
            scaledSize: new (window as any).google.maps.Size(45, 45),
        });
        newMarker.setLabel({
            text: String(cluster.count),
            color: 'white',
            fontSize: '12px',
        });
        newMarker.setZIndex(Number((window as any).google.maps.Marker.MAX_ZINDEX) + cluster.count);

        return newMarker;
    }
}
