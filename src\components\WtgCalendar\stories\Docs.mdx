import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgCalendar from './WtgCalendar.stories.ts';

<Meta of={WtgCalendar} />

<div className="component-header">
    <h1>Calendar</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td></td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

<div className="banner-warning">

    <div>
        <h4>⚠️ Please note</h4>
        <p>Calendar is included in the component library to aid our transition from Vue 2 to Vue 3. It's future use should be carefully considered and most likely it should not be used in Supply applications.</p>
    </div>

</div>

<Canvas className="canvas-preview" of={WtgCalendar.Default} />

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
