# This script copies required wtg-components files to wtg-material-ui

$ErrorActionPreference = 'Stop'

$cwd = $PSScriptRoot
Push-Location $cwd
Write-Output "Working directory set to '$cwd'"

$src_component_dir = '..\src\components\';
$tgt_component_dir = '..\..\..\WTG.WebUI\src\wtg-ui-vue\packages\wtg-material-ui\src\supply\components\';

$files = @(
    @('WtgAvatar','WtgAvatar'),
    @('WtgButton','WtgButton'),
    @('WtgButton','WtgButton'),
    @('WtgCheckbox','WtgCheckbox'),
    @('WtgColorField','WtgColorField'),
    @('WtgChip','WtgChip'),
    @('WtgDateField','WtgDateField'),
    @('WtgDateTimeField','WtgDateTimeField'),
    @('WtgDropdownButton','WtgDropdownButton'),
    @('WtgDurationField','WtgDurationField'),
    @('WtgIcon','WtgIcon'),
    @('WtgIconButton','WtgIconButton'),
    @('WtgLabel','WtgLabel'),
    @('WtgLoader','WtgLoader'),
    @('WtgNumberField','WtgNumberField'),
    @('WtgPanel','WtgPanel'),
    @('WtgPanel','WtgPanelHeader'),
    @('WtgPasswordField','WtgPasswordField'),
    @('WtgPopover','WtgPopover')
    @('WtgRadio','WtgRadio'),
    @('WtgRadio','WtgRadioGroup'),
    @('WtgSearchField','WtgSearchField'),
    @('WtgSegmentedControl','WtgSegmentedControl'),
    @('WtgSwitch','WtgSwitch'),
    @('WtgTextField','WtgTextField'),
    @('WtgTimeField','WtgTimeField'),
    @('WtgTimePicker','WtgTimePicker')
)

for ($i = 0; $i -le ($files.length - 1); $i += 1) {
    $folder = $files[$i][0]
    $file = $files[$i][1]
    $src = [System.IO.Path]::Combine($cwd, "$src_component_dir$folder\$file.vue")
    $srcIndex = [System.IO.Path]::Combine($cwd, "$src_component_dir$folder\index.ts")
    Write-Output "Copying src $src..."
    $tgtDir = [System.IO.Path]::Combine($cwd, "$tgt_component_dir$folder")
    Write-Output "Create tgtDir $tgtDir..."
    New-Item -ItemType Directory -Force -Path $tgtDir | Out-Null
    Copy-Item -Path $src -Destination $tgtDir
    Copy-Item -Path $srcIndex -Destination $tgtDir
}

Pop-Location
Exit 0
