<template>
    <MastheadNotifications v-if="showNotifications && actions.length === 1" />
    <WtgIconButton
        v-else-if="application.searchProvider && actions.length === 1"
        icon="s-icon-search"
        variant="ghost"
        :aria-label="application.ariaLabels.searchDialogIcon"
        aria-haspopup="dialog"
        :aria-expanded="searchOverlay ? 'true' : 'false'"
        data-testid="search-button"
        @click="$emit('toggle-search')"
    />
    <template v-else>
        <WtgNotifications v-model="open" :notifications="notifications" hide-button />
        <WtgPopupMenu
            :actions="actions"
            left
            offset-y
            :min-width="$wtgMaterialUi.breakpoint.smAndUp ? 'auto' : '75vw'"
            nudge-bottom="8px"
            @action="onAction"
        >
            <template #activator="{ props: activatorProps }">
                <WtgIconButton
                    class="mobile-masthead-menu-button"
                    variant="ghost"
                    icon="s-icon-menu-meatballs"
                    :tooltip="formatCaption('drawer.entityActions')"
                    :aria-label="!!ariaLabels && ariaLabels.taskActionsOverflowIcon"
                    aria-haspopup="menu"
                    :aria-expanded="open ? 'true' : 'false'"
                    v-bind="activatorProps"
                >
                    <WtgNotification :model-value="notifications.length > 0">
                        <WtgIcon>s-icon-menu-meatballs</WtgIcon>
                    </WtgNotification>
                </WtgIconButton>
            </template>
        </WtgPopupMenu>
    </template>
</template>

<script setup lang="ts">
import {
    WtgFrameworkAriaLabels,
    WtgFrameworkDocumentsMenuItem,
    WtgFrameworkNotification,
    WtgFrameworkTask,
    WtgFrameworkTaskGenericAction,
    WtgFrameworkTaskGenericActionPlacement,
    WtgFrameworkTaskStandardAction,
} from '@components/framework/types';
import WtgIcon from '@components/WtgIcon';
import WtgIconButton from '@components/WtgIconButton';
import { WtgActionItemData } from '@components/WtgMenu';
import WtgPopupMenu from '@components/WtgMenuBar/popup';
import WtgNotification from '@components/WtgNotification';
import WtgNotifications from '@components/WtgNotifications';
import { useApplication } from '@composables/application';
import { useLocale } from '@composables/locale';
import { computed, PropType, reactive } from 'vue';
import MastheadNotifications from '../MastheadNotifications';

const props = defineProps({
    task: {
        type: Object as PropType<WtgFrameworkTask>,
        default: null,
    },
    searchOverlay: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits<{ 'toggle-search': [] }>();

const application = useApplication();
const { formatCaption } = useLocale();

const ariaLabels = computed((): WtgFrameworkAriaLabels => {
    return application.ariaLabels;
});

const currentTask = computed((): WtgFrameworkTask => {
    return props.task ?? new WtgFrameworkTask();
});

const genericActions = computed((): WtgFrameworkTaskGenericAction[] => {
    return currentTask.value.genericActions;
});

const transitionTasks = computed((): WtgFrameworkTaskGenericAction[] => {
    const filteredActions = genericActions.value.filter(
        (action) => action.placement === WtgFrameworkTaskGenericActionPlacement.TaskAction
    );
    return filteredActions;
});

const taskActions = computed((): WtgActionItemData[] => {
    const taskActions: WtgActionItemData[] = [];
    transitionTasks.value.forEach((task) => taskActions.push(mapTaskActionToActions(task, task.icon)));
    if (currentTask.value.showEDocsAction.visible) {
        taskActions.push(mapTaskActionToActions(currentTask.value.showEDocsAction, 's-icon-eDocs'));
    }
    if (currentTask.value.documents.visible) {
        const item = reactive({
            actions: [],
            id: 'document',
            caption: currentTask.value.documents.caption,
            icon: '$fileDocument',
            loadDocumentsAsync: currentTask.value.documents.loadDocumentsAsync,
            loading: false,
            submenu: true,
        });
        taskActions.push(item);
    }
    if (currentTask.value.showLogsAction.visible) {
        taskActions.push(mapTaskActionToActions(currentTask.value.showLogsAction, 's-icon-logs'));
    }
    if (currentTask.value.showMessagesAction.visible) {
        taskActions.push(mapTaskActionToActions(currentTask.value.showMessagesAction, 's-icon-chat'));
    }
    if (currentTask.value.showNotesAction.visible) {
        taskActions.push(mapTaskActionToActions(currentTask.value.showNotesAction, 's-icon-notes'));
    }
    if (currentTask.value.showWorkflowActions.visible) {
        taskActions.push({
            caption: currentTask.value.showWorkflowActions.caption,
            icon: 's-icon-workflow',
            submenu: true,
            actions: currentTask.value.showWorkflowActions.menuItems.map((item): WtgActionItemData => {
                return {
                    caption: item.caption,
                    click: (): void => item.onInvoke(),
                };
            }),
        });
    }
    return taskActions;
});

const open = computed({
    get(): boolean {
        return application.notificationsDrawer.visible;
    },
    set(value: boolean): void {
        application.notificationsDrawer.visible = value;
    },
});

const showNotifications = computed((): boolean => {
    return currentTask.value.showNotifications ?? false;
});

const navigationActions = computed((): WtgActionItemData[] => {
    return currentTask.value.others.visible
        ? [
              {
                  caption: formatCaption('navigation.nextRecord'),
                  click: (): void => currentTask.value.others.onNext(),
                  isDisabled: !currentTask.value.others.hasNext,
              },
              {
                  caption: formatCaption('navigation.previousRecord'),
                  click: (): void => currentTask.value.others.onPrevious(),
                  isDisabled: !currentTask.value.others.hasPrevious,
              },
          ]
        : [];
});

const actions = computed(() => {
    const additionalActions: WtgActionItemData[] = [];
    if (showNotifications.value) {
        additionalActions.push({
            caption: notifications.value.length
                ? `${application.captions.alerts} (${notifications.value.length})`
                : application.captions.alerts,
            icon: 's-icon-status-warning',
            notification: !!notifications.value.length,
            click: () =>
                application.notificationsDrawer.visible
                    ? application.notificationsDrawer.close()
                    : application.notificationsDrawer.open(),
        });
    }

    if (application.searchProvider) {
        additionalActions.push({
            caption: 'Search',
            icon: '$search',
            click: () => emit('toggle-search'),
        });
    }

    const result = [...taskActions.value, ...additionalActions];

    if (result.length && navigationActions.value.length) {
        result.push({ divider: true, caption: '' });
    }

    result.push(...navigationActions.value);

    return result;
});

let nextId = 1;
const onAction = async (action: WtgFrameworkDocumentsMenuItem): Promise<void> => {
    if (action.loadDocumentsAsync) {
        action.loading = true;
        const documentItems = await action.loadDocumentsAsync();
        action.actions = documentItems.map((item) =>
            reactive({
                actions: [],
                id: 'doc-' + nextId++,
                loading: false,
                ...item,
            })
        );

        action.loading = false;
    } else if (action.click) {
        action.click();
    }
};

function mapTaskActionToActions(
    action: WtgFrameworkTaskStandardAction | WtgFrameworkTaskGenericAction,
    icon?: string
): WtgActionItemData {
    return {
        caption: action.caption,
        click: (): void => action.onInvoke(),
        icon: icon,
    };
}

const notifications = computed((): WtgFrameworkNotification[] => {
    return currentTask.value.notifications ?? [];
});
</script>

<style lang="scss">
.mobile-masthead-menu-button.wtg-button > .wtg-button__content {
    overflow: visible;
}
</style>
