<template>
    <div v-if="isRealAddressOnlyMode">
        <WtgLayoutGrid>
            <WtgTextField
                v-if="!hideStreetDetails"
                required
                :model-value="popupAddress.company"
                :label="companyCaption"
                readonly
                :sentiment="getValidationStates(validationPropertyMapping?.company)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.company)?.messages"
            />
            <WtgAddressField
                search-by-address
                required
                :model-value="popupAddress.guid"
                :show-selected-address="false"
                :item-provider="dataProvider?.companyProvider"
                :readonly="readonly"
                return-object
                :label="streetCaption"
                :sentiment="getValidationStates(validationPropertyMapping?.street)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.street)?.messages"
                @update:model-value="onAddressSelected"
            />
            <WtgTextField
                v-if="!hideStreetDetails"
                :model-value="popupAddress.streetAlt"
                :label="streetAltCaption"
                readonly
                :sentiment="getValidationStates(validationPropertyMapping?.streetAlt)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.streetAlt)?.messages"
            />
            <WtgTextField
                required
                :model-value="popupAddress.city"
                :label="cityCaption"
                columns="col-6"
                readonly
                :sentiment="getValidationStates(validationPropertyMapping?.city)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.city)?.messages"
            />
            <WtgTextField
                :model-value="popupAddress.postcode"
                :label="postcodeCaption"
                columns="col-6"
                readonly
                :sentiment="getValidationStates(validationPropertyMapping?.postcode)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.postcode)?.messages"
            />
            <WtgTextField
                :model-value="popupAddress.state"
                :label="stateCaption"
                columns="col-6"
                readonly
                :sentiment="getValidationStates(validationPropertyMapping?.state)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.state)?.messages"
            />
            <WtgSearchField
                v-if="!hideCountry"
                required
                :model-value="popupAddress.countryCode"
                :label="countryCaption"
                columns="col-6"
                :item-provider="dataProvider?.countryProvider"
                readonly
                :sentiment="getValidationStates(validationPropertyMapping?.countryCode)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.countryCode)?.messages"
            />
            <WtgTextArea
                :model-value="popupAddress.additionalInfo"
                :label="formatCaption('jobAddress.additionalInfoLabel')"
                columns="col-12"
                :sentiment="
                    getValidationStates(validationPropertyMapping?.additionalInfo)?.error ? 'critical' : undefined
                "
                :messages="getValidationStates(validationPropertyMapping?.additionalInfo)?.messages"
                readonly
            />
            <div v-if="!hideMapLocation">
                <WtgLabel display="block" typography="text-sm-default">
                    {{ mapLabel }}
                </WtgLabel>
                <WtgGoogleMap
                    :api-key="apiKey"
                    :markers="mapMarkers"
                    :options="mapOptions"
                    height="200px"
                    @api-loaded="updateLatLng"
                ></WtgGoogleMap>
            </div>
            <WtgLabel typography="title-sm-default" display="block">
                {{ contactHeader }}
            </WtgLabel>
            <WtgSearchField
                :model-value="popupContact.name"
                :label="contactCaption"
                item-text="caption"
                clearable
                :item-provider="dataProvider?.contactProvider"
                :readonly="readonly"
                :placeholder="contactPlaceholderCaption"
                columns="col-6"
                :sentiment="getValidationStates(validationPropertyMapping?.name)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.name)?.messages"
                @update:model-value="onContactSelected"
            />
            <WtgTextField
                :model-value="popupContact.name ? popupContact.email : popupAddress.email"
                :label="emailCaption"
                columns="col-6"
                :sentiment="getValidationStates(validationPropertyMapping?.email)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.email)?.messages"
                readonly
            />
            <WtgTextField
                :model-value="popupContact.name ? popupContact.phone : popupAddress.phone"
                :label="phoneCaption"
                columns="col-6"
                :sentiment="getValidationStates(validationPropertyMapping?.phone)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.phone)?.messages"
                readonly
            />
            <WtgTextField
                :model-value="popupContact.name ? popupContact.mobile : popupAddress.mobile"
                :label="mobileCaption"
                columns="col-6"
                :sentiment="getValidationStates(validationPropertyMapping?.mobile)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.mobile)?.messages"
                readonly
            />
        </WtgLayoutGrid>
    </div>
    <div v-else>
        <WtgLayoutGrid>
            <WtgTextField
                v-if="!hideStreetDetails"
                :model-value="popupAddress.company"
                :label="companyCaption"
                :readonly="readonly"
                required
                :sentiment="getValidationStates(validationPropertyMapping?.company)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.company)?.messages"
                @update:model-value="updateCompany"
            />
            <WtgAddressField
                search-by-address
                required
                :show-selected-address="false"
                :item-provider="dataProvider?.companyProvider"
                :model-value="popupAddress.guid"
                :address-override-value="popupAddress"
                :readonly="readonly"
                return-object
                allow-free-text-address-entry
                :label="streetCaption"
                :sentiment="getValidationStates(validationPropertyMapping?.street)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.street)?.messages"
                @update:model-value="onAddressSelected"
            />
            <WtgTextField
                v-if="!hideStreetDetails"
                :model-value="popupAddress.streetAlt"
                :readonly="readonly"
                :label="streetAltCaption"
                :sentiment="getValidationStates(validationPropertyMapping?.streetAlt)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.streetAlt)?.messages"
                @update:model-value="(value) => updateAddressDetails('streetAlt', value)"
            />
            <WtgTextField
                :model-value="popupAddress.city"
                :label="cityCaption"
                columns="col-6"
                :readonly="readonly"
                required
                :sentiment="getValidationStates(validationPropertyMapping?.city)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.city)?.messages"
                @update:model-value="(value) => updateAddressDetails('city', value)"
            />
            <WtgTextField
                :model-value="popupAddress.postcode"
                :label="postcodeCaption"
                columns="col-6"
                :readonly="readonly"
                :sentiment="getValidationStates(validationPropertyMapping?.postcode)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.postcode)?.messages"
                @update:model-value="(value) => updateAddressDetails('postcode', value)"
            />
            <WtgTextField
                :model-value="popupAddress.state"
                :label="stateCaption"
                columns="col-6"
                :readonly="readonly"
                :sentiment="getValidationStates(validationPropertyMapping?.state)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.state)?.messages"
                @update:model-value="(value) => updateAddressDetails('state', value)"
            />
            <WtgSearchField
                v-if="!hideCountry"
                :model-value="popupAddress.countryCode"
                :label="countryCaption"
                columns="col-6"
                :readonly="readonly"
                required
                :item-provider="dataProvider?.countryProvider"
                :sentiment="getValidationStates(validationPropertyMapping?.countryCode)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.countryCode)?.messages"
                @update:model-value="(value) => updateAddressDetails('countryCode', value)"
            />
            <WtgTextArea
                :model-value="popupAddress.additionalInfo"
                :label="formatCaption('jobAddress.additionalInfoLabel')"
                columns="col-12"
                :readonly="readonly"
                :sentiment="
                    getValidationStates(validationPropertyMapping?.additionalInfo)?.error ? 'critical' : undefined
                "
                :messages="getValidationStates(validationPropertyMapping?.additionalInfo)?.messages"
                @update:model-value="(value) => updateAddressDetails('additionalInfo', value)"
            />
            <div v-if="!hideMapLocation">
                <WtgLabel display="block" typography="text-sm-default">
                    {{ mapLabel }}
                </WtgLabel>
                <WtgGoogleMap
                    :api-key="apiKey"
                    :markers="mapMarkers"
                    :options="mapOptions"
                    height="200px"
                    @api-loaded="updateLatLng"
                ></WtgGoogleMap>
            </div>
            <WtgLabel typography="title-sm-default" display="block">
                {{ contactHeader }}
            </WtgLabel>
            <WtgSearchField
                :model-value="popupContact.name"
                :label="contactCaption"
                :item-provider="dataProvider?.contactProvider"
                clearable
                columns="col-6"
                allow-free-text-entry
                :placeholder="contactPlaceholderCaption"
                :readonly="readonly"
                :sentiment="getValidationStates(validationPropertyMapping?.name)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.name)?.messages"
                @update:model-value="onContactSelected"
            />
            <WtgTextField
                :model-value="popupContact.name || popupContactSelected ? popupContact.email : popupAddress.email"
                :label="emailCaption"
                columns="col-6"
                :readonly="readonly"
                :sentiment="getValidationStates(validationPropertyMapping?.email)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.email)?.messages"
                @update:model-value="(value) => updateContactDetails('email', value)"
            />
            <WtgTextField
                :model-value="popupContact.name || popupContactSelected ? popupContact.phone : popupAddress.phone"
                :label="phoneCaption"
                columns="col-6"
                :readonly="readonly"
                :sentiment="getValidationStates(validationPropertyMapping?.phone)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.phone)?.messages"
                @update:model-value="(value) => updateContactDetails('phone', value)"
            />
            <WtgTextField
                :model-value="popupContact.name || popupContactSelected ? popupContact.mobile : popupAddress.mobile"
                :label="mobileCaption"
                columns="col-6"
                :readonly="readonly"
                :sentiment="getValidationStates(validationPropertyMapping?.mobile)?.error ? 'critical' : undefined"
                :messages="getValidationStates(validationPropertyMapping?.mobile)?.messages"
                @update:model-value="(value) => updateContactDetails('mobile', value)"
            />
        </WtgLayoutGrid>
    </div>
</template>

<script setup lang="ts">
import WtgAddressField, { AddressLookupSearchContent, ContactLookupSearchContent } from '@components/WtgAddressField';
import WtgGoogleMap from '@components/WtgGoogleMap';
import jobAddressStyles from '@components/WtgGoogleMap/utils/jobAddressStyles';
import WtgLabel from '@components/WtgLabel';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgSearchField from '@components/WtgSearchField';
import WtgTextArea from '@components/WtgTextArea';
import WtgTextField from '@components/WtgTextField';
import { ValidationState } from '@composables';
import { setJobAddressOrg } from '@composables/application';
import { useLocale } from '@composables/locale';
import { computed, PropType, ref, watch } from 'vue';
import { WtgJobAddressProvider } from '../../types';

const props = defineProps({
    addressHeader: {
        type: String,
        default: '',
    },
    apiKey: {
        type: String,
        default: '',
    },
    companyHeader: {
        type: String,
        default: '',
    },
    contactHeader: {
        type: String,
        default: '',
    },
    hideCountry: {
        type: Boolean,
        default: false,
    },
    hideStreetDetails: {
        type: Boolean,
        default: false,
    },
    isRealAddressOnlyMode: {
        type: Boolean,
        default: false,
    },
    dataProvider: {
        type: Object as PropType<WtgJobAddressProvider>,
        default: undefined,
    },
    popupAddress: {
        type: Object as PropType<AddressLookupSearchContent>,
        default: (): {} => ({}),
    },
    popupContact: {
        type: Object as PropType<ContactLookupSearchContent>,
        default: (): {} => ({}),
    },
    popupContactSelected: {
        type: Boolean,
        default: false,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    hideMapLocation: {
        type: Boolean,
        default: false,
    },
    validationStates: {
        type: Array as PropType<Readonly<ValidationState>[]>,
        default: () => [],
    },
    validationPropertyMapping: {
        type: Object,
        default: undefined,
    },
});

const { formatCaption } = useLocale();
const latLng = ref<{ lat: number; lng: number } | undefined>(undefined);

const contactCaption = computed((): string => {
    return formatCaption('jobAddress.contactFieldLabel');
});

const contactPlaceholderCaption = computed((): string => {
    return formatCaption('jobAddress.contactPlaceholder');
});
const emailCaption = computed((): string => {
    return formatCaption('jobAddress.emailFieldLabel');
});
const phoneCaption = computed((): string => {
    return formatCaption('jobAddress.phoneFieldLabel');
});
const mobileCaption = computed((): string => {
    return formatCaption('jobAddress.mobileFieldLabel');
});
const companyCaption = computed((): string => {
    return formatCaption('jobAddress.companyNameLabel');
});
const countryCaption = computed((): string => {
    return formatCaption('jobAddress.countryFieldLabel');
});
const cityCaption = computed((): string => {
    return formatCaption('jobAddress.cityFieldLabel');
});
const streetAltCaption = computed((): string => {
    return formatCaption('jobAddress.address2FieldLabel');
});
const streetCaption = computed((): string => {
    return formatCaption('jobAddress.addressFieldLabel');
});
const stateCaption = computed((): string => {
    return formatCaption('jobAddress.stateFieldLabel');
});

const postcodeCaption = computed((): string => {
    return formatCaption('jobAddress.postcodeFieldLabel');
});

const mapOptions = computed(() => ({
    center: latLng.value ?? { lat: 0, lng: 0 },
    panControl: false,
    streetViewControl: false,
    zoom: latLng.value ? 15 : 0,
    zoomControl: true,
    styles: jobAddressStyles,
}));

const mapMarkers = computed(() => [{ position: latLng.value }]);

const updateLatLng = () => {
    props.dataProvider?.setLatLngAsync(props.popupAddress, latLng);
};

const jobAddressOrg = setJobAddressOrg(ref<string | undefined>(props.popupAddress.company));
const timeOut = ref<number | undefined>(undefined);

watch(
    () => props.popupAddress,
    () => {
        window.clearTimeout(timeOut.value);
        timeOut.value = window.setTimeout(updateLatLng, 200);
    },
    {
        deep: true,
    }
);

watch(
    () => props.popupAddress.company,
    (value) => {
        jobAddressOrg.value = value;
    }
);

const emit = defineEmits<{
    'address-changed': [value: AddressLookupSearchContent];
    'company-changed': [value: AddressLookupSearchContent];
    'contact-changed': [value: ContactLookupSearchContent];
}>();

const updateAddress = (value: AddressLookupSearchContent | undefined) => {
    emit('address-changed', value || {});
};

const updateAddressDetails = (property: keyof AddressLookupSearchContent, value: string) => {
    if (property === 'company') {
        updateAddress({ ...props.popupAddress, company: value, guid: undefined, companyGuid: undefined });
    } else {
        updateAddress({ ...props.popupAddress, [property]: value });
    }
};

const updateCompany = (value: string) => {
    emit('company-changed', { ...props.popupAddress, company: value, guid: undefined, companyGuid: undefined });
};

const onAddressSelected = (value: AddressLookupSearchContent | string | undefined) => {
    updateAddress(value as AddressLookupSearchContent | undefined);
    if (props.isRealAddressOnlyMode) {
        updateContact(undefined);
    }
};

const updateContact = (value: ContactLookupSearchContent | undefined) => {
    emit('contact-changed', value || {});
};

const updateContactDetails = (property: keyof ContactLookupSearchContent, value: string) => {
    if (props.popupContact.name || props.popupContactSelected) {
        updateContact({ ...props.popupContact, [property]: value });
    } else {
        updateContact({ ...props.popupAddress, [property]: value });
    }
};

const onContactSelected = async (value: string) => {
    if (!value && props.isRealAddressOnlyMode) {
        updateContact(undefined);
        return;
    }

    const item = await props.dataProvider?.contactProvider.getItemForValueAsync(value);
    if (item || props.isRealAddressOnlyMode) {
        updateContact(item?.contact);
    } else {
        updateContact({ ...props.popupContact, name: value });
    }
};

const getValidationStates = (targetKey: string | undefined) => {
    if (!targetKey) {
        return undefined;
    }

    const messages = props.validationStates.find((message: ValidationState) => {
        if (message.targetKey === targetKey || message.targetProperty === targetKey) {
            return message;
        }
        return undefined;
    });

    return messages;
};

const mapLabel = computed(() => formatCaption('jobAddress.mapLabel'));
</script>
