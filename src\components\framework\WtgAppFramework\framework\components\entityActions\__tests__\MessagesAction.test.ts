import { WtgFrameworkTask, WtgFrameworkTaskStandardAction } from '@components/framework/types';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgUi from '../../../../../../../WtgUi';
import MessagesAction from '../MessagesAction.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('messages-action', () => {
    let task: WtgFrameworkTask;
    let el: HTMLElement;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        task = new WtgFrameworkTask();
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is MessagesAction', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('MessagesAction');
    });

    describe('when given a showLogsAction', () => {
        beforeEach(() => {
            task.showMessagesAction = {
                visible: false,
                caption: 'Messages',
                label: 'Messages',
                onInvoke: jest.fn(),
            };
        });

        test('its does not render a button when the action visible is false', () => {
            const wrapper = mountComponent({ propsData: { task } });
            const buttons = wrapper.findAllComponents({ name: 'WtgButton' });
            expect(buttons.length).toBe(0);
        });

        test('its does render a button with an icon when the action visible is true', () => {
            task.showMessagesAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.exists()).toBe(true);
        });

        test('it renders the button with aria-haspopup="dialog" attribute', () => {
            task.showMessagesAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(button.attributes('aria-haspopup')).toBe('dialog');
        });

        test('the button calls the action onInvoke method when ed', async () => {
            task.showMessagesAction.visible = true;
            const wrapper = mountComponent({ propsData: { task } });
            const button = wrapper.findComponent({ name: 'WtgButton' });
            await button.trigger('click');

            expect(task.showMessagesAction.onInvoke).toBeCalledTimes(1);
        });

        describe('on a large screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = false;
                task.showMessagesAction.visible = true;
            });

            test('it display as a ghost button', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().variant).toBe('ghost');
                expect(button.text()).toBe('Messages');
            });

            test('it displays the button with a leading icon', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgButton' });
                expect(button.props().leadingIcon).toBe('s-icon-chat');
            });
        });

        describe('on a small screen', () => {
            beforeEach(() => {
                wtgUi.breakpoint.mdAndDown = true;
                task.showMessagesAction.visible = true;
            });

            test('it display as a icon button with a tooltip', () => {
                const wrapper = mountComponent({ propsData: { task } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.props().icon).toBe('s-icon-chat');
                expect(button.props().tooltip).toBe('Messages');
            });
        });
    });

    describe('Property tests', () => {
        test('when props is passed', () => {
            const wrapper: VueWrapper<any> = mountComponent({ propsData: { task: task } });
            expect(wrapper.vm.action).toStrictEqual(task.showMessagesAction);
        });

        test('when props is not passed and application.currentTask is not there', () => {
            const defaultValue: WtgFrameworkTaskStandardAction = {
                visible: false,
                caption: 'Messages',
                label: 'Messages',
                onInvoke: (): void => undefined,
            };
            const wrapper: VueWrapper<any> = mountComponent();
            expect(wrapper.vm.action.caption).toStrictEqual(defaultValue.caption);
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(MessagesAction, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
