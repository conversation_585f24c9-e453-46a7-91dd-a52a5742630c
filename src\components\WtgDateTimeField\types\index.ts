export interface WtgDateTimeFormatter {
    formatDate(date: string, showTimeZone?: boolean): string;
    parseDate(date: string, showTimeZone?: boolean): string | null;
    values(isoDate: string, showTimeZone?: boolean): WtgDateTimeFormat;
    today(showTimeZone?: boolean): string;
}

export interface WtgDateTimeFormat {
    year: number;
    month: number;
    day: number;
    hour: number;
    minutes: number;
    seconds: number;
    isValid: boolean;
    timeZone?: string;
}
