<template>
    <WtgDialog
        v-model="model"
        :eager="eager"
        :height="height"
        :fullscreen="fullscreen"
        :max-height="maxHeight"
        :max-width="maxWidth"
        :min-height="minHeight"
        :min-width="minWidth"
        :persistent="persistent"
        :transition="transition"
        :width="width"
    >
        <template #activator="{ props: activatorProps }: { props: Record<string, any> }">
            <slot name="activator" :props="activatorProps"></slot>
        </template>
        <template #default="{ isActive }: { isActive: Ref<Boolean> }">
            <div class="wtg-alert-modal__container">
                <div :class="computedModalHeaderClass">
                    <div class="wtg-alert-modal__icon">
                        <WtgIcon>{{ computedIcon }}</WtgIcon>
                    </div>
                    <div class="wtg-alert-modal__title">{{ props.title }}</div>
                    <div v-if="!props.persistent" class="wtg-alert-modal__close">
                        <WtgIconButton
                            :tooltip="formatCaption('dialog.cancel')"
                            icon="s-icon-close"
                            variant="ghost"
                            @click="model = false"
                        />
                    </div>
                </div>
                <div class="wtg-alert-modal__content">
                    <slot></slot>
                </div>
                <div class="wtg-alert-modal__content-actions">
                    <slot name="actions" :is-active="isActive"></slot>
                </div>
            </div>
        </template>
    </WtgDialog>
</template>

<script setup lang="ts">
import WtgDialog, { modalProps } from '@components/WtgDialog';
import { WtgIcon } from '@components/WtgIcon';
import { WtgIconButton } from '@components/WtgIconButton';
import { useLocale } from '@composables/locale';
import { computed, PropType, Ref } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * The sentiment or visual style of the alert modal.
     * Options include 'error', 'warning', 'success', 'info', 'question', or 'message-error'.
     */
    sentiment: {
        type: String as PropType<'error' | 'warning' | 'success' | 'info' | 'question' | 'message-error'>,
        default: 'success',
    },

    ...modalProps({ maxWidth: '443px' }),
});

const model = defineModel<boolean>({ default: false });

//
// Composables
//
const { formatCaption } = useLocale();

//
// Computed
//
const computedModalHeaderClass = computed(() => ({
    'wtg-alert-modal__header': true,
    'wtg-alert-modal--success': props.sentiment === 'success',
    'wtg-alert-modal--warning': props.sentiment === 'warning',
    'wtg-alert-modal--error': props.sentiment === 'error',
    'wtg-alert-modal--info': props.sentiment === 'info',
    'wtg-alert-modal--question': props.sentiment === 'question',
    'wtg-alert-modal--message-error': props.sentiment === 'message-error',
}));

const computedIcon = computed(() => {
    let icon = undefined;
    switch (props.sentiment) {
        case 'error':
            icon = 's-icon-status-critical';
            break;
        case 'warning':
            icon = 's-icon-status-warning';
            break;
        case 'success':
            icon = 's-icon-status-success';
            break;
        case 'question':
            icon = 's-icon-message-alert';
            break;
        case 'message-error':
            icon = 's-icon-email';
            break;
        default:
            icon = 's-icon-info-circle';
            break;
    }
    return icon;
});
</script>

<style lang="scss">
.wtg-alert-modal__container {
    background-color: var(--s-neutral-bg-default);
    border-radius: var(--s-radius-m);
    border: 1px solid var(--s-neutral-border-weak-default);
    color: var(--s-neutral-txt-default);
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.wtg-alert-modal__icon {
    display: flex;
    align-self: center;
}

.wtg-alert-modal__header {
    display: flex;
    padding: var(--s-padding-null) var(--s-padding-xl);
    border-start-start-radius: var(--s-radius-m);
    border-start-end-radius: var(--s-radius-m);
    gap: var(--s-spacing-m);
    align-items: center;
    border: 1px solid var(--s-neutral-border-weak-default);
    border-style: none none solid none;

    > .wtg-alert-modal__title {
        padding: var(--s-padding-l) var(--s-padding-null);
        font: var(--s-title-sm-default);
    }

    > .wtg-alert-modal__close {
        display: flex;
        flex-grow: 1;
        justify-content: flex-end;
    }

    &.wtg-alert-modal--success {
        color: var(--s-success-txt-default);
        background-color: var(--s-success-bg-weak-default);

        > .wtg-alert-modal__icon {
            color: var(--s-success-icon-default);
        }
    }

    &.wtg-alert-modal--warning {
        color: var(--s-warning-txt-default);
        background-color: var(--s-warning-bg-weak-default);

        > .wtg-alert-modal-icon {
            color: var(--s-warning-icon-default);
        }
    }

    &.wtg-alert-modal--error {
        color: var(--s-error-txt-default);
        background-color: var(--s-error-bg-weak-default);

        > .wtg-alert-modal-icon {
            color: var(--s-error-icon-default);
        }
    }

    &.wtg-alert-modal--info {
        color: var(--s-info-txt-default);
        background-color: var(--s-info-bg-weak-default);

        > .wtg-alert-modal-icon {
            color: var(--s-info-icon-default);
        }
    }

    &.wtg-alert-modal--question {
        color: var(--s-primary-txt-default);
        background-color: var(--s-primary-bg-weak-default);

        > .wtg-alert-modal-question {
            color: var(--s-primary-icon-default);
        }
    }

    &.wtg-alert-modal--message-error {
        color: var(--s-warning-txt-default);
        background-color: var(--s-warning-bg-weak-default);

        > .wtg-alert-modal-icon {
            color: var(--s-warning-icon-default);
        }
    }
}

.wtg-alert-modal__content {
    flex-grow: 1;
    overflow-y: auto;
    padding: var(--s-padding-xl);
}

.wtg-alert-modal__content-actions {
    display: flex;
    justify-content: flex-end;
    border: 1px solid var(--s-neutral-border-weak-default);
    border-style: solid none none none;
    padding: var(--s-padding-xl) var(--s-padding-null);
    margin: var(--s-padding-null) var(--s-padding-xl);
    gap: var(--s-padding-m);
}
</style>
