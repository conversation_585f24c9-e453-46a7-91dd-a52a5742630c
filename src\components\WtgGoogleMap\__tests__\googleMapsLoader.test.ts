describe('googleMapsLoader', () => {
    let loadMapsAsync = jest.fn();
    let script: HTMLScriptElement;

    beforeEach(() => {
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const loaderModule = require('../utils/googleMapsLoader');
        loadMapsAsync = loaderModule.default;

        script = {} as HTMLScriptElement;
        jest.spyOn(document.head, 'appendChild').mockImplementation((node: Node) => node);
        jest.spyOn(document, 'createElement').mockReturnValue(script);
    });

    afterEach(() => {
        jest.resetModules();
        (window as any).google = undefined;
    });

    test('loads map from google maps api', async () => {
        const loader = loadMapsAsync('apiKey');
        (window as any).wtgInitGoogleMap();
        await loader;

        expect(document.head.appendChild).toHaveBeenCalledWith(script);
        expect(script.src).toBe(
            'https://maps.googleapis.com/maps/api/js?key=apiKey&v=weekly&callback=wtgInitGoogleMap'
        );
    });

    test('only loads google maps once', async () => {
        const loader = loadMapsAsync('apiKey');
        (window as any).wtgInitGoogleMap();
        await loader;
        await loadMapsAsync('apiKey');

        expect(document.head.appendChild).toHaveBeenCalledTimes(1);
    });

    test('does not load if google.maps already exists on window', async () => {
        (window as any).google = { maps: {} };
        await loadMapsAsync('apiKey');

        expect(document.head.appendChild).toHaveBeenCalledTimes(0);
    });

    test('loads a localized map if language code is given', async () => {
        const loader = loadMapsAsync('apiKey', undefined, 'jp');
        (window as any).wtgInitGoogleMap();
        await loader;

        expect(document.head.appendChild).toHaveBeenCalledWith(script);
        expect(script.src).toBe(
            'https://maps.googleapis.com/maps/api/js?language=jp&key=apiKey&v=weekly&callback=wtgInitGoogleMap'
        );
    });
});
