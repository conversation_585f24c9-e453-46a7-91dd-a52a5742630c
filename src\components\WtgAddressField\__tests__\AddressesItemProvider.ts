import { SearchFieldDisplayMode, SearchFieldItemProvider } from '@components/WtgSearchField/types';
import { AddressSearchItem } from '../types';
import addresses from './addresses';

class AddressItemProvider implements SearchFieldItemProvider<AddressSearchItem> {
    getDisplayModeAsync(): Promise<SearchFieldDisplayMode> {
        return Promise.resolve(SearchFieldDisplayMode.DescOnly);
    }
    getSearchResultsDisplayModeAsync(): Promise<SearchFieldDisplayMode> {
        return Promise.resolve(SearchFieldDisplayMode.CodeDesc);
    }
    getDisplayStringAsync(value: string): Promise<string> {
        return this.getItemForValueAsync(value).then((item) => {
            let displayString = '';
            if (item) {
                displayString = item.description;
            }
            return displayString;
        });
    }
    getEmptyValueAsync(): Promise<string> {
        return Promise.resolve('');
    }
    getItemAsync(search: string): Promise<AddressSearchItem | undefined> {
        const address = addresses.find((address) => address.company === search);
        if (!address) {
            return Promise.resolve(undefined);
        }
        const item: AddressSearchItem = {
            code: address?.code ?? '',
            value: address?.guid,
            description: search,
            address: address,
        };
        return Promise.resolve(item);
    }
    getItemsAsync(search: string, skip: number): Promise<{ items: AddressSearchItem[]; total: number }> {
        const allItems = search
            ? addresses.filter(
                  (address: any) =>
                      address.company.toLocaleLowerCase().startsWith(search.toLocaleLowerCase()) ||
                      address.companyCode.toLocaleLowerCase().startsWith(search.toLocaleLowerCase())
              )
            : addresses;

        const items = allItems.slice(skip, skip + 50).map((address: any) => ({
            code: address.code,
            value: address.guid,
            description: address.company,
            address: address,
        }));

        return Promise.resolve({
            items,
            total: allItems.length,
        });
    }
    getItemForValueAsync(value: string): Promise<AddressSearchItem | undefined> {
        const address = addresses.find((address: any) => address.guid === value);
        let item: AddressSearchItem | undefined;
        if (address) {
            item = {
                code: address.code ?? '',
                value: address.guid,
                description: address.company ?? '',
                address: address,
            };
        }
        return Promise.resolve(item);
    }
    validateAsync(displayValue: string, item?: AddressSearchItem | undefined): Promise<boolean> {
        return Promise.resolve(!!(item || addresses.find((address: any) => address.guid === displayValue)));
    }
}

export default AddressItemProvider;
