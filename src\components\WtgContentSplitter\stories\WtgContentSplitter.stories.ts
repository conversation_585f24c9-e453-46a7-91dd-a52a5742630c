import WtgBox from '@components/WtgBox';
import WtgCol from '@components/WtgCol';
import WtgContentSplitter from '@components/WtgContentSplitter/WtgContentSplitter.vue';

import WtgDivider from '@components/WtgDivider/WtgDivider.vue';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgRow from '@components/WtgRow';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { ContentSplitterSandboxTemplate } from './templates/wtg-content-splitter-sandbox.stories-template';

type Story = StoryObj<typeof WtgContentSplitter>;
const meta: Meta<typeof WtgContentSplitter> = {
    title: 'Components/Content Splitter',
    component: WtgContentSplitter,
    parameters: {
        docs: {
            description: {
                component: `
The Content splitter component is intended to provide brief information about the connection between items in a series. E.g.:
- The distance between two points in a journey (Home [30min] Work), or
- A conjunction in a boolean expression (5 > 0 [AND] 5 < 10)`,
            },
        },
        layout: 'fullscreen',
        slots: {
            default: {
                description: 'Default slot',
            },
        },
    },
    render: (args) => ({
        components: { WtgContentSplitter },
        setup: () => ({ args }),
        methods: {
            changeAction: action('change'),
        },
        template: `
        <WtgContentSplitter v-bind="args">
            {{ args.default }}
        </WtgContentSplitter>
        `,
    }),
    decorators: [
        () => ({
            template: `
            <div style="display: flex; align-items: center; justify-content: center; min-inline-size: 10em; min-block-size: 10em; padding: 16px">
                <story class="flex-1-1 align-self-stretch" />
            </div>
            `,
        }),
    ],
    args: {
        default: 'Label',
        justify: 'center',
        variant: 'solid',
    },
    argTypes: {
        justify: {
            options: ['start', 'center', 'end'],
            control: 'radio',
        },
        variant: {
            options: ['solid', 'dashed'],
            control: 'radio',
        },
    },
};

export default meta;

export const Default: Story = {};

export const Sandbox: Story = {
    render: (args) => ({
        components: { WtgBox, WtgDivider, WtgContentSplitter, WtgCol, WtgRow, WtgLayoutGrid },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: templateWithRtl(ContentSplitterSandboxTemplate),
        parameters: getChromaticParameters(),
    }),
};
