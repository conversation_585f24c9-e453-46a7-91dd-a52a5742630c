import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import info from '../../../storybook/assets/info.png';

import { Meta, Title, Description, Story, Canvas, Controls, ArgTypes } from '@storybook/blocks';
import * as WtgLabel from './WtgLabel.stories.ts';

<Meta of={WtgLabel} />

<div className="component-header">
    <h1>Label</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td></td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

The Label component is a utility component designed to provide a standardized way to apply text-related styles and utility classes in Supply. It serves as an optional abstraction over the standard span element and typography utility classes, offering a more structured approach while maintaining flexibility for teams to use standard HTML and CSS directly if preferred.

<Canvas className="canvas-preview" of={WtgLabel.Default} />
<Controls of={WtgLabel.Default} sort={'alpha'} />

## Key features and benefits

<ul>
    <li>Provides a consistent API for applying typography styles</li>
    <li>Enhances maintainability and readability of text-related elements</li>
    <li>Supports the same utility classes available for text styling</li>
    <li>Lightweight and non-restrictive, allowing teams to use span and standard typography utilities if desired</li>
</ul>

## Why use the Label component

<ul>
    <li>Provides a consistent API for applying typography styles</li>
    <li>Enhances maintainability and readability of text-related elements</li>
    <li>Supports the same utility classes available for text styling</li>
    <li>Lightweight and non-restrictive, allowing teams to use span and standard typography utilities if desired</li>
</ul>

## Alternatives to Label

Teams are free to use native span elements and typography utility classes directly if it aligns better with their project’s styling conventions.

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
