import { layoutGridColumnKey } from '@components/WtgLayoutGrid/keys';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { WtgChip } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgChip', () => {
    test('it renders a <div> tag', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('DIV');
    });

    test('it passes its properties down to the WtgPopover component', () => {
        const wrapper = mountComponent({
            props: {
                closeOnContentClick: false,
                variant: 'dropdown',
            },
        });
        const menuProps = wrapper.findComponent({ name: 'WtgPopover' }).props();
        expect(menuProps.closeOnContentClick).toBe(false);
        expect(menuProps.disabled).toBe(false);
        expect(menuProps.location).toBe('bottom left');
        expect(menuProps.nudgeBottom).toBe('8px');
        expect(menuProps.modelValue).toBe(false);
        expect(menuProps.transition).toBe(false);
    });

    test('it emits a click event when the chip is clicked', async () => {
        const wrapper = mountComponent();
        const chip = wrapper.find('div');
        expect(wrapper.emitted('click')).toBeUndefined();
        await chip.trigger('click');
        expect(wrapper.emitted('click')!.length).toBe(1);
        expect(wrapper.emitted('click')![0][0]).toBeInstanceOf(MouseEvent);
    });

    test('it passes the default slot to the chip', () => {
        const wrapper = mountComponent();
        expect(wrapper.text()).toBe('My Chip');
    });

    test('it applies the wtg-chip-default class when variant is undefined', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toEqual(['wtg-chip--default', 'wtg-chip']);
    });

    test('it applies the wtg-chip-refinement class when variant is refinement', () => {
        const wrapper = mountComponent({
            props: {
                variant: 'refinement',
            },
        });
        expect(wrapper.classes()).toContain('wtg-chip--refinement');
    });

    test('it emits a close event when the close icon is clicked', async () => {
        const wrapper = mountComponent({
            props: {
                variant: 'close',
            },
        });
        const closeIcon = wrapper.findComponent({ name: 'WtgIcon' });
        await closeIcon.trigger('click');
        expect(wrapper.emitted('close-click')!.length).toBe(1);
        expect(wrapper.emitted('close-click')![0][0]).toBeInstanceOf(MouseEvent);
    });

    describe('when sentiment is set', () => {
        test('it applies the wtg-chip-success class if sentiment is success', () => {
            const wrapper = mountComponent({
                props: {
                    sentiment: 'success',
                },
            });
            expect(wrapper.classes()).toContain('wtg-chip--success');
        });

        test('it applies the wtg-chip-critical class if sentiment is critical', () => {
            const wrapper = mountComponent({
                props: {
                    sentiment: 'critical',
                },
            });
            expect(wrapper.classes()).toContain('wtg-chip--critical');
        });
    });

    describe('when sentiment is set', () => {
        test('it applies the wtg-chip-success class if color is success', () => {
            const wrapper = mountComponent({
                props: {
                    sentiment: 'success',
                },
            });
            expect(wrapper.classes()).toContain('wtg-chip--success');
        });

        test('it applies the wtg-chip-warning class if color is warning', () => {
            const wrapper = mountComponent({
                props: {
                    sentiment: 'warning',
                },
            });
            expect(wrapper.classes()).toContain('wtg-chip--warning');
        });

        test('it applies the wtg-chip-critical class if color is critical', () => {
            const wrapper = mountComponent({
                props: {
                    sentiment: 'critical',
                },
            });
            expect(wrapper.classes()).toContain('wtg-chip--critical');
        });

        test('it applies the wtg-chip-primary class if color is primary', () => {
            const wrapper = mountComponent({
                props: {
                    sentiment: 'primary',
                },
            });
            expect(wrapper.classes()).toContain('wtg-chip--primary');
        });
    });

    describe('when color is set', () => {
        test('it applies the wtg-chip-color class', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('wtg-chip--color');
        });

        test('it sets text color and border color to color prop', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'red',
                },
            });
            expect(wrapper.classes()).toContain('bg-red');
        });

        test('it sets the element style from vars', () => {
            const wrapper = mountComponent({
                props: {
                    color: 'var(--s-error-txt-default)',
                },
            });
            expect((wrapper.vm as any).computedStyle.background).toBe('var(--s-error-txt-default)');
            expect((wrapper.vm as any).computedStyle.color).toBe('var(--s-primary-txt-inv-default)');
        });
    });

    test('it has tooltip capability mixed in', () => {
        const wrapper: VueWrapper<any> = mountComponent({
            props: { tooltip: 'Some tooltip' },
        });
        expect(wrapper.vm.tooltipDirective.content).toBe('Some tooltip');
        expect(wrapper.vm.tooltipDirective.placement).toBe('bottom');
    });

    test('it has a columns property mixed in that allows it to be positioned inside a wtg-layout-grid', () => {
        const layoutGridColumn = {
            updateColumns: jest.fn(),
        };
        const wrapper = mountComponent({
            props: { columns: 'col-md-6 col-xl-4' },
            provide: {
                [layoutGridColumnKey]: layoutGridColumn,
            },
        });
        expect(wrapper.props('columns')).toBe('col-md-6 col-xl-4');
        expect(layoutGridColumn.updateColumns).toHaveBeenLastCalledWith('col-md-6 col-xl-4');
    });

    function mountComponent({ props = {}, provide = {} } = {}) {
        return mount(WtgChip, {
            props,
            slots: {
                default: 'My Chip',
            },
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
