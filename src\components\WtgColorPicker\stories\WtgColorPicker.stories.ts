import WtgDivider from '@components/WtgDivider';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgPanel from '@components/WtgPanel';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import WtgColorPicker from '../';
import { ColorPickerSandboxTemplate } from './templates/wtg-color-picker-sandbox.stories-template';
type Story = StoryObj<typeof WtgColorPicker>;
const meta: Meta<typeof WtgColorPicker> = {
    title: 'Components/Color Picker',
    component: WtgColorPicker,
    parameters: {
        docs: {
            description: {
                component: 'Color picker allows you to select a color using a variety of input methods.',
            },
        },
    },
    decorators: [
        () => ({
            template: `
                <div style="max-width:400px">
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {},
    render: (args) => ({
        components: { WtgColorPicker },
        setup: () => ({ args }),
        template: `<WtgColorPicker v-bind="args"></WtgColorPicker>`,
    }),
};

export const Sandbox: Story = {
    args: {
        showSwatches: true,
    },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgColorPicker, WtgDivider, WtgLayoutGrid, WtgPanel },
        setup: () => {
            const picker = ref(null);
            return { args, picker };
        },
        methods: {},
        template: templateWithRtl(ColorPickerSandboxTemplate),
    }),
};
