import WtgDocumentPreview from '@components/WtgDocumentPreview/WtgDocumentPreview.vue';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgDocumentPreview', () => {
    test('its name is WtgDocumentPreview', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgDocumentPreview');
    });

    describe('view model', () => {
        let wrapper: any;
        let getUrlAsyncMock: any;
        let getUrlResolve: any;

        beforeEach(() => {
            const getUrlPromise = new Promise((resolve) => {
                getUrlResolve = resolve;
            });
            getUrlAsyncMock = jest.fn().mockReturnValue(getUrlPromise);
            wrapper = mountComponent({
                props: {
                    src: 'edocSrc',
                    fileExtension: 'AAA',
                    urlProvider: { getUrlAsync: getUrlAsyncMock },
                },
            });
        });

        test('canShowImage', async () => {
            expect(wrapper.vm.canShowImage).toBe(false);
            await wrapper.setProps({ fileExtension: 'PDF' });
            expect(wrapper.vm.canShowImage).toBe(false);
            await wrapper.setProps({ fileExtension: 'TXT' });
            expect(wrapper.vm.canShowImage).toBe(false);

            await wrapper.setProps({ fileExtension: 'BMP' });
            expect(wrapper.vm.canShowImage).toBe(true);
            await wrapper.setProps({ fileExtension: 'GIF' });
            expect(wrapper.vm.canShowImage).toBe(true);
            await wrapper.setProps({ fileExtension: 'JPG' });
            expect(wrapper.vm.canShowImage).toBe(true);
            await wrapper.setProps({ fileExtension: 'JPEG' });
            expect(wrapper.vm.canShowImage).toBe(true);
            await wrapper.setProps({ fileExtension: 'PNG' });
            expect(wrapper.vm.canShowImage).toBe(true);

            await wrapper.setProps({ fileExtension: 'TIF' });
            expect(wrapper.vm.canShowImage).toBe(false);
            wrapper.vm.tifDecodeError();
            expect(wrapper.vm.canShowImage).toBe(true);
        });

        test('canShowInIframe', async () => {
            expect(wrapper.vm.canShowInIframe).toBe(false);
            await wrapper.setProps({ fileExtension: 'JPG' });
            expect(wrapper.vm.canShowInIframe).toBe(false);
            await wrapper.setProps({ fileExtension: 'TIF' });
            expect(wrapper.vm.canShowInIframe).toBe(false);

            await wrapper.setProps({ fileExtension: 'PDF' });
            expect(wrapper.vm.canShowInIframe).toBe(true);
            await wrapper.setProps({ fileExtension: 'TXT' });
            expect(wrapper.vm.canShowInIframe).toBe(true);
        });

        test('canShowTif', async () => {
            expect(wrapper.vm.canShowTif).toBe(false);
            await wrapper.setProps({ fileExtension: 'JPG' });
            expect(wrapper.vm.canShowTif).toBe(false);
            await wrapper.setProps({ fileExtension: 'PDF' });
            expect(wrapper.vm.canShowTif).toBe(false);
            await wrapper.setProps({ fileExtension: 'TXT' });
            expect(wrapper.vm.canShowTif).toBe(false);

            await wrapper.setProps({ fileExtension: 'TIF' });
            expect(wrapper.vm.canShowTif).toBe(true);
            wrapper.vm.tifDecodeError();
            expect(wrapper.vm.canShowTif).toBe(false);
        });

        test('showDocumentNotAvailable', async () => {
            expect(wrapper.vm.showDocumentNotAvailable).toBe(false);

            await wrapper.setProps({ src: 'newEDocSrc' });
            await wrapper.setProps({ fileExtension: 'JPG' });
            expect(wrapper.vm.showDocumentNotAvailable).toBe(false);

            await wrapper.setProps({ fileExtension: 'TIF' });
            expect(wrapper.vm.showDocumentNotAvailable).toBe(false);

            await wrapper.setProps({ fileExtension: 'PDF' });
            expect(wrapper.vm.showDocumentNotAvailable).toBe(false);

            wrapper.vm.error();
            expect(wrapper.vm.showUnsupported).toBe(true);
            expect(wrapper.vm.showDocumentNotAvailable).toBe(false);

            await wrapper.setProps({ loading: true });
            expect(wrapper.vm.isLoading).toBe(true);
            expect(wrapper.vm.showDocumentNotAvailable).toBe(false);

            await wrapper.setProps({ src: '' });
            expect(wrapper.vm.showDocumentNotAvailable).toBe(false);

            expect(wrapper.vm.showUnsupported).toBe(false);
            expect(wrapper.vm.showDocumentNotAvailable).toBe(false);

            await wrapper.setProps({ loading: false });
            wrapper.vm.load();
            expect(wrapper.vm.isLoading).toBe(false);
            expect(wrapper.vm.showDocumentNotAvailable).toBe(true);
        });

        test('showUnsupported', async () => {
            expect(wrapper.vm.showUnsupported).toBe(true);

            await wrapper.setProps({ src: '' });
            expect(wrapper.vm.showUnsupported).toBe(false);

            await wrapper.setProps({ src: 'newEDocSrc' });
            await wrapper.setProps({ fileExtension: 'JPG' });
            expect(wrapper.vm.showUnsupported).toBe(false);

            await wrapper.setProps({ fileExtension: 'TIF' });
            expect(wrapper.vm.showUnsupported).toBe(false);

            await wrapper.setProps({ fileExtension: 'SLN' });
            expect(wrapper.vm.showUnsupported).toBe(true);

            await wrapper.setProps({ fileExtension: 'PDF' });
            expect(wrapper.vm.showUnsupported).toBe(false);

            wrapper.vm.error();
            await nextTick();
            expect(wrapper.vm.showUnsupported).toBe(true);
        });

        test('error handler', async () => {
            wrapper.setProps({ src: 'newEDocSrc' });
            wrapper.setProps({ fileExtension: 'JPG' });
            await nextTick();
            expect(wrapper.vm.isLoading).toBe(true);
            expect(wrapper.vm.unsupported).toBe(false);

            wrapper.vm.error();
            expect(wrapper.vm.loading).toBe(false);
            expect(wrapper.vm.unsupported).toBe(true);
        });

        test('tif error handler', async () => {
            wrapper.setProps({ src: 'newEDocSrc' });
            wrapper.setProps({ fileExtension: 'JPG' });
            await nextTick();
            expect(wrapper.vm.showTifAsImage).toBe(false);

            wrapper.vm.tifDecodeError();
            expect(wrapper.vm.showTifAsImage).toBe(true);
        });

        test('on load', async () => {
            wrapper.setProps({ src: 'newEDocSrc' });
            wrapper.setProps({ fileExtension: 'JPG' });
            await nextTick();
            expect(wrapper.vm.isLoading).toBe(true);

            wrapper.vm.load();
            expect(wrapper.vm.isLoading).toBe(false);
        });

        describe('watching src - with urlProvider', () => {
            beforeEach(async () => {
                wrapper.vm.objectUrl = 'some obj url';
                wrapper.vm.unsupported = true;
                wrapper.vm.showTifAsImage = true;
                wrapper.setProps({ src: 'newEDocSrc' });
                wrapper.setProps({ fileExtension: 'AAA' });
                await nextTick();
            });

            test('should blank out objectUrl', () => {
                expect(wrapper.vm.objectUrl).toBe('');
            });

            test('should set unsupported to false', () => {
                expect(wrapper.vm.unsupported).toBe(false);
            });

            test('should set showTifAsImage to false', async () => {
                expect(wrapper.vm.showTifAsImage).toBe(false);
            });

            test('isLoading should be false', () => {
                expect(wrapper.vm.isLoading).toBe(false);
            });

            test('should not call fetch', () => {
                expect(getUrlAsyncMock).not.toHaveBeenCalled();
            });

            describe('when type is supported for preview', () => {
                let disposeMock: any;
                beforeEach(async () => {
                    wrapper.setProps({ src: 'newEDocSrc2' });
                    wrapper.setProps({ fileExtension: 'JPG' });
                    disposeMock = jest.fn();
                    wrapper.vm.disposableUrl = {
                        dispose: disposeMock,
                    };
                    await nextTick();
                });

                test('should revoke last object url', () => {
                    expect(disposeMock).toHaveBeenCalled();
                });

                test('should set isLoading to true', () => {
                    expect(wrapper.vm.isLoading).toBe(true);
                });

                test('should call fetch with src', () => {
                    expect(getUrlAsyncMock).toHaveBeenCalledWith('newEDocSrc2', 'JPG');
                });

                describe('when resolved with nothing', () => {
                    beforeEach(() => {
                        getUrlResolve(undefined);
                    });

                    test('should set unsupported to true', () => {
                        expect(wrapper.vm.disposableUrl).toBeUndefined();
                    });

                    test('should keep blank objectUrl', () => {
                        expect(wrapper.vm.objectUrl).toBe('');
                    });

                    test('should set unsupported to true', () => {
                        expect(wrapper.vm.unsupported).toBe(true);
                    });

                    test('isLoading should be false', () => {
                        expect(wrapper.vm.isLoading).toBe(false);
                    });
                });

                describe('when resolved with url', () => {
                    let disposableUrl: any;
                    beforeEach(() => {
                        disposableUrl = {
                            url: 'some blob url',
                        };
                        getUrlResolve(disposableUrl);
                    });

                    test('should set unsupported to true', () => {
                        expect(wrapper.vm.disposableUrl).toEqual(disposableUrl);
                    });

                    test('should keep blank objectUrl', () => {
                        expect(wrapper.vm.objectUrl).toBe('some blob url');
                    });
                });
            });
        });

        describe('watching src - without urlProvider', () => {
            beforeEach(async () => {
                wrapper.vm.objectUrl = 'some obj url';
                wrapper.vm.unsupported = true;
                wrapper.setProps({ src: 'newEDocSrc' });
                wrapper.setProps({ fileExtension: 'AAA' });
                wrapper.setProps({ urlProvider: undefined });
                await nextTick();
            });

            test('should blank out objectUrl', () => {
                expect(wrapper.vm.objectUrl).toBe('');
            });

            test('should set unsupported to false', () => {
                expect(wrapper.vm.unsupported).toBe(false);
            });

            test('isLoading should be false', () => {
                expect(wrapper.vm.isLoading).toBe(false);
            });

            describe('when type is supported for preview', () => {
                beforeEach(async () => {
                    wrapper.setProps({ src: 'newEDocSrc2' });
                    wrapper.setProps({ fileExtension: 'JPG' });
                    await nextTick();
                });

                test('should keep blank objectUrl', () => {
                    expect(wrapper.vm.objectUrl).toBe('newEDocSrc2');
                });
            });
        });

        describe('beforeDestroy', () => {
            let disposeMock: any;
            beforeEach(() => {
                disposeMock = jest.fn();
                wrapper.vm.disposableUrl = {
                    dispose: disposeMock,
                };
                wrapper.unmount();
            });

            test('should revoke object url', () => {
                expect(disposeMock).toHaveBeenCalled();
            });
        });
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgDocumentPreview, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
