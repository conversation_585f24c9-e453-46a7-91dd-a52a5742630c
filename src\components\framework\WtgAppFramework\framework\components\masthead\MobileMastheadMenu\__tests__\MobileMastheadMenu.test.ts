import { WtgFramework, WtgFrameworkTask, WtgFrameworkTaskGenericAction } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { mount } from '@vue/test-utils';
import { nextTick, reactive, ref } from 'vue';
import WtgUi from '../../../../../../../../WtgUi';
import MobileMastheadMenu from '../MobileMastheadMenu.vue';
import MockSearchProvider from '@components/framework/types/__tests__/__mocks__/SearchProvider';

const wtgUi = new WtgUi();

describe('MobileMastheadMenu', () => {
    let application: WtgFramework;
    const task = ref<WtgFrameworkTask | undefined | null>();

    beforeEach(() => {
        application = reactive(new WtgFramework());
        setApplication(application);
    });

    test('when the only action is the notification action, it renders a MastheadNotifications component with the correct props', async () => {
        task.value = {
            showEDocsAction: {
                visible: false,
                caption: 'eDocs',
                label: 'eDocs',
                onInvoke: jest.fn(),
            },
            showLogsAction: {
                visible: false,
                caption: 'Logs',
                label: 'Logs',
                onInvoke: jest.fn(),
            },
            showMessagesAction: {
                visible: false,
                caption: 'Messages',
                label: 'Messages',
                onInvoke: jest.fn(),
            },
            showNotesAction: {
                visible: false,
                caption: 'Notes',
                label: 'Notes',
                onInvoke: jest.fn(),
            },
            showWorkflowActions: {
                visible: false,
                caption: 'Workflows',
                menuItems: [
                    {
                        caption: 'Milestones',
                        onInvoke: jest.fn(),
                    },
                ],
            },
            documents: {
                visible: false,
                caption: 'Documents',
                loadDocumentsAsync: jest.fn().mockResolvedValue([
                    { caption: 'Option 1', submenu: false, click: jest.fn() },
                    { caption: 'Option 2', submenu: false, click: jest.fn() },
                    {
                        caption: 'Submenu 1',
                        submenu: true,
                        loadDocumentsAsync: jest.fn().mockResolvedValue([
                            { caption: 'Option 1 1', submenu: false, click: jest.fn() },
                            { caption: 'Option 1 2', submenu: false, click: jest.fn() },
                        ]),
                    },
                ]),
            },
            genericActions: [],
            showNotifications: true,
            others: {
                visible: false,
            },
        } as any;
        const wrapper = mountComponent({ propsData: { task: task.value } });
        const mastheadNotifications = wrapper.findComponent({ name: 'MastheadNotifications' });
        expect(mastheadNotifications.exists()).toBe(true);
    });

    test('when the only action is the search action, it renders an icon button with the correct props', async () => {
        application.searchProvider = new MockSearchProvider();
        const wrapper = mountComponent();
        const searchButton = wrapper.findComponent({ name: 'WtgIconButton' });
        expect(searchButton.exists()).toBe(true);
        expect(searchButton.attributes('aria-expanded')).toBe('false');

        wrapper.setProps({
            searchOverlay: true,
        });
        await nextTick();
        expect(searchButton.attributes('aria-expanded')).toBe('true');
        expect(wrapper.emitted('toggle-search')).toBe(undefined);

        await searchButton.trigger('click');
        expect(wrapper.emitted('toggle-search')!.length).toBe(1);
    });

    describe('when there is more than one action', () => {
        beforeEach(() => {
            task.value = {
                showEDocsAction: {
                    visible: true,
                    caption: 'eDocs',
                    label: 'eDocs',
                    onInvoke: jest.fn(),
                },
                showLogsAction: {
                    visible: true,
                    caption: 'Logs',
                    label: 'Logs',
                    onInvoke: jest.fn(),
                },
                showMessagesAction: {
                    visible: true,
                    caption: 'Messages',
                    label: 'Messages',
                    onInvoke: jest.fn(),
                },
                showNotesAction: {
                    visible: true,
                    caption: 'Notes',
                    label: 'Notes',
                    onInvoke: jest.fn(),
                },
                showWorkflowActions: {
                    visible: true,
                    caption: 'Workflows',
                    menuItems: [
                        {
                            caption: 'Milestones',
                            onInvoke: jest.fn(),
                        },
                    ],
                },
                documents: {
                    visible: true,
                    caption: 'Documents',
                    loadDocumentsAsync: jest.fn().mockResolvedValue([
                        { caption: 'Option 1', submenu: false, click: jest.fn() },
                        { caption: 'Option 2', submenu: false, click: jest.fn() },
                        {
                            caption: 'Submenu 1',
                            submenu: true,
                            loadDocumentsAsync: jest.fn().mockResolvedValue([
                                { caption: 'Option 1 1', submenu: false, click: jest.fn() },
                                { caption: 'Option 1 2', submenu: false, click: jest.fn() },
                            ]),
                        },
                    ]),
                },
                genericActions: [],
                showNotifications: true,
                others: {
                    visible: false,
                },
            } as any;
        });

        test('it will render the Toggle Actions button with tooltip="Entity Actions" and "dotsHorizontal" icon', () => {
            const wrapper = mountComponent({ propsData: { task: task.value } });

            const actionsButton = wrapper.findComponent({ name: 'WtgIconButton' });
            expect(actionsButton.exists()).toBe(true);
            expect(actionsButton.props('tooltip')).toBe('Entity actions');
            expect(actionsButton.props('icon')).toBe('s-icon-menu-meatballs');
        });

        describe('notifications', () => {
            test('it renders a notifications menu item', async () => {
                const wrapper = mountComponent({ propsData: { task: task.value } });
                const menuButton = wrapper.findComponent({ name: 'WtgIconButton' });
                menuButton.trigger('click');
                await nextTick();
                const listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                const notificationsMenuItem = listItems.at(6);
                expect(notificationsMenuItem!.exists()).toBe(true);
                expect(notificationsMenuItem!.text()).toBe('Alerts');
                expect(notificationsMenuItem!.find('i').classes('s-icon-status-warning')).toBe(true);
                application.searchProvider = undefined;
            });

            test('it renders a notification on the menu activator icon button when there are notifications active', async () => {
                const wrapper = mountComponent({ propsData: { task: task.value } });
                const notification = wrapper.findComponent({ name: 'WtgNotification' });
                expect(notification.props('modelValue')).toBe(false);
                task.value!.notifications = [{ id: 1, message: 'Test notification' }] as any;
                await nextTick();
                expect(notification.props('modelValue')).toBe(true);
            });

            test('it renders a notification on the notification menu item when there are notifications active', async () => {
                const wrapper = mountComponent({ propsData: { task: task.value } });
                const menuButton = wrapper.findComponent({ name: 'WtgIconButton' });
                menuButton.trigger('click');
                await nextTick();

                const badge = wrapper
                    .findAllComponents({ name: 'WtgListItem' })
                    .at(6)!
                    .findComponent({ name: 'WtgNotification' });
                expect(badge.props('modelValue')).toBe(false);

                task.value!.notifications = [{ id: 1, message: 'Test notification' }] as any;
                await nextTick();
                expect(badge.props('modelValue')).toBe(true);
            });

            test('it opens the notifications drawer when the notification menu item is clicked', async () => {
                const wrapper = mountComponent({ propsData: { task: task.value } });
                const menuButton = wrapper.findComponent({ name: 'WtgIconButton' });
                menuButton.trigger('click');
                await nextTick();

                const notificationMenuItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(6);
                expect(application.notificationsDrawer.visible).toBe(false);
                notificationMenuItem!.trigger('click');
                await nextTick();

                expect(application.notificationsDrawer.visible).toBe(true);
            });
        });

        describe('search', () => {
            test('it renders a search menu item when there is a search provider', async () => {
                const wrapper = mountComponent({ propsData: { task: task.value } });
                const menuButton = wrapper.findComponent({ name: 'WtgIconButton' });
                menuButton.trigger('click');
                await nextTick();
                let listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                expect(listItems.length).toBe(7);
                application.searchProvider = new MockSearchProvider();
                await nextTick();

                listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                expect(listItems.length).toBe(8);
                const searchMenuItem = listItems.at(7)!;
                expect(searchMenuItem.exists()).toBe(true);
                expect(searchMenuItem.text()).toBe('Search');
                expect(searchMenuItem.find('i').classes('s-icon-search')).toBe(true);
                application.searchProvider = undefined;
            });

            test('it opens the masthead search overlay when the search menu item is clicked', async () => {
                application.searchProvider = new MockSearchProvider();
                const wrapper = mountComponent({ propsData: { task: task.value } });
                const menuButton = wrapper.findComponent({ name: 'WtgIconButton' });
                menuButton.trigger('click');
                await nextTick();

                const searchMenuItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(7)!;
                expect(wrapper.emitted('toggle-search')).toBe(undefined);
                searchMenuItem.trigger('click');
                await nextTick();

                expect(wrapper.emitted('toggle-search')!.length).toBe(1);
                application.searchProvider = undefined;
            });
        });

        describe('task actions', () => {
            let wrapper: any;
            let genericActions: WtgFrameworkTaskGenericAction[];

            beforeEach(async () => {
                wrapper = mountComponent({ propsData: { task: task.value } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                await button.trigger('click');
                genericActions = [
                    {
                        id: 'someguid1',
                        caption: 'Action 1',
                        placement: 'default',
                        onInvoke: jest.fn(),
                    },
                    {
                        id: 'someguid2',
                        caption: 'Action 2',
                        placement: 'taskactions',
                        icon: '$icon',
                        onInvoke: jest.fn(),
                    },
                ];
            });

            test('its renders a menu item if the action is visible', () => {
                const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                expect(menuItems.length).toBe(7);
                expect(menuItems.at(0).text()).toBe('eDocs');
                expect(menuItems.at(1).text()).toBe('Documents');
                expect(menuItems.at(2).text()).toBe('Logs');
                expect(menuItems.at(3).text()).toBe('Messages');
                expect(menuItems.at(4).text()).toBe('Notes');
                expect(menuItems.at(5).text()).toBe('Workflows');
            });

            test('it will render the Toggle Actions button with aria-label, aria-haspopup="menu" and aria-expanded="false" attributes', () => {
                task.value!.showWorkflowActions.visible = true;
                const wrapper = mountComponent({ propsData: { task: task.value } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                expect(button.attributes('aria-label')).toBe('Toggle Actions');
                expect(button.attributes('aria-haspopup')).toBe('menu');
                expect(button.attributes('aria-expanded')).toBe('false');
            });

            test('it will render the Toggle Actions button with aria-expanded="true" attribute when Actions menu is open', async () => {
                task.value!.showWorkflowActions.visible = true;
                const wrapper = mountComponent({ propsData: { task: task.value } });
                const button = wrapper.findComponent({ name: 'WtgIconButton' });
                await button.trigger('click');
                expect(button.attributes('aria-expanded')).toBe('true');
            });

            test('it renders a menu item for any transition placed as a task action', async () => {
                task.value!.genericActions = genericActions;
                await nextTick();

                const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                expect(menuItems.length).toBe(8);
                expect(menuItems.at(0).text()).toContain('Action 2');
            });

            test('it calls eDocs onInvoke when eDocs is clicked', async () => {
                const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                await menuItems.at(0).trigger('click');

                expect(task.value?.showEDocsAction.onInvoke).toHaveBeenCalledTimes(1);
            });

            test('it calls logs onInvoke when Logs is clicked', async () => {
                const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                await menuItems.at(2).trigger('click');

                expect(task.value?.showLogsAction.onInvoke).toHaveBeenCalledTimes(1);
            });

            test('it calls messages onInvoke when Messages is clicked', async () => {
                const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                await menuItems.at(3).trigger('click');

                expect(task.value?.showMessagesAction.onInvoke).toHaveBeenCalledTimes(1);
            });

            test('it calls notes onInvoke when Notes is clicked', async () => {
                const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
                await menuItems.at(4).trigger('click');

                expect(task.value?.showNotesAction.onInvoke).toHaveBeenCalledTimes(1);
            });

            test('it renders the workflow items when workflows is clicked', async () => {
                const popupMenus = wrapper.findAllComponents({ name: 'PopupMenuList' });
                let subItems = popupMenus.at(2).findAllComponents({ name: 'WtgListItem' });
                expect(subItems.length).toBe(1);
                await subItems.at(0).trigger('click');

                subItems = popupMenus.at(2).findAllComponents({ name: 'WtgListItem' });
                expect(subItems.length).toBe(2);
                expect(subItems.at(1).text()).toBe('Milestones');
                await subItems.at(1).trigger('click');

                expect(task.value?.showWorkflowActions.menuItems[0].onInvoke).toHaveBeenCalledTimes(1);
            });

            test('it renders the document items when documents is clicked', async () => {
                const popupMenus = wrapper.findAllComponents({ name: 'PopupMenuList' });
                let subItems = popupMenus.at(1).findAllComponents({ name: 'WtgListItem' });
                expect(subItems.length).toBe(1);
                await subItems.at(0).trigger('click');

                subItems = popupMenus.at(1).findAllComponents({ name: 'WtgListItem' });
                expect(subItems.length).toBe(4);
                expect(subItems.at(1).text()).toBe('Option 1');
                expect(subItems.at(2).text()).toBe('Option 2');
                expect(subItems.at(3).text()).toBe('Submenu 1');
            });
        });
    });

    describe('when there are navigation actions', () => {
        beforeEach(() => {
            task.value = {
                showEDocsAction: {
                    visible: false,
                },
                showLogsAction: {
                    visible: false,
                },
                showMessagesAction: {
                    visible: false,
                },
                showNotesAction: {
                    visible: false,
                },
                showWorkflowActions: {
                    visible: false,
                },
                documents: {
                    visible: false,
                },
                genericActions: [],
                others: {
                    visible: true,
                    hasNext: true,
                    hasPrevious: true,
                    indexCountText: '2 of 3',
                    onNext: jest.fn(),
                    onPrevious: jest.fn(),
                },
            } as any;
        });

        test('it renders the navigation actions', async () => {
            const wrapper = mountComponent({ propsData: { task: task.value } });
            const button = wrapper.findComponent({ name: 'WtgIconButton' });
            await button.trigger('click');
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(menuItems.length).toBe(2);
            expect(menuItems.at(0)!.text()).toBe('Next record');
            expect(menuItems.at(1)!.text()).toBe('Previous record');
        });

        test('it disables the previous button when hasPrevious is false', async () => {
            task.value!.others.hasPrevious = false;
            const wrapper = mountComponent({ propsData: { task: task.value } });
            const button = wrapper.findComponent({ name: 'WtgIconButton' });
            await button.trigger('click');
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(menuItems.length).toBe(2);
            expect(menuItems.at(0)!.props('disabled')).toBe(false);
            expect(menuItems.at(1)!.props('disabled')).toBe(true);
        });

        test('it disables the next button when hasNext is false', async () => {
            task.value!.others.hasNext = false;
            const wrapper = mountComponent({ propsData: { task: task.value } });
            const button = wrapper.findComponent({ name: 'WtgIconButton' });
            await button.trigger('click');
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(menuItems.length).toBe(2);
            expect(menuItems.at(0)!.props('disabled')).toBe(true);
            expect(menuItems.at(1)!.props('disabled')).toBe(false);
        });

        test('it calls onPrevious when the previous button is clicked', async () => {
            const onPrevious = jest.fn();
            task.value!.others.onPrevious = onPrevious;
            const wrapper = mountComponent({ propsData: { task: task.value } });
            const button = wrapper.findComponent({ name: 'WtgIconButton' });
            await button.trigger('click');
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await menuItems.at(1)!.trigger('click');
            expect(onPrevious).toHaveBeenCalledTimes(1);
        });

        test('it calls onNext when the next button is clicked', async () => {
            const onNext = jest.fn();
            task.value!.others.onNext = onNext;
            const wrapper = mountComponent({ propsData: { task: task.value } });
            const button = wrapper.findComponent({ name: 'WtgIconButton' });
            await button.trigger('click');
            const menuItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            await menuItems.at(0)!.trigger('click');
            expect(onNext).toHaveBeenCalledTimes(1);
        });
    });

    function mountComponent({ propsData = {} } = {}) {
        return mount<any>(MobileMastheadMenu as any, {
            props: { ...propsData },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
