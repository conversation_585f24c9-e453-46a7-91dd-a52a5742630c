import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgButton from './WtgButton.stories.ts';

<Meta of={WtgButton} />

<div className="component-header">
    <h1>Button</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable} />[
                Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-1836&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

## API

<Canvas className="canvas-preview" of={WtgButton.Default} />
<Controls of={WtgButton.Default} sort={'alpha'} />

## How to use

-   Keep button labels concise and clearly explain the action to be expected.

-   Use strong, actionable verbs in labels such as “Add new”, “Close”, “Cancel”, or “Save”.

-   Use one primary action per page, guiding users to the logical next step in their workflow (excluding framework related actions).

-   Avoid using buttons to link to other pages, instead consider a hyperlink.

## Behavior

### Icons in buttons

Icons can provide visual cues to support the button's purpose, but should be carefully considered.

-   Use icons in buttons sparingly and only when the icon is providing additional context to the action being taken.

-   Always prioritize clarity and understandability when deciding whether to include an icon.

-   In cases where an icon doesn't add value or may confuse users, a label-only Button is more appropriate.

### Leading icons

Use as immediate visual indicators of the button’s purpose. Leading icons precede the text, offering a quick, visual shorthand of the action the button performs. For example, adding a "+" icon before "Add new" quickly communicates the action of adding or creating something new, allowing users to understand the button's function before reading the text.

### Trailing icons

Use to indicate the direction of the action initiated by the button. Positioned after the text, they help users anticipate the action's effect or the UI flow change. For example, an arrow icon following "Next" or "Continue" can signal that the action will advance the user to another view or step, clarifying the forward movement or transition expected after the button is pressed.

<Canvas className="canvas-preview" of={WtgButton.Icons} sourceState={'none'} />

## Variants and sentiments

<p>
    Button variants are used to highlight different levels of action importance on an interface. Button sentiments
    provide additional meaning and nuance to button variants. In combination, the variants and sentiments create 9
    possible combinations that allow a high level of flexibility in button design to support an obvious visual
    hierarchy.
</p>

<br></br>

<Canvas className="canvas-preview" of={WtgButton.Sentiments} sourceState={'none'} />

<br></br>

### Fill variant and its sentiments

<p>
    The fill buttons have the highest visual prominence and are used for the main call-to-action on a page or
    experience. There should only ever be one Fill Button in the main content area of a page.
</p>

<p>
    <b>Fill primary:</b> The standard sentiment for the fill button. The primary sentiment represents the most important
    action on the page or experience.
</p>
<p>
    <b>Fill success:</b> Used for important positive actions, such as completing a task or confirming an operation.
</p>
<p>
    <b>Fill critical:</b> Used for actions that have significant consequences, like deletion or irreversible changes.
</p>

<br></br>

### Outline variant and its sentiments

<p>
    The outline buttons are less prominent and are typically used to support fill Button actions, or for normal Button
    use on a page.
</p>

<p>
    <b>Outline default:</b> The standard sentiment for the outline button. Used for actions of lesser importance than
    the primary action.
</p>
<p>
    <b>Outline success:</b>Used as a secondary option for positive actions.
</p>
<p>
    <b>Outline critical:</b> Used for secondary actions with potential risks, offering a subtler warning compared to the
    fill critical button.
</p>

<br></br>

### Ghost variant and its sentiments

<p>
    Ghost buttons are the lowest priority actions in an interface. They add a third layer in the button hierarchy,
    ensuring that users focus on button actions in the right order.
</p>

<p>
    <b>Ghost default:</b> The standard sentiment for the Ghost button. Represents the lowest priority actions, not
    demanding immediate attention.
</p>

<p>
    <b>Ghost success:</b> Used for low-priority actions that reinforce a positive or successful outcome.
</p>

<p>
    <b>Ghost critical:</b> Used for low-priority actions that may lead to destructive or irreversible changes. Helps
    signal caution without drawing strong visual attention, such as “delete” or “Revoke access” in popover menus.
</p>
## Content guidelines

Always follow Supply's [Content Guidelines](/docs/guidelines-content--overview).

-   Use clear, actionable verbs and keep labels concise, ideally 1–3 words (e.g. “Add new”, “Close”, “Cancel”, “Save”).

## Accessibility

### Non-text content

-   Leading and trailing icons in buttons are supplementary and purely presentational visual aids. They should not convey additional information that is not already covered by the text content.
-   Use the `aria-hidden` attribute to exclude icons from assistive technologies, ensuring they are not announced by screen readers.

For more information, see [WCAG 1.1.1 Non-text Content](https://www.w3.org/TR/WCAG22/#non-text-content).

### Recommended props

Although the button component supports a broad set of props, we recommend sticking primarily to the following for accessibility and consistency:

-   **`aria-label`**: Use this to provide a clear, concise label for buttons with icons but no visible text.
-   **`aria-disabled`**: Use this to indicate that a button is disabled and cannot be interacted with.
-   **`aria-expanded`**: Use this for buttons that toggle expandable content, such as dropdowns or accordions, to indicate the current state (expanded or collapsed).

> **Note**: Avoid unnecessary changes to button colors, sizes, or styles, as this may negatively impact accessibility and create an inconsistent user experience.

### Regarding the `active` prop

The `active` state imitates `:active` styling but suppresses the visual indications for both `:active` and `:hover` states. This behavior is useful in specific scenarios but should be used with caution:

-   **When to use**: Use the `active` state for "no-op" actions, such as navigation links to the current page or location.
-   **When to avoid**: Avoid using the `active` state for buttons with meaningful actions (e.g., sorting a data table) to prevent confusion.
-   **Alternative**: For toggleable buttons, consider combining a `toggle` prop with a boolean model value to provide clear visual feedback.

> **Note**: The `invert` attribute, where clicking the button implies a toggle or update, is acceptable. However, ensure that `:active` and `:hover` indicators are implemented for clarity.

### Accessibility responsibilities

Accessibility is a shared responsibility between the `WtgButton` component and its consumers. The following table clarifies these responsibilities:

| Accessibility aspect                    | `WtgButton` responsibility | Consumer responsibility                |
| --------------------------------------- | -------------------------- | -------------------------------------- |
| Native `<button>` semantics             | Provided automatically     | —                                      |
| Focus ring & keyboard support           | Built in                   | —                                      |
| Visual prominence (variant + sentiment) | Provided via props         | Select appropriate variant & sentiment |
| Placement / DOM order                   | —                          | Ensure logical tab sequence            |
| Label text content                      | —                          | Write concise, descriptive labels      |

## Related components

-   [Hyperlink](/docs/components-hyperlink--docs)
-   [Icon button](/docs/components-icon-button--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
