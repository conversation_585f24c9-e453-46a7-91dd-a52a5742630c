export const searchData = [{"text":"Component status ⚠️ Please note This status page focuses on our new Supply component library. A library which is being built from the ground up and will be detached from Vuetify and Material Design concepts. Please visit the official [GLOW 321](https://www-hyetip.cargowise.com/Portals/321/Desktop#/page/9c700f8509594495a0cdc87749b3b76e) portal to learn more about available GLOW specific components. For the old Material Design content please clone [this repository](https://devops.wisetechglobal.com/wtg/Shared/_git/WTG.MaterialDesign) and run it locally. ### Base Components ### Structure Components ### Framework Components","title":"Components/Component Status","path":"/?path=/docs/components-component-status--overview"},{"text":"Action Bar UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=305%3A17858&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) [Default](#default) ## Quick tips The Main Actions section should always contain one clear primary action along with additional secondary actions as required. 'Destructive Actions' like 'Delete' that are hard to undo should be represented in the critical default button style. If you find your Action Bar has to collapse into an overflow state, even on full-sized desktop screens, consider evaluating if all secondary actions are essential to the experience. ## Order of ### Primary actions Primary actions in the Action Bar are designed to be prominent, representing the most saving, confirming, or submitting and guiding users to the next key step in the workflow. ### Secondary actions While processes. These actions, not always advancing the main workflow, may provide options for different paths or additional tasks from the current screen. ### Destructive actions Destructive actions are used when an action is destructive or irreversible, such as deleting data. They should be represented in the critical default button style to highlight their destructive actions such as ‘Back’ or ‘Cancel’ carry less risk and are displayed in the default button style. ## Action Bar overflow for secondary actions If the Action Bar runs low on space due to multiple secondary actions or smaller screens, an overflow state will trigger and actions will collapse into a meatball menu as shown below. This behavior is especially useful for smaller screens, so if you find your Action Bar has to collapse even on full sized desktop screens, consider evaluating if all secondary actions are essential to the experience. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Action Bar","path":"/?path=/docs/conceptual-action-bar--docs"},{"text":"Address Field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=149%3A5675&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Address Field","path":"/?path=/docs/components-address-field--docs"},{"text":"Alert 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=305-20913&mode=design) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Alert","path":"/?path=/docs/components-alert--docs"},{"text":"App UI/UX Build Documentation ## Overview","title":"App","path":"/?path=/docs/utilities-app--docs"},{"text":"Avatar 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=305%3A20614&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Avatar","path":"/?path=/docs/components-avatar--docs"},{"text":"Badge 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=188-15929&mode=design) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Make sure the status of the badge is directly related to the component it is attached too. Make sure to use the correct status type when adding a badge to a larger component. If using an icon for a unique status be sure the icon in question is legible enough to be understood on its own ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Badge","path":"/?path=/docs/components-badge--docs"},{"text":"Box UI/UX Build Documentation ## Overview Component preview","title":"Box","path":"/?path=/docs/utilities-box--docs"},{"text":"Breadcrumbs UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=305%3A20672&mode=design&t=CjNcT6ckPZSb6gCU-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Overflow behavior](#overflow-behavior) ## Quick tips Breadcrumbs complement but don't replace the main page navigation. Breadcrumbs should only be used to communicate location and are not meant to display additional information. In Figma, the Breadcrumb component is available on its own or as part of the Masthead component. ## Overflow behavior In cases where the hierarchy exceeds three levels, Breadcrumbs display the first level, an ellipsis hyperlink for intermediate levels, and the current page. Users can access hidden levels by clicking the ellipsis, which reveals a list of the in-between pages. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Breadcrumbs","path":"/?path=/docs/conceptual-breadcrumbs--docs"},{"text":"Button 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=59%3A1790&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Component variants](#component-variants) [Sentiments](#sentiments) [Using icons in buttons](#using-icons-in-buttons) ## Quick tips Keep Button labels concise and clearly explain the action to be expected. Use strong, actionable verbs in labels such as “Add new”, “Close”, “Cancel”, or “Save”. Use one primary action per page, guiding users to the logical next step in their workflow (excluding framework related actions). Avoid using Buttons to link to other pages, instead use a hyperlink. ## Button Variants and Sentiments Button Variants are used to highlight different levels of action provide additional meaning and nuance to Button Variants. In combination, the Variants and Sentiments create 7 possible combinations that allow a high level of flexibility in Button design to support an obvious visual hierarchy. ### Fill Variant and its Sentiments The Fill Buttons have the highest visual prominence and are used for the main call-to-action on a page or experience. There should only ever be one Fill Button in the main content area of a page. Fill Primary: The standard sentiment for the Fill button. The Primary sentiment represents the most action on the page or experience. Fill Success: Used for Fill Critical: Used for actions that have significant consequences, like deletion or irreversible changes. ### Outline Variant and its Sentiments The Outline Buttons are less prominent and are typically used to support Fill Button actions, or for normal Button use on a page. Outline Default: The standard sentiment for the Outline button. Used for actions of lesser the primary action. Outline Success:Used as a secondary option for positive actions. Outline Critical: Used for secondary actions with potential risks, offering a subtler warning compared to the Fill Critical button. ### Ghost Variant and its Sentiments Ghost Buttons are the lowest priority actions in an interface. They add a third layer in the Button hierarchy, ensuring that users focus on Button actions in the right order. Ghost default: The standard sentiment for the Ghost button. Represents the lowest priority actions, not demanding immediate attention. ## Using icons in buttons Icons can provide visual cues to support the Button's purpose, but should be carefully considered first. Use icons in buttons sparingly and only when the icon is providing additional context to the action being taken. Always prioritize clarity and understandability when deciding whether to include an icon. In cases where an icon doesn't add value or may confuse users, a label-only Button is more appropriate. ### Leading icons Use as immediate visual indicators of the button’s purpose. Leading icons precede the text, offering a quick, visual shorthand of the action the button performs. For example, adding a \"+\" icon before \"Add new\" quickly communicates the action of adding or creating something new, allowing users to understand the button's function before reading the text. ### Trailing icons Use to indicate the direction of the action initiated by the button. Positioned after the text, they help users anticipate the action's effect or the UI flow change. For example, an arrow icon following \"Next\" or \"Continue\" can signal that the action will advance the user to another view or step, clarifying the forward movement or transition expected after the button is pressed. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Button","path":"/?path=/docs/components-button--docs"},{"text":"Charts Chart components are just simple wrappers around the different chart types out of Chart.js, which is a very impressive open source project for displaying charts in the browser. For more samples and a full description of the API, see www.chartjs.org. ## Chart Types Bar Chart Bubble Chart Line Chart Pie Chart Polar Area Chart Radar Chart Scatter Chart ## Loading If you need time to fetch and/or process the chart data, you can use the loading prop to inform the user that there is an operation taking place. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Data viz/Overview","path":"/?path=/docs/data-viz-overview--overview"},{"text":"Bar Chart UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Bar Chart","path":"/?path=/docs/data-viz-bar-chart--docs"},{"text":"Bubble Chart UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Bubble Chart","path":"/?path=/docs/data-viz-bubble-chart--docs"},{"text":"Line Chart UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Line Chart","path":"/?path=/docs/data-viz-line-chart--docs"},{"text":"Pie Chart UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Pie Chart","path":"/?path=/docs/data-viz-pie-chart--docs"},{"text":"Polar Area Chart UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Polar Area Chart","path":"/?path=/docs/data-viz-polar-area-chart--docs"},{"text":"Radar Chart UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Radar Chart","path":"/?path=/docs/data-viz-radar-chart--docs"},{"text":"Scatter Chart UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Scatter Chart","path":"/?path=/docs/data-viz-scatter-chart--docs"},{"text":"Checkbox 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=174%3A19025&mode=design&t=lcJMuRJ69NOQhpJq-1) ## Overview Checkboxes enable users to toggle between checked and unchecked states, allowing for multiple selections ranging from zero to several. They commonly appear in forms, settings, and Datagrids. ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) {/* [Decision tree](#decision-tree) */} [Best practices](#best-practices) [Component variants](#component-variants) {/* [Related components](#related-components) */} ## Quick tips Keep Checkbox labels brief and directly related to what they represent. If you turn off the Checkbox label, ensure the context clearly indicates what the Checkbox is for. Frame Checkbox labels positively. For example, use 'Enable notifications' instead of 'Disable notifications'. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Component variants ### Single Checkbox Use Single Checkboxes to confirm a user's choice or to collect consent for an upcoming action. They are ideal for capturing affirmative responses such as “I agree to the terms and conditions”. ### Checkbox with Label Checkboxes should have clear, concise labels to convey their function. If you choose to turn off the Checkbox label, ensure the checkbox's purpose is still obvious from its context.{' '} ### Checkbox Group A Checkbox Group is ideal for scenarios allowing users to select multiple related options, either independently or collectively. Checkbox Groups come without a default selection, making them ideal for listing options where choosing is optional{' '} 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Checkbox","path":"/?path=/docs/components-checkbox--docs"},{"text":"Chip 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=13830%3A5680&mode=design&t=ql777v7iL0cPgfGA-1) ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Chip","path":"/?path=/docs/components-chip--docs"},{"text":"Col UI/UX Build Documentation ## Overview","title":"Col","path":"/?path=/docs/utilities-col--docs"},{"text":"Container UI/UX Build Documentation ## Overview","title":"Container","path":"/?path=/docs/utilities-container--docs"},{"text":"Date field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=11001%3A35524&mode=design&t=scnxWyPBoONnOg3U-1) ## Overview The Date Field captures a single date typically in YYYY-MM-DD format. Users can input a date manually or choose from a calendar dropdown, which appears when interacting with the calendar icon and closes after selection. ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Our aim for Supply is to standardize the ISO 8601 date format YYYY-MM-DD across CargoWise portals. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Date field","path":"/?path=/docs/components-date-field--docs"},{"text":"Date Picker 1.0.0 UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Date Picker","path":"/?path=/docs/components-date-picker--docs"},{"text":"Date Range field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=11001%3A35524&mode=design&t=scnxWyPBoONnOg3U-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Our aim for Supply is to standardize the ISO 8601 date format YYYY-MM-DD across CargoWise portals. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Date Range field","path":"/?path=/docs/components-date-range-field--docs"},{"text":"Date Time field 1.0.0 UI/UX Build Documentation ### Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Our aim for Supply is to standardize the ISO 8601 date format YYYY-MM-DD across CargoWise portals. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Date Time field","path":"/?path=/docs/components-date-time-field--docs"},{"text":"Date Time Picker UI/UX Build Documentation ## Overview Component preview 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Date Time Picker","path":"/?path=/docs/conceptual-date-time-picker--docs"},{"text":"Key-value pair 1.0.0 UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Key-value pair","path":"/?path=/docs/components-display-field--docs"},{"text":"Divider UI/UX Build Documentation ## Overview","title":"Divider","path":"/?path=/docs/utilities-divider--docs"},{"text":"Duration Field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=149%3A5675&mode=design&t=c0XDR0gukHr3vYg9-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Duration Field","path":"/?path=/docs/components-duration-field--docs"},{"text":"Entity Actions UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=684%3A32659&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Entity Actions","path":"/?path=/docs/conceptual-entity-actions--docs"},{"text":"Expander 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=732%3A20646&mode=design&t=drcw3SerpNLaU1aU-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Expander","path":"/?path=/docs/conceptual-expander--docs"},{"text":"Footer UI/UX Build Documentation ## Overview","title":"Footer","path":"/?path=/docs/utilities-footer--docs"},{"text":"Google Map UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=11001%3A35803&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview GoogleMaps is the default maps type we support but we understand that other map may be required later and please reach out if you have a requirement to add a different map type. Component preview ## Guidelines ### Clusterer This example uses a clusterer with markers that combines nearby markers into clusters ### Polylines Here is an example showing a flight path by using the polylines property ### Polygons This example creates a map with a simple polygon representing the Bermuda Triangle ### Shapes This example creates a map with circles and rectangles to show information to represent capital cities 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Google Map","path":"/?path=/docs/components-google-map--docs"},{"text":"Hyperlink 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=9390%3A13978&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Hyperlink","path":"/?path=/docs/components-hyperlink--docs"},{"text":"Icon UI/UX Build Documentation ## Overview Component preview ## General icon usage Icons serve as visual aids to improve usability, increase comprehension, and reduce cognitive load when calling attention to an action, command, or section. Can’t find what you’re looking for? See our [contribution process](?path=/docs/icons-images-icon-contribution--overview). ## Search Supply icons","title":"Icon","path":"/?path=/docs/icons-images-icon--docs"},{"text":"Icon Button 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=9375%3A7383&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Usage guidelines](#usage-guidelines) [Component variants](#component-variants) ## Usage guidelines Icon Buttons are best used when limited space is available and when the icon being used is clear in meaning and intent. They are a great way of presenting a series of repeatable common actions related to a specific panel or data grid. Icon buttons however sacrifice legibility for space and rely on the icon being used to be clear in meaning and intent at a glance. Ask yourself the below questions in order before considering using an Icon Button. If your answer is Yes to all of the below questions, then an Icon Button can be used. Otherwise, a Standard Button might be a safer choice. Question one Question two Question three Is the action a core task? Is the icon button an uncommon action e.g. create scope? Can the action be easily understood with just an icon and a tooltip? If you decide that an Icon Button is the right choice, adhere to the following best practices: Always ensure the built-in tooltip comes with a clear and concise description of the intended action Try to ensure the Icon is easily understood and not already used elsewhere for another action ## Component variants ### Primary Primary Icon Buttons are reserved for the highest priority actions on the panel and should only be considered if there isn’t enough space for a Primary Button or Primary Split Button. Use sparingly as primary icon Buttons run the risk of being lost on a page. Always pair with a tooltip to ensure the most clarity on the primary action being shown. ### Default Default Icon Buttons are best used for core functionality such as up-down navigation or opening in new window. It can also be used as a replacement for Default Buttons if a Default Button or Default Split Button only when there isn’t enough space for either. Try to always pair with a tooltip to ensure the action is easily understood when interacting with it ### Ghost Ghost Icon Buttons are best used for the lowest priority core functionality, such as closing a modal. A tooltip on hover may not be required for these sorts of actions. However, the icon being used must be contextually understandable at a glance without the need for supporting information. It may not need a tooltip, but it should be easily understood what will happen when clicked 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Icon Button","path":"/?path=/docs/components-icon-button--docs"},{"text":"Image UI/UX Build Documentation ## Overview ## Image types ### Default Default Image usage ### Cover If the provided aspect ratio doesn’t match that of the actual image, the default behavior is to fill as much space as possible without cropping. To fill the entire available space use the cover prop. ### Height wtg-image will automatically grow to the size of its src, preserving the correct aspect ratio. You can limit this with the height and max-height props. #### Height #### height with cover #### max-height #### max-height with cover","title":"Image","path":"/?path=/docs/icons-images-image--docs"},{"text":"WtgLabel UI/UX Build Documentation ## Overview Component preview","title":"WtgLabel","path":"/?path=/docs/utilities-label--docs"},{"text":"LayoutGrid UI/UX Build Documentation ## Overview Component preview","title":"LayoutGrid","path":"/?path=/docs/utilities-layout-grid--docs"},{"text":"Main UI/UX Build Documentation ## Overview","title":"Main","path":"/?path=/docs/utilities-main--docs"},{"text":"Masthead UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=305%3A20612&mode=design&t=CjNcT6ckPZSb6gCU-1) ## Overview ## Overview The Masthead component, a key element of the core framework, appears at the top of every portal page. Customizable to meet the specific requirements of different portals, its primary role is to orient users, provide easy navigation, and offer consistent actions across entities. ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) [Masthead configuration](#masthead-configuration) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Masthead configuration The configuration of the Masthead component is designed to be flexible, varying according to the specific needs of each portal and the type of page it's displayed on. Here are two types of Masthead that are typical in CargoWise portals: Non-entity Masthead Typically contains: Breadcrumbs: This helps you track your location and easily move back through your workflow. Notifcations Provides feedback on events requiring action or attention across the portal. Entity Masthead Additional functionality is added to the Masthead when a user enters an entity: Entity header and back button: Provides context about the entity the user is currently in with an option to return to the previous page. Content tabs: Allows the user to navigate to different sections within an entity. Entity action menu: A set of actions that can be performed on the entity, available consistently at this level. Entity actions within the menu are configurable to unique portal requirements. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01) ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Masthead","path":"/?path=/docs/conceptual-masthead--docs"},{"text":"Modal UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=305%3A20913&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Modal","path":"/?path=/docs/utilities-modal--docs"},{"text":"Modal Alert 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=13121%3A1708&mode=design&t=Z4johADr7KWvCw44-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Modal Alert","path":"/?path=/docs/conceptual-modal-alert--docs"},{"text":"Navigation UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=310%3A18203&mode=design&t=CjNcT6ckPZSb6gCU-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Limit Navigation item hierarchy to a maximum of three levels. Beyond this, the length of item labels becomes drastically reduced. Select icons for top-level navigation items only. Ensure they are distinct and convey the page they represent. Avoid using icons that are widely recognized for standard UI actions in the Navigation. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Navigation","path":"/?path=/docs/conceptual-navigation--docs"},{"text":"Number Field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=11001%3A36068&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Number Field","path":"/?path=/docs/components-number-field--docs"},{"text":"Overlay UI/UX {' '} Build Documentation ## Overview","title":"Overlay","path":"/?path=/docs/utilities-overlay--docs"},{"text":"Panel 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=3156%3A35922&mode=design&t=CjNcT6ckPZSb6gCU-1) ## Overview Component preview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) {/* [Default](#default) */} ## Quick tips All content should be contained within a Panel. Use a header within a Panel to define it's content clearly, unless there is a similar related header in close proximity to the Panel. Use panels to clarify groupings and relationships between content. ## Best practices ### Panel sizing The grid system allows panels to have a width ranging from a minimum of 2 columns to a maximum of 12 columns. Based on your content requirements, you can choose a panel size between these parameters. The Grid can help you decide on the appropriate size. Some examples are: 12-column Panel: Ideal for full-page display of Datagrids requiring ample horizontal space. 2-column Panels: Perfect for referential information or primary interactive content driving displayed data or workflows. In smaller Panels like this, stack content vertically and avoid placing multiple input fields side by side. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Panel","path":"/?path=/docs/components-panel--docs"},{"text":"Password Field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=15074%3A35626&mode=design&t=kAb7Hqo3SkT1HnY0-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Password Field","path":"/?path=/docs/components-password-field--docs"},{"text":"Popover 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=15532%3A45539&mode=design&t=lbSYfqKoG50qqGRs-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Popover","path":"/?path=/docs/components-popover--docs"},{"text":"Progress Circular 1.0.0 UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips By default, a circular progress has the correct color for either a light or a dark background. The color prop allows you to select your own color. Using the indeterminate prop, a circular progress continues to animate indefinitely. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Progress Circular","path":"/?path=/docs/components-progress-circular--docs"},{"text":"Progress Linear 1.0.0 UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips By default, a linear progress has the correct color for either a light or a dark background. The color prop allows you to select your own color. Using the indeterminate prop, a linear progress continues to animate indefinitely. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Progress Linear","path":"/?path=/docs/components-progress-linear--docs"},{"text":"Radio 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=149%3A5677&mode=design&t=CjNcT6ckPZSb6gCU-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) [Component variants](#component-variants) ## Quick tips Radio Buttons must have at least 2 options. On page load Radio Buttons must always have a default selection placed at the top of the list. Avoid using Radio Buttons in nested lists. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Component variants ### Radio group ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Radio","path":"/?path=/docs/components-radio--docs"},{"text":"Row UI/UX Build Documentation ## Overview","title":"Row","path":"/?path=/docs/utilities-row--docs"},{"text":"Search Field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=11001%3A36598&mode=design&t=CGPOGaRUwzeiT80f-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":" Search Field","path":"/?path=/docs/components-search-field--docs"},{"text":"Segmented Control 1.0.0 UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Segmented Control","path":"/?path=/docs/components-segmented-control--docs"},{"text":"Select Field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=11001%3A36333&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview Component preview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Best used for selecting a single option from between 4 or more pre-defined options. Use a short, clear description for the label of the Input Select. List options alphabetically or via another logical order where applicable ## Best practices ### Optional searching Should your Input Select have many options, then you can also consider adding a free-text search field into the dropdown at the top of your options. This allows the user to quickly find the specific option from the list without having to scroll. Only consider if your list is exceptionally long. ### Categorization If you have a long and complex Select list, it may be helpful to group similar items into categories. You can display these categories as non-interactable sub-headers within the Select list. This will make it easier to organize large amounts of content and present options in a way that is more relevant to the user. Categories are a great way to organise a series of related items under a common heading in complex input select lists. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Select Field","path":"/?path=/docs/components-select-field--docs"},{"text":"Spacer UI/UX Build Documentation ## Overview","title":"Spacer","path":"/?path=/docs/utilities-spacer--docs"},{"text":"Split Button 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=9390%3A12042&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Split Button","path":"/?path=/docs/components-split-button--docs"},{"text":"Status 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=149%3A5674&mode=design&t=CjNcT6ckPZSb6gCU-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Status","path":"/?path=/docs/components-status--docs"},{"text":"Switch 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=3789%3A1896&mode=design&t=CjNcT6ckPZSb6gCU-1) ## Overview ⚠️ Please note Switches can often be misinterpreted, so their use should be carefully evaluated. Immediate system changes are not typical in the CargoWise interaction paradigm. Consider that Switches and their pattern may be better suited for mobile experiences. ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Before using a switch, consider whether Radio Buttons or Checkboxes would be more suitable for your use case. Switch labels should clearly indicate the action that will occur when the switch is turned on; they should avoid being ambiguous. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Switch","path":"/?path=/docs/components-switch--docs"},{"text":"Table UI/UX Build Documentation ## Overview ### Default usage ### Height Use the height prop to set the height of the table. ### Fixed header Use the fixed-header prop together with the height prop to fix the header to the top of the table.","title":"Table","path":"/?path=/docs/utilities-table--docs"},{"text":"Tabs UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=149%3A5681&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Tabs","path":"/?path=/docs/conceptual-tabs--docs"},{"text":"Text Area 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=10997%3A33971&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Best used for long form text which spans over multiple lines. Be sure to provide enough space for the expected user input. Label your Text Area clearly so that the appropriate longform content is added. ## Best practices ### Default Text Area The Default Text Area is your standard multi-line input. It should be used when a user needs to free-type long-form content into the UI and requires more space than a single-line text input can provide. Text Areas should be set by default to the expected size of the user’s content and can be adjusted for more space if required. ### Rich Text Area Rich Text Areas provide an interface for editing “rich text” content (i.e. with formatting). Rich Text Areas come with a number of basic formatting options such as bold, italic and underlines. Rich Text Areas are best used when a user’s long-form copy requires additional style and formatting in order to create a clearer hierarchy and structure in the content being submitted. Allow enough space for Rich Text Areas to give the user enough room to add and format their copy. If possible, place a Rich Text Area on a separate line in a form and avoid positioning it next to other inputs. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Text Area","path":"/?path=/docs/components-text-area--docs"},{"text":"Text Field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=10193%3A16735&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Best used for small amounts of text that can fit on a single line in the available space. Flag mandatory Fields as necessary and try to group them near each other where possible. Be sure to use the Numerical Text Field for number values when applicable. ## Best practices ### Truncation Text Fields are designed to hold a small amount of content, one or two words, or a brief sentence. When the Text Field is filled with more content than it can display, the text exceeding the limit is truncated and replaced with three dots “…” on the far right of the Field. If you find too much content being added frequently, we recommend using a Text Area instead. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Text Field","path":"/?path=/docs/components-text-field--docs"},{"text":"Theme Provider UI/UX Build Documentation ## Overview Component preview","title":"Theme Provider","path":"/?path=/docs/utilities-theme-provider--docs"},{"text":"Time Field 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=11001%3A35803&mode=design&t=JPdWB6zxo7Izr3NK-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips Time always represented in 24 hour time. Timezone option is manually set from the organization level. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ### Time Select Used to capture a single instance of a 24-hour time input from the user. Interacting with the field will give users a dedicated dropdown for Hour and minute fields. Once a time has been selected, the dropdown menu will close and only re-open upon another interaction. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Time Field","path":"/?path=/docs/components-time-field--docs"},{"text":"Time Picker 1.0.0 UI/UX Build Documentation ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Time Picker","path":"/?path=/docs/conceptual-time-picker--docs"},{"text":"Tooltip 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=152%3A36563&mode=design&t=drcw3SerpNLaU1aU-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Tooltip","path":"/?path=/docs/components-tooltip--docs"},{"text":"UnitField 1.0.0 UI/UX Build Documentation [Available in Figma](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=11001%3A36068&mode=design&t=ql777v7iL0cPgfGA-1) ## Overview ## Table of contents [Overview](#overview) [Quick tips](#quick-tips) [Best practices](#best-practices) ## Quick tips 🔨 Please bear with us while we're getting this part of our documentation ready. ## Best practices 🔨 Please bear with us while we're getting this part of our documentation ready. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"UnitField","path":"/?path=/docs/components-unit-field--docs"},{"text":"Localization As an international company, we've got to ensure our user interface displays correctly in each of the many languages we support. It is - how to display a date - how to format numbers - whether the language is read from left-to-right or right-to-left (RTL) - and even things like what to consider to be the first day of a week. We refer to the combination of a language and the regional rules as a locale. A locale is uniquely identified by a language code. Select a locale from the globe in the toolbar and the changes will be reflected in the sections below. ## Date and Time ## Translatable captions ## RTL Support Certain languages such as Arabic and Hebrew are read from right-to-left (RTL). We mirror UI's for RTL locales to ensure content is easy to understand. Try it out by selecting the 'ar' locale from the globe in the toolbar. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Localization","path":"/?path=/docs/guidelines-localization--docs"},{"text":"About Supply Supply is WiseTech Global's multi-channel Design System. It provides our product teams with guidelines, tools, and standards that help to develop modern, cohesive, and accessible experiences in an effective and scalable manner powered by GLOW. What is a Design System? Design Systems bring order and cohesion to digital products. They help protect the brand, elevate the user experience, and increase the speed and efficiency of designing and building products. Design systems in software are often compared to LEGO bricks. Just as LEGO bricks allow the construction of diverse models through imagination, software components enable various builds. However, guidelines and instructions are crucial to achieve consistent, high-quality builds. Design systems offer guidelines and education to product teams comparable to a LEGO instruction manual for builders, ensuring successful, cohesive software development at scale. Our Goals Establish a cohesive user experience that helps to design and build high-quality applications Champion design awareness across the business by uplifting our user experiences to contemporary expectations Establish a centralised knowledge hub and create a shared vocabulary between design, engineering and product management Increase efficiency in designing and building production-ready applications Ensuring applications are future-friendly due to ongoing iterations of the design system itself ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"About","path":"/?path=/docs/about--overview"},{"text":"Changelog ## Changelog - 1.0.0-alpha.15 [**********](https://svc-ediprod.wtg.zone/Services/edit/WI/**********) - Icon Library Update 1.3.3 [**********](https://svc-ediprod.wtg.zone/Services/edit/WI/**********) - RTL compatibility [**********](https://svc-ediprod.wtg.zone/Services/edit/WI/**********) - Supply Time Picker Component ## Changelog - 1.0.0-alpha.14 [**********](https://svc-ediprod.wtg.zone/Services/edit/WI/**********) - WtgPasswordField Show/Hide Password Functionality [**********](https://svc-ediprod.wtg.zone/Services/edit/WI/**********) - Support for restricted prop on inputs [WI00719216](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00719216) - Support for notifications on WtgAvatar [WI00720976](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00720976) - WtgChart ## Changelog - 1.0.0-alpha.13 [WI00713802](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00713802) - SUPPLY Address Search Component 1.0 [WI00714356](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00714356) - SUPPLY Button Toggle Component 1.0 [WI00717504](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00717504) - Icon Library Update 1.3.0 ## Changelog - 1.0.0-alpha.12 [WI00708948](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00708948) - WtgDisplayField [WI00713120](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00713120) - SUPPLYFieldsDisplayOnlyAppearance [WI00712424](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00712424) - SUPPLY WtgTextArea [WI00709623](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00709623) - Status component [WI00711498](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00711498) - SUPPLY Date Picker Component [WI00713390](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00713390) - SUPPLY Select Component 1.0 ## Changelog - 1.0.0-alpha.11 [WI00705123](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00705123) - WtgHyperlinkComponent [WI00703418](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00703418) - Tooltip Support [WI00706672](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00706672) - WtgTable Component [WI00704460](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00704460) - WtgActionBar Component [WI00705619](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00705619) - WtgModalAlert and WtgModal Components [WI00709890](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00709890) - Icon library update 1.2.3 [WI00709004](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00709004) - WtgLabel Component [WI00711421](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00711421) - WtgOverlayComponent [WI00706006](https://svc-ediprod.wtg.zone/Services/edit/WI/WI00706006) - WtgRadioGroup Component ## Changelog - 1.0.0-alpha.10 [WI00700770](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/1b9f6f0f-f0df-4e26-b743-0fc8bf07c244?lang=en-gb) - Icon Library 1.2.1 [WI00703188](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/2407c46b-b19a-420c-bb4d-7dae75aa600b?lang=en-gb) - Input Validation Logic [WI00704982](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/df6443bc-6665-4e68-ab4f-35aacf9990e4?lang=en-gb) - WtgFooter Component 2024 / 01 / 29 ## Changelog - 1.0.0-alpha.09 [WI00698202](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/7e9199e0-cf28-4d94-811b-6fa06ede8b7c?lang=en-gb) - Icon Library 1.1.9 [WI00697628](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/11b95b82-73fe-4d54-b5ac-f67be39e6b30?lang=en-gb) - WtgSearchField Component [WI00697144](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/82290594-514e-4e4c-aeb7-3e2959aa2c73?lang=en-gb) - WtgTimeField Component [WI00697610](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/fa7dd3ec-1940-4729-a03b-cf18811295de?lang=en-gb) - WtgDateTimeField Component [WI00699419](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/cbc630da-01c1-48d4-9b02-1b7ee6a2f261?lang=en-gb) - WtgDateRangeField Component [WI00700437](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/647f3cc7-e9d1-4b2a-9119-20f342cafe89?lang=en-gb) - WtgDurationField Component [WI00697249](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/79ee48c4-0323-49bf-bacb-db911e4d4378?lang=en-gb) - WtgNumberField Component 2024 / 01 / 22 ## Changelog - 1.0.0-alpha.08 [WI00690518](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/3d1f0ca8-e425-44ef-bd39-f7fa5ebc34e5?lang=en-gb) - WtgPopover Component [WI00690515](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/dfd06fc3-e31d-4d8d-8245-cff0967058e2?lang=en-gb) - WtgNavigation Component [WI00689098](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/8a9fb50e-8c7a-4422-9626-1fe4a68c98ba?lang=en-gb) - WtgDateField Component 2024 / 01 / 19 ## Changelog - 1.0.0-alpha.07 [WI00676848](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/7617cda3-7e7d-42fa-a0d6-73429d10cd7d?lang=en-gb) - WtgTabs Component [WI00688325](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/9ddbe545-66df-4a98-8aa3-0cf0f37d0468?lang=en-gb) - Barcode Icon Added [WI00676862](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/6447837c-7157-4bdf-a4ff-86eca71329af?lang=en-gb) - WtgMasthead component [WI00687352](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/f12737d1-bcf6-4b35-b9d1-434fad1ed0a7?lang=en-gb) - WtgTextField component [WI00689855](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/e92d7c3c-56b8-40be-8c39-b181848a1df4?lang=en-gb) - WtgPassword component 2023 / 12 / 15 ## Changelog - 1.0.0-alpha.06 [WI00684832](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/085ce673-c9e1-4077-b35b-cb46556c8918?lang=en-gb) - Theme Provider [WI00681545](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/5efd6d0f-5c43-459a-9a18-f37e43a534cd?lang=en-gb) - Storybook Layout Stories 2023 / 12 / 08 ## Changelog - 1.0.0-alpha.05 [WI00684776](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/69bc8577-9e50-478d-812d-cefd0dc1ae7e?lang=en-gb) - Button Sentiment property [WI00684327](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/b1f8d09a-fa08-4372-a649-7776bae3c551?lang=en-gb) - Button Fill property [WI00684299](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/334a4622-b742-43fd-b0f9-7ccbefa5036d?lang=en-gb) - Localization example [WI00676847](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/dcdbc302-aeeb-4e77-97b5-8ead9641bc00?lang=en-gb) - WtgExpander Component (alpha - dev) [WI00682420](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/d8430dce-39f3-470a-be1d-f11157924360?lang=en-gb) - WtgEntityActions Component (alpha - dev) [WI00680000](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/0e8181be-b713-4d5c-9803-b0d96abcf378?lang=en-gb) - WtgBreadcrumbs Component (alpha - dev) [WI00677211](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/bdbce73a-b038-4ad2-b2cd-b613d54956e5?lang=en-gb) - WgtSplitButton Component (alpha - dev) 2023 / 12 / 01 ## Changelog - 1.0.0-alpha.04 [WI00679374](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/6e718fb9-6be7-4c68-a1ce-0121a35755c6?lang=en-gb) - loading indicator position [WI00678290](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/558f125d-4ff6-4eb5-9b1d-997ec1eefb86?lang=en-gb) - iconography and tokens for GLOW [WI00678412](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/e0229783-b74e-4323-8da1-1dc94afc721b?lang=en-gb) - Size Composable [WI00676438](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/21c4fd3e-41d7-4bde-ae48-a0cbb3edde96?lang=en-gb) - OnePassword autofill not working [WI00676846](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/WorkItem/beed9d8d-3465-40f9-8eef-44ca4117314c?lang=en-gb) - WtgAlert Component (alpha - dev) 2023 / 11 / 18 ## Changelog - 1.0.0-alpha.03 WI00675276 - LayoutGrid Component (alpha - dev) WI00675276 - Box Component (alpha - dev) Button WI00672549 - Fixed mouse behaviour WI00676828 - LeadingIcon and trailingIcon props WI00677039 - Dark mode appearance 2023 / 11 / 10 ## Changelog - 1.0.0-alpha.02 WI00672019 - Switch Component (alpha - dev) WI00665012 - Badge Component (alpha - dev) WI00673655 - Themeing for GLOW 2 pages in GLOW 1 portals WI00671976 - Remove GLOW dense property from Layout Grids WI00672487 - CTP portal SUPPLY 0.5 WI00662394 - Datatable grouping close groups button 2023 / 11 / 04 ## Changelog - 1.0.0-alpha.01 WI00665006 - Avatar Component (alpha - dev) WI00647898 - Button Component (alpha - dev) WI00657727 - Checkbox Component (alpha - dev) WI00665004 - Chip Component (alpha - dev) WI00663266 - Icon Component (alpha - dev) WI00647898 - Panel Component (alpha - dev) WI00661509 - Radio Component (alpha - dev) 2023 / 10 / 27","title":"Changelog","path":"/?path=/docs/changelog--overview"},{"text":"Conceptual components ## What are conceptual components? Conceptual Components represent forward-thinking design ideas and innovations that have not yet been implemented in our GLOW environment. These components are envisioned as part of our design system's future evolution, showcasing potential directions and enhancements for our user interfaces. They serve as a canvas for exploring new ideas, solving user needs in novel ways, and enhancing the overall user experience. ## What is their purpose? The primary goal of Conceptual Components is to spark discussion, inspire creativity, and gather feedback within our design and development teams. They provide a glimpse into potential future developments and allow us to align our design strategy with emerging user needs and technological advancements. ## What is the current status? It's ## Feedback and collaboration We encourage all team members to engage with these Conceptual Components—providing feedback, suggesting modifications, or contributing new ideas. Collaboration is key to refining these concepts and integrating successful components into our GLOW environment in the future. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Conceptual/Overview","path":"/?path=/docs/conceptual-overview--overview"},{"text":"Contact Need assistance? We're here to help. Key contacts Paula Zitinski <NAME_EMAIL> Marc Obieglo UI & <NAME_EMAIL> Donn Delaney <NAME_EMAIL> Join our MS Teams community We have established a Design Systems User group on Microsoft Teams and encourage everyone to join. It's a dedicated open forum for troubleshooting, problem-solving, and support related to Supply. You can also keep up to date with the latest releases of our Figma & Component libraries here.{' '} Join our MS Teams community{' '}","title":"Contact","path":"/?path=/docs/contact--overview"},{"text":"Color system Our color palette is designed to support the various use cases of our product, CargoWise. Accessibility in color is ratios, and ensuring information is not being conveyed using color alone. [Architecture](#architecture) [How it works](#how-it-works) [Application](#application) ## Architecture The system is built on four layers that give each color a specific role across our applications. ## How it works ### Sentiment Colors can convey specific sentiments and emotions to the user. Primary The Primary color is displayed most frequently across screens and components, patterns and layouts. It is used to convey high-emphasis actions. Neutral The Neutral color is used for general UI Interface elements but can also be used as a Secondary option for Buttons and other interactive components. Success The Success color is used after an action is performed successfully and the status needs to be fed back to the user. Info The Info color is used when a general message needs to be conveyed to the user without distracting them from their current task. Warning The Warning color is used when a message needs to be conveyed to the user with a higher prominence than the Info color. Critical The Critical color is used whenever there is a risk of performing a destructive action. ### Usage Describes the UI element to which this color is applied. Background Used as the background color for elements such as Button, Alerts, Cards. Border Used as the border color for elements such as Button, Alerts, Cards. Text & Icon Used as the text & icon colour for elements such as Button labels, Alerts headline, Card headline ### Prominence This layer describes the visual prominence of color within the interface. Default Full brightness and expression of the color. The default appearance aims for a minimum of 4.5:1 Contrast ratio to achieve WCAG AA compliance. Weak This color denotes a lower hierarchy than standard drawing slightly less attention. Weak aims to achieve a contrast ratio of 3.0:1 for non-text and 4.5:1 for text colors. ### Modifier This layer includes the interactive state for color. Default Default color state of an interactive element. Hover Default color but slightly lightened by 10-20% and used when hovering over an element. Active Default color but slightly darkened by 10-20% and used to display an element in its active or focus state. Disabled Colors that signal inactive UI elements, designed to draw less attention than a default state. ## Application ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Design Tokens/Color/Overview","path":"/?path=/docs/design-tokens-color-overview--overview"},{"text":"Color design tokens Neutral Primary Success Error Warning Info Brand ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Design Tokens/Color/Tokens","path":"/?path=/docs/design-tokens-color-tokens--overview"},{"text":"Grid concept Grids are a fundamental concept in web design that involves using a systematic layout structure consisting of columns and rows. They function as an invisible framework for organizing and aligning content, offering a structured yet unseen guide for page layouts. Quick tips When designing in Figma, use the [Supply Grid styles](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=68%3A578&mode=design&t=CjNcT6ckPZSb6gCU-1) to align elements and determine the correct spacing between Panels and content. Maintain the Grid settings as-is in Figma to ensure a consistent structure across all your pages. Gutters & Margins Gutters refer to the spacing between columns within our Grid system. They are essential for maintaining separation and alignment between different sections of content. In our design system, we employ a fixed gutter spacing, which helps create clear visual boundaries and improves the overall readability of the interface. Margins are similar to gutters but are used along the outside of a page to help keep content within a standard viewport. This ensures that elements are evenly placed on the screen, providing an optimal user experience regardless of the screen size you are working on. Columns Columns define the areas within a design where content is placed. In our Grid system, the width of each column is calculated based on the available screen width, taking into account the 12-column layout, gutter, and margin px values. Columns are the vertical divisions or sections that help define the spacing and vertical dimension of content within a Grid system. Columns provide a systematic way to arrange elements like text, images, buttons, and other components in a balanced and visually appealing manner. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Guidelines/Layouts/Grid concept","path":"/?path=/docs/guidelines-layouts-grid-concept--overview"},{"text":"Object styles Typography plays a crucial role in establishing the visual identity and legibility of our design system. Our typography choices are carefully selected to ensure optimal readability and maintain a consistent hierarchy across various parts of the product experiences. ## Border radius Border-radius is used to give any element rounded corners, this is applied across multiple patterns and components to provide a more approachable design style through our elements. Border radius can be used on anything from Panels to input fields. ## Elevation Elevation is the distance between two surfaces on the z-axis, it is represented in design tokens as different severities of drop shadow that can be applied to various windows to provide additional hierarchy on a screen. Elevation helps to define where one surface ends and another begins when stacked on top of each other. ### Elevation 100 Apply the default elevation style to infuse common components with a sense of depth and emphasize elements that are interactive for the user. Panel Button Navigation Input Alert ### Elevation 200 Utilize this elevation for elements that demand greater user attention. By conceptually placing these components closer to the user, they appear more significant in the visual hierarchy, emphasizing their Sheet Modal ### Elevation 300 This elevation style is the least commonly used and is reserved for elements that are triggered from elements that already have an elevated appearance. For instance, it could be applied to a modal that needs to be opened via the Sheet. ### Popover Popover styling is employed for elements like Selects, Dropdowns, and Filters, where a user initiates an action that necessitates displaying a set of actions or data in a popover element. Keep in mind that this style already accounts for the hierarchy of a popover positioned above any other element using an elevated style. Besides the modified box-shadow values, these popovers also feature a slightly reduced background opacity (94%) to enable the application of a background blur for aesthetic purposes. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Design Tokens/Object styles","path":"/?path=/docs/design-tokens-object-styles--overview"},{"text":"Object styles","title":"Design Tokens/Object styles/Tokens","path":"/?path=/docs/design-tokens-object-styles-tokens--overview"},{"text":"What are design tokens? Design Tokens are a technology-agnostic way to organize and consolidate UI variables such as color, spacing or size. Tokens provide flexibility and control over the fundamental building blocks of a design system and ensure both UI/UX Designers and Developers share a common language and structure for components, patterns and layouts. [Why are Tokens [How do they work?](#how-do-they-work) [Token structure](#token-structure) [Primitive Tokens](#primitive-tokens) [Semantic Tokens](#semantic-tokens) [Modes](#modes) ## Why are Tokens Tokens enable a design system to have a single source of truth across code and UI/UX design. They provide a series of reusable assets that are used as building blocks for new designs, maintaining an underlying structure of defined style choices as well as providing a way to manage and maintain design changes across the product. A unified approach to application design across UI/UX and development The power to push style updates and changes through to an entire product or suite of products easily Establish and record consistent series of UI/UX and developer choices that can be re-used Create a consistent and trusted experience for the user through the entire application ## How do they work? Design Tokens represent the small, repeated design decisions that make up the visual style of a design system. They are used to replace static values and are instead created as reference points for development and design work to be built on top of. In this instance, the way the button has been designed in Figma is directly reflected in the way developers have built the button in the code. For the Background color, both Designers and Developers are referencing the same Token called “Primary.bg-default” which points to a static hex code. Design and engineering can now be confident the same color will be used in both places. Consistency remains intact even if the assigned color value to the Token is modified or updated. ## Token structure ### Primitive tokens Primitive Tokens are the core values of the design system, creating and defining all of the available style options that can be referenced. Definitions for spacing, padding & sizing Typestyles and weights Hex codes for colour values Primitives provide the base value for Semantic Tokens to point to, based on the device, brand, screen size or another context. ### Semantic tokens Semantic Tokens are the actual applied values that comprise the makeup of a component or pattern. They are organized by specific use cases and define the structure of a component by providing a visual guide for elements like spacing, color, typography and size. Semantic Tokens help Designers and Engineers avoid rigid values like hex codes or specific pixel sizes. Using the above structure provides a powerful way for Designers and Engineers to create and implement designs that are flexible, scalable, and consistent. ### Modes Modes serve as a collection of options for the same Token definition that can be swapped in and out depending on what is needed. This allows for a component to point to a single Token location and have its values updated when the Token is used in a different context. For example, a Button component could have multiple Modes containing different Tokens for values like background color, text, and size when switching between dark mode or small screen. By using modes you only need to apply a single token to a component while still allowing it to switch depending on where it is being used or viewed. Adjust size when switching from desktop to mobile Invert color based on whether you’re in light or dark mode Change color entirely if designing for another brand Modes provide a way for Semantic Tokens to adjust based on contextual needs while still only referencing a single value in the system. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Design Tokens/Overview","path":"/?path=/docs/design-tokens-overview--overview"},{"text":"Spacing, padding & sizing system Spacing guidelines help define consistent space between elements of a design in order to make content easier to scan. The use of a spacing system creates a more harmonious experience for the end user, providing a foundation for responsive design and customisable UI density for different screen sizes. ## Spacing guidance Spacing tokens should be used based on the needs of the designs, with smaller tokens being reserved for limited space components and patterns and larger tokens reserved for padding inside of or larger components. While not set rules here are some examples of where we use and apply specific tokens XS Used between very small elements such as an icon next to a label M Used to space between a group of like elements such as a series of buttons in an action bar XL Used to create breathing room between larger elements on a page such as default space between panels or space around a framework component like the action bar or masthead. ## Padding guidance Padding tokens provide a consistent set of values that can be applied inside of a component to create breathing room between specific elements within a component or pattern such as the space between an icon and text inside of a button component. Padding tokens should be used based on the needs of the designs, with smaller tokens being reserved for limited space components and patterns and larger tokens reserved for padding inside of or larger components. While not set rules here are some examples of where we use and apply specific tokens. XS Used to provide breathing room around an Icon button S Used on the top and bottom of buttons to match button size with line height M Used to define breathing room and the left and right of a button for better legibility XL Default padding inside of larger elements such as panels to provide adequate breathing room ## Sizing guidance Our sizing system allow for different visual treatments to be placed within content depending on the needs of the design. It helps to provide a guideline for how to use icons and how large or small they can be presented on a screen. The sizing values are further defined in our design tokens section. Sizing tokens should be used sparingly and only based on the needs of the designs when an element needs to be hard defined as a certain size in order to better sit next too other components on a page. While not set rules here are some examples of where we use and apply specific tokens M Used to hard define the bounding box around an icon in order to keep it consistently sized next to labels or text. XL Used to define the largest supported size that the avatar component can be in a design. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Design Tokens/Space","path":"/?path=/docs/design-tokens-space--overview"},{"text":"Padding, Spacing & Sizing","title":"Design Tokens/Space/Tokens","path":"/?path=/docs/design-tokens-space-tokens--overview"},{"text":"Typography system Typography plays a crucial role in establishing the visual identity and legibility of our design system. Our typography choices are carefully selected to ensure optimal readability and maintain a consistent hierarchy across various parts of the product experiences.{' '} [Typeface](#typeface) [Numerics](#numerics) [Styles](#styles) [Scale](#scale) [Line height](#line-height) [Line length](#line-length) ## Typeface Our primary typeface is Inter, chosen for its high-contrast san-serif design and legibility across large and small text sizes. It also supports multiple language glyph types and is particularly suited to our smaller-than-average baseline typography size of 13px.{' '} ## Numerics Inter supports a secondary mono style which we use for displaying numbers within our system. This subtle change allows for easier scalability of large sections of text and provides a visual differentiator between body copy and numerical copy such as order codes. ## Styles Our typography style is carefully crafted to ensure consistency and usability across our products. Headers, body copy, and other design tokens related to typography have already been configured with predefined bold, roman, or italic characteristics. This approach guarantees consistent and coherent usage throughout our various product experiences.{' '} ## Scale Our typographic avoids semantic naming conventions such as H1, H2, etc., due to the nature of our products we needed a system that had more flexibility for our diverse product experiences. Instead, our scale is based on a 13px font size and is adjusted up and down using rem scaling. The decision for a smaller-than-average font size of 13px was deliberately made in order to support more information being presented on screen at once. ## Line height Consistent line height is essential for ensuring legibility and ease of reading. Our line height is based on a 16px x-height in order to provide maximum legibility for body copy across the application. Our Design tokens for typography have line-height prebuilt into their characteristics to ensure consistency is maintained regardless of the application. ## Line length To enhance readability, our typographic guidelines suggest keeping line lengths within a specific range. We recommend aiming for around 50-90 characters, including spacing, in each line. Shorter lines are generally more comfortable to read, as they prevent eye fatigue and maintain focus. By adhering to an optimal line length, we can enhance the overall reading experience for our users. ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Design Tokens/Typography","path":"/?path=/docs/design-tokens-typography--overview"},{"text":"export const typography = { type: { primary: '\"Inter\", sans-serif', }, weight: { regular: '400', medium: '500', semibold: '600', bold: '700', }, size: { s100: 10, s200: 12, s300: 13, s400: 16, s500: 20, s600: 28, s700: 32, s800: 40, }, }; export const SampleText = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'; Typography **Font:** Inter **Weights:** 400(regular), 500(medium), 600(semibold), 700(bold) ## Large Screen Typescale ## Display ## Title ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Design Tokens/Typography/Tokens","path":"/?path=/docs/design-tokens-typography-tokens--overview"},{"text":"Channels ## Overview The Supply design system is designed with a multi-channel approach in mind. This allows maximum flexibility for our broad range of use cases and audiences. All channels have specific design principles to guide decision-making when designing applications. [Core, Surface & Satellite](#core-surface--satellite) [Core channel](#core-channel) [Surface channel](#surface-channel) [Satellite channel](#satellite-channel) [Design principles](#design-principles) ## Core, Surface & Satellite We use three distinct channels to categorize our applications. The key differentiator between all three channels is the required amount of guidance and device specificity. ### Core channel The majority of CargoWise applications that use Supply will fall into this category. Most users in this Channel are logistics experts with extensive CargoWise knowledge and don’t require a lot of guidance when fulfilling their tasks. Taking a detailed form as an example, the best practice approach is to display all fields directly, instead of breaking it down into a step-by-step wizard. Overall UI / Form Types Dense forms Everything required for task at hand Primary goal Focus on efficiency to task completion Give experts the data they need Level of expertise Logistics & CargoWise experts Level of expertise Low ### Surface channel The standout differentiator here is that experiences allow for more user guidance. The typical Surface application will also heavily use curated Dashboard concepts and other content aggregators to provide a top-level view of their operations. Breaking down forms into multiple steps and making use of components such as helper texts is encouraged in this Channel to ensure users feel supported. Processes are typically more linear than they are in the Core Channel. Overall UI / Form Types Less dense More guided workflows Primary goal Deliver overviews Enable users to pay attention to the right things Level of expertise Logistics professionals - no prior CW knowledge expected Level of expertise Medium ### Satellite channel This Channel is for purpose-built and device-specific applications and has the broadest range of users. Users can range from Logistics experts in big corporations to Christmas casual workers helping out in warehouses for the holiday rush. Design principles will not be established for this channel due to its broad range of applications. Applications can range from Truck dashboards to RF-scanning devices and many more. Over time, groups of use cases might evolve in this space that constitute their own channel. Overall UI / Form Types Use case specific (mobile, tablet, display) Primary goal Focus on leveraging device to speed up processes & tasks for my specific use case Level of expertise Specific to use case and environment (Warehouse Workers, Truckers, Logistic experts) Level of expertise Low to Medium ## Design principles Design principles are a set of values that act as a **compass** for our products. They’re an **agreed-upon** truth: the guideposts that keep our entire team on the same path as we move through the design process. Design principles help us **establish the values** of our products, and then make decisions that uphold the integrity of those values. When we have a set of agreements to come back to throughout an iterative design process, we will automatically have a shared language and set of criteria to use while working together. ### Global design principle The global design principle is applied to every channel regardless of use case, user groups, and devices. It will help us create cohesive and seamless experiences across all channels. ☝️ **Prioritise efficiency** Our experiences prioritize efficiency and productivity over aesthetics. ### Core channel 🤓 **Complexity is power, in the hands of an expert** Embrace complexity and guide users confidently through their decisions by providing contextual, timely and accurate information. 🖌️ **Design to customize** Prioritize user preferences, design with modularity, and be prepared for variable processes. ### Surface channel 💪 **Guide to empower** Empower users by providing curated experiences that will support them in making complex decisions confidently. 🪄 **Direct attention** Direct attention to what's actions, we ensure users remain informed and ready to act, enhancing productivity without clutter. ### Satellite channel ⚠️ **Please note** This channel will not have a default set of design principles as guidance and rules in this space have to be defined on a case-by-case basis. However, the global design principles still apply.{' '} ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Guidelines/Channels","path":"/?path=/docs/guidelines-channels--overview"},{"text":"Contributing We invite and encourage contributions to our design system. How to contribute If you would like to propose a new component or pattern, or suggest updates to an existing component, see instructions on our We use this planner to collect new requests from our teams regarding our Component Library. This planner is public, and we encourage everyone to create new tasks to help us understand how we can better support our teams. 👍 What is this for? Requesting new components or patterns. Extending existing component functionalities. Highlighting use cases that currently don't have a sufficient solution. 👎 What is this not for? Reporting bugs (Please use the appropriate channel in the MS Teams group). Questions about component usage (Please use the appropriate channel in the MS Teams group).","title":"Getting started/Design/Contributing","path":"/?path=/docs/getting-started-design-contributing--overview"},{"text":"Getting started ## Onboarding In your first few days you will go through our designer onboarding and will receive a ticket about Supply. Once you get to that point, someone from the Supply team will reach out and schedule a intro session with you. ## Figma Component Library Our component library should automatically be enabled in any file you open, the direct link to it can be found below. [Link to component Library](https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=64%3A111&mode=design&t=jDpa51YIs58uCGoI-1) ## Figma Icon Library Our Icon Library which is using the Streamline icon set as a base. Our icon request/contribution process can be found on Storybook or directly in the library file under \"Request an icon\" [Link to icon Library](https://www.figma.com/file/zNTWDDhsGn2gcDJCuJbkay/%5BCargoWise%5D-SUPPLY---Icon-Library?type=design&node-id=0%3A1&mode=design&t=rkLGQCEGhXzywdfM-1) ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Getting started/Design/Overview","path":"/?path=/docs/getting-started-design-overview--overview"},{"text":"Developer Resources ## How To [Update the Icon Library](https://devops.wisetechglobal.com/wtg/CargoWise/_wiki/wikis/CargoWise.wiki/8615/Updating-the-Icon-Library) [Update Design Tokens](https://devops.wisetechglobal.com/wtg/CargoWise/_wiki/wikis/CargoWise.wiki/8838/Updating-design-tokens) [Use the RTL Friendly CSS Styles](https://devops.wisetechglobal.com/wtg/CargoWise/_wiki/wikis/CargoWise.wiki/10358/RTL-Friendly-CSS-Guide) ## Articles [Accessibility](https://devops.wisetechglobal.com/wtg/CargoWise/_wiki/wikis/CargoWise.wiki/9347/A-Guide-to-Learn-Accessibility-and-Improve-Status-for-GLOW-Components) [Atomic Design](https://bradfrost.com/blog/post/atomic-web-design/) [BEM convention](https://www.geeksforgeeks.org/understanding-the-css-bem-convention/) [Component-Driven Development](https://www.chromatic.com/blog/component-driven-development/) [Design Systems Handbook](https://www.designbetter.co/design-systems-handbook/introducing-design-systems)","title":"Getting started/Engineering/Resources","path":"/?path=/docs/getting-started-engineering-resources--overview"},{"text":"Engineering ## Recommended IDE Setup The Web Template attempts to use toolchain similar to GLOW, please check this guide for the IDE setup. [Recommended client devtools](https://devops.wisetechglobal.com/wtg/CargoWise/_wiki/wikis/CargoWise.wiki/5612/Recommended-Client-DevTools) ## Installation The Web Template is the simplest way to start developing Vue 3 application using SUPPLY components. To get started, follow these steps: ### 1. Cloning the template repository: ### 2. Change directory to the cloned directory: ### 3. Install packages with: ### 4. Spin up the application with: The application shall be running on http://localhost:5173/ ## Customize configuration The template uses vite as its build tool, please check this guide for more information. See [Vite Configuration Reference](https://vitejs.dev/config/). ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Getting started/Engineering/Overview","path":"/?path=/docs/getting-started-engineering-overview--overview"},{"text":"Upgrade guide Care has been taken to ensure the upgrade process from wtg-material-ui and SUPPLY 0.5 to SUPPLY 1.0 is as easy as possible. However, not all properties or components have matching support in SUPPLY 1.0. This page contains a detailed list of breaking changes and the steps required to upgrade your application to SUPPLY 1.0. ⚠️ Note: This page is incomplete. Please check back later for more information. If you have additional questions, reach out to us via the support channels. ## Components ### Avatar Deprecated Properties tile rounded ### Autocomplete Deprecated Properties multiple ### Badge Deprecated Properties avatar bordered bottom dot inline left overlap tile ### Button Deprecated Properties block use fill property as an alternative icon use the Icon Button component as an alternative outlined, text use variant property retainFocusOnClick value x-small, small, large, x-large use size property as an alternative ### Checkbox Deprecated Properties color dense hideDetails inputValue multiple ### Chip Deprecated Properties close closeIcon color dark label outlined textColor tile x-small, small, large, x-large ### Divider Deprecated Properties slot The default slot has been removed and divider ca no longer have children ### Image Deprecated Properties contain contentClass dark light position ### Input Deprecated Properties appendOuterIcon prependInnerIcon ### Label Deprecated Properties color typography values h1, h2, h3, h4, h5, h6 ### Modal Deprecated Properties dark hideOverlay light retainFocus scrollable the component will always handle this ### Radio Deprecated Properties color ### Switch Deprecated Properties color dense hideDetails inputValue multiple","title":"Getting started/Engineering/Upgrade guide","path":"/?path=/docs/getting-started-engineering-upgrade-guide--overview"},{"text":"Accessibility It is our responsibility to create an inclusive product without barriers. Section 5 of Australia’s Disability Discrimination Act of 1992 requires equal access for people with disabilities. It establishes a legal standard for the contrast level essential between the text and its background. The baseline AA contrast standard is 4.5:1 for most text and 3:1 for large text. In addition to that, it is also recommended to avoid pure black text on a white background to help with dyslexia, Irlen Syndrome, light sensitivity, and autism. Don’t use color exclusively to convey meaning. Color should only be used as an enhancement — if color is the only cue, that cue won’t get through as intended to everyone. [Learn more about Accessibility Design considerations.](https://www.w3.org/WAI/fundamentals/accessibility-intro/) ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Guidelines/Accessibility","path":"/?path=/docs/guidelines-accessibility--overview"},{"text":"Layouts [Panel Layouts](#panel-layouts) [Panel Layout templates](#panel-layout-templates) [Layout scaling](#layout-scaling) ## Panel Layouts Panel layouts are the visual arrangement of Panel components that sits within the standard framework shell present in every portal. Panels contain content specific to individual portals. Generally speaking, a good layout: Directs attention to the action users want to take. Scales across multiple viewport sizes. Has consistent spacing, establishes hierarchy, and logical grouping of content. ## Panel Layout templates Below are some suggested Panel layouts. These are certainly not the only options available but rather serve as a starting point. All examples are shown on our default viewport size: 1920px. For the source code of these panel layout templates, refer to the [Templates page](/docs/structure-templates--overview). Full Page panel A single full-page Panel that maximizes all available space. Examples and potential use cases More coming soon! Ideal for displaying high-density Datagrids. A good starting point if your application is driven by a Datagrid by default. Full page with side panel A Panel layout with a fixed left Panel beside a Panel that scales. Examples and potential use cases More coming soon! Ideal for showcasing a full-page Datagrid alongside a complementary side Panel that either directly relates to the Datagrid or interacts with its content. Full page top panel A Panel layout with a fixed full-width top Panel alongside a bottom Panel that scales vertically depending on contained content. Examples and potential use cases More coming soon! Ideal for showcasing a full-page Datagrid alongside a complementary top Panel that either directly relates to the Datagrid or interacts with its content. Fixed multi-panel A Panel layout with multiple equal-width Panels. The panel layout does not continue to scale after the viewport exceeds 1920px. Examples and potential use cases Coming soon! Fixed multi-panel with side panel A multi-panel layout with the addition of a fixed side panel. The panel layout does not continue to scale after the viewport exceeds 1920px. Examples and potential use cases Coming soon! Fixed multi-panel with bottom panel A Panel layout with two equal-width top Panels side by side and a single full-width Panel below. The panel layout does not continue to scale after the viewport exceeds 1920px. Examples and potential use cases Coming soon! ## Layout scaling There are two key scaling options: fixed max-width and full page. Fixed max-width For screens wider than 1920px, Panel layouts will center-align and scale to the maximum available space of 2000px. The Panels won't change size as the screen width increases - only the space within the left and right margins will increase. Full page For screens wider than 1920px, Panel layouts continue to fill the available space as the screen width increases. This is recommended for high-density screens where using all available space is optimal, e.g. Datagrid display. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Guidelines/Layouts","path":"/?path=/docs/guidelines-layouts--overview"},{"text":"WiseTech Global's multi-channel Design System powered by GLOW. 1.0.0-alpha.15 Guidelines A growing list of guidelines that will support you in building great applications using the Supply design system. [Learn more](/docs/guidelines-localization--docs) Design Tokens Design tokens are variables that encapsulate visual properties like color, font, and spacing for consistent UI styling across platforms. [Learn more](/docs/design-tokens-overview--overview) Components Our reusable building blocks, built in Vue and compatible with our internal Platform Builder, powered by GLOW. [Learn more](/docs/components-component-status--overview) Icons A comprehensive collection of approved Icons that can be used in our CargoWise Applications. [Learn more](/docs/icons-images-icon--docs) ## The team Agustina Ceballos Product Manager Paula Zitinski Product Manager Marc Obieglo UI & Design Principal Donn Delaney Tech Lead Greg Sippel Engineering Team Lead Dan Lynch Senior UI Designer John Mills Senior UX Designer Vicknesh Ravikumar Software Engineer Ramesh Perera Senior Engineer Tulsa Daley Associate UI Designer Ted Vu Software Engineer Jacob McIver Software Engineer","title":"Intro","path":"/?path=/docs/intro--overview"},{"text":"Roadmap SUPPLY is always under development. We are constantly working towards improving the existing codebase and adding new features. ## Roadmap Checkout the [Supply Design System roadmap](https://miro.com/app/board/uXjVMMfrkxc=/?moveToWidget=3458764572766959185&cot=14) ## Under development ### Supply 1.0 Target Release: Q2 2024 Overview: Version 1 of our flagship wtg-component library. Vue 2 and Vue 3 compatible Project: [PRJ00044896](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/6fbd7e6e-b0f6-42ab-87d4-90877bdd2842?lang=en-gb) - SUPPLY 1.0 Core components API and Documentation ## Released Checkout the [Changelog](/docs/changelog--overview) for already released minor and major version updates","title":"Roadmap","path":"/?path=/docs/roadmap--overview"},{"text":"Grid implementation The layout grid is based on the 12 point grid system popularized by{' '} Bootstrap and is used to create responsive layouts that adapt to different device sizes. It is based on 5 types of media breakpoints that allow it to respond to the different screen sizes. xs for a small to large phone (width &lt; 600px) sm for a small to medium tablet (width &lt; 960px) md for a large tablet to a laptop (width &lt; 1280px) lg for a laptop to desktop (width &lt; 1920px) xl for a 1080p to 1440p desktop (width &lt; 2560px) xxl for 4k and ultra-wide screens (width &gt; 2560px) Sub-components wtg-container [wtg-container](/docs/layouts-container--docs) rovides the ability to center and horizontally pad your site’s contents. wtg-col [wtg-col](/docs/layouts-col--docs) is a content holder that must be a direct child of wtg-row. It's use is often implicit as the primary use case is implemented as part of wtg-layout-grid. wtg-row [wtg-row](/docs/layouts-row--docs) is a wrapper component for wtg-col. It utilizes flex properties to control the layout and flow of its inner columns. It uses a standard gutter of 24px. This can be reduced with the dense prop or removed completely with no-gutters. wtg-spacer [wtg-spacer](/docs/layouts-spacer--docs) is a basic yet versatile spacing component used to distribute remaining width in-between a parents child components. When placing a single wtg-spacer before or after the child components, the components will push to the right and left of its container. When more than one v-spacer’s are used between multiple components, the remaining width is evenly distributed between each spacer. wtg-layout-grid [wtg-layout-grid](/docs/layouts-layoutgrid--docs) is SUPPLY's grid component whose primary purpose is to simplify responsive design code by negating the need for individual wtg-col layout construction in favor of a columns property on individual components. Examples Columns When wrapped in a wtg-layout-grid component, you can control the layout behaviour of components through their columns property (if it has one). The example below shows how you should interpret the composite string value of the{' '} columns property. When you set columns=\"col-sm-6 col-md-4\", what you mean is: This control takes up all 12 columns on extra small devices, it takes up 6 out of 12 columns on small devices and it takes up 4 out of 12 columns on medium, large and extra large devices. Resize your browser and watch what happens in the example below when we reach the various breakpoints. Gutters Gutters refers to the gap between the different columns. If the layout wraps over multiple rows it refers to the gap between the rows as well. The grid has a default gutter size of 24px. This can be removed completely with the no-gutters property. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Guidelines/Layouts/Grid implementation","path":"/?path=/docs/guidelines-layouts-grid-implementation--overview"},{"text":"Layout examples Available Layouts ⚠️ Note: In the following examples, 100vh has been replaced by height: 400px. In your own application the templates would always use 100vh when leveraging these templates. Full Page panel Full page with side panel Full page with top panel Fixed multi-panel Fixed multi-panel with side panel Fixed multi-panel with bottom panel 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Guidelines/Layouts/Examples","path":"/?path=/docs/guidelines-layouts-examples--overview"},{"text":"What are structure components? Structure Components are the utility container components used to organize applications and pages. Application structure components App Container Main Content structure components Box Col Layout Grid Row Spacer","title":"Utilities/Overview","path":"/?path=/docs/utilities-overview--overview"},{"text":"Icon contribution Before contributing an icon Double-check that your icon does not already exist in the current Icon Library. Icons enhance usability, guiding actions, commands, and sections while reducing cognitive load. They should not be used to 'decorate' an interface. Icons should be used thoughtfully and sparingly; if you're unsure about an icon's necessity, there’s a good chance you don’t need it! Supply icons: Streamline We source selected icons from the Ultimate Regular icon set provided by Streamline Icons. Icon requests must come from this set to ensure our library remains visually consistent. Contributing icons I am an embedded designer or I work with an embedded designer Designers to follow the Figma branching process to contribute an icon to the library. I don’t have have embedded designer on my team Raise an eRequest with the following information and the Design Systems Team will follow up with your request. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Icons & Images/Icon contribution","path":"/?path=/docs/icons-images-icon-contribution--overview"},{"text":"Theming Coming soon... ## Questions? For any queries, please reach out to these contacts. 💙 Have ideas, comments, or suggestions to improve this page? [Let us know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)","title":"Theming","path":"/?path=/docs/guidelines-theming--docs"}]