import { enableAutoUnmount, mount } from '@vue/test-utils';
import { VDivider } from 'vuetify/components/VDivider';
import { WtgDivider } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgDivider', () => {
    test('its name is WtgDivider', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgDivider');
    });

    test('it renders a VDivider component', () => {
        const wrapper = mountComponent();
        const divider = wrapper.findComponent(VDivider);
        expect(divider.exists()).toBe(true);
    });

    test('it passes the vertical prop to the VDivider', () => {
        const wrapper = mountComponent({
            props: {
                vertical: true,
            },
        });
        const divider = wrapper.findComponent(VDivider);
        expect(divider.props().vertical).toBe(true);
    });

    test('it defaults vertical to false when the prop is omitted', () => {
        const wrapper = mountComponent({
            props: {
                vertical: true,
            },
        });
        const divider = wrapper.findComponent(VDivider);
        expect(divider.props().vertical).toBe(true);
    });

    test('it applies the wtg-divider--solid class when variant="solid"', () => {
        const wrapper = mountComponent({
            props: { variant: 'solid' },
        });

        expect(wrapper.classes()).toContain('wtg-divider--solid');
        expect(wrapper.classes()).not.toContain('wtg-divider--dashed');
    });

    test('it applies the wtg-divider--dashed class when variant="dashed"', () => {
        const wrapper = mountComponent({
            props: { variant: 'dashed' },
        });
        expect(wrapper.classes()).toContain('wtg-divider--dashed');
        expect(wrapper.classes()).not.toContain('wtg-divider--solid');
    });

    test('it defaults the variant to solid when the prop is omitted', () => {
        const wrapper = mountComponent();
        expect(wrapper.props().variant).toBe('solid');
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgDivider, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
