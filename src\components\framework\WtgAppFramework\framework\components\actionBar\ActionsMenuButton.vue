<template>
    <WtgPopupMenu
        v-if="actions.length > 0"
        v-model="actionsPopupOpen"
        :actions="actions"
        content-class="wtg-actionbar-actions-menu"
        :disabled="disabled"
        min-width="100vw"
        nudge-top="17px"
        nudge-right="-12px"
    >
        <template #default="{ on }">
            <WtgIconButton
                :aria-expanded="actionsPopupOpen"
                aria-haspopup="true"
                :aria-label="formatCaption('menu.more')"
                icon="s-icon-menu-meatballs"
                class="wtg-actions-menu-button"
                @keyup.enter="onSelectDropdownAction"
                @keyup.space="onSelectDropdownAction"
                v-on="on"
            />
        </template>
    </WtgPopupMenu>
</template>

<script setup lang="ts">
import { WtgIconButton } from '@components/WtgIconButton';
import { WtgActionItemData } from '@components/WtgMenu';
import { WtgPopupMenu } from '@components/WtgMenuBar';
import { useLocale } from '@composables/locale';
import { PropType, ref } from 'vue';

const { formatCaption } = useLocale();
const actionsPopupOpen = ref(false);

defineProps({
    actions: {
        type: Array as PropType<WtgActionItemData[]>,
        default: (): [] => [],
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    sentiment: {
        type: String as PropType<'critical' | 'primary' | 'success'>,
        default: undefined,
    },
    variant: {
        type: String as PropType<'fill'>,
        default: undefined,
    },
});

const onSelectDropdownAction = (e: Event) => {
    e.stopPropagation();
};
</script>
<style lang="scss">
.wtg-actionbar-actions-menu {
    .wtg-popover {
        border: unset;
        border-radius: unset;
        border-top: 1px solid var(--s-neutral-border-weak-default);
    }

    .wtg-elevation-300 {
        box-shadow: unset;
    }
}
</style>
