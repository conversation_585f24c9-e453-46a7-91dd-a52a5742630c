import WtgButton from '@components/WtgButton';
import WtgDialog from '@components/WtgDialog/WtgDialog.vue';
import { mount, enableAutoUnmount } from '@vue/test-utils';
import { defineComponent, nextTick } from 'vue';
import { VDialog } from 'vuetify/components/VDialog';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

window.innerWidth = 2000;
const wtgUi = new WtgUi();

describe('WtgDialog', () => {
    test('it renders component and trigger - does not render modal content as it is not open', () => {
        const wrapper = mountComponent();

        expect(wrapper.findComponent({ name: 'VDialog' }).exists()).toBe(true);

        expect(wrapper.find('button').exists()).toBe(true);

        expect(document.querySelector('#modal_content')).toBeNull();
    });

    test('it renders modal content if modal is opened', async () => {
        const wrapper = mountComponent();

        const dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.exists()).toBe(true);

        expect(document.querySelector('#modal_content')).toBeNull();

        const openModalButton = wrapper.find('button');
        expect(openModalButton.exists()).toBe(true);

        await openModalButton.trigger('click');
        await wrapper.vm.$nextTick();

        const modalContent = document.querySelector('#modal_content');
        expect(modalContent).not.toBeNull();
        expect(modalContent?.innerHTML).toEqual('modal content!');
    });

    test('activator supports Vue 2-style { on } destructuring to be backwards compatible with VUE(tify) 2', async () => {
        const wrapper = mountComponent({
            slots: {
                activator: `
                    <template #activator="{ on }">
                        <button data-test="btn" v-on="on">Activator</button>'
                    </template>`,
            },
        });

        expect(document.querySelector('#modal_content')).toBeNull();

        const button = wrapper.find('[data-test="btn"]');
        await button.trigger('click');
        await wrapper.vm.$nextTick();

        const modalContent = document.querySelector('#modal_content');
        expect(modalContent).not.toBeNull();
        expect(modalContent?.innerHTML).toEqual('modal content!');
    });

    test('it passes its properties to the VDialog component', async () => {
        const wrapper = mountComponent({
            propsData: {
                absolute: true,
                eager: true,
                fullscreen: true,
                location: 'top',
                height: 100,
                minHeight: 200,
                maxHeight: 300,
                width: 400,
                minWidth: 500,
                maxWidth: 600,
                modelValue: true,
                persistent: true,
                retainFocus: false,
                transition: 'custom-transition',
            },
        });
        const dialog = wrapper.findComponent(VDialog);
        expect(dialog.props('absolute')).toBe(true);
        expect(dialog.props('eager')).toBe(true);
        expect(dialog.props('fullscreen')).toBe(true);
        expect(dialog.props('location')).toBe('top');
        expect(dialog.props('height')).toBe(100);
        expect(dialog.props('minHeight')).toBe(200);
        expect(dialog.props('maxHeight')).toBe(300);
        expect(dialog.props('width')).toBe(400);
        expect(dialog.props('minWidth')).toBe(500);
        expect(dialog.props('maxWidth')).toBe(600);
        expect(dialog.props('modelValue')).toBe(true);
        expect(dialog.props('persistent')).toBe(true);
        expect(dialog.props('retainFocus')).toBe(false);
        expect(dialog.props('transition')).toBe('custom-transition');

        await wrapper.setProps({ fullscreen: false });
        expect(dialog.props('width')).toBe(400);
    });

    test('retain-focus property on VDialog is true by default', async () => {
        const wrapper = mountComponent();

        const dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.props('retainFocus')).toBe(true);
    });

    test('it renders with size xl', async () => {
        let wrapper = mountComponent({
            propsData: {
                modelValue: true,
                size: 'xl',
            },
        });
        let dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.exists()).toBe(true);
        expect(dialog.props('minHeight')).toBe('90vh');
        expect(dialog.props('width')).toBe('90vw');
        expect(dialog.props('fullscreen')).toBe(false);

        window.innerWidth = 1000;
        wrapper = mountComponent(
            {
                propsData: {
                    modelValue: true,
                    size: 'xl',
                },
            },
            new WtgUi()
        );
        dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.props('fullscreen')).toBe(true);
        window.innerWidth = 2000;
    });

    test('it renders with size l', async () => {
        let wrapper = mountComponent({
            propsData: {
                modelValue: true,
                size: 'l',
            },
        });
        let dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.exists()).toBe(true);
        expect(dialog.props('height')).toBe('90vh');
        expect(dialog.props('maxHeight')).toBe('1600');
        expect(dialog.props('width')).toBe('1200');
        expect(dialog.props('fullscreen')).toBe(false);

        window.innerWidth = 1000;
        wrapper = mountComponent(
            {
                propsData: {
                    modelValue: true,
                    size: 'l',
                },
            },
            new WtgUi()
        );
        dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.props('fullscreen')).toBe(true);
        window.innerWidth = 2000;
    });

    test('it renders with size m', async () => {
        let wrapper = mountComponent({
            propsData: {
                modelValue: true,
                size: 'm',
            },
        });
        let dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.exists()).toBe(true);
        expect(dialog.props('height')).toBe('60vh');
        expect(dialog.props('maxHeight')).toBe('1200');
        expect(dialog.props('width')).toBe('780');
        expect(dialog.props('fullscreen')).toBe(false);

        window.innerWidth = 1000;
        wrapper = mountComponent(
            {
                propsData: {
                    modelValue: true,
                    size: 'm',
                },
            },
            new WtgUi()
        );
        dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.props('fullscreen')).toBe(false);

        window.innerWidth = 500;
        wrapper = mountComponent(
            {
                propsData: {
                    modelValue: true,
                    size: 'm',
                },
            },
            new WtgUi()
        );
        dialog = wrapper.findComponent({ name: 'VDialog' });
        expect(dialog.props('fullscreen')).toBe(true);
        window.innerWidth = 2000;
    });

    test('it implements v-model through the modelValue property', async () => {
        const component = defineComponent({
            components: { WtgDialog, WtgButton },
            data: () => {
                return {
                    isActive: true,
                };
            },
            template: `<wtg-button variant="fill" sentiment="primary" @click="isActive = true">Open Error Modal</wtg-button>
                <wtg-dialog v-model="isActive" persistent title="Tasks cleared" sentiment="success">
                    <template #default>
                        All tasks in your channel for this week have been cleared.
                    </template>
                    <template #actions>
                        <wtg-button variant="fill" sentiment="primary" @click="isActive = false">Okay</wtg-button>
                    </template>
                </wtg-dialog>`,
        });
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });
        const modal = wrapper.findComponent(VDialog);
        expect(modal.props('modelValue')).toBe(true);

        modal.vm.$emit('update:modelValue', false);
        const data = wrapper.vm.$data as any;
        expect(data.isActive).toBe(false);

        await nextTick();
        expect(modal.props('modelValue')).toBe(false);
    });

    describe('Deprecated', () => {
        let component: any, wrapper: any;
        beforeEach(() => {
            component = defineComponent({
                components: { WtgDialog, WtgButton },
                data: () => {
                    return {
                        isActive: true,
                    };
                },
                methods: {
                    onInput(value: boolean) {
                        this.isActive = value;
                    },
                },
                template: `<wtg-button variant="fill" sentiment="primary" @click="isActive = true">Open Error Modal</wtg-button>
                    <wtg-dialog :value="isActive" @input="onInput" persistent title="Tasks cleared" sentiment="success">
                        <template #default>
                            All tasks in your channel for this week have been cleared.
                        </template>
                        <template #actions>
                            <wtg-button variant="fill" sentiment="primary" @click="isActive = false">Okay</wtg-button>
                        </template>
                    </wtg-dialog>`,
            });

            wrapper = mount(component, {
                global: {
                    plugins: [wtgUi],
                },
            });
        });

        test('it has (deprecated) VALUE property and INPUT event that allows it to be backwards compatible with the V-MODEL handling of the VUE 2 implementation', async () => {
            const modal = wrapper.findComponent(VDialog);
            expect(modal.props('modelValue')).toBe(true);

            modal.vm.$emit('update:modelValue', false);
            const data = wrapper.vm.$data as any;
            expect(data.isActive).toBe(false);

            await nextTick();
            expect(modal.props('modelValue')).toBe(false);
        });

        test('emits model-compat:input when modelValue is updated', async () => {
            const modal = wrapper.findComponent(WtgDialog);
            const dialog = wrapper.findComponent(VDialog);

            dialog.vm.$emit('update:modelValue', true);
            expect(modal.emitted('model-compat:input')).toBeTruthy();
            expect(modal.emitted('model-compat:input')?.[0]).toEqual([true]);

            dialog.vm.$emit('update:modelValue', false);

            expect(modal.emitted('model-compat:input')?.[1]).toEqual([false]);
        });
    });

    test.each(['g-modal-open', 'tox-dialog__disable-scroll'])(
        `it monitors the DOM for presence of a GLOW modal dialog or any other known dialog
        & disables the focus trap as needed (monitoring '%s' CSS class name)`,
        async (monitoredCSSClassName) => {
            const wrapper = mountComponent();

            const dialog = wrapper.findComponent({ name: 'VDialog' });
            expect(dialog.props().retainFocus).toBe(true);

            document.body.classList.add(monitoredCSSClassName);
            await wrapper.vm.$nextTick();
            expect(dialog.props().retainFocus).toBe(false);

            document.body.classList.remove(monitoredCSSClassName);
            await wrapper.vm.$nextTick();
            await wrapper.vm.$nextTick();
            expect(dialog.props().retainFocus).toBe(true);
        }
    );

    function mountComponent({ propsData = {}, slots = {} } = {}, wtgLocalUi?: WtgUi) {
        return mount(WtgDialog, {
            props: propsData,
            slots: {
                activator: '<button v-bind="params.props">Activator</button>',
                default:
                    '<div><div id="modal_content">modal content!</div><button @click="() => params.isActive.value = false">Close Dialog</button></div>',
                ...slots,
            },
            global: {
                plugins: [wtgLocalUi ?? wtgUi],
                provide: {
                    darkMode: false,
                },
            },
        });
    }
});
