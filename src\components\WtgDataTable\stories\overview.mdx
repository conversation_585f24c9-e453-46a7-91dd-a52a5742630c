import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';
import * as WtgSearchDataTable from './WtgDataTable.stories.ts';
import * as WtgDataTable from '../WtgDataTable/stories/WtgDataTable.stories.ts';
import * as WtgDataTableServer from '../WtgDataTableServer/stories/WtgDataTableServer.stories.ts';

import image from '../../../storybook/assets/component-button-decisiontree.svg';

import { Canvas, ColorItem, ColorPalette, Description, Meta, Story, Title } from '@storybook/blocks';

<Meta title="Components/Data Table" />

<div className="component-header">
    <h1>Data Table</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img>[Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-5423&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img> With pending updates
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

### Pending updates

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Project</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style={{ width: '25%' }}>
                [PRJ00050045](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/3d1ed6ab-b5bf-4938-b623-f43dc231c174?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply DataTable - Grouping (including multi-level).</td>
        </tr>
        <tr>
            <td style={{ width: '25%' }}>[PRJ00052167](https://svc-ediprod.wtg.zone/Services/edit/PRJ/PRJ00052167)</td>
            <td style={{ width: '75%' }}>Supply DataTable - Filter Chips.</td>
        </tr>
        <tr>
            <td style={{ width: '25%' }}>
                [PRJ00050052](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/6838f3ea-cae3-414b-bc9c-c6b560af0602?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply DataTable - Filters.</td>
        </tr>
        <tr>
            <td style={{ width: '25%' }}>[PRJ00051930](https://svc-ediprod.wtg.zone/Services/edit/PRJ/PRJ00051930)</td>
            <td style={{ width: '75%' }}>Supply Data table - actions configurability.</td>
        </tr>
        <tr>
            <td style={{ width: '25%' }}>
                [PRJ00050050](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/30abe354-b923-4750-90a0-6581d13c43b1?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply DataTable - Color schemes.</td>
        </tr>
        <tr>
            <td style={{ width: '25%' }}>
                [PRJ00049262](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/f586ead6-2964-4192-ad45-b045f2f41123?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply DataTable - Column Wrapping.</td>
        </tr>
        <tr>
            <td style={{ width: '25%' }}>
                [PRJ00050044](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/bde0ee68-19d6-45e7-9bbd-efaf72b0b143?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply DataTable - Density.</td>
        </tr>
        <tr>
            <td style={{ width: '25%' }}>
                [PRJ00049143](https://svc-ediprod.wtg.zone/Services/link/ShowEditForm/Project/bb624d0f-674b-43c4-87f7-c907dd911ff4?lang=en-gb)
            </td>
            <td style={{ width: '75%' }}>Supply DataTable - Sequencing \ reordering.</td>
        </tr>
    </tbody>
</table>

## Overview

Data tables are a crucial component in web applications, often used to organize, display, and manage structured data in a clear and interactive format. They are widely employed in a variety of use cases across Supply applications.

<div className="banner-warning">

    <div>
        <h4>⚠️ Figma Data table</h4>
        <p>In figma, there is a single data table component as there is no need to distinguish how data is accessed. In development there are numerous data table components and the correct component to use is to be determined by the use case. Below, find the information you need to help you choose the correct data table for use in your applications.</p>
    </div>

</div>

## Components

Data tables are powerful tools that can be enhanced with features like responsive design, custom styling and integration with backed API's to suit your needs.

Before diving into the guides and examples, let’s take a moment to understand the core data table components available. These are variations optimized for different scenarios so please take the time to ensure you are choosing the correct component for your use case.

## WtgSearchDataTable

Search Data Table is our design system's robust and versatile data table solution. It is designed to serve as the primary data table for all applications within our ecosystem, offering a rich set of features tailored to meet enterprise-grade requirements. Inspired by commercial enterprise data tables such as AG Grid, it delivers unparalleled functionality, flexibility, and performance.

### Key features

<b>High performance:</b> Search Data Table is optimized to handle large datasets efficiently. With features like virtual
scrolling and lazy loading, it ensures smooth performance even with millions of rows.

<b>Rich feature set:</b> Sorting: Multi-column, custom comparator support.

<b>Filtering:</b> Built-in filter support for basic types, and support for custom filters.

<b>Pagination:</b> Configurable pagination options, including server-side and client-side modes.

<b>Column management:</b> Resizable, reorderable, and hideable columns.

<b>Row grouping and aggregation:</b> Advanced grouping options and aggregate functions for summarizing data.

<b>Editing:</b> Inline, popup, and custom editing templates.

<b>Advanced integration capabilities:</b> Server-Side Operations: Seamless integration with APIs for data fetching, filtering,
and sorting.

<b>Custom components:</b> Support for embedding custom renderers and editors.

<b>Extensibility and customization:</b> Easily extend base functionality to fit specific use cases.

<div class="docs-page">
    <Canvas of={WtgSearchDataTable.Default} />
</div>

<div class="docs-page">
    <Canvas of={WtgSearchDataTable.Editable} />
</div>

## WtgDataTable

The data table component is used for displaying tabular data. Features include sorting, searching, pagination, grouping, and row selection. This simple data table presumes that the entire data set is available locally.

### When to use

<ul className="docs__unordered-list">
    <li>
        <p>The entire data set is available locally.</p>
    </li>
    <li>
        <p>The data set is small and requires display with no user customization.</p>
    </li>
</ul>

### API

<div className="banner-warning">

    <div>
        <p>The base data table API is the same API offered by the equivalent Vuetify components. Visit [Vuetify Data Tables](https://vuetifyjs.com/en/components/data-tables/introduction/) for more information.</p>
        <p class="mt-4">This is a practical decision taken to aid the migration of Vuetify applications to Supply. The styling of the data table will follow the Supply standards.</p>
    </div>

</div>

<div class="docs-page">
    <Canvas of={WtgDataTable.Default} />
</div>

## WtgDataTableServer

The data table server component is meant to be used for very large datasets, where it would be inefficient to load all the data into the client. It supports sorting, filtering, pagination, and selection like a standard data table, but all the logic must be handled externally by your backend or database.

### When to use

<ul className="docs__unordered-list">
    <li>
        <p>The entire data set is very large.</p>
    </li>
</ul>

#### API

<div className="banner-warning">

    <div>
        <p>The base data table API is the same API offered by the equivalent Vuetify components. Visit [Vuetify Data Tables](https://vuetifyjs.com/en/components/data-tables/introduction/) for more information.</p>
        <p class="mt-4">This is a practical decision taken to aid the migration of Vuetify applications to Supply. The styling of the data table will follow the Supply standards.</p>
    </div>

</div>

<div class="docs-page">
    <Canvas of={WtgDataTableServer.Default} />
</div>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
