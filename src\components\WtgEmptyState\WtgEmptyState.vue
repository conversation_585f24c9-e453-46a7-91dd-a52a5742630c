<template>
    <div :class="computedClasses">
        <WtgLayoutGrid>
            <div class="wtg-empty-state__image">
                <WtgImage
                    v-if="type !== ''"
                    :src="computedImagePath"
                    :alt="computedImageAltText"
                    :max-height="imageMaxSize"
                    :max-width="imageMaxSize"
                />
            </div>
            <div class="wtg-empty-state__content">
                <WtgLabel :typography="computedHeaderTypography" class="wtg-empty-state__header">{{ header }}</WtgLabel>
                <WtgLabel typography="text-md-default" class="wtg-empty-state__bodycopy">{{ bodycopy }}</WtgLabel>
                <WtgBox>
                    <slot />
                </WtgBox>
                <WtgLabel v-if="microcopy" class="wtg-empty-state__microcopy" typography="text-xs-default">
                    {{ microcopy }}
                </WtgLabel>
            </div>
        </WtgLayoutGrid>
    </div>
</template>

<script setup lang="ts">
import { WtgBox } from '@components/WtgBox';
import { WtgImage } from '@components/WtgImage';
import WtgLabel from '@components/WtgLabel';
import { WtgLayoutGrid } from '@components/WtgLayoutGrid';
import { PropType, computed } from 'vue';
import emptyStateErrorCondensed from '../../assets/WtgEmptyState/error-condensed.svg';
import emptyStateErrorDefault from '../../assets/WtgEmptyState/error-default.svg';
import emptyStateFirstUseCondensed from '../../assets/WtgEmptyState/first-use-condensed.svg';
import emptyStateFirstUseDefault from '../../assets/WtgEmptyState/first-use-default.svg';
import emptyStateMissingPageCondensed from '../../assets/WtgEmptyState/missing-page-condensed.svg';
import emptyStateMissingPageDefault from '../../assets/WtgEmptyState/missing-page-default.svg';
import emptyStateNoDataCondensed from '../../assets/WtgEmptyState/no-data-condensed.svg';
import emptyStateNoDataDefault from '../../assets/WtgEmptyState/no-data-default.svg';
import emptyStateRestrictedCondensed from '../../assets/WtgEmptyState/restricted-condensed.svg';
import emptyStateRestrictedDefault from '../../assets/WtgEmptyState/restricted-default.svg';
import emptyStateUserClearedCondensed from '../../assets/WtgEmptyState/user-cleared-condensed.svg';
import emptyStateUserClearedDefault from '../../assets/WtgEmptyState/user-cleared-default.svg';

//
// Properties
//
const props = defineProps({
    /**
     * The variant of the empty state component.
     * Options include:
     * - `'default'`: Displays the default layout.
     * - `'condensed'`: Displays a more compact layout.
     */
    variant: {
        type: String as PropType<'condensed' | 'default'>,
        default: 'default',
    },

    /**
     * The type of the empty state.
     * Options include:
     * - `'firstUse'`: Indicates a first-use scenario.
     * - `'userCleared'`: Indicates a user-cleared state.
     * - `'missingPage'`: Indicates a missing page.
     * - `'error'`: Indicates an error state.
     * - `'restricted'`: Indicates restricted access.
     * - `'noData'`: Indicates no data is available.
     * - `''`: No specific type.
     */
    type: {
        type: String as PropType<'firstUse' | 'userCleared' | 'missingPage' | 'error' | 'restricted' | 'noData' | ''>,
        default: '',
    },

    /**
     * The header text to display in the empty state.
     */
    header: {
        type: String,
        default: '',
    },

    /**
     * The body copy text to display in the empty state.
     */
    bodycopy: {
        type: String,
        default: '',
    },

    /**
     * The microcopy text to display in the empty state.
     * Typically used for additional context or small notes.
     */
    microcopy: {
        type: String,
        default: '',
    },
});

//
// Computed
//
const computedClasses = computed(() =>
    props.variant === 'default'
        ? ['wtg-empty-state', 'wtg-empty-state_default']
        : ['wtg-empty-state', 'wtg-empty-state_condensed']
);

const imageMaxSize = computed((): string => (props.variant === 'default' ? '200px' : '100px'));

const computedImagePath = computed(() => {
    switch (props.type) {
        case 'firstUse':
            return props.variant === 'condensed' ? emptyStateFirstUseCondensed : emptyStateFirstUseDefault;
        case 'userCleared':
            return props.variant === 'condensed' ? emptyStateUserClearedCondensed : emptyStateUserClearedDefault;
        case 'missingPage':
            return props.variant === 'condensed' ? emptyStateMissingPageCondensed : emptyStateMissingPageDefault;
        case 'error':
            return props.variant === 'condensed' ? emptyStateErrorCondensed : emptyStateErrorDefault;
        case 'restricted':
            return props.variant === 'condensed' ? emptyStateRestrictedCondensed : emptyStateRestrictedDefault;
        case 'noData':
            return props.variant === 'condensed' ? emptyStateNoDataCondensed : emptyStateNoDataDefault;
        default:
            return '';
    }
});

const computedImageAltText = computed(() => {
    switch (props.type) {
        case 'firstUse':
            return 'Empty state first use';
        case 'userCleared':
            return 'Empty state user cleared';
        case 'missingPage':
            return 'Empty state missing page';
        case 'error':
            return 'Empty state error';
        case 'restricted':
            return 'Empty state restricted';
        case 'noData':
            return 'Empty state no data';
        default:
            return '';
    }
});

const computedHeaderTypography = computed(() => {
    if (props.variant === 'condensed' || props.type === '') {
        return 'title-md-default';
    }
    return 'title-lg-default';
});
</script>

<style lang="scss">
.wtg-empty-state_default {
    max-width: 400px;
}

.wtg-empty-state_condensed {
    max-width: 300px;
}
.wtg-empty-state__image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.wtg-empty-state__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    > .wtg-empty-state__header,
    > .wtg-empty-state__bodycopy,
    > .wtg-empty-state__microcopy {
        max-width: 100%;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        white-space: normal;
        word-wrap: break-word;
        text-align: center;
    }
}
.wtg-empty-state__header {
    color: var(--s-neutral-txt-default);
    margin-bottom: var(--s-spacing-m);
    -webkit-line-clamp: 3; // max 3 lines
    max-height: calc(3 * var(--s-lineheight-500));
}
.wtg-empty-state__bodycopy {
    color: var(--s-neutral-txt-weak-default);
    margin-bottom: var(--s-spacing-l);
    -webkit-line-clamp: 3;
    max-height: calc(3 * var(--s-lineheight-300));
}
.wtg-empty-state__microcopy {
    padding: var(--s-spacing-m) 0;
    -webkit-line-clamp: 1;
    max-height: calc(var(--s-lineheight-100) + var(--s-spacing-m));
}
</style>
