<template>
    <i
        v-floating-vue-tooltip="tooltipDirective"
        :aria-hidden="true"
        :class="[computedClasses(), sizeClass]"
        :style="computedStyle"
        @click="onClick"
    >
        <span class="path1"></span>
        <span class="path2"></span>
        <span class="path3"></span>
        <template v-if="computedClasses().length < 2">
            <slot />
        </template>
    </i>
</template>

<script setup lang="ts">
import { useColor } from '@composables/color';
import { makeSizeProps, useSize } from '@composables/size';
import { makeTooltipProps, useTooltip } from '@composables/tooltip';
import { computed, useSlots } from 'vue';
import supplyTheme from '../../theme/presets/supply';

//
// Properties
//
const props = defineProps({
    /**
     * The color of the icon.
     */
    color: {
        type: String,
        default: undefined,
    },
    /**
     * The icon name to display.
     */
    icon: {
        type: String,
        default: undefined,
    },
    /**
     * If true, the icon is disabled and cannot be interacted with.
     */
    disabled: {
        type: Boolean,
        default: false,
    },
    ...makeSizeProps(),
    ...makeTooltipProps(),
});

//
// Emits
//
const emit = defineEmits<{
    /**
     * Emitted when the button is clicked.
     * @event
     * @param {MouseEvent} e - The click event object.
     */
    click: [e: MouseEvent];
}>();

//
// Slots
//
const slots = useSlots();

//
// Composables
//
const { colorClasses, colorStyles } = useColor(props);
const { sizeClass } = useSize(props, 'icon');
const { tooltipDirective } = useTooltip(props);

//
// Computed
//
const computedClasses = () => {
    const classes = [];
    const icons = supplyTheme.icons as any;
    const iconName =
        props.icon ||
        (slots.default && typeof slots.default()[0].children === 'string'
            ? (slots.default()[0].children! as string).trim()
            : '');
    if (typeof iconName === 'string' && iconName.length > 0) {
        classes.push('wtg-icon');

        let iconClass = iconName;
        if (iconClass.startsWith('$') && icons[iconClass.substring(1)]) {
            iconClass = `${icons[iconName.substring(1)]}`;
        }

        if (iconClass.startsWith('mdi-')) {
            classes.push(`mdi ${iconClass}`);
        } else if (iconClass.startsWith('s-icon-') || iconClass.startsWith('icon-')) {
            classes.push(iconClass);
        }
    }
    if (props.disabled) {
        classes.push('wtg-icon--disabled');
    }

    return [...classes, ...colorClasses.value];
};

const computedStyle = computed(() => {
    return { ...colorStyles.value };
});

//
// Event Handlers
//
function onClick(e: MouseEvent) {
    emit('click', e);
}
</script>

<style lang="scss">
.wtg-icon {
    align-items: center;
    display: inline-flex;
    font-size: var(--s-sizing-icon-md);
    vertical-align: middle;
    line-height: 1;

    &.wtg-icon--xs {
        font-size: var(--s-sizing-icon-xs);
    }

    &.wtg-icon--s {
        font-size: var(--s-sizing-icon-sm);
    }

    &.wtg-icon--m {
        font-size: var(--s-sizing-icon-md);
    }

    &.wtg-icon--l {
        font-size: var(--s-sizing-icon-lg);
    }

    &.wtg-icon--xl {
        font-size: var(--s-sizing-icon-xl);
    }

    &.wtg-icon--xxl {
        font-size: var(--s-sizing-icon-2xl);
    }

    &--disabled {
        pointer-events: none;
    }
}
</style>
