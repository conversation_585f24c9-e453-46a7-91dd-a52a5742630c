import WtgDisplayField from '@components/WtgDisplayField/WtgDisplayField.vue';
import WtgPanel from '@components/WtgPanel';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { Meta, StoryObj } from '@storybook/vue3';
import { WtgDisplayFieldTypes } from '../types';
import { DisplayFieldSandboxTemplate } from './templates/wtg-display-field-sandbox.stories-template';

type Story = StoryObj<typeof WtgDisplayField>;
const meta: Meta<typeof WtgDisplayField> = {
    title: 'Components/Display Field',
    component: WtgDisplayField,
    parameters: {
        docs: {
            description: {
                component: 'Display Fields are lists of properties (keys) followed by their corresponding value.',
            },
        },
        layout: 'centered',
    },
    argTypes: {
        fontWeight: {
            options: ['black', 'bold', 'medium', 'regular', 'light', 'thin'],
            control: {
                type: 'select',
            },
        },
        valueType: {
            options: ['', 'date', 'datetime', 'duration', 'measure', 'number', 'time'],
            control: {
                type: 'select',
            },
        },
    },
    render: (args) => ({
        components: { WtgDisplayField },
        setup: () => ({ args }),
        template: `<WtgDisplayField
                        v-bind="args">
                    </WtgDisplayField>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        label: 'Date Field',
        fontWeight: 'bold',
        valueType: WtgDisplayFieldTypes.Date,
        value: new Date().toDateString(),
    },
};

export const Sandbox: Story = {
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgDisplayField, WtgPanel },
        setup: () => ({ args }),
        template: templateWithRtl(DisplayFieldSandboxTemplate),
    }),
};
