import info from '../../../storybook/assets/info.png';
import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';

import image from '../../../storybook/assets/component-button-decisiontree.svg';

import { ArgTypes, Canvas, Controls, Description, Meta, Story, Title } from '@storybook/blocks';
import * as WtgButton from '../../WtgButton/stories/WtgButton.stories';
import * as WtgIconButton from './WtgIconButton.stories.ts';

<Meta of={WtgIconButton} />

<div className="component-header">
    <h1>Icon button</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <img className="status-chip" src={statusAvailable}></img> [Figma](https://www.figma.com/design/t1WU3xc7CsJksBy4E6XDjQ/Components--SUPPLY-?m=auto&node-id=79-2121&t=CWv9BqTEfICTenvS-1)
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">Icon buttons represent common actions without accompanying text labels.</p>

## API

<Canvas className="canvas-preview" of={WtgIconButton.Default} />
<Controls of={WtgIconButton.Default} sort={'alpha'} />

## Behavior

The icon button inherits globally applicable button behaviors like interactive states. For details, see [button](/docs/components-button--docs#behavior).

## How to use

### ✅ Do

-   Use for common, easily recognizable UI actions (e.g. search, settings, close, delete).

-   Use intentionally when space is limited or as a collapsed version of a icon and text button.

-   Use for secondary actions that don’t require a full-text label but still need quick access.

-   Always pair with a tooltip to reinforce meaning.

### ❌ Don’t

-   Use icons that are abstract or unclear without supporting text.

-   Rely on icon buttons for primary actions that require strong emphasis or clarity.

## Variants and sentiments

For variant and sentiment information, see [button documentation](/docs/components-button--docs).

<Canvas className="canvas-preview" of={WtgIconButton.Sentiments} sourceState={'none'} />

## Related components

-   [Button](/docs/components-button--docs)
-   [Dropdown button](/docs/components-dropdown-button--docs)
-   [Hyperlink](/docs/components-hyperlink--docs)

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
