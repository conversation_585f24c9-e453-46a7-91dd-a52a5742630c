import WtgChart from '@components/WtgChart/WtgChart.vue';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { BarController, BarElement, CategoryScale, Chart, ChartData, ChartOptions, LinearScale } from 'chart.js';
import { defineComponent, h, PropType, VNode } from 'vue';

Chart.register(BarElement, BarController, CategoryScale, LinearScale);

export default defineComponent({
    name: 'WtgBarChart',
    props: {
        data: {
            type: Object as PropType<ChartData>,
            default: (): ChartData => {
                return {
                    datasets: [],
                };
            },
        },
        options: {
            type: Object as PropType<ChartOptions>,
            default: (): ChartOptions => {
                return {};
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    setup(props) {
        useLayoutGridColumn(props);
    },
    render(): VNode {
        return h(WtgChart, {
            type: 'bar',
            data: this.data,
            options: this.options,
            loading: this.loading,
        });
    },
});
