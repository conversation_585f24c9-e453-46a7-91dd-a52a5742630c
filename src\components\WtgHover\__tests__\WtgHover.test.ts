import { WtgHover } from '../';
import WtgUi from '../../../WtgUi';
import { mount, enableAutoUnmount } from '@vue/test-utils';
import { VHover } from 'vuetify/components/VHover';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgHover', () => {
    test('it renders a VHover component', () => {
        const wrapper = mountComponent();
        const hover = wrapper.findComponent(VHover);
        expect(hover.exists()).toBe(true);
    });

    test('it passes the default slot content to the VHover component', () => {
        const wrapper = mountComponent();
        expect(wrapper.html()).toContain('Some Text');
    });

    function mountComponent({ props = {}, provide = {} } = {}) {
        return mount(WtgHover, {
            props,
            slots: {
                default: '<span id="text">Some Text</span>',
            },
            global: {
                plugins: [wtgUi],
                provide,
            },
        });
    }
});
