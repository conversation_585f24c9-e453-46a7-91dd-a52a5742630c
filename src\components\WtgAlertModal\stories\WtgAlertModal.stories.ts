import { WtgAlertModal, WtgButton, WtgPopover, WtgTextField } from '@components';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { within } from '@storybook/test';
import { Meta, StoryContext, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import { VLocaleProvider } from 'vuetify/lib/components/index.mjs';
import { AlertModalSandboxTemplate } from './wtg-alert-modal-sandbox.stories-template';

function createProps(storyContext: StoryContext): string {
    const props = [];
    for (const arg in storyContext.args) {
        props.push(`${arg}="${storyContext.args[arg] + ''}"`);
    }
    return props.sort().join(' ');
}
type Story = StoryObj<typeof WtgAlertModal>;
const meta: Meta<typeof WtgAlertModal> = {
    title: 'Components/Alert Modal',
    component: WtgAlertModal,
    parameters: {
        docs: {
            description: {
                component:
                    'An Alert modal is a frame used for intended friction in the workflow to display validation messaging that requires immediate attention before continuing. Alert Modals should be used sparingly and only when necessary.',
            },
            source: {
                transform: (source: string, storyContext: StoryContext) => `
<template>
    <WtgContainer>
        <WtgButton variant="fill" sentiment="primary" @click="isActive = true">Open modal</WtgButton>

        <WtgAlertModal v-model="isActive" ${createProps(storyContext)}>
            <template #default>
                <p>Explanation of the error.</p>
            </template>

            <template #actions>
                <WtgButton variant="ghost" @click="isActive = false">Cancel</WtgButton>
                <WtgButton @click="isActive = false">Default</WtgButton>
                <WtgButton variant="fill" sentiment="primary" @click="isActive = false">Primary</WtgButton>
            </template>
        </WtgAlertModal>
    </WtgContainer>
</template>

<script lang="ts" setup>
    import { ref } from 'vue';
    import { WtgButton, WtgContainer, WtgAlertModal } from '@wtg/wtg-components';

    const isActive = ref(false);
</script>`,
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?node-id=3127%3A25127',
        },
        layout: 'centered',
        slots: {
            activator: `Default slot content`,
        },
    },
    render: (args) => ({
        components: { WtgAlertModal, WtgButton },
        setup: () => {
            const isActive = ref(false);

            return { args, isActive };
        },
        template: `
            <WtgButton variant="fill" sentiment="primary" @click="isActive = true">Open modal</WtgButton>
            <WtgAlertModal v-model="isActive" v-bind="args">
                <template #default>
                    <p>Explanation of the error.</p>
                </template>
                <template #actions>
                    <WtgButton variant="ghost" @click="isActive = false">Cancel</WtgButton>
                    <WtgButton @click="isActive = false">Default</WtgButton>
                    <WtgButton variant="fill" sentiment="primary" @click="isActive = false">Primary</WtgButton>
                </template>
            </WtgAlertModal>
        `,
    }),
};

export default meta;
export const Default: Story = {
    args: {
        title: 'Alert reason',
        sentiment: 'success',
    },
};

export const Sentiments: Story = {
    render: (args) => ({
        components: { WtgAlertModal, WtgButton },
        setup: () => {
            const isActive1 = ref(false);
            const isActive2 = ref(false);
            const isActive3 = ref(false);

            return { args, isActive1, isActive2, isActive3 };
        },
        template: `
        <div class="d-flex flex-column" style="gap: 8px; max-width: 200px;">
        <WtgButton variant="fill" sentiment="primary" @click="isActive1 = true">Open success modal</WtgButton>
        <WtgButton variant="fill" sentiment="primary" @click="isActive2 = true">Open warning modal</WtgButton>
        <WtgButton variant="fill" sentiment="primary" @click="isActive3 = true">Open error modal</WtgButton>

        <WtgAlertModal v-model="isActive1" persistent title="Tasks cleared" sentiment="success">
            <template #default>
                All tasks in your channel for this week have been cleared.
            </template>
            <template #actions>
                <WtgButton variant="fill" sentiment="primary" @click="isActive1 = false">OK</WtgButton>
            </template>
        </WtgAlertModal>
        
        <WtgAlertModal v-model="isActive2" title="Branch not configured" sentiment="warning">
            <template #default>
                <div>
                    <p>
                        The branch you have specified is not configured for the company <b>WiseTech Global</b>.
                    </p>
                </div>
            </template>
            <template #actions>
                <WtgButton variant="ghost" @click="isActive2 = false">Cancel</WtgButton>
                <WtgButton variant="fill" sentiment="primary" @click="isActive2 = false">Screen entities</WtgButton>
            </template>
        </WtgAlertModal>
        
        <WtgAlertModal v-model="isActive3" title="Delete branch" sentiment="error">
            <template #default>
                <div>
                    <p>
                        You're about to delete the branch you have configured for the company <b>WiseTech Global</b>.
                    </p>
                </div>
            </template>
            <template #actions>
                <WtgButton variant="ghost" @click="isActive3 = false">Cancel</WtgButton>
                <WtgButton variant="fill" sentiment="critical" @click="isActive3 = false">Delete branch</WtgButton>
            </template>
        </WtgAlertModal>
        </div>`,
    }),
};

export const Sandbox: Story = {
    args: {},
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgAlertModal, WtgButton, WtgPopover, WtgTextField, VLocaleProvider },
        setup: () => {
            return { args };
        },
        template: templateWithRtl(AlertModalSandboxTemplate),
    }),
    play: async ({ canvasElement, step }) => {
        const canvas = within(canvasElement);
        const ltrContainer = canvas.getByTestId('ltrContainer');
        const ltrContainerTemplate = within(ltrContainer);
        const button = ltrContainerTemplate.getByText('Open Success Single Action Modal');
        await step('Force display of alert modal for chromatic visual regression capture', async () => {
            button.click();
        });
    },
};
