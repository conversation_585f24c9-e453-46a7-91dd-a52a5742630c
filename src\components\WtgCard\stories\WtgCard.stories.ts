import WtgCard from '@components/WtgCard/WtgCard.vue';
import WtgSpacer from '@components/WtgSpacer';
import WtgStatus from '@components/WtgStatus';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { Meta, StoryObj } from '@storybook/vue3';
import { CardSandboxTemplate } from './wtg-card-sandbox.stories-template';

type Story = StoryObj<typeof WtgCard>;
const meta: Meta<typeof WtgCard> = {
    title: 'Components/Card',
    component: WtgCard,
    parameters: {
        docs: {
            description: {
                component: 'Cards contain content and actions about a single subject.',
            },
        },
    },
    render: (args) => ({
        components: { WtgCard, WtgSpacer, WtgStatus },
        setup: () => ({ args }),
        template: `
        <WtgCard v-bind="args" class="d-flex flex-column pa-m" style="gap; 4px" max-width="400">
            <div class="d-flex mb-2">
                <span class="font-weight-bold">Version ID1234567</span>
                <WtgSpacer />
                <WtgStatus label="Unpublished" />
            </div>
            <span>Butterflies causing disruptions</span>
            <span>Connected: OY20, OY21, OY22</span>
        </WtgCard>`,
    }),
};

export default meta;
export const Default: Story = {
    args: {},
};
export const Sandbox: Story = {
    render: (args) => ({
        components: {
            WtgCard,
            WtgSpacer,
            WtgStatus,
        },
        setup: () => ({ args }),
        template: templateWithRtl(CardSandboxTemplate),
    }),
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    argTypes: {},
};
