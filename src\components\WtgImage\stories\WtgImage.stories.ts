import WtgImage from '@components/WtgImage/WtgImage.vue';
import { action } from '@storybook/addon-actions';
import { StoryObj } from '@storybook/vue3';

import component_WtgImage_01 from '../../../storybook/assets/component_WtgImage_01.jpg';
import component_WtgImage_02 from '../../../storybook/assets/component_WtgImage_02.jpg';

type Story = StoryObj<typeof WtgImage>;

export default {
    title: 'Icons & Images/Image',
    component: WtgImage,
    parameters: {
        docs: {
            description: {
                component:
                    'Image is a helper component whose primary purpose is to simplify access to images. The component is packed with features to support rich media.',
            },
        },
    },
    render: (args) => ({
        components: { WtgImage },
        setup: () => ({ args }),
        methods: {
            changeAction: action('change'),
        },
        template: `<WtgImage v-bind="args"></WtgImage>`,
    }),
} as Story;

export const DefaultImage: Story = {
    args: {
        width: '600',
        src: component_WtgImage_01,
        aspectRatio: 0,
    },
    render: (args) => ({
        components: { WtgImage },
        setup: () => ({ args }),
        template: `<div class="ma-4">
                        <WtgImage v-bind="args" alt="sample image" class="image-background" ></WtgImage>
                    </div>`,
    }),
};

export const CoverImage: Story = {
    args: {
        width: '600',
        src: component_WtgImage_02,
        cover: true,
        aspectRatio: 1,
    },
    render: (args) => ({
        components: { WtgImage },
        setup: () => ({ args }),
        template: `<div class="ma-4">
                         <WtgImage v-bind="args" alt="sample image" class="image-background"></WtgImage>
                    </div>`,
    }),
};

export const HeightImage: Story = {
    args: {
        height: '125',
        src: component_WtgImage_01,
    },
    render: (args) => ({
        components: { WtgImage },
        setup: () => ({ args }),
        template: `<div class="ma-4">
                         <WtgImage v-bind="args" alt="sample image" class="image-background"></WtgImage>
                    </div>`,
    }),
};

export const HeightWithCover: Story = {
    args: {
        height: '125',
        src: component_WtgImage_01,
        cover: true,
    },
    render: (args) => ({
        components: { WtgImage },
        setup: () => ({ args }),
        template: `<div class="ma-4">
                         <WtgImage v-bind="args" alt="sample image" class="image-background"></WtgImage>
                    </div>`,
    }),
};

export const MaxHeight: Story = {
    args: {
        maxHeight: '125',
        src: component_WtgImage_02,
    },
    render: (args) => ({
        components: { WtgImage },
        setup: () => ({ args }),
        template: `<div class="ma-4">
                         <WtgImage v-bind="args" alt="sample image" class="image-background"></WtgImage>
                    </div>`,
    }),
};

export const MaxHeightWithCover: Story = {
    args: {
        maxHeight: '125',
        src: component_WtgImage_02,
        cover: true,
    },
    render: (args) => ({
        components: { WtgImage },
        setup: () => ({ args }),
        template: `<div class="ma-4">
                         <WtgImage v-bind="args" alt="sample image" class="image-background"></WtgImage>
                    </div>`,
    }),
};
