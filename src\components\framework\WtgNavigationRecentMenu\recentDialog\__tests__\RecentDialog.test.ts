import { enableAutoUnmount, mount } from '@vue/test-utils';
import WtgUi from '../../../../../WtgUi';
import RecentDialog from '../RecentDialog.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('recent-button-dialog', () => {
    test('its name is RecentButtonDialog', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('RecentDialog');
    });

    test('it renders a modal', () => {
        const wrapper = mountComponent();
        const dialog = wrapper.findComponent({ name: 'WtgDialog' });
        expect(dialog.exists()).toBe(true);
    });

    test('it ensures the dialog sizes to content', async () => {
        const wrapper = mountComponent();
        const dialog = wrapper.findComponent({ name: 'WtgDialog' });
        expect(dialog.props('width')).toBe('auto');
    });

    describe('when opened', () => {
        let wrapper: any;

        beforeEach(async () => {
            const propsData = { modelValue: true, captionClose: 'Close Test' };
            wrapper = mountComponent(propsData);
        });

        test('it renders the recent menu & close button', async () => {
            const recentMenu = wrapper.findComponent({ name: 'RecentList' });
            const closeButton = wrapper.findComponent({ name: 'WtgButton' });
            expect(recentMenu.exists()).toBe(true);
            expect(closeButton.exists()).toBe(true);
            expect(closeButton.text()).toBe('Close Test');
        });

        test('when recent menu emits a change event it calls the dialog closes', () => {
            const recentMenu = wrapper.findComponent({ name: 'RecentList' });
            recentMenu.vm.$emit('item-click');
            expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
            expect(wrapper.emitted('item-click')?.length).toBe(1);
            expect(wrapper.emitted('update:modelValue')?.at(0)).toContain(false);
        });

        test('when close button is clicked the dialog closes', async () => {
            const closeButton = wrapper.findComponent({ name: 'WtgButton' });
            await closeButton.trigger('click');

            expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
            expect(wrapper.emitted('update:modelValue')?.at(0)).toContain(false);
        });
    });

    function mountComponent(propsData = {}) {
        return mount(RecentDialog, {
            propsData,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
