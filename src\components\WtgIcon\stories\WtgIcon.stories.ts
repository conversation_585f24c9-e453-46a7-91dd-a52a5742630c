import WtgCol from '@components/WtgCol';
import WtgIcon from '@components/WtgIcon/WtgIcon.vue';
import WtgLabel from '@components/WtgLabel';
import WtgLayoutGrid, { WtgLayoutGridColumn } from '@components/WtgLayoutGrid';
import WtgPanel from '@components/WtgPanel/WtgPanel.vue';
import WtgRow from '@components/WtgRow';
import { useSupplyPrefixIconsName } from '@composables/icon';
import { sizeArgTypes } from '@composables/size';
import { tooltipArgTypes } from '@composables/tooltip';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { StoryObj } from '@storybook/vue3';
import IconsPage from '../../../storybook/icons/IconsPage.vue';
import { IconSandboxTemplate } from './templates/wtg-icon-sandbox.stories-template';

type Story = StoryObj<typeof WtgIcon>;

const icons = useSupplyPrefixIconsName();
// More on how to set up stories at: https://storybook.js.org/docs/vue/writing-stories/introduction
export default {
    title: 'Icons & Images/Icon',
    component: WtgIcon,
    parameters: {
        docs: {
            description: {
                component:
                    'Icon is a helper component whose primary purpose is to simplify access to SUPPLY icons. Often used as the base icon when constructing composite components.',
            },
        },
    },
    render: (args) => ({
        components: { WtgIcon },
        setup: () => ({ args }),
        template: '<wtg-icon v-bind="args">{{ args.icon }}</wtg-icon>',
    }),
    argTypes: {
        icon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
        ...sizeArgTypes,
        ...tooltipArgTypes,
    },
} as Story;

export const IconSearch: Story = {
    render: () => ({
        components: { IconsPage },
        template: '<icons-page></icons-page>',
    }),
};

// More on writing stories with args: https://storybook.js.org/docs/vue/writing-stories/args
export const Default = { args: { icon: 's-icon-home' } };

export const Sandbox: Story = {
    args: {
        icon: 's-icon-home',
        tooltip: 'This is an icon',
    },
    parameters: {
        ...getChromaticParameters(),
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgCol, WtgLayoutGrid, WtgRow, WtgIcon, WtgPanel, WtgLayoutGridColumn, WtgLabel },
        setup: () => ({ args }),
        methods: {},
        template: templateWithRtl(IconSandboxTemplate),
    }),
};
