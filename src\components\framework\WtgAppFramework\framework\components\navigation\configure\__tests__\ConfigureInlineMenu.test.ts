import { WtgFramework } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import ConfigureInlineMenu from '../ConfigureInlineMenu.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('configure-inline-menu', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        application.captions = {
            configure: 'Configure',
            theme: 'Theme',
        } as any;
        application.user.onThemeConfiguration = jest.fn();
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is Configure Inline Menu', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('ConfigureInlineMenu');
    });

    test('it will render a list group called Configure and an option', async () => {
        const wrapper = await mountComponentAsync();
        let titles = wrapper.findAll('.wtg-list-item__container');
        expect(titles.at(0)?.text()).toBe('Configure');
        await titles.at(0)?.trigger('click');

        titles = wrapper.findAll('.wtg-list-item__container');
        expect(titles.at(1)?.text()).toBe('Theme');
    });

    test('it will call the onThemeConfiguration click handler when theme is clicked', async () => {
        const wrapper = await mountComponentAsync();
        const titles = wrapper.findAll('.wtg-list-item__container');
        await titles.at(0)?.trigger('click');

        const themeItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(1);
        await themeItem?.trigger('click');

        expect(application.user.onThemeConfiguration).toHaveBeenCalledTimes(1);
    });

    describe('Accessibility', () => {
        test('it will render a list group with aria properties', async () => {
            const wrapper = await mountComponentAsync();
            const listGroup = wrapper.findComponent({ name: 'WtgListGroup' });
            expect(listGroup.attributes('role')).toBe('menuitem');
            expect(listGroup.attributes('aria-label')).toBe(application.captions.configure);
            expect(listGroup.attributes('aria-haspopup')).toBe('menu');
        });

        test('it will render a list item with aria properties', async () => {
            const wrapper = await mountComponentAsync();
            const titles = wrapper.findAll('.wtg-list-item__container');
            await titles.at(0)?.trigger('click');

            const listItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(1);
            expect(listItem?.attributes('role')).toBe('menuitem');
            expect(listItem?.attributes('aria-label')).toBe(application.captions.theme);
            expect(listItem?.attributes('aria-haspopup')).toBe('menu');
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(ConfigureInlineMenu) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(ConfigureInlineMenu);
    }
});
