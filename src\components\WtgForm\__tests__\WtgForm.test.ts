import WtgButton from '@components/WtgButton';
import WtgTextField from '@components/WtgTextField';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import WtgForm from '..';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgForm', () => {
    test('its name is WtgForm', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgForm');
    });

    test('it renders a VForm component', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('v-form');
    });

    test('it exposes a resetValidation method that calls the resetValidation method on the inner VForm component', () => {
        const wrapper: VueWrapper<any> = mountComponent();
        jest.spyOn(wrapper.vm.$refs.formRef, 'resetValidation');

        wrapper.vm.resetValidation();
        expect(wrapper.vm.$refs.formRef.resetValidation).toHaveBeenCalled();
    });

    test('it exposes a validate method that calls the validate method on the inner VForm component', () => {
        const wrapper: VueWrapper<any> = mountComponent();
        jest.spyOn(wrapper.vm.$refs.formRef, 'validate');

        wrapper.vm.validate();
        expect(wrapper.vm.$refs.formRef.validate).toHaveBeenCalled();
    });

    test('it exposes a reset method that calls the reset method on the inner VForm component', () => {
        const wrapper: VueWrapper<any> = mountComponent();
        jest.spyOn(wrapper.vm.$refs.formRef, 'reset');

        wrapper.vm.reset();
        expect(wrapper.vm.$refs.formRef.reset).toHaveBeenCalled();
    });

    test('it passes all its properties to the inner VForm component', () => {
        const wrapper = mountComponent({
            props: {
                disabled: true,
                modelValue: true,
            },
        });
        const props = wrapper.findComponent({ name: 'VForm' }).props();
        expect(props.disabled).toBe(true);
        expect(props.modelValue).toBe(true);
    });

    test('it listens to the update:modelValue event on inner VForm component, which is used to communicate the valid/invalid state through v-model', async () => {
        const wrapper = mountComponent();
        await wrapper.findComponent({ name: 'VForm' }).vm.$emit('update:modelValue', true);

        expect(wrapper.emitted('update:modelValue')?.length).toBe(1);
        expect(wrapper.emitted('update:modelValue')?.[0][0]).toBe(true);
    });

    test('it passes the default slot to the inner VForm component', () => {
        const wrapper = mountComponent({
            slots: {
                default: '<div class="my-div">Some Text</div>',
            },
        });

        expect(wrapper.find('.my-div').text()).toBe('Some Text');
    });

    test('emits the input to its parent through the submit event', async () => {
        const component = {
            components: { WtgButton, WtgForm, WtgTextField },
            template: `<wtg-form><wtg-text-field /><wtg-button type='submit'>Submit</wtg-button></wtg-form>`,
        };
        const wrapper = mount(component, {
            global: {
                plugins: [wtgUi],
            },
        });

        const form = wrapper.findComponent(WtgForm);
        expect(form.exists()).toBe(true);
        expect(form.emitted('submit')).toBeUndefined();

        await wrapper.find('form').trigger('submit');

        expect(form.emitted('submit')).toBeDefined();
    });

    function mountComponent({ props = {}, slots = {} } = {}) {
        return mount(WtgForm, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
