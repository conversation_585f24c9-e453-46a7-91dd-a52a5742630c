import WtgApp from '@components/WtgApp';
import WtgButton from '@components/WtgButton';
import WtgContainer from '@components/WtgContainer';
import WtgForm from '@components/WtgForm/WtgForm.vue';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgMain from '@components/WtgMain';
import WtgPanel from '@components/WtgPanel';
import WtgTextField from '@components/WtgTextField';
import { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import ValidationExample from './ValidationExample.vue';

type Story = StoryObj<typeof WtgForm>;
const meta: Meta<typeof WtgForm> = {
    title: 'Utilities/Form',
    component: WtgForm,
    parameters: {
        docs: {
            description: {
                component:
                    'The Form component makes it easy to add validation to form inputs. All input components have a rules prop that can be used to specify conditions in which the input is either valid or invalid.',
            },
        },
        layout: 'fullscreen',
    },
    render: () => ({
        components: { WtgApp, WtgForm, WtgLayoutGrid, WtgPanel, WtgButton, WtgTextField },
        setup: () => {
            const textValue = ref('');
            const messages = ref('');
            const sentiment = ref('');

            return { textValue, messages, sentiment };
        },
        template: `<WtgApp>
            <WtgPanel class="mt-6" max-width="400" caption="Text Field Validation. Click submit to verify rules">
                <WtgForm @submit.prevent="() => { if (textValue === '') { messages = 'Required.'; sentiment = 'critical'; } else { messages = ''; sentiment = ''; } }">
                    <WtgLayoutGrid>
                        <WtgTextField v-model="textValue" label="Validation" :messages="messages" :sentiment="sentiment" />
                        <WtgButton type='submit'>Submit</WtgButton>
                    </WtgLayoutGrid>
                </WtgForm>
            </WtgPanel>
        </WtgApp>`,
    }),
    decorators: [
        () => ({
            template: `
            <div style="display: flex; flex-wrap: wrap;">
                <story/>
            </div>
            `,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {},
};

type StoryValidation = StoryObj<typeof ValidationExample>;

export const Validation: StoryValidation = {
    args: {},
    render: (args) => ({
        components: { WtgApp, WtgContainer, WtgMain, ValidationExample },
        setup: () => ({ args }),
        template: `<WtgApp>
            <WtgContainer>
                <WtgMain>            
                    <ValidationExample />
                </WtgMain>            
            </WtgContainer>
        </WtgApp>`,
    }),
};
