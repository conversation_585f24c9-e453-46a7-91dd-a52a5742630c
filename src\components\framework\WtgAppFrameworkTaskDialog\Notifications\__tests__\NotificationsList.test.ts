describe('Dummy Test', () => {
    test('its runs a test', () => {
        expect(true).toBe(true);
    });
});

// import Vue from 'vue';
// import NotificationsList from '../NotificationsList.vue';
// import { mount, createLocalVue } from '@vue/test-utils';
// import wtgUi, { CurrentNotificationType } from '../../../../../../WtgUi';
// import { WtgFrameworkNotification, WtgFrameworkNotificationType } from '../../../../types';

// Vue.use(WtgUi);

// const localVue = createLocalVue();
// const wtgUi = new WtgUi();

// describe('notifications-list', () => {
//     let notifications: WtgFrameworkNotification[];

//     beforeEach(() => {
//         notifications = [];
//     });

//     test('it is called NotificationsList', () => {
//         const wrapper = mountComponent();
//         expect(wrapper.vm.$options.name).toBe('NotificationsList');
//     });

//     describe('when given notifications', () => {
//         beforeEach(() => {
//             notifications = [
//                 {
//                     id: '5715d3bf-242b-4ffe-8e6e-801f90beca3f',
//                     caption: 'Caption 1',
//                     text: 'Some info text',
//                     type: WtgFrameworkNotificationType.Information,
//                     propertyName: 'Property1',
//                 },
//                 {
//                     id: '36d28d28-3df0-4160-8237-30d9fec08cda',
//                     caption: 'Caption 2',
//                     text: 'Some warning text',
//                     type: WtgFrameworkNotificationType.Warning,
//                     propertyName: 'Property2',
//                 },
//                 {
//                     id: '0262c565-c1c4-4c9b-9f2d-b188a20eb459',
//                     caption: 'Caption 3',
//                     text: 'Some error text',
//                     type: WtgFrameworkNotificationType.Error,
//                     propertyName: 'Property3',
//                 },
//             ] as WtgFrameworkNotification[];
//         });

//         test('it creates a list item for every notification', () => {
//             const wrapper = mountComponent();
//             const listItems = wrapper.findAllComponents({ name: 'wtg-list-item' });
//             expect(listItems.length).toBe(3);
//         });

//         test('each list item has the correct caption and list items are placed top to bottom in the given order', () => {
//             const wrapper = mountComponent();
//             const items = wrapper.vm.items;
//             expect(items.length).toBe(3);

//             expect(items[0].id).toBe('5715d3bf-242b-4ffe-8e6e-801f90beca3f');
//             expect(items[0].caption).toBe('Caption 1');
//             expect(items[0].text).toBe('Some info text');
//             expect(items[0].icon).toBe('mdi-message');
//             expect(items[0].color).toBe('info');
//             expect(items[0].requiresAcknowledgement).toBe(false);
//             expect(items[0].isAcknowledged).toBe(false);

//             expect(items[1].id).toBe('36d28d28-3df0-4160-8237-30d9fec08cda');
//             expect(items[1].caption).toBe('Caption 2');
//             expect(items[1].text).toBe('Some warning text');
//             expect(items[1].icon).toBe('mdi-message-alert');
//             expect(items[1].color).toBe('warning');
//             expect(items[1].requiresAcknowledgement).toBe(false);
//             expect(items[1].isAcknowledged).toBe(false);

//             expect(items[2].id).toBe('0262c565-c1c4-4c9b-9f2d-b188a20eb459');
//             expect(items[2].caption).toBe('Caption 3');
//             expect(items[2].text).toBe('Some error text');
//             expect(items[2].icon).toBe('mdi-alert-outline');
//             expect(items[2].color).toBe('error');
//             expect(items[2].requiresAcknowledgement).toBe(false);
//             expect(items[2].isAcknowledged).toBe(false);
//         });

//         test('it will render alerts to match the notifications', () => {
//             const wrapper = mountComponent();
//             const alerts = wrapper.findAllComponents({ name: 'WtgAlert' });
//             expect(alerts.length).toBe(3);

//             expect(alerts.at(0).props().type).toBe('info');
//             expect(alerts.at(0).findComponent({ name: 'WtgSpacer' }).text()).toBe('Caption 1');
//             expect(alerts.at(0).find('span').text()).toBe('Some info text');

//             expect(alerts.at(1).props().type).toBe('warning');
//             expect(alerts.at(1).findComponent({ name: 'WtgSpacer' }).text()).toBe('Caption 2');
//             expect(alerts.at(1).find('span').text()).toBe('Some warning text');

//             expect(alerts.at(2).props().type).toBe('error');
//             expect(alerts.at(2).findComponent({ name: 'WtgSpacer' }).text()).toBe('Caption 3');
//             expect(alerts.at(2).find('span').text()).toBe('Some error text');
//         });

//         test('it sets $wtgUi.currentNotification.Click on click', () => {
//             const wrapper = mountComponent();
//             expect(wtgUi.currentNotification).toEqual({});

//             const alerts = wrapper.findAllComponents({ name: 'WtgAlert' });
//             expect(alerts.length).toBe(3);
//             alerts.at(1).trigger('click');

//             expect(wtgUi.currentNotification.propertyName).toBe('Property2');
//             expect(wtgUi.currentNotification.type).toBe(CurrentNotificationType.Click);

//             wtgUi.currentNotification = {};
//         });

//         test('it sets $wtgUi.currentNotification.Mouse$$$ on mouseover and mouseleave', () => {
//             const wrapper = mountComponent();
//             expect(wtgUi.currentNotification).toEqual({});

//             const alerts = wrapper.findAllComponents({ name: 'WtgAlert' });
//             expect(alerts.length).toBe(3);
//             alerts.at(1).trigger('mouseover');

//             expect(wtgUi.currentNotification.propertyName).toBe('Property2');
//             expect(wtgUi.currentNotification.type).toBe(CurrentNotificationType.MouseOver);

//             alerts.at(1).trigger('mouseleave');

//             expect(wtgUi.currentNotification.propertyName).toBe('Property2');
//             expect(wtgUi.currentNotification.type).toBe(CurrentNotificationType.MouseOut);

//             wtgUi.currentNotification = {};
//         });
//     });

//     describe('when given a notification that requires to be acknowledged', () => {
//         beforeEach(() => {
//             notifications = [
//                 {
//                     id: '36d28d28-3df0-4160-8237-30d9fec08cdb',
//                     caption: 'Caption 4',
//                     text: 'Some warning text',
//                     type: WtgFrameworkNotificationType.Warning,
//                     requiresAcknowledgement: true,
//                     isAcknowledged: false,
//                     toggleIsAcknowledged: jest.fn(),
//                 } as any,
//             ] as WtgFrameworkNotification[];
//         });

//         test('it creates a list item for every notification', () => {
//             const wrapper = mountComponent();
//             const listItems = wrapper.findAllComponents({ name: 'wtg-list-item' });
//             expect(listItems.length).toBe(1);
//         });

//         test('the notification that can be acknowledged has correct properties', () => {
//             const wrapper = mountComponent();
//             const items = wrapper.vm.items;
//             expect(items.length).toBe(1);

//             expect(items[0].id).toBe('36d28d28-3df0-4160-8237-30d9fec08cdb');
//             expect(items[0].caption).toBe('Caption 4');
//             expect(items[0].text).toBe('Some warning text');
//             expect(items[0].icon).toBe('mdi-message-alert');
//             expect(items[0].color).toBe('warning');
//             expect(items[0].requiresAcknowledgement).toBe(true);
//             expect(items[0].isAcknowledged).toBe(false);
//         });

//         test('when the checkbox value changes toggleIsAcknowledge should be called', async () => {
//             const wrapper = mountComponent();
//             const listItem = wrapper.findComponent({ name: 'wtg-list-item' });
//             const checkbox = listItem.findComponent({ name: 'wtg-checkbox' });
//             expect(checkbox.exists()).toBe(true);
//             expect(checkbox.props().inputValue).toBe(false);

//             const toggleIsAcknowledgedSpy = notifications[0].toggleIsAcknowledged;
//             expect(toggleIsAcknowledgedSpy).toHaveBeenCalledTimes(0);

//             await checkbox.vm.$emit('change', true);
//             expect(toggleIsAcknowledgedSpy).toHaveBeenCalledTimes(1);
//             expect(toggleIsAcknowledgedSpy).toHaveBeenCalledWith(true);

//             await checkbox.vm.$emit('change', false);
//             expect(toggleIsAcknowledgedSpy).toHaveBeenCalledTimes(2);
//             expect(toggleIsAcknowledgedSpy).toHaveBeenCalledWith(false);
//         });
//     });

//     function mountComponent() {
//         return mount<any>(NotificationsList as any, {
//             localVue,
//             wtgUi,
//             propsData: {
//                 notifications,
//             },
//         });
//     }
// });
