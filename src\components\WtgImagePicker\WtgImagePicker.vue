<template>
    <WtgImage
        :alt="alt"
        :aspect-ratio="aspectRatio"
        :class="{ 'wtg-image-picker--border': !borderless }"
        :cover="cover"
        :eager="eager"
        :gradient="gradient"
        :height="height"
        :max-height="maxHeight"
        :max-width="maxWidth"
        :min-height="minHeight"
        :min-width="minWidth"
        :src="src"
        :style="{ cursor: readonly ? null : 'pointer' }"
        :transition="transition"
        :width="width"
        @click="selectFile"
    >
        <div v-if="!readonly" class="d-flex justify-center wtg-image-picker__overlay-add">
            <WtgIcon>s-icon-camera</WtgIcon>
            <WtgIcon v-if="src" @click.stop="clearImage">s-icon-close</WtgIcon>
        </div>
        <input
            v-if="!readonly"
            v-show="false"
            ref="fileInputRef"
            type="file"
            accept="image/bmp, image/jpg, image/jpeg, image/png"
            @change="onFileSelected"
        />
        <WtgAlertModal v-model="hasErrors" :title="fileErrorHeaderCaption" sentiment="error">
            <template #default>{{ errorText }}</template>
            <template #actions>
                <WtgButton variant="fill" sentiment="primary" @click="hasErrors = false">
                    {{ acceptButtonCaption }}
                </WtgButton>
            </template>
        </WtgAlertModal>
    </WtgImage>
</template>

<script setup lang="ts">
import WtgAlertModal from '@components/WtgAlertModal';
import WtgButton from '@components/WtgButton';
import WtgIcon from '@components/WtgIcon';
import WtgImage from '@components/WtgImage';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import { computed, ref, watch } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * Alternative text for the image.
     */
    alt: {
        type: String,
        default: undefined,
    },
    /**
     * Aspect ratio (width/height) of the image.
     */
    aspectRatio: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * If true, removes the border from the image picker.
     */
    borderless: {
        type: Boolean,
        default: false,
    },
    /**
     * If true, the image will cover its container.
     */
    cover: {
        type: Boolean,
        default: false,
    },
    /**
     * If true, the image will be loaded eagerly.
     */
    eager: {
        type: Boolean,
        default: false,
    },
    /**
     * CSS gradient to apply over the image.
     */
    gradient: {
        type: String,
        default: undefined,
    },
    /**
     * Height of the image (in pixels or CSS units).
     */
    height: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Maximum height of the image (in pixels or CSS units).
     */
    maxHeight: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Maximum width of the image (in pixels or CSS units).
     */
    maxWidth: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Minimum height of the image (in pixels or CSS units).
     */
    minHeight: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * Minimum width of the image (in pixels or CSS units).
     */
    minWidth: {
        type: [Number, String],
        default: undefined,
    },
    /**
     * If true, disables image selection and hides the overlay.
     */
    readonly: {
        type: Boolean,
        default: false,
    },
    /**
     * Image source URL.
     */
    src: {
        type: String,
        default: undefined,
    },
    /**
     * Transition effect when loading the image.
     * Can be a boolean or a transition name.
     */
    transition: {
        type: [Boolean, String],
        default: 'fade-transition',
    },
    /**
     * Width of the image (in pixels or CSS units).
     */
    width: {
        type: [Number, String],
        default: undefined,
    },
    ...makeLayoutGridColumnProps(),
});

//
// Emits
//
const emit = defineEmits<{
    'image-selected': [value?: File];
}>();

//
// State
//
const MAX_FILE_SIZE = 10485760; // 10MB = 1024 * 1024 * 10

const fileInputRef = ref<HTMLInputElement>();
const errorText = ref('');
const hasErrors = ref(false);

//
// Composables
//
const { formatCaption } = useLocale();

useLayoutGridColumn(props);

//
// Computed
//
const acceptButtonCaption = computed(() => {
    return formatCaption('imagePicker.acceptButton');
});

const fileErrorHeaderCaption = computed(() => {
    return formatCaption('imagePicker.fileErrorHeader');
});

//
// Watchers
//
watch(
    () => errorText.value,
    (newValue) => {
        hasErrors.value = !!newValue;
    }
);

watch(
    () => hasErrors.value,
    (newValue) => {
        if (!newValue) {
            errorText.value = '';
        }
    }
);

const selectFile = () => {
    if (!props.readonly && fileInputRef.value) {
        fileInputRef.value.click();
    }
};

//
// Event Handlers
//
function onFileSelected(event: Event) {
    const target = event.target as HTMLInputElement;
    const files = target.files || [];
    if (files.length === 1) {
        const selectedFile = files[0];
        if (fileInputRef.value) {
            fileInputRef.value.value = '';
        }

        if (!fileTypeIsAllowed(selectedFile.type)) {
            errorText.value = formatCaption('imagePicker.invalidFileError');
            return;
        }

        if (selectedFile.size > MAX_FILE_SIZE) {
            errorText.value = formatCaption('imagePicker.maxFileSizeError');
            return;
        }

        emit('image-selected', selectedFile);
    }
}

//
// Helpers
//
function clearImage() {
    if (fileInputRef.value) {
        fileInputRef.value.value = '';
    }
    emit('image-selected', undefined);
}

function fileTypeIsAllowed(fileType: string): boolean {
    return (
        fileType === 'image/*' ||
        fileType === 'image/bmp' ||
        fileType === 'image/jpg' ||
        fileType === 'image/jpeg' ||
        fileType === 'image/png'
    );
}
</script>

<style lang="scss">
.wtg-image-picker__overlay-add {
    position: absolute;
    bottom: 0px;
    width: 100%;
    cursor: pointer;
    background: var(--s-neutral-bg-active);
    opacity: 0.4;

    i {
        color: var(--s-neutral-icon-inv-active);
    }
}
.wtg-image-picker--border {
    border: 1px var(--s-neutral-border-weak-default) solid;
}
</style>
