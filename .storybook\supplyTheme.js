// .storybook/YourTheme.js

import { create } from '@storybook/theming/create';

export default create({
    base: 'light',
    // Typography
    fontBase: '"Inter", sans-serif',
    fontCode: 'monospace',

    brandTitle: 'Supply',
    brandUrl: 'https://devops.wisetechglobal.com/wtg/CargoWise/_wiki/wikis/CargoWise.wiki/6298/Design-System-(UXUI)',
    brandImage: 'logo',
    brandTarget: '_self',

    //
    colorPrimary: '#30302E',
    colorSecondary: '#371EE1',

    // UI
    appBg: '#ffffff',
    appContentBg: '#ffffff',
    appBorderRadius: 4,

    // Text colors
    textColor: '#30302E',
    textInverseColor: '#ffffff',

    // Toolbar default and active colors
    barTextColor: '#9E9E9E',
    barSelectedColor: '#585C6D',
    barBg: '#ffffff',

    // Form colors
    inputBg: '#ffffff',
    inputBorder: '#6B6B68',
    inputTextColor: '#30302E',
    inputBorderRadius: 2,
});
