import { WtgRecentItem, WtgRecentItemAction } from '@components/framework/WtgNavigationRecentMenu/types';
import { Toast } from '@components/WtgToast/types';
import { ThemeOptions } from '../../../theme';
import { WtgFrameworkSearchHandler, WtgFrameworkSearchProvider } from './search';
import { WtgFrameworkTask } from './task';

export interface WtgFrameworkAbout {
    applicationVersion: string;
    applicationVersionLabel: string;
    dialogTitle: string;
    dialogClose: string;
    frameworkVersion: string;
    frameworkVersionLabel: string;
    portalName: string;
    portalURL: string;
    portalURLLabel: string;
    resourcesLabel: string;
    serviceURL: string;
    serviceURLLabel: string;
    UTCLabel: string;
    versionLabel: string;
    versionsLabel: string;
}

const defaultAbout: WtgFrameworkAbout = {
    applicationVersion: '0.0.1',
    applicationVersionLabel: 'Application',
    dialogTitle: 'About',
    dialogClose: 'Close About',
    frameworkVersion: '0.0.1',
    frameworkVersionLabel: 'Framework',
    portalName: 'Portal Name',
    portalURL: '',
    portalURLLabel: 'Portal',
    resourcesLabel: 'Resource Locations',
    serviceURL: '',
    serviceURLLabel: 'Service',
    UTCLabel: 'UTC Time',
    versionLabel: 'Version',
    versionsLabel: 'Version',
};

export interface WtgFrameworkCaptions {
    addNew: string;
    appearance: string;
    configure: string;
    density: string;
    densityDefault: string;
    densitySpacious: string;
    densityComfortable: string;
    densityCompact: string;
    densityTight: string;
    light: string;
    dark: string;
    settings: string;
    user: string;
    help: string;
    changePassword: string;
    changeBranchDepartment: string;
    impersonateContactUser: string;
    choose: string;
    logOff: string;
    userImageAlt: string;
    darkMode: string;
    lightMode: string;
    mutedMode: string;
    dense: string;
    manageAccount: string;
    myAccount: string;
    myProfile: string;
    alerts: string;
    alertsAll: string;
    alertsEmpty: string;
    alertErrors: string;
    alertInfos: string;
    alertWarnings: string;
    portalSwitcherMenu: string;
    reset: string;
    resetAllSettings: string;
    favorites: string;
    toggleFavorite: string;
    recent: string;
    about: string;
    pageHelp: string;
    actions: string;
    close: string;
    unsavedChanges: string;
    primaryColor: string;
    theme: string;
    errorReporting: string;
}

const defaultCaptions: WtgFrameworkCaptions = {
    addNew: 'Add new',
    appearance: 'Appearance',
    configure: 'Configure',
    density: 'Density',
    densityDefault: 'Default',
    densitySpacious: 'Spacious',
    densityComfortable: 'Comfortable',
    densityCompact: 'Compact',
    densityTight: 'Tight',
    light: 'Light',
    dark: 'Dark',
    settings: 'Settings',
    user: 'User',
    help: 'Help',
    changePassword: 'Change password',
    changeBranchDepartment: 'Change branch/department',
    impersonateContactUser: 'Impersonate Contact User',
    choose: 'Choose',
    logOff: 'Log Out',
    userImageAlt: 'User Image',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    mutedMode: 'Muted Mode',
    dense: 'Dense',
    manageAccount: 'Manage my account',
    myAccount: 'My Account',
    myProfile: 'My Profile',
    alerts: 'Alerts',
    alertsAll: 'All alerts',
    alertsEmpty: 'No alerts to display',
    alertErrors: 'Error',
    alertInfos: 'Info',
    alertWarnings: 'Warning',
    portalSwitcherMenu: 'Application Portals',
    reset: 'Reset',
    resetAllSettings: 'Reset All Settings',
    favorites: 'Favorites',
    toggleFavorite: 'Toggle Favorite',
    recent: 'Recent',
    about: 'About',
    pageHelp: 'Page Help',
    actions: 'Actions',
    close: 'Close',
    unsavedChanges: 'You have unsaved changes',
    primaryColor: 'Primary Color',
    theme: 'Theme',
    errorReporting: 'An error has occurred',
};

export interface WtgFrameworkAriaLabels {
    alertHubIcon: string;
    applicationTitle: string;
    backButton: string;
    breadcrumbs: string;
    closeButton: string;
    logoImageAlt: string;
    masthead: string;
    navigationDrawer: string;
    navigationDrawerIcon: string;
    search: string;
    searchDialogIcon: string;
    taskActionsOverflowIcon: string;
    userAccountPrefix: string;
}

const defaultAriaLabels: WtgFrameworkAriaLabels = {
    alertHubIcon: 'Toggle Alert Hub',
    applicationTitle: 'Title',
    backButton: 'Back',
    breadcrumbs: 'Breadcrumbs',
    closeButton: 'Close',
    logoImageAlt: 'Logo',
    masthead: 'Head',
    navigationDrawer: 'Drawer',
    navigationDrawerIcon: 'Toggle Navigation Drawer',
    search: 'Search',
    searchDialogIcon: 'Toggle Search',
    taskActionsOverflowIcon: 'Toggle Actions',
    userAccountPrefix: 'User Account for',
};

export enum WtgFrameworkMenuItemType {
    None = 'None',
    Menu = 'Menu',
    Link = 'Link',
}

export interface WtgFrameworkMenuItem {
    id: string;
    caption: string;
    action: WtgFrameworkMenuItemType;
    active?: boolean;
    home?: boolean;
    icon?: string;
    href?: string;
    to?: string;
    items?: WtgFrameworkMenuItem[];
    onClick?: () => void;
    heading?: boolean;
}

export enum WtgFrameworkNotificationType {
    None = 0,
    Information = 1,
    Warning = 2,
    MessageError = 3,
    Error = 4,
}

export class WtgFrameworkNotification {
    id: string;
    caption: string;
    text: string;
    propertyName: string;
    targetKey: string;
    type: WtgFrameworkNotificationType;
    requiresAcknowledgement?: boolean;
    isAcknowledged?: boolean;
    toggleIsAcknowledged?: () => void;

    constructor(id: string, caption = '', text = '', propertyName = '', targetKey = '') {
        this.id = id;
        this.caption = caption;
        this.text = text;
        this.propertyName = propertyName;
        this.targetKey = targetKey;
        this.type = WtgFrameworkNotificationType.Information;
    }
}

export class WtgFrameworkNotificationsDrawer {
    visible: boolean;

    constructor() {
        this.visible = false;
    }

    close(): void {
        this.visible = false;
    }

    open(): void {
        this.visible = true;
    }
}

export interface WtgFrameworkPageHelp {
    visible: boolean;
    loading: boolean;

    name: string;
    text: string;
    alwaysOpenHelp: boolean;

    onAlwaysOpenHelpChanged: (alwaysOpenHelp: boolean) => void;
    onPageHelpOpening: () => void;
    onPageHelpClosing: () => void;

    captions?: {
        title?: string;
        close?: string;
        loading?: string;
        alwaysOpenHelp?: string;
    };
}

export interface WtgFrameworkRecentItem extends WtgRecentItem {}

export interface WtgFrameworkRecentItemAction extends WtgRecentItemAction {}

export interface WtgFrameworkTheme {
    name: string;
    options: ThemeOptions;
}

export interface WtgFrameworkUser {
    name: string;
    orgCode: string;
    image: {
        image: string;
        fallbackImage: string;
    };
    isImpersonated?: boolean;
    emailAddress?: string;
    onChangePassword?: (target: string) => void;
    onChangeBranchDepartment?: (target: string) => void;
    onImpersonateContactUser?: () => void;
    onProfile?: (target: string) => void;
    onLogOff?: () => void;
    onThemeConfiguration?: () => void;
}

const defaultUser: WtgFrameworkUser = {
    name: '',
    orgCode: '',
    image: {
        image: '',
        fallbackImage: '',
    },
};

export class WtgFrameworkDialog {
    visible: boolean;

    constructor() {
        this.visible = false;
    }

    close(): void {
        this.visible = false;
    }

    open(): void {
        this.visible = true;
    }
}

export class WtgFrameworkDialogs {
    about: WtgFrameworkDialog;
    navigationHelpMenu: WtgFrameworkDialog;
    settings: WtgFrameworkDialog;
    recentItems: WtgFrameworkDialog;
    changeBranchDepartment: WtgFrameworkDialog;
    changePassword: WtgFrameworkDialog;

    constructor() {
        this.about = new WtgFrameworkDialog();
        this.navigationHelpMenu = new WtgFrameworkDialog();
        this.recentItems = new WtgFrameworkDialog();
        this.settings = new WtgFrameworkDialog();
        this.changeBranchDepartment = new WtgFrameworkDialog();
        this.changePassword = new WtgFrameworkDialog();
    }

    get overlayVisible(): boolean {
        return this.navigationHelpMenu.visible;
    }
}

export class WtgFrameworkHelpItem {
    id: string;
    title: string;
    link: string;
    onClick?: (event: UIEvent) => void;

    constructor(id: string, title: string, link: string, click?: (event: UIEvent) => void) {
        this.id = id;
        this.title = title;
        this.link = link;
        this.onClick = click;
    }
}

export class WtgFrameworkNavigationDrawer {
    isRailActive: boolean;
    visible: boolean;

    constructor() {
        this.isRailActive = false;
        this.visible = false;
    }

    close(): void {
        this.visible = false;
    }

    open(): void {
        this.visible = true;
    }
}

export class WtgFrameworkPortalSwitcherMenuItem {
    title: string;
    icon: string;
    color: string;
    path: string;

    constructor(title: string, icon: string, color: string, path: string) {
        this.title = title;
        this.icon = icon;
        this.color = color;
        this.path = path;
    }
}

export class WtgFramework {
    href: string;
    hideAppBar: boolean;
    hideBackButton: boolean;
    hideFooter: boolean;
    loading: boolean;
    title: string;
    portalCode: string;
    logoDark: string;
    logoLight: string;
    toasts: Toast[];

    menu: WtgFrameworkMenuItem[];
    entityCreationMenu: WtgFrameworkMenuItem[];

    favorites: WtgFrameworkRecentItem[];
    recentItems: WtgFrameworkRecentItem[];

    helpItems: WtgFrameworkHelpItem[];
    portalSwitcherItems: WtgFrameworkPortalSwitcherMenuItem[];

    about: WtgFrameworkAbout;
    ariaLabels: WtgFrameworkAriaLabels;
    captions: WtgFrameworkCaptions;
    pageHelp?: WtgFrameworkPageHelp;
    user: WtgFrameworkUser;

    currentTask: WtgFrameworkTask | null;
    companyURL: string;

    searchHandler: WtgFrameworkSearchHandler;
    searchProvider: WtgFrameworkSearchProvider | undefined;

    dialogs: WtgFrameworkDialogs;
    navDrawer: WtgFrameworkNavigationDrawer;
    notificationsDrawer: WtgFrameworkNotificationsDrawer;

    themes: WtgFrameworkTheme[];
    themeConfigurations: WtgFrameworkTheme[];
    themeOptions?: ThemeOptions;

    clickFavoritesHandler?: Function;

    constructor() {
        this.href = '#/index';
        this.hideAppBar = false;
        this.hideBackButton = false;
        this.hideFooter = false;
        this.loading = false;
        this.title = '';
        this.portalCode = '';
        this.logoDark = '';
        this.logoLight = '';
        this.toasts = [];

        this.menu = [];
        this.entityCreationMenu = [];

        this.favorites = [];
        this.recentItems = [];

        this.helpItems = [];
        this.portalSwitcherItems = [];

        this.about = { ...defaultAbout };
        this.ariaLabels = { ...defaultAriaLabels };
        this.captions = { ...defaultCaptions };
        this.user = { ...defaultUser };

        this.pageHelp = undefined;

        this.currentTask = null;
        this.companyURL = 'https://www.cargowise.com';

        this.searchHandler = new WtgFrameworkSearchHandler();
        this.searchProvider = undefined;

        this.dialogs = new WtgFrameworkDialogs();

        this.navDrawer = new WtgFrameworkNavigationDrawer();
        this.notificationsDrawer = new WtgFrameworkNotificationsDrawer();

        this.themes = [];
        this.themeConfigurations = [];
        this.themeOptions = undefined;

        this.clickFavoritesHandler = () => null;
    }

    get footerVisible(): boolean {
        if (!this.currentTask || this.hideFooter) {
            return false;
        }

        const { cancelAction, showFooter, saveAction, genericActions } = this.currentTask;

        return showFooter && (cancelAction?.visible || saveAction?.visible || genericActions?.length > 0);
    }

    get closeButtonVisible(): boolean {
        if (!this.currentTask || this.hideFooter) {
            return false;
        }

        const { visible } = this.currentTask.cancelAction;
        return visible;
    }

    applyAbout(about: Partial<WtgFrameworkAbout>): void {
        this.about = { ...this.about, ...about };
    }

    applyAriaLabels(ariaLabels: Partial<WtgFrameworkAriaLabels>): void {
        this.ariaLabels = { ...this.ariaLabels, ...ariaLabels };
    }

    applyCaptions(captions: Partial<WtgFrameworkCaptions>): void {
        this.captions = { ...this.captions, ...captions };
    }

    applyUser(user: Partial<WtgFrameworkUser>): void {
        this.user = { ...this.user, ...user };
    }

    applyThemeOptions(options?: ThemeOptions): void {
        const parsedOptions = JSON.stringify(options);
        const parsedInternalOptions = JSON.stringify(this.themeOptions);
        if (parsedOptions !== parsedInternalOptions) {
            this.themeOptions = options;
        }
    }

    closePageHelp(): void {
        if (this.pageHelp) {
            this.pageHelp.visible = false;
        }
    }

    openPageHelp(): void {
        if (this.pageHelp) {
            this.pageHelp.visible = true;
        }
    }
}

export default WtgFramework;
