import { inputArgTypes } from '@composables/input';
import { AlertLevel } from '@composables/notifications';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import WtgFileInput from '..';

type Story = StoryObj<typeof WtgFileInput>;
const meta: Meta<typeof WtgFileInput> = {
    title: 'Components/File Input',
    component: WtgFileInput,
    parameters: {
        docs: {
            description: {
                component: 'The file-input component is used for choosing one or more files for upload.',
            },
        },
        layout: 'centered',
    },
    argTypes: {
        ...inputArgTypes,
    },
    render: (args) => ({
        components: { WtgFileInput },
        methods: {
            updateModel: action('update:model'),
        },
        setup: () => ({ args }),
        template: `<div style="width:300px">
            <WtgFileInput v-bind="args"
                @update:model-value="updateModel"
            />
        </div>`,
    }),
};

export default meta;
export const Default: Story = {
    args: {
        label: 'Label for the file input',
    },
};

export const Multiple: Story = {
    args: {
        label: 'File input allowing selection of multiple files',
        multiple: true,
    },
};

export const AcceptedFiles: Story = {
    args: {
        label: 'File input accepting image and pdf files only',
        acceptedFiles: ['image/*', '.pdf'],
    },
};

export const Error: Story = {
    args: {
        label: 'Label for the file input',
        required: true,
        validationState: {
            alertLevel: AlertLevel.Error,
            messages: ['An error message!'],
            warning: false,
            error: true,
            targetKey: undefined,
            targetProperty: '',
        },
    },
};
