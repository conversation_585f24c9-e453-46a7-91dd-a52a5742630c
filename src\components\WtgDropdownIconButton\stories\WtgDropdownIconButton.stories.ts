import WtgCol from '@components/WtgCol';
import WtgDropdownIconButton from '@components/WtgDropdownIconButton/WtgDropdownIconButton.vue';
import WtgList, { WtgListItem } from '@components/WtgList';
import WtgRow from '@components/WtgRow';
import { useSupplyPrefixIconsName } from '@composables/icon';
import { tooltipArgTypes } from '@composables/tooltip';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { DropdownIconButtonSandboxTemplate } from './templates/wtg-dropdown-icon-button-sandbox.stories-template';

type Story = StoryObj<typeof WtgDropdownIconButton>;

const icons = useSupplyPrefixIconsName();
const meta: Meta<typeof WtgDropdownIconButton> = {
    title: 'Components/Dropdown Icon Button',
    component: WtgDropdownIconButton,
    parameters: {
        docs: {
            description: {
                component: 'placeholder',
            },
        },
        design: {
            type: 'figma',
            url: 'placeholder',
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgDropdownIconButton, WtgList, WtgListItem },
        methods: {
            action: action('dropdown-click'),
        },
        setup: () => ({ args }),
        template: `<WtgDropdownIconButton 
            v-bind="args"
            @dropdown-click="action">
                <template #popover>
                    <WtgList>
                        <WtgListItem type="subheader">Title</WtgListItem>
                        <WtgListItem type="divider" />
                        <WtgListItem>Item 1</WtgListItem>
                        <WtgListItem>Item 2</WtgListItem>
                        <WtgListItem>Item 3</WtgListItem>
                    </WtgList>
                </template>
        </WtgDropdownIconButton>`,
    }),
    argTypes: {
        ...tooltipArgTypes,
        icon: {
            options: icons,
            control: {
                type: 'select',
            },
        },
        sentiment: {
            options: ['', 'critical', 'primary', 'success'],
            control: {
                type: 'select',
            },
        },
        variant: {
            options: ['', 'fill', 'ghost'],
            control: {
                type: 'select',
            },
        },
    },
    decorators: [
        () => ({
            template: `
            <div style="display: flex; flex-wrap: wrap;">
                <story/>
            </div>
            `,
        }),
    ],
};
export default meta;

export const Default: Story = {
    args: {
        icon: 's-icon-placeholder',
    },
};

export const Sandbox: Story = {
    args: {
        icon: 's-icon-placeholder',
    },
    parameters: {
        controls: {
            exclude: /.*/g,
        },
    },
    render: (args) => ({
        components: { WtgDropdownIconButton, WtgCol, WtgRow, WtgList, WtgListItem },
        methods: {
            action: action('click'),
        },
        setup: () => ({ args }),
        template: DropdownIconButtonSandboxTemplate,
    }),
};
