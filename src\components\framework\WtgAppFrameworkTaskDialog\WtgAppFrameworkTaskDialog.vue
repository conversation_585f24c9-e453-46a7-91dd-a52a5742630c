<template>
    <WtgModal
        :model-value="internalValue"
        :fullscreen="computedFullscreen"
        :max-height="maximized ? undefined : '90vh'"
        :width="maximized ? undefined : 'auto'"
        persistent
        :sizable="isDialogResizingEnabled"
        :draggable="isDialogResizingEnabled"
        :title="title"
        :size="maximized ? 'xl' : undefined"
        @update:model-value="onUpdateModelValue"
    >
        <template #status>
            <WtgStatus
                v-if="!isStatusHidden"
                :value="currentStatus?.value"
                :label="currentStatus?.label"
                :sentiment="currentStatus?.sentiment"
                :variant="currentStatus?.variant"
                :editable="isStatusEditable"
                :items="statusItems"
                class="ml-2 align-self-center"
                @update:model-value="updateStatus($event)"
            />
        </template>
        <template #toolbar>
            <EntityActions v-if="showTaskActions" class="order-last order-sm-0" :task="currentTask" />

            <NotificationsButton />
        </template>
        <template v-if="tabInfo" #tabs>
            <div class="flex-shrink-1" style="min-width: 0">
                <WtgTabs v-model="tabInfo.current" background-color="transparent" show-arrows class="mx-0">
                    <WtgTab v-for="(tab, index) in tabInfo.tabs" :key="index">
                        {{ tab.caption }}
                    </WtgTab>
                </WtgTabs>
            </div>
        </template>
        <slot />
        <template #actions>
            <MobileBar v-if="onMobile" :task="currentTask" />
            <DesktopBar v-else :task="currentTask" />
        </template>
    </WtgModal>
</template>

<script setup lang="ts">
import { WtgFrameworkTask } from '@components/framework/types';
import DesktopBar from '@components/framework/WtgAppFramework/framework/components/actionBar/DesktopBar.vue';
import MobileBar from '@components/framework/WtgAppFramework/framework/components/actionBar/MobileBar.vue';
import EntityActions from '@components/framework/WtgAppFramework/framework/components/entityActions/EntityActions.vue';
import WtgModal from '@components/WtgModal';
import WtgStatus from '@components/WtgStatus';
import { WtgTab, WtgTabs } from '@components/WtgTabs';
import { createAppLevelTabs, hasTaskActions, setCurrentTask, useTaskStatus } from '@composables/application';
import { useDisplay } from '@composables/display';
import { useFramework } from '@composables/framework';
import { PropType, computed, ref, watchEffect } from 'vue';
import { NotificationsButton } from './Notifications';
import { useFeatureFlag } from '@composables/global';

//
// Properties
//
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: undefined,
    },
    task: {
        type: Object as PropType<WtgFrameworkTask>,
        default: (): WtgFrameworkTask => new WtgFrameworkTask(),
    },
    maximized: {
        type: Boolean,
        default: false,
    },

    /**
     * @deprecated Use modelValue instead
     */
    value: {
        type: Boolean,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    input: [value: boolean];
    'update:modelValue': [value: boolean];
}>();

//
// State
//
const internalValue = ref(false);

//
// Composables
//
const { onMobile } = useDisplay();
const { isMobile } = useFramework();
const { isDialogResizingEnabled } = useFeatureFlag();

const currentTask = computed(() => props.task);
setCurrentTask(currentTask);

const tabInfo = createAppLevelTabs();
const showTaskActions = hasTaskActions(currentTask);
const { currentStatus, statusItems, isStatusEditable, isStatusHidden, updateStatus } = useTaskStatus(currentTask);

//
// Computed
//
const computedFullscreen = computed(() => {
    return isMobile.value ?? undefined;
});

const title = computed((): string => {
    return props.task.entityName ? props.task.entityName + ' -  ' + props.task.title : props.task.title;
});

//
// Watchers
//
watchEffect(() => {
    internalValue.value = props.modelValue ?? props.value ?? false;
});

//
// Event Handlers
//
function onUpdateModelValue(state: boolean) {
    internalValue.value = state;
    emit('input', state);
    emit('update:modelValue', state);
}
</script>
