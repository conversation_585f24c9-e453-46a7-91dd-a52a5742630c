<template>
    <VBottomSheet v-model="internalValue" :persistent="persistent" @update:model-value="onUpdateModelValue">
        <div class="wtg-bottom-sheet">
            <slot />
        </div>
    </VBottomSheet>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { VBottomSheet } from 'vuetify/components/VBottomSheet';

//
// Properties
//
const props = defineProps({
    /**
     * Controls the visibility of the bottom sheet.
     * Use v-model to bind this prop.
     */
    modelValue: {
        type: Boolean,
        default: false,
    },
    /**
     * If true, the bottom sheet cannot be dismissed by clicking outside or pressing ESC.
     */
    persistent: {
        type: Boolean,
        default: false,
    },
});

//
// Emits
//
const emit = defineEmits<{
    'update:modelValue': [value: boolean];
    confirm: [];
}>();

//
// State
//
const internalValue = ref(false);

//
// Watchers
//
watchEffect(() => {
    internalValue.value = props.modelValue ?? false;
});

//
// Event handlers
//
function onUpdateModelValue(state: boolean) {
    internalValue.value = state;
    emit('update:modelValue', state);
}
</script>

<style lang="scss">
.wtg-bottom-sheet {
    border: 1px solid var(--s-neutral-border-weak-default);
    background: var(--s-neutral-bg-default);
    color: var(--s-neutral-txt-default);
    padding: var(--s-padding-xl);
}
</style>
