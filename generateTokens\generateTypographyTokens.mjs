import * as utils from './utils.mjs';

export default function generateTypographyTokens() {
    utils.fetchFromFigma('styles').then(async (json) => {
        const typographyTokens = {};
        const textStyles = json.meta.styles.filter((style) => style.style_type === 'TEXT');
        const nodeIds = textStyles.map((style) => style.node_id);
        const nodes = await utils.fetchFromFigma(`nodes?ids=${nodeIds.join(',')}`).then((json) => json.nodes);
        const variables = await utils.getVariables();
        for (const style of textStyles) {
            const name = utils.formatName(style.name);
            const boundVariables = nodes[style.node_id].document.boundVariables;
            const fontWeight = variables[boundVariables.fontWeight[0].id].codeSyntax.WEB.toLowerCase();
            const fontFamily = variables[boundVariables.fontFamily[0].id].codeSyntax.WEB.toLowerCase();
            const fontSize = variables[boundVariables.fontSize[0].id].codeSyntax.WEB.toLowerCase();
            const lineHeight = variables[boundVariables.lineHeight[0].id].codeSyntax.WEB.toLowerCase();
            const styleString = `${fontWeight} ${fontSize}/${lineHeight} ${fontFamily}`;

            typographyTokens[name] = styleString;
        }
        utils.writeTokens(typographyTokens, 'supply-typography-core.css', ':root');
    });
}
