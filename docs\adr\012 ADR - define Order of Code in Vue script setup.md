# Define Order of Code in Vue `<script setup>`

## Status

**Status**: Proposed

> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

In Vue 3’s Composition API with `<script setup>`, there is flexibility in how code is organized. Without clear conventions, component files can become inconsistent and difficult to maintain, especially as they grow larger.

To improve readability, consistency, and developer experience across the Supply component library and related projects, we are proposing a standardized order for how code should appear within the `<script setup>` block.

This structure will make it easier for any developer to quickly understand the shape and behavior of a component by scanning the file top-down.

## Decision

We propose the following standardized order for all code inside a `<script setup>` block:

1. **Imports**
    - External packages
    - Absolute internal imports (`@/`)
    - Relative imports (`./`, `../`)
    - _No comments above imports._
2. **Type Definitions**
    - TypeScript `interface`, `type`, `enum`
    - _Add section comment:_
      ```vue
      //
      // Type Definitions
      //
      ```
3. **Properties**
    - `defineProps()`
    - _Add section comment:_
      ```vue
      //
      // Properties
      //
      ```
    - **Properties must be documented with JSDoc comments inside the `defineProps` definition.**
4. **Emits**
    - `defineEmits()`
    - _Add section comment:_
      ```vue
      //
      // Emits
      //
      ```
5. **Slots**
    - `defineSlots()`
    - _Add section comment:_
      ```vue
      //
      // Slots
      //
      ```
6. **State**
    - `ref`, `reactive`
    - _Add section comment:_
      ```vue
      //
      // State
      //
      ```
7. **Composables**
    - E.g., `useRoute()`, `useStore()`, `useFocus()`, etc.
    - _Add section comment:_
      ```vue
      //
      // Composables
      //
      ```
    - Composables are initialized after state to allow full control over local variables before using external features.
8. **Computed**
    - `computed()`
    - _Add section comment:_
      ```vue
      //
      // Computed
      //
      ```
9. **Watchers**
    - `watch()`, `watchEffect()`
    - _Add section comment:_
      ```vue
      //
      // Watchers
      //
      ```
10. **Event Handlers**
    - Functions triggered by user interaction, typically called directly from the template.
    - Should use `on` prefixes (e.g., `onClick`, `onSubmit`).
    - _Add section comment:_
      ```vue
      //
      // Event Handlers
      //
      ```
11. **Helpers**
    - Pure supporting logic functions not directly tied to template events.
    - _Add section comment:_
      ```vue
      //
      // Helpers
      //
      ```
12. **Lifecycle**
    - `onMounted()`, `onUnmounted()`, etc.
    - _Add section comment:_
      ```vue
      //
      // Lifecycle
      //
      ```
13. **Expose**
    - `defineExpose()`, if needed.
    - _Add section comment:_
      ```vue
      //
      // Expose
      //
      ```



### Notes on Functions

- **Event Handlers** must always have an `on` prefix to clearly distinguish them from internal helpers.
- **Helpers** must not have an `on` prefix.

This convention improves clarity by separating externally triggered actions from internal logic, reducing cognitive load during component development and review.

---

### Example Usage

```vue
<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import MyComponent from '@/components/MyComponent.vue';

//
// Type Definitions
//
interface User {
    id: number;
    name: string;
}

//
// Properties
//
const props = defineProps<{
    /**
     * ID of the user to load.
     */
    userId: number;
}>();

//
// Emits
//
const emit = defineEmits<{
    /**
     * Fired when the user is updated.
     * @param payload Updated user information.
     */
    (e: 'update', payload: User): void;
}>();

//
// Slots
//
const slots = defineSlots<{
    /**
     * Default slot for main content.
     */
    default: () => any;
    
    /**
     * Footer slot for actions or information.
     */
    footer: () => any;
}>();

//
// State
//
const user = ref<User | null>(null);

//
// Composables
//
const route = useRoute();

//
// Computed
//
const userName = computed(() => user.value?.name ?? '');

//
// Watchers
//
watch(() => props.userId, fetchUser);

//
// Event Handlers
//
function onClickSave() {
    saveUser();
}

function onInputChange(event: Event) {
    const input = event.target as HTMLInputElement;
    user.value = { id: props.userId, name: input.value };
}

//
// Helpers
//
function fetchUser() {
    // Fetch user logic
}

function saveUser() {
    if (user.value) {
        emit('update', user.value);
    }
}

//
// Lifecycle
//
onMounted(fetchUser);

//
// Expose
//
defineExpose({ user });
</script>
```

---

## Consequences

### Benefits

- Improves codebase consistency across all Vue 3 projects.
- Reduces onboarding time for new team members.
- Makes components easier to scan, debug, and maintain.
- Helps code reviews go faster with predictable structure.
- Enables smoother transitions to tooling like automated formatters or linters.
- Improves API clarity for properties, events, and slots through JSDoc documentation embedded inside definitions.
- Separating Event Handlers from Helpers clarifies the source of interactions vs internal logic.

### Trade-offs / Risks

- Requires small upfront effort to reformat existing files.
- May require gentle enforcement through code review, tooling (e.g., ESLint rules), or developer reminders.
- Developers must balance structure against pragmatism (e.g., very small files may not need full sections).
- Excessive splitting might feel verbose for very small components (developer judgment needed).

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `002 ADR - title.md`, etc.

---

### Best Practice Notes (Team Adoption Guidance)

If you haven’t adopted a standardized code order yet, here’s a phased approach:

1. **Trial in new files** – Start applying the structure in all new components.
2. **Soft adoption** – Update components opportunistically when touching them for other reasons.
3. **Linters and templates** – Consider adding custom ESLint rules or VSCode snippets to guide new file creation.
4. **Documentation** – Update the Supply component library README or contribution guidelines to include this ADR.

