import WtgPanel from '@components/WtgPanel';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import WtgDateTimePicker from '..';

type Story = StoryObj<typeof WtgDateTimePicker>;
const meta: Meta<typeof WtgDateTimePicker> = {
    title: 'Components/Date Time Picker',
    component: WtgDateTimePicker,
    parameters: {
        docs: {
            description: {
                component: 'DateTimePicker allows users to select a date and time.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=383-43226',
        },
        layout: 'centered',
    },
    render: (args) => ({
        components: { WtgDateTimePicker, WtgPanel },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `<WtgPanel  class="d-inline-flex">
                    <WtgDateTimePicker
                        v-bind="args"
                        @update:model-value="updateModel"
                        @change="changeAction"
                        @input="inputAction"
                        @focus="focusAction"
                        @blur="blurAction">
                      </WtgDateTimePicker>
                    </WtgPanel>`,
    }),
    decorators: [
        () => ({
            template: `
                <div>
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        modelValue: '08-Jan-24 12:00',
    },
};
