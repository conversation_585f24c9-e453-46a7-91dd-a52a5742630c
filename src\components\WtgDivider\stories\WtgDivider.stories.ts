import WtgDivider from '@components/WtgDivider/WtgDivider.vue';
import getChromaticParameters from '@storybook-utils/getChromaticParameters';
import templateWithRtl from '@storybook-utils/templateWithRtl';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import { DividerSandboxTemplate } from './templates/wtg-divider-stories-template';

type Story = StoryObj<typeof WtgDivider>;
const meta: Meta<typeof WtgDivider> = {
    title: 'Utilities/Divider',
    component: WtgDivider,
    parameters: {
        docs: {
            description: {
                component: 'The WtgDivider component is used to separate sections of lists or layouts.',
            },
        },
    },
    render: (args) => ({
        components: { WtgDivider },
        setup: () => ({ args }),
        methods: {
            changeAction: action('change'),
        },
        template: '<WtgDivider v-bind="args"/>',
    }),
    decorators: [
        () => ({
            template: `
            <div style="display: flex; align-items: center; justify-content: center; min-inline-size: 10em; min-block-size: 10em;">
                <story/>
            </div>
            `,
        }),
    ],
    args: {
        variant: 'solid',
    },
    argTypes: {
        vertical: {
            control: 'boolean',
        },
        variant: {
            options: ['solid', 'dashed'],
            control: 'radio',
        },
    },
};

export default meta;

export const Default: Story = {
    args: {},
};

export const Sandbox: Story = {
    render: (args) => ({
        components: { WtgDivider },
        setup: () => ({ args }),
        methods: {
            action: action('click'),
        },
        template: templateWithRtl(DividerSandboxTemplate),
        parameters: getChromaticParameters(),
    }),
};

export const Direction: Story = {
    render: (args) => ({
        components: { WtgDivider },
        setup: () => ({ args }),
        template: `
            <div style="display: flex; gap: 4rem; align-items: flex-start;">
                <!-- Horizontal -->
                <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                    <strong>Horizontal</strong>
                    <div style="width: 200px;">
                        <WtgDivider v-bind="args" />
                    </div>
                </div>

                <!-- Vertical -->
                <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                    <strong>Vertical</strong>
                    <div style="height: 100px; display: flex; align-items: stretch;">
                        <WtgDivider v-bind="args" :vertical="true" />
                    </div>
                </div>
            </div>
        `,
    }),
    args: {
        variant: 'solid',
    },
    tags: ['!dev'],
};

export const DashedVsSolid: Story = {
    render: (args) => ({
        components: { WtgDivider },
        setup: () => ({ args }),
        template: `
            <div style="display: flex; gap: 4rem; align-items: flex-start;">
                <!-- Solid -->
                <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                    <strong>Solid</strong>
                    <div style="width: 200px;">
                        <WtgDivider v-bind="args" variant="solid" />
                    </div>
                </div>

                <!-- Dashed -->
                <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                    <strong>Dashed</strong>
                    <div style="width: 200px;">
                        <WtgDivider v-bind="args" variant="dashed" />
                    </div>
                </div>
            </div>
        `,
    }),
    args: {
        vertical: false,
    },
    tags: ['!dev'],
};
