<template>
    <WtgDialog v-model="internalValue" max-width="560" min-width="280" persistent>
        <div>
            <div class="wtg-modal__title">
                <WtgLabel typography="title-md-default">
                    {{ addFollowersCaption }}
                </WtgLabel>
                <WtgSpacer />
                <WtgIconButton
                    :tooltip="formatCaption('conversation.dialog.cancel')"
                    icon="s-icon-close"
                    variant="ghost"
                    @click="onCancel"
                />
            </div>

            <div class="wtg-modal__content-canvas">
                <WtgPanel layout="grid">
                    <WtgTabs
                        v-model="tab"
                        show-arrows
                        next-icon="s-icon-move-right"
                        prev-icon="s-icon-move-left"
                        class="mb-2"
                    >
                        <WtgTab
                            v-for="followerUserType in followerUserTypes"
                            :key="followerUserType.type"
                            class="pa-2"
                            @click="setUserType(followerUserType.type)"
                        >
                            {{ followerUserType.caption }}
                        </WtgTab>
                    </WtgTabs>

                    <WtgTabsWindow v-model="tab">
                        <WtgTabsWindowItem v-for="{ type, caption } in followerUserTypes" :key="type">
                            <WtgSelectField
                                v-model="selectedValues[type]"
                                multiple
                                :items="allItems"
                                :placeholder="formatCaption('conversation.searchPotentialFollowers', caption)"
                                filterable
                            >
                                <template #item="{ item, on, attrs }">
                                    <WtgListItem v-bind="attrs" style="display: block" v-on="on">
                                        <div class="d-flex align-center">
                                            <WtgAvatar
                                                :icon="item.icon"
                                                :image="item.photo?.image"
                                                :alt="item.name"
                                                :fallback-image="item.photo?.fallbackImage"
                                                class="mr-2"
                                            />
                                            <div style="min-width: 0">
                                                <WtgLabel class="text-truncate d-block" font-weight="bold">
                                                    {{ item.label }}
                                                </WtgLabel>
                                                <WtgLabel
                                                    v-if="item.secondaryText"
                                                    class="text-truncate d-block"
                                                    color="secondary-text"
                                                    typography="text-sm-default"
                                                >
                                                    {{ item.secondaryText }}
                                                </WtgLabel>
                                                <div v-if="item.chips">
                                                    <WtgTag
                                                        v-for="(chip, idx) in item.chips"
                                                        :key="idx"
                                                        :color="chip.color"
                                                        class="mr-2"
                                                    >
                                                        {{ chip.label }}
                                                    </WtgTag>
                                                </div>
                                            </div>
                                            <WtgSpacer />
                                            <div v-if="item.actionText" class="wtg-text-neutral ml-2">
                                                {{ item.actionText }}
                                            </div>
                                        </div>
                                    </WtgListItem>
                                </template>
                            </WtgSelectField>
                        </WtgTabsWindowItem>
                    </WtgTabsWindow>
                </WtgPanel>
            </div>

            <div class="wtg-modal__actions">
                <WtgSpacer />
                <WtgButton variant="ghost" :min-width="88" @click="onCancel">
                    {{ cancel }}
                </WtgButton>
                <WtgButton
                    sentiment="primary"
                    :min-width="88"
                    :loading="loading"
                    :disabled="!hasSelection"
                    variant="fill"
                    @click="onAddFollowers"
                >
                    {{ addFollowersCaption }}
                </WtgButton>
            </div>
        </div>

        <WtgAlertModal v-model="errorDialog" width="auto" sentiment="error" :title="errorDialogTitle">
            <div id="message" class="mt-2">
                {{ errorDialogMessage }}
            </div>
            <template #actions>
                <WtgSpacer />
                <WtgButton variant="fill" sentiment="critical" min-width="88" @click="onErrorDialogOkClick">
                    {{ ok }}
                </WtgButton>
            </template>
        </WtgAlertModal>
    </WtgDialog>
</template>

<script lang="ts">
// @ts-nocheck
import WtgAlertModal from '@components/WtgAlertModal';
import WtgAvatar from '@components/WtgAvatar';
import WtgButton from '@components/WtgButton';
import WtgTag from '@components/WtgTag';
import WtgDialog from '@components/WtgDialog';
import WtgIconButton from '@components/WtgIconButton';
import WtgLabel from '@components/WtgLabel';
import WtgListItem from '@components/WtgList/WtgListItem.vue';
import WtgPanel from '@components/WtgPanel';
import WtgSelectField from '@components/WtgSelectField';
import WtgSpacer from '@components/WtgSpacer';
import WtgTabs, { WtgTab, WtgTabsWindow, WtgTabsWindowItem } from '@components/WtgTabs';
import { useLocale } from '@composables/locale';
import { defineComponent, PropType, ref } from 'vue';
import PotentialFollowersItemProvider from '../data/PotentialFollowersItemProvider';
import { Conversation, FollowerUserType } from '../types';

export default defineComponent({
    name: 'AddFollowersDialog',
    components: {
        WtgAlertModal,
        WtgAvatar,
        WtgButton,
        WtgIconButton,
        WtgTag,
        WtgPanel,
        WtgDialog,
        WtgLabel,
        WtgListItem,
        WtgSelectField,
        WtgTab,
        WtgTabs,
        WtgSpacer,
        WtgTabsWindow,
        WtgTabsWindowItem,
    },
    props: {
        conversation: {
            type: Object as PropType<Conversation>,
            default: undefined,
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['input', 'close'],
    setup() {
        const { formatCaption } = useLocale();
        const allItems = ref([] as string[]);

        return { formatCaption, allItems };
    },
    data() {
        const selectedValues: Record<string, string[]> = {};
        this.conversation?.followerUserTypes.forEach(({ type: userType }) => {
            selectedValues[userType] = [];
        });
        return {
            tab: undefined,
            errorDialog: false,
            internalValue: false,
            itemProviders: {} as Record<number, PotentialFollowersItemProvider>,
            loading: false,
            selectedValues,
            userType:
                this.conversation?.followerUserTypes && this.conversation.followerUserTypes.length > 0
                    ? this.conversation?.followerUserTypes[0].type
                    : 0,
        };
    },
    computed: {
        followerUserTypes(): FollowerUserType[] {
            return this.conversation?.followerUserTypes || [];
        },
        addFollowersCaption(): string {
            return this.formatCaption('conversation.addFollowers');
        },
        errorDialogTitle(): string {
            return this.formatCaption('conversation.dialog.addFollowersFailure.title');
        },
        hasSelection(): boolean {
            return !!this.followerUserTypes.find(({ type: userType }) => this.selectedValues[userType]?.length > 0);
        },
        errorDialogMessage(): string {
            if (!this.errorDialog) {
                return '';
            }

            const erroredUserTypes = this.followerUserTypes
                .filter(({ type: userType }) => this.selectedValues[userType]?.length > 0)
                .map((userType) => userType.caption);

            return this.formatCaption('conversation.dialog.addFollowersFailure.message', erroredUserTypes.join(', '));
        },
        ok(): string {
            return this.formatCaption('dialog.ok');
        },
        cancel(): string {
            return this.formatCaption('dialog.cancel');
        },
    },
    watch: {
        internalValue: {
            async handler(): Promise<void> {
                if (this.internalValue) {
                    this.resetSelectedValues();
                    this.resetItemProviders();
                    this.resetAllItems();
                    this.allItems = await this.getFollowers(this.userType);
                }
                this.$emit('input', this.internalValue);
            },
            immediate: true,
        },
        value: {
            handler(): void {
                this.internalValue = this.value;
            },
            immediate: true,
        },
        userType: {
            async handler(): Promise<void> {
                this.resetAllItems();
                this.allItems = await this.getFollowers(this.userType);
            },
            immediate: true,
        },
    },
    methods: {
        async onAddFollowers(): Promise<void> {
            const requestData = this.followerUserTypes
                .filter(({ type: userType }) => this.selectedValues[userType]?.length > 0)
                .reduce(
                    (result, { type: userType }) => ({
                        ...result,
                        [userType]: this.selectedValues[userType],
                    }),
                    {}
                );

            this.loading = true;

            try {
                if (this.conversation) {
                    const failedData = await this.conversation.provider.addFollowersAsync(
                        this.conversation,
                        requestData
                    );
                    const hasFailure = Object.keys(failedData).length > 0;

                    this.followerUserTypes.forEach(({ type: userType }) => {
                        this.selectedValues[userType] = failedData[userType] || [];
                    });

                    this.internalValue = this.errorDialog = hasFailure;

                    if (hasFailure) {
                        this.userType = Number(Object.keys(failedData)[0]);
                    } else {
                        this.resetUserTypeSelection();
                    }
                }
            } finally {
                this.loading = false;
                this.$emit('close');
            }
        },
        onCancel(): void {
            this.internalValue = false;

            if (this.followerUserTypes.length > 0) {
                this.resetSelectedValues();
                this.resetUserTypeSelection();
            }

            this.$emit('close');
        },
        onErrorDialogOkClick(): void {
            this.errorDialog = false;
        },
        resetItemProviders(): void {
            this.followerUserTypes.forEach(({ type: userType }) => {
                if (this.conversation) {
                    this.itemProviders[userType] = new PotentialFollowersItemProvider(
                        this.conversation,
                        userType,
                        this.conversation.provider
                    );
                }
            });
        },
        resetSelectedValues(): void {
            this.followerUserTypes.forEach(({ type: userType }) => {
                this.selectedValues[userType] = [];
            });
        },
        resetUserTypeSelection(): void {
            this.userType = this.followerUserTypes[0].type;
        },
        resetAllItems(): void {
            this.allItems = [];
        },
        async getFollowers(userType: number): Promise<any[]> {
            const itemProvider = this.itemProviders[userType];
            if (itemProvider) {
                const followerResponse = await itemProvider.getItemsAsync('', 0);
                if (followerResponse) {
                    const selectItems = followerResponse?.items;
                    return selectItems;
                }
            }
            return [];
        },
        setUserType(userType: number): void {
            this.userType = userType;
        },
    },
});
</script>
<style lang="scss" scoped>
:deep(.v-slide-group__prev),
:deep(.v-slide-group__next) {
    min-width: 30px;
}
</style>
