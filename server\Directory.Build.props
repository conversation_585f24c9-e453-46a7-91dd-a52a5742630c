<Project>

	<PropertyGroup>
		<WTGBranchRoot>$(MSBuildThisFileDirectory)..</WTGBranchRoot>
		<WTGIsTestProject Condition="$(MSBuildProjectFile.Contains('.Test.'))">true</WTGIsTestProject>
		<WTGAnalyzersWarnAll>true</WTGAnalyzersWarnAll>
		<AnalysisMode>AllEnabledByDefault</AnalysisMode>
	</PropertyGroup>

	<!-- Binding redirects -->
	<PropertyGroup Condition="$(MSBuildProjectFile.Contains('.Test.'))">
		<AutoGenerateBindingRedirects Condition="'$(AutoGenerateBindingRedirects)' == ''">true</AutoGenerateBindingRedirects>
		<GenerateBindingRedirectsOutputType Condition="'$(GenerateBindingRedirectsOutputType)' == ''">true</GenerateBindingRedirectsOutputType>
	</PropertyGroup>

	<PropertyGroup>
		<LangVersion>8.0</LangVersion>
		<GenerateAssemblyInfo>False</GenerateAssemblyInfo>
	</PropertyGroup>

	<ItemGroup Condition="'$(MSBuildProjectExtension)' == '.csproj'">
		<Compile Include="$(WTGBranchRoot)\CommonAssemblyInfo.cs" Link="Properties\CommonAssemblyInfo.cs" />
	</ItemGroup>

</Project>
