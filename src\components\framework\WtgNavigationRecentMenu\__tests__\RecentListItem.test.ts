import { reactive } from 'vue';
import RecentListItem from '../RecentListItem.vue';
import { WtgRecentItem } from '../types/index';
import WtgUi from '../../../../WtgUi';
import { mount, enableAutoUnmount, VueWrapper } from '@vue/test-utils';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('RecentListItem', () => {
    let item: WtgRecentItem;
    const toggleFavorite = (): boolean => (item.favorite = !item.favorite);

    beforeEach(() => {
        item = reactive({
            id: '5715d3bf-242b-4ffe-8e6e-801f90beca3f',
            caption: '',
            href: '',
            favorite: false,
            actions: [],
            onAddToFavorites: jest.fn(),
            onRemoveFromFavorites: jest.fn(),
        });
    });

    test('its name is RecentListItem', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('RecentListItem');
    });

    describe('when given an item without actions', () => {
        let wrapper: VueWrapper;

        beforeEach(() => {
            item = reactive({
                id: '5715d3bf-242b-4ffe-8e6e-801f90beca3f',
                caption: 'Item Caption',
                href: 'Link One',
                favorite: true,
                actions: [],
                onAddToFavorites: jest.fn(toggleFavorite),
                onRemoveFromFavorites: jest.fn(toggleFavorite),
            });
            wrapper = mountComponent();
        });

        test('it renders a list item', () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            expect(listItem.exists()).toBe(true);
        });

        test('it has the correct caption', () => {
            expect(wrapper.text()).toBe('Item Caption');
        });

        test('it acts as a navigation link', () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            expect(listItem.vm.$props.href).toBe('Link One');
        });

        test('it emits an event when the item is clicked', async () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            await listItem.trigger('click');

            expect(wrapper.emitted('item-click')?.length).toBe(1);
        });

        test('it has a button with an icon that you can click on to either add or remove it as a favorite, depending on its current state', async () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            let btn = listItem.findComponent({ name: 'WtgIconButton' });
            let icon = listItem.findComponent({ name: 'WtgIcon' });
            expect(icon.classes()).toContain('s-icon-star-filled');
            await btn.trigger('click');

            btn = listItem.findComponent({ name: 'WtgIconButton' });
            icon = listItem.findComponent({ name: 'WtgIcon' });
            expect(item.onAddToFavorites).toHaveBeenCalledTimes(0);
            expect(item.onRemoveFromFavorites).toHaveBeenCalledTimes(1);
            btn = listItem.findComponent({ name: 'WtgIconButton' });
            expect(icon.classes()).toContain('s-icon-star-empty');
            await btn.trigger('click');

            btn = listItem.findComponent({ name: 'WtgIconButton' });
            icon = listItem.findComponent({ name: 'WtgIcon' });
            expect(item.onAddToFavorites).toHaveBeenCalledTimes(1);
            expect(item.onRemoveFromFavorites).toHaveBeenCalledTimes(1);
            expect(icon.classes()).toContain('s-icon-star-filled');
        });
    });

    describe('when given an item with actions', () => {
        let wrapper: VueWrapper;

        beforeEach(() => {
            item = reactive({
                id: '5715d3bf-242b-4ffe-8e6e-801f90beca3f',
                caption: 'Item Caption',
                favorite: true,
                actions: [
                    {
                        id: 'bbd679b1-5aeb-4417-be62-3382b20cacb8',
                        caption: 'Action One',
                        href: 'Link Action One',
                    },
                    {
                        id: 'e97ce2d8-6486-4501-b50b-d1001c9a9b3a',
                        caption: 'Action Two',
                        href: 'Link Action Two',
                    },
                ],
                onAddToFavorites: jest.fn(toggleFavorite),
                onRemoveFromFavorites: jest.fn(toggleFavorite),
            });
            wrapper = mountComponent();
        });

        test('it has the correct caption', () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            expect(listItem.text()).toBe('Item Caption');
        });

        test('it has a button with a star icon actions', async () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            const icon = listItem.findComponent({ name: 'WtgIcon' });

            expect(icon.classes()).toContain('s-icon-star-filled');
        });

        test('it has a button with a chevron down icon for expansion', async () => {
            const listItem = wrapper.findComponent({ name: 'WtgListGroup' });
            const icon = listItem.findComponent({ name: 'WtgListItem' }).findAllComponents({ name: 'WtgIcon' }).at(1);

            expect(icon?.classes()).toContain('s-icon-caret-down');
        });

        test('when the menu item is clicked, it shows a list of actions with correct props', async () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            await listItem.trigger('click');

            const firstActionItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(1)!;
            expect(firstActionItem.text()).toBe('Action One');
            expect(firstActionItem.props('href')).toBe('Link Action One');

            const secondActionItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(2)!;
            expect(secondActionItem.text()).toBe('Action Two');
            expect(secondActionItem.props('href')).toBe('Link Action Two');
        });

        test('when the menu item is clicked, it shows a list of actions with correct props', async () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });

            let listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(listItems.filter((item) => item.isVisible()).length).toBe(1);
            await listItem.trigger('click');
            listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(listItems.filter((item) => item.isVisible()).length).toBe(3);

            const firstActionItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(1)!;
            expect(firstActionItem.text()).toBe('Action One');
            expect(firstActionItem.props('href')).toBe('Link Action One');

            const secondActionItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(2)!;
            expect(secondActionItem.text()).toBe('Action Two');
            expect(secondActionItem.props('href')).toBe('Link Action Two');

            await listItem.trigger('click');
            listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(listItems.filter((item) => item.isVisible()).length).toBe(1);
        });

        test('when the favorite menu item is clicked, the favorite methods are called correctly', async () => {
            const favoriteBtn = wrapper.findComponent({ name: 'WtgIconButton' });
            await favoriteBtn.trigger('click');

            expect(item.onAddToFavorites).toHaveBeenCalledTimes(0);
            expect(item.onRemoveFromFavorites).toHaveBeenCalledTimes(1);
        });

        test('when the action menu item is clicked, it emits an item-click event with correct argument', async () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            await listItem.trigger('click');

            const firstActionItem = wrapper.findAllComponents({ name: 'WtgListItem' }).at(1)!;

            await firstActionItem.trigger('click');
            expect(wrapper.emitted('item-click')?.length).toBe(1);
            expect(wrapper.emitted('item-click')?.at(0)).toContain(item);
        });
    });

    describe('Accessibility', () => {
        let wrapper: VueWrapper;

        beforeEach(() => {
            item = reactive({
                id: '5715d3bf-242b-4ffe-8e6e-801f90beca3f',
                caption: 'Item Caption',
                href: '',
                favorite: true,
                actions: [
                    {
                        id: 'bbd679b1-5aeb-4417-be62-3382b20cacb8',
                        caption: 'Action One',
                        href: 'Link Action One',
                    },
                    {
                        id: 'e97ce2d8-6486-4501-b50b-d1001c9a9b3a',
                        caption: 'Action Two',
                        href: 'Link Action Two',
                    },
                ],
                onAddToFavorites: jest.fn(toggleFavorite),
                onRemoveFromFavorites: jest.fn(toggleFavorite),
            });
            wrapper = mountComponent();
        });

        test('it renders list items with a role menu item aria label of the item caption and aria has popup if required', async () => {
            const listItem = wrapper.findComponent({ name: 'WtgListItem' });
            await listItem.trigger('click');

            const listItems = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(listItems.at(0)!.attributes('role')).toBe('menuitem');
            expect(listItems.at(0)!.attributes('aria-label')).toBe(item.caption);
            expect(listItems.at(0)!.attributes('aria-haspopup')).toBe('menu');

            expect(listItems.at(1)!.attributes('role')).toBe('menuitem');
            expect(listItems.at(1)!.attributes('aria-label')).toBe(item.actions![0].caption);
            expect(listItems.at(1)!.attributes('aria-haspopup')).toBeUndefined();

            expect(listItems.at(2)!.attributes('role')).toBe('menuitem');
            expect(listItems.at(2)!.attributes('aria-label')).toBe(item.actions![1].caption);
            expect(listItems.at(2)!.attributes('aria-haspopup')).toBeUndefined();
        });

        test('it renders list item with a button that has an aria label', async () => {
            const listItemButton = wrapper.findComponent({ name: 'WtgIconButton' });

            expect(listItemButton.attributes('aria-label')).toBe('Toggle Favorite');
        });
    });

    function mountComponent() {
        return mount(RecentListItem, {
            propsData: {
                item,
            },
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
