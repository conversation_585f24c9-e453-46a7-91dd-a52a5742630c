import WtgDocumentViewer from '@components/WtgDocumentViewer';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WtgUi from '../../../WtgUi';
import DocumentViewerImage from '../components/imageViewer/DocumentViewerImage.vue';
import DocumentViewerPdfDocument from '../components/pdfViewer/DocumentViewerPdfDocument.vue';
import DocumentViewerPdfPage from '../components/pdfViewer/DocumentViewerPdfPage.vue';

enableAutoUnmount(afterEach);
const wtgUi = new WtgUi();
window.URL.createObjectURL = jest.fn().mockReturnValue('dummyBlob');

jest.mock('pdfjs-dist/webpack.mjs', () => ({
    PDFDocumentProxy: jest.fn(),
    PDFPageProxy: jest.fn().mockImplementation(() => ({
        getViewport: jest.fn().mockReturnValue({ width: 800, height: 600 }),
        render: jest.fn().mockResolvedValue({
            promise: Promise.resolve(),
            cancel: jest.fn(),
        }),
    })),
    getDocument: jest.fn().mockReturnValue({
        promise: Promise.resolve({
            numPages: 2,
            getPage: jest.fn().mockResolvedValue({
                getViewport: jest.fn().mockReturnValue({ width: 800, height: 600 }),
                render: jest.fn().mockResolvedValue({
                    onContinue: jest.fn(),
                    promise: Promise.resolve(),
                    cancel: jest.fn(),
                    separateAnnots: true,
                }),
            }),
        }),
    }),
    GlobalWorkerOptions: { workerSrc: '' },
}));

describe('Document Viewer', () => {
    let wrapper: any;

    beforeEach(() => {});

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('its name is WtgDocumentViewer', () => {
        wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgDocumentViewer');
    });

    it('should display the loading spinner when loading is true', async () => {
        wrapper = mountComponent();
        await wrapper.setProps({ loading: true });
        const spinner = wrapper.findComponent({ name: 'WtgProgressCircular' });
        expect(spinner.exists()).toBe(true);
        expect(wrapper.find('.document-wrap').exists()).toBe(false);
    });

    it('should hide the loading spinner when loading is false', async () => {
        wrapper = mountComponent();
        await wrapper.setProps({ loading: false });
        const spinner = wrapper.findComponent({ name: 'WtgProgressCircular' });
        expect(spinner.exists()).toBe(false);
        expect(wrapper.find('.document-wrap').exists()).toBe(true);
    });

    it('should display PDF viewer if fileType is pdf', async () => {
        const sampleData = getSampleData('pdf');
        wrapper = mountComponent({
            props: {
                documentSource: sampleData,
            },
        });

        await nextTick();

        const pdfViewers = wrapper.findAllComponents({ name: 'DocumentViewerPdfDocument' });
        expect(pdfViewers).toHaveLength(2);
        expect(pdfViewers.at(0).props('pagePreviewMode')).toBe(true);
        expect(pdfViewers.at(1).props('pagePreviewMode')).toBe(false);
        expect(wrapper.findComponent(DocumentViewerImage).exists()).toBe(false);
    });

    it('should display image viewer if fileType is an image', async () => {
        const sampleData = getSampleData('jpeg');
        wrapper = mountComponent({
            props: {
                documentSource: sampleData,
            },
        });

        await nextTick();

        expect(wrapper.findComponent(DocumentViewerImage).exists()).toBe(true);
        expect(wrapper.findComponent(DocumentViewerPdfDocument).exists()).toBe(false);
    });

    it('should display unsupported file message for unsupported file types', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('svg'),
            },
        });

        await nextTick();
        expect(wrapper.findComponent({ name: 'WtgEmptyState' }).exists()).toBe(true);
        expect(wrapper.text()).toContain('Document not loaded');
    });

    it('should not render controls for unsupported file types', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('svg'),
            },
        });

        await nextTick();

        expect(wrapper.findComponent({ name: 'DocumentViewerControls' }).exists()).toBe(false);
    });

    it('should not render print, download and info buttons for unsupported file types', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('svg'),
            },
        });

        await nextTick();

        const iconButtons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(iconButtons.filter((ib: any) => ib.props().tooltip === 'Print document').length).toBe(0);
        expect(iconButtons.filter((ib: any) => ib.props().tooltip === 'Download document').length).toBe(0);
        expect(iconButtons.filter((ib: any) => ib.props().tooltip === 'Document information').length).toBe(0);
    });

    it('should not render print button when enable print is false', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                enablePrint: false,
            },
        });

        await nextTick();

        const iconButtons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(iconButtons.filter((ib: any) => ib.props().tooltip === 'Print document').length).toBe(0);
    });

    it('should not render download button when enable download is false', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                enableDownload: false,
            },
        });

        await nextTick();

        const iconButtons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(iconButtons.filter((ib: any) => ib.props().tooltip === 'Download document').length).toBe(0);
    });

    it('should not render document info button when enable document info is false', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                enableDocumentInfo: false,
            },
        });

        await nextTick();

        const iconButtons = wrapper.findAllComponents({ name: 'WtgIconButton' });
        expect(iconButtons.filter((ib: any) => ib.props().tooltip === 'Document information').length).toBe(0);
    });

    it('should not render preview component when enable thumbnail is false', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                enableThumbnail: false,
            },
        });

        await nextTick();

        const pdfViewers = wrapper.findAllComponents({ name: 'DocumentViewerPdfDocument' });
        expect(pdfViewers).toHaveLength(1);
        expect(pdfViewers.at(0).props('pagePreviewMode')).toBe(false);
    });

    it('shows default name for unsupported file', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('svg'),
            },
        });

        await nextTick();

        const nameLabel = wrapper.findAllComponents({ name: 'WtgLabel' }).at(0);
        expect(nameLabel.text()).toBe('Document viewer');
    });

    it('should not render controls when enableControls is false', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                enableControls: false,
            },
        });

        await nextTick();

        expect(wrapper.findComponent({ name: 'DocumentViewerControls' }).exists()).toBe(false);
    });

    it('should toggle thumbnails visibility', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        expect(wrapper.vm.showThumbnails).toBe(false);

        const controls = wrapper.findComponent({ name: 'DocumentViewerControls' });
        controls.vm.$emit('thumbnails');
        await nextTick();

        expect(wrapper.vm.showThumbnails).toBe(true);
    });

    it('should set viewerWrapWidth based on the viewerWrap element width', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        await wrapper.setProps({ loading: true });

        const viewerWrap = wrapper.vm.$refs.viewerWrap;
        Object.defineProperty(viewerWrap, 'clientWidth', { value: 500 });

        await wrapper.setProps({ loading: false });
        await nextTick();
        expect(wrapper.vm.viewerWrapWidth).toBe(500);
    });

    it('toggle info visibility on document information button click', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        expect(wrapper.vm.showDocumentInfo).toBe(false);

        await wrapper.findAllComponents({ name: 'WtgIconButton' }).at(0).trigger('click');
        await nextTick();

        expect(wrapper.vm.showDocumentInfo).toBe(true);
    });

    it('open print window on document print button click', async () => {
        const mockPrintWindow = {
            print: jest.fn(),
            onload: jest.fn(),
        };
        const windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => mockPrintWindow as unknown as Window);

        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                urlProvider: { printUrl: 'http://example.com/print' },
            },
        });

        await wrapper.findAllComponents({ name: 'WtgIconButton' }).at(1).trigger('click');
        expect(windowOpenSpy).toHaveBeenCalledWith('http://example.com/print', '_blank');
        mockPrintWindow.onload();
        expect(mockPrintWindow.print).toHaveBeenCalled();
        windowOpenSpy.mockRestore();
    });

    it('open download window on document download button click', async () => {
        const windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => null);
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                urlProvider: { downloadUrl: 'http://example.com/download' },
            },
        });

        await wrapper.findAllComponents({ name: 'WtgIconButton' }).at(2).trigger('click');
        expect(windowOpenSpy).toHaveBeenCalledWith('http://example.com/download');
        windowOpenSpy.mockRestore();
    });

    it('update rotation degree when rotation button is clicked on the controls', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        await nextTick();

        expect(wrapper.vm.rotationDegree).toBe(0);
        const documentControls = wrapper.findComponent({ name: 'DocumentViewerControls' });
        documentControls.vm.$emit('update-rotate', 90);
        expect(wrapper.vm.rotationDegree).toBe(90);
    });

    it('update scale config when scale mode is changed in controls', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        await nextTick();

        expect(wrapper.vm.scaleConfig).toStrictEqual({ resetScale: false, scaleMode: 'scr', scaleValue: 0 });
        const documentControls = wrapper.findComponent({ name: 'DocumentViewerControls' });
        documentControls.vm.$emit('update-scale', 'act');
        expect(wrapper.vm.scaleConfig).toStrictEqual({ resetScale: true, scaleMode: 'act', scaleValue: 0 });
    });

    it('update scale config when scale is recalculated in document', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        await nextTick();

        expect(wrapper.vm.scaleConfig).toStrictEqual({ resetScale: false, scaleMode: 'scr', scaleValue: 0 });
        const pdfDocument = wrapper.findAllComponents({ name: 'DocumentViewerPdfDocument' }).at(1);
        pdfDocument.vm.$emit('update-scale', { resetScale: false, scaleMode: 'ver', scaleValue: 0.66 });
        expect(wrapper.vm.scaleConfig).toStrictEqual({ resetScale: false, scaleMode: 'ver', scaleValue: 0.66 });
    });

    it('update zoom percentage when zoom in/out buttons are clicked on the controls', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        await nextTick();

        expect(wrapper.vm.zoomPercentage).toBe('');
        const documentControls = wrapper.findComponent({ name: 'DocumentViewerControls' });
        documentControls.vm.$emit('update-zoom', '74%');
        expect(wrapper.vm.zoomPercentage).toBe('74%');
    });

    it('update page count when pdf document is loaded', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        await flushPromises();

        expect(wrapper.emitted()['page-count-updated']?.length).toBe(1);
        expect(wrapper.emitted()['page-count-updated']![0][0]).toStrictEqual(2);
        expect(wrapper.vm.pageCount).toBe(2);

        const pdfDocument = wrapper.findAllComponents({ name: 'DocumentViewerPdfDocument' }).at(1);
        pdfDocument.vm.$emit('page-count-updated', 8);

        expect(wrapper.vm.pageCount).toBe(8);
        expect(wrapper.emitted()['page-count-updated']?.length).toBe(2);
        expect(wrapper.emitted()['page-count-updated']![1][0]).toStrictEqual(8);
    });

    it('update active page number, when user change active page in the document', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
        });

        await nextTick();

        expect(wrapper.vm.activePageNumber).toBe(1);
        const pdfDocument = wrapper.findAllComponents({ name: 'DocumentViewerPdfDocument' }).at(1);
        pdfDocument.vm.$emit('active-page-updated', 3);
        expect(wrapper.vm.activePageNumber).toBe(3);
    });

    it('it has slots with page data for each pdf page', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
            slots: {
                'pdf-page-1': `<div class="highlight-slot-1">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
                'pdf-page-2': `<div class="highlight-slot-2">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
            },
        });
        await flushPromises();

        const pdfDocuments = wrapper.findAllComponents(DocumentViewerPdfDocument);
        expect(pdfDocuments.length).toBe(2);

        const pages = [1, 2];

        const previewDocument = pdfDocuments.at(0);
        pages.forEach((page) => {
            const slotContent = previewDocument.find(`.highlight-slot-${page}`);
            expect(slotContent.exists()).toBe(false);
        });

        const viewerDocument = pdfDocuments.at(1);
        pages.forEach((page) => {
            const slotContent = viewerDocument.find(`.highlight-slot-${page}`);
            expect(slotContent.exists()).toBe(true);
            expect(slotContent.text()).toContain(`Page Width: 0, Page Height: 0, Page Number: ${page}`);
        });
    });

    it('when pageWrapWidth changes for a page, page data is updated', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
            slots: {
                'pdf-page-1': `<div class="highlight-slot-1">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
                'pdf-page-2': `<div class="highlight-slot-2">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
            },
        });
        await flushPromises();

        const pdfDocuments = wrapper.findAllComponents(DocumentViewerPdfDocument);
        expect(pdfDocuments.length).toBe(2);
        const viewerDocument = pdfDocuments.at(1);

        const slotContent = viewerDocument.find(`.highlight-slot-2`);
        expect(slotContent.exists()).toBe(true);
        expect(slotContent.text()).toContain(`Page Width: 0, Page Height: 0, Page Number: 2`);

        const pdfPages = viewerDocument.findAllComponents(DocumentViewerPdfPage);
        pdfPages.at(1).vm.pageWrapWidth = 180;
        await nextTick();

        expect(slotContent.text()).toContain('Page Width: 180, Page Height: 0, Page Number: 2');
    });

    it('when pageWrapHeight changes for a page, page data is updated', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
            slots: {
                'pdf-page-1': `<div class="highlight-slot-1">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
                'pdf-page-2': `<div class="highlight-slot-2">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
            },
        });
        await flushPromises();

        const pdfDocuments = wrapper.findAllComponents(DocumentViewerPdfDocument);
        expect(pdfDocuments.length).toBe(2);
        const viewerDocument = pdfDocuments.at(1);

        const slotContent = viewerDocument.find(`.highlight-slot-2`);
        expect(slotContent.exists()).toBe(true);
        expect(slotContent.text()).toContain(`Page Width: 0, Page Height: 0, Page Number: 2`);

        const pdfPages = viewerDocument.findAllComponents(DocumentViewerPdfPage);
        pdfPages.at(1).vm.pageWrapHeight = 900;
        await nextTick();

        expect(slotContent.text()).toContain('Page Width: 0, Page Height: 900, Page Number: 2');
    });

    it('update viewer wrap width and height when loading value changes', async () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                loading: true,
            },
        });
        expect(wrapper.vm.viewerWrapWidth).toBe(0);
        expect(wrapper.vm.viewerWrapHeight).toBe(0);

        const mockClientWidth = jest.spyOn(wrapper.vm.$refs.viewerWrap, 'clientWidth', 'get').mockReturnValue(500);
        const mockClientHeight = jest.spyOn(wrapper.vm.$refs.viewerWrap, 'clientHeight', 'get').mockReturnValue(300);

        await wrapper.setProps({ loading: false });
        await nextTick();

        expect(wrapper.vm.viewerWrapWidth).toBe(500);
        expect(wrapper.vm.viewerWrapHeight).toBe(300);

        mockClientWidth.mockRestore();
        mockClientHeight.mockRestore();
    });

    it('update viewer wrap width and height on mount', async () => {
        const mockClientWidth = jest.spyOn(HTMLElement.prototype, 'clientWidth', 'get').mockReturnValue(500);
        const mockClientHeight = jest.spyOn(HTMLElement.prototype, 'clientHeight', 'get').mockReturnValue(300);

        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
                loading: true,
            },
        });

        expect(wrapper.vm.viewerWrapWidth).toBe(500);
        expect(wrapper.vm.viewerWrapHeight).toBe(300);

        mockClientWidth.mockRestore();
        mockClientHeight.mockRestore();
    });

    test('it renders the default slot', () => {
        wrapper = mountComponent({
            props: {
                documentSource: getSampleData('pdf'),
            },
            slots: {
                default: '<div class="my-div">Some Text</div>',
            },
        });

        expect(wrapper.find('.my-div').text()).toBe('Some Text');
    });

    test('it has a image slot', () => {
        const sampleData = getSampleData('jpeg');
        wrapper = mountComponent({
            props: {
                documentSource: sampleData,
            },
            slots: {
                image: `<div class="test-div">
                            Image Width: {{ imageData.imageWidth }},
                            Image Height: {{ imageData.imageHeight }},
                       </div>`,
            },
        });

        const slotContent = wrapper.find('.test-div');
        expect(slotContent.exists()).toBe(true);
        expect(slotContent.text()).toContain('Image Width: 0, Image Height: 0');
    });

    describe('Supported file types', () => {
        test('it supports PDF files', async () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('pdf'),
                },
            });
            expect(wrapper.vm.fileType).toBe('pdf');
            expect(wrapper.vm.isImage).toBe(false);
        });

        test('it supports JPEG files', async () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('jpeg'),
                },
            });
            expect(wrapper.vm.fileType).toBe('jpeg');
            expect(wrapper.vm.isImage).toBe(true);
        });

        it('it supports PNG files', () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('png'),
                },
            });
            expect(wrapper.vm.fileType).toBe('png');
            expect(wrapper.vm.isImage).toBe(true);
        });

        it('it supports GIF files', () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('gif'),
                },
            });
            expect(wrapper.vm.fileType).toBe('gif');
            expect(wrapper.vm.isImage).toBe(true);
        });

        it('it supports BMP files', () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('bmp'),
                },
            });
            expect(wrapper.vm.fileType).toBe('bmp');
            expect(wrapper.vm.isImage).toBe(true);
        });

        it('it supports DIB files', () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('dib'),
                },
            });
            expect(wrapper.vm.fileType).toBe('dib');
            expect(wrapper.vm.isImage).toBe(true);
        });

        it('it supports TIFF files', async () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('tiff'),
                },
            });
            expect(wrapper.vm.fileType).toBe('tiff');
            expect(wrapper.vm.isImage).toBe(true);
        });

        it('it supports WEBP files', () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('webp'),
                },
            });
            expect(wrapper.vm.fileType).toBe('webp');
            expect(wrapper.vm.isImage).toBe(true);
        });

        it('should return "unsupported" for unsupported files', () => {
            wrapper = mountComponent({
                props: {
                    documentSource: getSampleData('svg'),
                },
            });
            expect(wrapper.vm.fileType).toBe('unsupported');
            expect(wrapper.vm.isImage).toBe(false);
        });

        it('should return "unsupported" when documentSource is undefined', () => {
            wrapper = mountComponent({
                props: {
                    documentSource: undefined,
                },
            });
            expect(wrapper.vm.fileType).toBe('unsupported');
            expect(wrapper.vm.isImage).toBe(false);
        });
    });
});

const fileHeaders: { [key: string]: number[] } = {
    pdf: [
        0x25, 0x50, 0x44, 0x46, 0x2d, 0x31, 0x2e, 0x34, 0x0a, 0x25, 0xe2, 0xe3, 0xcf, 0xd3, 0x0a, 0x31, 0x20, 0x30,
        0x20, 0x6f,
    ],
    jpeg: [
        0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00, 0x60, 0x00, 0x60,
        0x00, 0x00,
    ],
    png: [
        0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52, 0x00, 0x00,
        0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    ],
    gif: [
        0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x21, 0xf9, 0x04, 0x00, 0x00, 0x00, 0x00,
    ],
    bmp: [
        0x42, 0x4d, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
        0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
    ],
    dib: [
        0x28, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x0b, 0x00, 0x00, 0x13, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    ],
    tiff: [
        0x49, 0x49, 0x2a, 0x00, 0x08, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x01, 0x03, 0x00, 0x01, 0x00, 0x00, 0x00,
        0xc8, 0x00, 0x00, 0x00, 0x01, 0x01, 0x03, 0x00, 0x01, 0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00, 0x02, 0x01,
        0x03, 0x00, 0x01, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x03, 0x01, 0x03, 0x00, 0x01, 0x00, 0x00, 0x00,
        0x01, 0x00, 0x00, 0x00, 0x11, 0x01, 0x04, 0x00, 0x01, 0x00, 0x00, 0x00, 0x7a, 0x00, 0x00, 0x00, 0x17, 0x01,
        0x04, 0x00, 0x01, 0x00, 0x00, 0x00, 0x4e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x4e, 0x00, 0x00,
        0x5a, 0x00, 0x00, 0x00,
    ],
    webp: [
        0x52, 0x49, 0x46, 0x46, 0x00, 0x00, 0x00, 0x00, 0x57, 0x45, 0x42, 0x50, 0x56, 0x50, 0x38, 0x4c, 0x00, 0x00,
        0x00, 0x00,
    ],
    svg: Array.from(
        `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="40" stroke="black" stroke-width="3" fill="red" />
        </svg>`
            .split('')
            .map((char) => char.charCodeAt(0))
    ),
};

function getSampleData(fileType: string): ArrayBuffer {
    const header = fileHeaders[fileType];
    if (!header) {
        throw new Error(`Unsupported file type: ${fileType}`);
    }
    const uint8Array = new Uint8Array(header);
    return uint8Array.buffer;
}

function mountComponent({ props = {}, slots = {} } = {}) {
    return mount(WtgDocumentViewer, {
        props,
        slots,
        global: {
            plugins: [wtgUi],
        },
    });
}
