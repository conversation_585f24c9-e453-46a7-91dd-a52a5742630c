import { WtgFramework } from '@components/framework/types';
import { mount, type VueWrapper } from '@vue/test-utils';
import { WtgAlertDialog } from '../';
import WtgUi from '../../../../WtgUi';
import { ButtonType } from '../types';

const wtgUi = new WtgUi();

describe('WtgAlertDialog', () => {
    let application: WtgFramework;
    let el: HTMLElement;

    beforeEach(() => {
        application = new WtgFramework();
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is WtgAlertDialog', () => {
        const wrapper = mountComponent();
        expect(wrapper.vm.$options.__name).toBe('WtgAlertDialog');
    });

    describe('when the dialog is shown', () => {
        let wrapper: VueWrapper;
        let buttons: ButtonType[];

        beforeEach(async () => {
            wrapper = mountComponent();
            buttons = [
                {
                    caption: 'Button 1',
                    isDefault: false,
                    isLoading: false,
                    onClick: jest.fn(),
                },
                {
                    caption: 'Button 2',
                    isDefault: true,
                    isLoading: false,
                    onClick: jest.fn(),
                },
            ];
            await wrapper.setProps({
                modelValue: true,
                title: 'Alert Title',
                message: 'This is an alert message',
                buttons,
            });
        });

        test('it renders a persistent modal with with auto', () => {
            const alertModal = wrapper.findComponent({ name: 'WtgAlertModal' });
            expect(alertModal.props('modelValue')).toBe(true);
            expect(alertModal.props('persistent')).toBe(true);
            expect(alertModal.props('width')).toBe('auto');
        });

        test('it passes the title to the modal', async () => {
            const alertModal = wrapper.findComponent({ name: 'WtgAlertModal' });
            expect(alertModal.props('title')).toBe('Alert Title');
        });

        test('it sets the sentiment of the modal based on the message type', async () => {
            const alertModal = wrapper.findComponent({ name: 'WtgAlertModal' });
            expect(alertModal.props('sentiment')).toBe('success');
            await wrapper.setProps({ messageType: 2 });

            expect(alertModal.props('sentiment')).toBe('warning');
            await wrapper.setProps({ messageType: 4 });

            expect(alertModal.props('sentiment')).toBe('error');
            await wrapper.setProps({ messageType: 6 });

            expect(alertModal.props('sentiment')).toBe('question');
            await wrapper.setProps({ messageType: 3 });

            expect(alertModal.props('sentiment')).toBe('message-error');
            await wrapper.setProps({ messageType: 1 });

            expect(alertModal.props('sentiment')).toBe('info');
        });

        test('it displays the message passed', async () => {
            const message = document.querySelector('#message');
            expect(message?.innerHTML).toContain('This is an alert message');
        });

        test('it renders the buttons passed', async () => {
            const alertButtons = wrapper.findAllComponents({ name: 'WtgButton' });
            expect(alertButtons.at(0)?.text()).toBe('Button 1');
            expect(alertButtons.at(0)?.props('variant')).toBe(undefined);
            expect(alertButtons.at(0)?.props('sentiment')).toBe(undefined);
            await alertButtons.at(0)?.trigger('click');

            expect(buttons[0].onClick).toHaveBeenCalled();
            expect(alertButtons.at(1)?.text()).toBe('Button 2');
            expect(alertButtons.at(1)?.props('variant')).toBe('fill');
            expect(alertButtons.at(1)?.props('sentiment')).toBe('primary');
            await alertButtons.at(1)?.trigger('click');

            expect(buttons[1].onClick).toHaveBeenCalled();
        });

        describe('when the messageResultType is CustomTextConfirmation', () => {
            beforeEach(async () => {
                await wrapper.setProps({
                    modelValue: true,
                    title: 'Alert Title',
                    message: 'Please type text to confirm: ',
                    buttons,
                    messageType: 4,
                    messageResultType: 'CustomTextConfirmation',
                    customMessage: 'YES',
                });
            });

            test('it displays the custom message', async () => {
                const message = document.querySelector('#custom');
                expect(message?.innerHTML).toContain('YES');
            });

            test('it renders a text field', () => {
                const textField = wrapper.findComponent({ name: 'WtgTextField' });
                expect(textField.exists()).toBe(true);
            });

            test('it disables the default button when the text field and custom messages do not match', () => {
                const alertButtons = wrapper.findAllComponents({ name: 'WtgButton' });
                expect(alertButtons.at(0)?.props('variant')).toBe(undefined);
                expect(alertButtons.at(0)?.props('sentiment')).toBe(undefined);
                expect(alertButtons.at(0)?.props('disabled')).toBe(false);
                expect(alertButtons.at(1)?.props('variant')).toBe('fill');
                expect(alertButtons.at(1)?.props('sentiment')).toBe('primary');
                expect(alertButtons.at(1)?.props('disabled')).toBe(true);
            });

            test('it enables the default button when the text field and custom messages do not match', async () => {
                const textField = wrapper.findComponent({ name: 'WtgTextField' });
                await textField.setValue('YES', 'modelValue');

                const alertButtons = wrapper.findAllComponents({ name: 'WtgButton' });
                expect(alertButtons.at(0)?.props('variant')).toBe(undefined);
                expect(alertButtons.at(0)?.props('sentiment')).toBe(undefined);
                expect(alertButtons.at(0)?.props('disabled')).toBe(false);
                expect(alertButtons.at(1)?.props('variant')).toBe('fill');
                expect(alertButtons.at(1)?.props('sentiment')).toBe('primary');
                expect(alertButtons.at(1)?.props('disabled')).toBe(false);
            });
        });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgAlertDialog, {
            wtgUi,
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
            provide: { framework: { application } },
        });
    }
});
