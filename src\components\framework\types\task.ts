import { WtgFrameworkNotification } from './framework';

export enum WtgFrameworkTaskGenericActionPlacement {
    Primary = 'primary',
    Secondary = 'secondary',
    Default = 'default',
    TaskAction = 'taskactions',
}

export interface WtgFrameworkActionMenuItem {
    label: string;
    onClick?: () => void;
    loadItemsAsync?: () => Promise<Array<WtgFrameworkActionMenuItem>>;
}

export interface WtgFrameworkDocumentsMenuItem {
    caption: string;
    click?: () => void;
    loading?: boolean;
    loadDocumentsAsync?: () => Promise<Array<WtgFrameworkDocumentsMenuItem>>;
    submenu?: boolean;
    actions?: WtgFrameworkDocumentsMenuItem[];
}

export interface WtgFrameworkDocuments {
    visible: boolean;
    caption: string;
    loadDocumentsAsync?: () => Promise<Array<WtgFrameworkDocumentsMenuItem>>;
}

export interface WtgFrameworkTaskGenericAction {
    id: string;
    caption: string;
    category?: string;
    placement: string;
    icon?: string;
    onInvoke: () => void;
}

export interface WtgFrameworkTaskStandardAction {
    visible: boolean;
    caption: string;
    label: string;
    loading?: boolean;
    onInvoke: () => void;
}

export interface WtgFrameworkTaskWorkflowActionMenuItem {
    caption: string;
    onInvoke: () => void;
}

export interface WtgFrameworkTaskWorkflowAction {
    visible: boolean;
    caption: string;
    menuItems: WtgFrameworkTaskWorkflowActionMenuItem[];
}

export interface WtgFrameworkTaskStatus {
    code: string;
    label: string;
    sentiment: string | undefined;
    variant: string | undefined;
}

export interface WtgFrameworkEntityNavigation {
    visible: boolean;
    indexCountText: String;
    hasPrevious: Boolean;
    hasNext: Boolean;
    onPrevious: Function;
    onNext: Function;
}

export enum WtgFrameworkTaskEntityStatusDisplayMode {
    ReadOnly = 'ReadOnly',
    Editable = 'Editable',
    Hidden = 'Hidden',
}

export class WtgFrameworkTask {
    title: string;
    currentStatus: WtgFrameworkTaskStatus | undefined;
    onCurrentStatusChange: Function;
    currentStatusDisplayMode: WtgFrameworkTaskEntityStatusDisplayMode | undefined;
    statusItems: WtgFrameworkTaskStatus[];
    entityName: string;
    entityUrl: string;
    isFavorite: boolean;
    hasChanges: boolean;
    showTaskTitle: boolean;
    showCloseButton: boolean;
    showFooter: boolean;
    showBackButton: boolean;
    showNotifications: boolean;
    saveAction: WtgFrameworkTaskStandardAction;
    saveCloseAction: WtgFrameworkTaskStandardAction;
    cancelAction: WtgFrameworkTaskStandardAction;
    showEDocsAction: WtgFrameworkTaskStandardAction;
    showLogsAction: WtgFrameworkTaskStandardAction;
    showMessagesAction: WtgFrameworkTaskStandardAction;
    showNotesAction: WtgFrameworkTaskStandardAction;
    showWorkflowActions: WtgFrameworkTaskWorkflowAction;
    genericActions: WtgFrameworkTaskGenericAction[];
    notifications: WtgFrameworkNotification[];
    documents: WtgFrameworkDocuments;
    toggleFavorite: () => Promise<void>;
    others: WtgFrameworkEntityNavigation;

    constructor() {
        this.title = '';
        this.currentStatus = {
            code: '',
            label: '',
            sentiment: '',
            variant: '',
        };
        this.currentStatusDisplayMode = WtgFrameworkTaskEntityStatusDisplayMode.Hidden;
        this.statusItems = [];
        this.entityName = '';
        this.entityUrl = '';
        this.isFavorite = false;
        this.hasChanges = false;
        this.showTaskTitle = false;
        this.showCloseButton = false;
        this.showFooter = false;
        this.showNotifications = false;
        this.onCurrentStatusChange = (): void => undefined;
        this.showBackButton = true;
        this.cancelAction = {
            visible: false,
            caption: 'Cancel',
            label: 'Cancel',
            onInvoke: (): void => undefined,
        };
        this.saveAction = {
            visible: false,
            caption: 'Save changes',
            label: 'Save',
            onInvoke: (): void => undefined,
        };
        this.saveCloseAction = {
            visible: false,
            caption: 'Save and close',
            label: 'Close',
            onInvoke: (): void => undefined,
        };
        this.showEDocsAction = {
            visible: false,
            caption: 'eDocs',
            label: 'eDocs',
            onInvoke: (): void => undefined,
        };
        this.showLogsAction = {
            visible: false,
            caption: 'Logs',
            label: 'Logs',
            onInvoke: (): void => undefined,
        };
        this.showMessagesAction = {
            visible: false,
            caption: 'Messages',
            label: 'Messages',
            onInvoke: (): void => undefined,
        };
        this.showNotesAction = {
            visible: false,
            caption: 'Notes',
            label: 'Notes',
            onInvoke: (): void => undefined,
        };
        this.showWorkflowActions = {
            visible: false,
            caption: 'Workflows',
            menuItems: [],
        };
        this.documents = {
            visible: false,
            caption: 'Documents',
        };
        this.genericActions = [];
        this.notifications = [];
        this.toggleFavorite = (): Promise<void> => Promise.resolve();
        this.others = {
            visible: false,
            indexCountText: '',
            hasPrevious: false,
            hasNext: false,
            onPrevious: (): void => undefined,
            onNext: (): void => undefined,
        };
    }
}

export default WtgFrameworkTask;
