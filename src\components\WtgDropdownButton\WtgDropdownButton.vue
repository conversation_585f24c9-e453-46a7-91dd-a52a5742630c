<template>
    <div v-floating-vue-tooltip="tooltipDirective" class="wtg-dropdown-button" :style="{ ...measurableStyles }">
        <WtgButton
            v-if="isSplitButton"
            :disabled="disabled"
            :fill="fill"
            :loading="loading"
            :sentiment="sentiment"
            :variant="variant"
            :leading-icon="leadingIcon"
            class="wtg-left-button"
            style="overflow: hidden"
            @click="onButtonClicked"
        >
            <slot />
        </WtgButton>
        <WtgPopover
            v-model="isOpen"
            :location="`${openPosition} right`"
            :max-height="popoverMaxHeight"
            :nudge-top="openPosition === 'top' ? '4px' : undefined"
            :nudge-bottom="openPosition === 'bottom' ? '4px' : undefined"
            :close-on-content-click="closePopoverOnContentClick"
        >
            <template #activator="{ props: activatorProps }: { props: Record<string, any> }">
                <WtgButton
                    v-if="!isSplitButton"
                    :disabled="disabled"
                    :fill="fill"
                    :loading="loading"
                    :sentiment="sentiment"
                    :variant="variant"
                    :leading-icon="leadingIcon"
                    :trailing-icon="!loading ? 's-icon-caret-switch' : ''"
                    v-bind="activatorProps"
                    :active="isOpen"
                    style="overflow: hidden"
                    @click="onButtonClicked"
                >
                    <slot />
                </WtgButton>
                <WtgIconButton
                    v-else
                    v-bind="activatorProps"
                    class="wtg-icon-button--prompter"
                    icon="s-icon-caret-switch"
                    :variant="variant"
                    :sentiment="sentiment"
                    :active="isOpen"
                    :disabled="disabled"
                    :aria-label="dropdownAria ?? formatCaption('dropdownButton.toggleMenu')"
                    data-testid="dropdown-button"
                    @click="onDropdownButtonClicked"
                >
                </WtgIconButton>
            </template>
            <slot name="popover" />
        </WtgPopover>
    </div>
</template>

<script setup lang="ts">
import { WtgButton } from '@components/WtgButton';
import { WtgIconButton } from '@components/WtgIconButton';
import { WtgPopover } from '@components/WtgPopover';
import { useLocale } from '@composables/locale';
import { makeMeasureProps, useMeasure } from '@composables/measure';
import { makeTooltipProps, useTooltip } from '@composables/tooltip';
import { PropType } from 'vue';

const props = defineProps({
    /**
     * If true, the popover will close when its content is clicked.
     */
    closePopoverOnContentClick: {
        type: Boolean,
        default: true,
    },

    /**
     * Disables the dropdown button, making it unclickable and visually indicating its disabled state.
     */
    disabled: {
        type: Boolean,
        default: false,
    },

    /**
     * The ARIA label for the dropdown button.
     */
    dropdownAria: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the button will fill the available space.
     */
    fill: {
        type: Boolean,
        default: false,
    },

    /**
     * If true, the dropdown button will be displayed as a split button.
     */
    isSplitButton: {
        type: Boolean,
        default: true,
    },

    /**
     * The name of the leading icon to display in the button.
     */
    leadingIcon: {
        type: String,
        default: undefined,
    },

    /**
     * If true, a loading indicator will be displayed on the button.
     */
    loading: {
        type: Boolean,
        default: false,
    },

    /**
     * The position where the dropdown popover will open.
     * Options include 'top' or 'bottom'.
     */
    openPosition: {
        type: String as PropType<'top' | 'bottom'>,
        default: 'bottom',
    },

    /**
     * The maximum height of the dropdown popover, in pixels.
     */
    popoverMaxHeight: {
        type: Number,
        default: undefined,
    },

    /**
     * The sentiment or visual style of the button.
     * Options include 'critical', 'primary', or 'success'.
     */
    sentiment: {
        type: String as PropType<'critical' | 'primary' | 'success'>,
        default: undefined,
    },

    /**
     * The variant of the button.
     * Options include 'fill'.
     */
    variant: {
        type: String as PropType<'fill'>,
        default: undefined,
    },

    ...makeTooltipProps(),
    ...makeMeasureProps(),
});

const { measurableStyles } = useMeasure(props);
const { formatCaption } = useLocale();

const isOpen = defineModel<boolean>({ default: undefined });

const emit = defineEmits<{
    /**
     * Emitted when the button is clicked.
     * @event
     * @param {MouseEvent} e - The click event object.
     */
    click: [e: MouseEvent];
    'dropdown-click': [e: MouseEvent];
}>();

const { tooltipDirective } = useTooltip(props);

const onDropdownButtonClicked = (e: MouseEvent) => {
    emit('dropdown-click', e);
};

const onButtonClicked = (e: MouseEvent) => {
    emit('click', e);
};
</script>

<style lang="scss">
.wtg-dropdown-button {
    display: inline-flex;
    align-items: flex-start;

    & > .wtg-left-button {
        border-start-end-radius: 0;
        border-end-end-radius: 0;
        border-inline-end: 0;
        flex-grow: 1;
        .wtg-button__shadow {
            border-start-end-radius: 0;
            border-end-end-radius: 0;
        }
    }
}
</style>
