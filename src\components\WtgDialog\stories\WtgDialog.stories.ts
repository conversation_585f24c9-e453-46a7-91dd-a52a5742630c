import WtgButton from '@components/WtgButton';
import WtgDialog from '@components/WtgDialog';
import { Meta, StoryContext, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';

function createProps(storyContext: StoryContext): string {
    const props = [];
    for (const arg in storyContext.args) {
        props.push(`${arg}="${storyContext.args[arg] + ''}"`);
    }
    return props.sort().join(' ');
}
type Story = StoryObj<typeof WtgDialog>;
const meta: Meta<typeof WtgDialog> = {
    title: 'Components/Dialog',
    component: WtgDialog,
    parameters: {
        docs: {
            description: {
                component:
                    'The Dialog component inform users about a specific task and may contain critical information, require decisions, or involve multiple tasks.',
            },
            source: {
                transform: (source: string, storyContext: StoryContext) => `
<template>
    <WtgContainer>
        <WtgButton variant="fill" sentiment="primary" @click="isActive = true">Open Dialog</WtgButton>

        <WtgDialog v-model="isActive" ${createProps(storyContext)}>
            <template #default>
                <div class="wtg-content-background pa-xl">
                    <p>This is a dialog. Click Cancel or outside the dialog to close.</p>
                    <div class="d-flex justify-end pt-xl">
                        <WtgButton variant="ghost" @click="isActive = false">Cancel</WtgButton>
                    </div>
                </div>
            </template>
        </WtgDialog>
    </WtgContainer>
</template>

<script lang="ts" setup>
    import { ref } from 'vue';
    import { WtgButton, WtgContainer, WtgDialog } from '@wtg/wtg-components';

    const isActive = ref(false);
</script>`,
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?node-id=3127%3A25127',
        },
        layout: 'centered',
        slots: {
            activator: `Default slot content`,
        },
    },
    render: (args) => ({
        components: { WtgDialog, WtgButton },
        setup: () => {
            const isActive = ref(false);

            return { args, isActive };
        },
        template: `
            <WtgButton variant="fill" sentiment="primary" @click="isActive = true">Open Dialog</WtgButton>
            <WtgDialog v-model="isActive" v-bind="args">
                <div class="wtg-content-background pa-xl">
                    <p>This is a dialog. Click Cancel or outside the dialog to close.</p>
                    <div class="d-flex justify-end pt-xl">
                        <WtgButton variant="ghost" @click="isActive = false">Cancel</WtgButton>
                    </div>
                </div>
            </WtgDialog>
        `,
    }),
};

export default meta;
export const Default: Story = {
    args: {
        width: 'auto',
    },
};
