export const CheckboxSentimentsTemplate = `
<WtgRow>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgCheckbox 
            v-bind="args"
            label="Default"
            @click="action">                
        </WtgCheckbox>
        <WtgCheckbox 
            v-bind="args"
            sentiment="critical"
            label="Critical"
            @click="action">                
        </WtgCheckbox>
        <WtgCheckbox 
            v-bind="args"
            sentiment="warning"
            label="Warning"
            @click="action">                
        </WtgCheckbox>
        <WtgCheckbox 
            v-bind="args"
            sentiment="success"
            label="Success"
            @click="action">                
        </WtgCheckbox>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;" class="d-flex flex-column">
        <WtgCheckbox 
            v-bind="args"
            label="Default"
            :model-value="true"
            @click="action">                
        </WtgCheckbox>
        <WtgCheckbox 
            v-bind="args"
            sentiment="critical"
            label="Critical"
            :model-value="true"
            @click="action">                
        </WtgCheckbox>
        <WtgCheckbox 
            v-bind="args"
            sentiment="warning"
            label="Warning"
            :model-value="true"
            @click="action">                
        </WtgCheckbox>
        <WtgCheckbox 
            v-bind="args"
            sentiment="success"
            label="Success"
            :model-value="true"
            @click="action">                
        </WtgCheckbox>
    </WtgCol>
    <WtgCol style="max-width: fit-content; gap: 8px;">
        <WtgCheckbox 
            v-bind="args"
            label="Default"
            :model-value="true"
            indeterminate
            @click="action">                
        </WtgCheckbox>
    </WtgCol>
</WtgRow>`;
