<template>
    <WtgBox style="border-radius: var(--s-radius-s)">
        <template v-if="showOrganizationName">
            <WtgLabel typography="text-md-strong"> {{ companyHeader }} </WtgLabel>
            {{ companyCode }}
        </template>
        <div v-if="addressLines.length">
            <WtgLabel v-for="(line, index) in addressLines" :key="index" typography="text-md-default" class="d-flex">
                {{ line }}
            </WtgLabel>
        </div>
        <WtgLabel v-if="showAdditionalInformation && additionalInformation" class="d-flex">
            {{ additionalInformation }}
        </WtgLabel>
        <WtgLabel v-if="showContact" typography="text-md-default" class="ml-0 d-flex">
            <span v-if="contact && contact !== ''">{{ contact }}</span>
            <span v-else style="color: var(--s-neutral-txt-weak-default)">{{ noAddressSelected }}</span>
        </WtgLabel>
    </WtgBox>
</template>

<script setup lang="ts">
import WtgBox from '@components/WtgBox';
import WtgLabel from '@components/WtgLabel';
import { useLocale } from '@composables/locale';
import { computed, PropType } from 'vue';

defineProps({
    addressLines: {
        type: Array as PropType<string[]>,
        default: (): string[] => [],
    },
    companyCode: {
        type: String,
        default: '',
    },
    companyHeader: {
        type: String,
        default: '',
    },
    contact: {
        type: String,
        default: undefined,
    },
    showAdditionalInformation: {
        type: Boolean,
        default: false,
    },
    showContact: {
        type: Boolean,
        default: false,
    },
    showOrganizationName: {
        type: Boolean,
        default: true,
    },
    additionalInformation: {
        type: String,
        default: undefined,
    },
});
const { formatCaption } = useLocale();
const noAddressSelected = computed(() => formatCaption('jobAddress.noContactDetails'));
</script>
