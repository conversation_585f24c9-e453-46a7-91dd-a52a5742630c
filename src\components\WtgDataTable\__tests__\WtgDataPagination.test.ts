import WtgDataPagination from '@components/WtgDataTable/WtgDataPagination.vue';
import { enableAutoUnmount, mount, VueWrapper } from '@vue/test-utils';
import { VPagination } from 'vuetify/components/VPagination';
import WtgUi from '../../../WtgUi';

const wtgUi = new WtgUi();

enableAutoUnmount(afterEach);

describe('WtgDataPagination', () => {
    test('it renders a VPagination component', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent(VPagination).exists()).toBe(true);
    });

    test('it initializes the VPagination component with the correct appearance', () => {
        const wrapper = mountComponent({
            propsData: {
                itemsLength: 1000,
                itemsPerPage: 12,
            },
        });

        const props = wrapper.findComponent(VPagination).props();
        expect(props.density).toBe('compact');
        expect(props.length).toBe(84);
        expect(props.totalVisible).toBe(0);
        expect(props.showFirstLastPage).toBe(true);
    });

    test('it displays the current caption as a formatted string', () => {
        const wrapper = mountComponent({
            propsData: {
                itemsLength: 1000,
                itemsPerPage: 12,
                page: 2,
            },
        });
        const currentItemsCaption = wrapper.find('[data-testid="current-items-caption"]');
        expect(currentItemsCaption.text()).toBe('13-24 of 1000');
    });

    test('it bounds the itemsPerPage to the itemsLength maximum when formatting', () => {
        const wrapper = mountComponent({
            propsData: {
                itemsLength: 5,
                itemsPerPage: 12,
            },
        });
        const currentItemsCaption = wrapper.find('[data-testid="current-items-caption"]');
        expect(currentItemsCaption.text()).toBe('1-5 of 5');
    });

    test('it displays the correct caption when no items are present', () => {
        const wrapper = mountComponent({
            propsData: {
                itemsLength: -1,
                itemsPerPage: 12,
                page: 2,
            },
        });
        const currentItemsCaption = wrapper.find('[data-testid="current-items-caption"]');
        expect(currentItemsCaption.text()).toBe('0-0 of 0');
    });

    test('it passes all slots to the VPagination component', () => {
        const wrapper = mountComponent();
        expect(wrapper.findAllComponents({ name: 'WtgIconButton' }).length).toBe(4);
    });

    test('it has a itemsPerPageOptions prop that determines what options are presented in the items per page select field', () => {
        const wrapper = mountComponent({ propsData: { itemsPerPageOptions: [10, 25, 50] } });
        expect(wrapper.findComponent({ name: 'WtgSelectField' }).props('items')).toStrictEqual([10, 25, 50]);
    });

    test('it emits update:options with internalOptions when updateOptions is called', async () => {
        const wrapper: VueWrapper<any> = mountComponent({ propsData: { itemsLength: 100, itemsPerPage: 10, page: 2 } });

        wrapper.vm.updateOptions();

        expect(wrapper.emitted('update:options')).toBeTruthy();
        expect(wrapper.emitted('update:options')![0][0]).toEqual({ page: 2, itemsPerPage: 10 });
    });

    test('it computes internalOptions correctly when props change', async () => {
        const wrapper: VueWrapper<any> = mountComponent({ propsData: { itemsLength: 50, itemsPerPage: 5, page: 3 } });

        expect(wrapper.vm.internalOptions).toEqual({ page: 3, itemsPerPage: 5 });

        await wrapper.setProps({ page: 4, itemsPerPage: 25 });

        expect(wrapper.vm.internalOptions).toEqual({ page: 4, itemsPerPage: 25 });
    });

    test('it emits update:options and update:page when updatePage is called', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            propsData: {
                itemsLength: 100,
                itemsPerPage: 10,
                page: 1,
            },
        });

        wrapper.vm.updatePage(2);

        expect(wrapper.emitted('update:page')).toBeTruthy();
        expect(wrapper.emitted('update:page')![0][0]).toBe(2);

        expect(wrapper.emitted('update:options')).toBeTruthy();
        expect(wrapper.emitted('update:options')![0][0]).toEqual({ page: 2, itemsPerPage: 10 });
    });

    test('it emits update:items-per-page and update:options when onUpdateItemsPerPage is called', async () => {
        const wrapper: VueWrapper<any> = mountComponent({
            propsData: {
                itemsLength: 100,
                itemsPerPage: 10,
                page: 1,
            },
        });

        wrapper.vm.onUpdateItemsPerPage('25');

        expect(wrapper.emitted('update:items-per-page')).toBeTruthy();
        expect(wrapper.emitted('update:items-per-page')![0][0]).toBe(25);

        expect(wrapper.emitted('update:options')).toBeTruthy();
        expect(wrapper.emitted('update:options')!.at(-1)?.[0]).toEqual({ page: 1, itemsPerPage: 25 });
    });

    function mountComponent({ propsData = {}, slots = {} } = {}) {
        return mount(WtgDataPagination as any, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
