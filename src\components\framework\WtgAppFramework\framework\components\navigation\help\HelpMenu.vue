<template>
    <HelpInlineMenu v-if="isTabletOrMobile" @item-click="emit('item-click')" />
    <WtgPopover
        v-else
        close-on-content-click
        location="right bottom"
        nudge-right="var(--s-padding-xl)"
        min-width="312px"
        max-width="312px"
    >
        <template #activator="args">
            <WtgListItem
                role="menuitem"
                :aria-label="application.captions.help"
                aria-haspopup="menu"
                :class="!railActive ? '' : 'd-flex justify-center'"
                v-bind="args.props"
                :collapsed="railActive"
                leading-icon="s-icon-help"
                trailing-icon="s-icon-caret-right"
            >
                {{ application.captions.help }}
            </WtgListItem>
        </template>
        <WtgList>
            <div class="wtg-typography-title-small">
                {{ application.captions.help }}
            </div>
            <WtgDivider />
            <WtgListItem v-if="hasPageHelp" @click="onPageHelpClick">
                {{ application.captions.pageHelp }}
            </WtgListItem>
            <WtgListItem
                v-for="item in helpItems"
                :key="item.title"
                :href="item.link"
                role="menuitem"
                :aria-label="item.title"
                target="_blank"
                @click="onItemClick($event, item)"
            >
                {{ item.title }}
            </WtgListItem>
            <WtgListItem @click="onAboutClick">
                {{ application.captions.about }}
            </WtgListItem>
        </WtgList>
    </WtgPopover>
</template>

<script setup lang="ts">
import { WtgFrameworkHelpItem } from '@components/framework/types';
import WtgDivider from '@components/WtgDivider';
import { WtgList, WtgListItem } from '@components/WtgList';
import WtgPopover from '@components/WtgPopover';
import { useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { computed } from 'vue';
import HelpInlineMenu from './HelpInlineMenu.vue';

const application = useApplication();
const { isTabletOrMobile } = useFramework();

const emit = defineEmits<{
    'item-click': [];
}>();

const railActive = computed((): boolean => {
    return isTabletOrMobile.value ? false : application.navDrawer.isRailActive;
});

const hasPageHelp = computed((): boolean => {
    return !!application.pageHelp;
});

const helpItems = computed((): WtgFrameworkHelpItem[] => {
    return application.helpItems;
});

function onItemClick(event: UIEvent, item: WtgFrameworkHelpItem): void {
    if (item.onClick) {
        item.onClick(event);
    }
}

function onPageHelpClick(): void {
    application.openPageHelp();
}

function onAboutClick(): void {
    application.dialogs.about.open();
}
</script>
