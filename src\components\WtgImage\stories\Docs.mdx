import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import info from '../../../storybook/assets/info.png';

import { Meta, Title, Description, Story, Canvas, Controls, ArgTypes } from '@storybook/blocks';
import * as WtgImage from './WtgImage.stories.ts';

<Meta of={WtgImage} />

<div className="component-header">
    <h1>Image</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th scope="row">
                <img className="status-chip" src={statusAvailable}></img>
            </th>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

## Image types

### Default

<p>Default Image usage</p>

<Story of={WtgImage.DefaultImage} />

### Cover

<p>
    If the provided aspect ratio doesn’t match that of the actual image, the default behavior is to fill as much space
    as possible without cropping. To fill the entire available space use the cover prop.
</p>

<Story of={WtgImage.CoverImage} />

### Height

<p>
    wtg-image will automatically grow to the size of its src, preserving the correct aspect ratio. You can limit this
    with the height and max-height props.
</p>

#### Height

<Story of={WtgImage.HeightImage} />

#### height with cover

<Story of={WtgImage.HeightWithCover} />

#### max-height

<Story of={WtgImage.MaxHeight} />

#### max-height with cover

<Story of={WtgImage.MaxHeightWithCover} />

## Accessibility

### Alternative text

-   Provide descriptive alt text for informative images to convey their purpose or content to screen readers.
-   Use an empty alt attribute (alt="") for [decorative images](https://webaim.org/techniques/alttext/#decorative) that do not add meaningful information.
-   Avoid phrases like "image of" or "picture of" in alt text, as screen readers already announce the element as an image.

### Image source

-   Ensure a minimum contrast ratio of 4.5:1 for any text overlaid on images to meet accessibility standards.
-   Non-text content, such as infographics, must have sufficient contrast to ensure readability.
-   Use an online [contrast checker](https://webaim.org/resources/contrastchecker/) to test your images and ensure they meet accessibility requirements.

For more information, see [WCAG 2.1 Success Criterion 1.4.3: Contrast (Minimum)](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html).

### Using the `aria-describedby` attribute

The `aria-describedby` attribute provides additional context for images that cannot be fully described using the `alt` attribute alone. For example, use it for:

-   Complex charts or graphs that need a detailed explanation.

For more information, see [W3C WAI-ARIA 1.1 Specification](https://www.w3.org/TR/wai-aria-1.1/#aria-describedby).

### Accessibility responsibilities

Accessibility is a shared responsibility between the `WtgImage` component and its consumers. The following table clarifies these responsibilities:

| Accessibility aspect                      | `WtgImage` responsibility             | Consumer responsibility                                                         |
| ----------------------------------------- | ------------------------------------- | ------------------------------------------------------------------------------- |
| Native `<img>` semantics                  | Provided automatically                | —                                                                               |
| Intrinsic aspect ratio & responsive fit   | Provided via `cover` and sizing props | Use `cover`/`sizing` props as needed to achieve desired layout and aspect ratio |
| Alternative text (`alt`)                  | —                                     | Provide meaningful text or `""` for decorative images                           |
| Detailed description (`aria-describedby`) | —                                     | Link to detailed description when necessary                                     |
| Contrast of overlaid text                 | —                                     | Ensure a minimum 4.5:1 contrast ratio                                           |
| Caption / surrounding context             | —                                     | Provide meaningful captions or context in page markup                           |

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
