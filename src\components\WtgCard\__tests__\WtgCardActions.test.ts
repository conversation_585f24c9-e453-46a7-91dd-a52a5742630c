import { enableAutoUnmount, mount } from '@vue/test-utils';
import { WtgCardActions } from '../';
import WtgUi from '../../../WtgUi';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

describe('WtgCardActions', () => {
    test('it renders a <div> component', () => {
        const wrapper = mountComponent();
        expect(wrapper.element.tagName).toBe('DIV');
    });

    test('it adds the wtg-card-actions class to allow application styling to be added', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('wtg-card-actions');
    });

    test('it renders as a VCardActions to ensure we are backwards compatible with Vue2 code', () => {
        const wrapper = mountComponent();
        expect(wrapper.classes()).toContain('v-card-actions');
    });

    test('it passes the default slot to the <div>', () => {
        const wrapper = mountComponent();
        expect(wrapper.text()).toBe('Some Content');
    });

    function mountComponent({ propsData = {}, slots = { default: 'Some Content' } } = {}) {
        return mount(WtgCardActions, {
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
    }
});
