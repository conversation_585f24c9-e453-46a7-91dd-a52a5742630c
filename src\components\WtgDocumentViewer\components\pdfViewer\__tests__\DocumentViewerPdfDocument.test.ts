import DocumentViewerPdfDocument from '@components/WtgDocumentViewer/components/pdfViewer/DocumentViewerPdfDocument.vue';
import DocumentViewerPdfPage from '@components/WtgDocumentViewer/components/pdfViewer/DocumentViewerPdfPage.vue';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { ComponentPublicInstance, nextTick } from 'vue';
import WtgUi from '../../../../../WtgUi';

const wtgUi = new WtgUi();
enableAutoUnmount(afterEach);

jest.mock('pdfjs-dist/webpack.mjs', () => ({
    PDFDocumentProxy: jest.fn(),
    PDFPageProxy: jest.fn().mockImplementation(() => ({
        getViewport: jest.fn().mockReturnValue({ width: 800, height: 600 }),
        render: jest.fn().mockResolvedValue({
            promise: Promise.resolve(),
            cancel: jest.fn(),
        }),
    })),
    getDocument: jest.fn().mockReturnValue({
        promise: Promise.resolve({
            numPages: 5,
            getPage: jest.fn().mockResolvedValue({
                getViewport: jest.fn().mockReturnValue({ width: 800, height: 600 }),
                render: jest.fn().mockResolvedValue({
                    onContinue: jest.fn(),
                    promise: Promise.resolve(),
                    cancel: jest.fn(),
                    separateAnnots: true,
                }),
            }),
        }),
    }),
    GlobalWorkerOptions: { workerSrc: '' },
}));

describe('Document Viewer Pdf Document', () => {
    let wrapper: any;

    beforeEach(async () => {
        wrapper = mountComponent({
            props: {
                pdfSource: new ArrayBuffer(8),
                docViewerWrapWidth: 1200,
            },
            slots: {
                'pdf-page-1': `<div class="highlight-slot-1">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
                'pdf-page-2': `<div class="highlight-slot-2">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
                'pdf-page-3': `<div class="highlight-slot-3">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
                'pdf-page-4': `<div class="highlight-slot-4">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
                'pdf-page-5': `<div class="highlight-slot-5">
                                    Page Width: {{ pdfPageData.pageWidth }},
                                    Page Height: {{ pdfPageData.pageHeight }},
                                    Page Number: {{ pdfPageData.pageNumber }}
                                </div>`,
            },
        });
        await flushPromises();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('loads the PDF and sets the page count and scale', async () => {
        expect(wrapper.vm.pageCount).toBe(5);
        expect(wrapper.vm.internalScaleConfig.scaleValue).toBeCloseTo(0.1); // Scale based on docViewerWrapWidth and PDF width
        expect(wrapper.emitted()['page-count-updated']?.length).toBe(1);
        expect(wrapper.emitted()['page-count-updated']![0][0]).toStrictEqual(5);

        expect(wrapper.emitted()['update-scale']?.length).toBe(1);
        expect(wrapper.emitted()['update-scale']![0][0]).toStrictEqual({
            resetScale: false,
            scaleMode: 'scr',
            scaleValue: 0.1,
        });

        expect(wrapper.emitted()['zoom-percentage-updated']?.length).toBe(1);
        expect(wrapper.emitted()['zoom-percentage-updated']![0][0]).toStrictEqual('10%');
    });

    it('renders multiple PdfPage components', async () => {
        expect(wrapper.findAllComponents(DocumentViewerPdfPage).length).toBe(5);
    });

    it('updates scale when zoom value changes', async () => {
        expect(wrapper.emitted()['update-scale']?.length).toBe(1);
        expect(wrapper.emitted()['update-scale']![0][0]).toStrictEqual({
            resetScale: false,
            scaleMode: 'scr',
            scaleValue: 0.1,
        });

        await wrapper.setProps({ zoomPercentage: '35%' });
        await nextTick();
        expect(wrapper.emitted()['update-scale']?.length).toBe(2);
        expect(wrapper.emitted()['update-scale']![1][0]).toStrictEqual({
            resetScale: false,
            scaleMode: 'scr',
            scaleValue: 0.30000000000000004,
        });

        await wrapper.setProps({ zoomPercentage: '10%' });
        await nextTick();
        expect(wrapper.emitted()['update-scale']?.length).toBe(3);
        expect(wrapper.emitted()['update-scale']![2][0]).toStrictEqual({
            resetScale: false,
            scaleMode: 'scr',
            scaleValue: 0.2, //minimum scale value is 0.2
        });
    });

    it('passes roation degree value to pdf page', async () => {
        await wrapper.vm.loadPdf();
        expect(wrapper.vm.rotationDegree).toBe(0);
        await wrapper.setProps({ rotationDegree: 90 }); // Rotated 90 degrees
        await nextTick();

        const firstPdfPage = wrapper.findAllComponents(DocumentViewerPdfPage).at(0);
        expect(firstPdfPage.props('pdfRotation')).toBe(90);

        await wrapper.setProps({ rotationDegree: 180 }); // Rotated another 90 degrees
        await nextTick();

        expect(firstPdfPage.props('pdfRotation')).toBe(180);
    });

    it('emits event when user click on a pdf page in preview mode', async () => {
        await wrapper.setProps({
            pagePreviewMode: true,
        });

        const thirdPdfPage = wrapper.findAllComponents(DocumentViewerPdfPage).at(2);
        expect(thirdPdfPage.find('.page-preview').exists()).toBe(true);

        expect(wrapper.vm.activePage).toBe(1);
        thirdPdfPage.find('.page-preview').trigger('click');
        expect(wrapper.vm.activePage).toBe(3);

        expect(wrapper.emitted()['active-page-updated']?.length).toBe(1);
        expect(wrapper.emitted()['active-page-updated']![0][0]).toStrictEqual(3);
    });

    it('scrolls to new active page when activePageNumber prop changes', async () => {
        const scrollIntoViewMock = jest.fn();
        document.querySelectorAll = jest.fn().mockReturnValue([
            {
                scrollIntoView: scrollIntoViewMock,
            },
        ]);

        await wrapper.setProps({
            activePageNumber: 3,
        });

        expect(scrollIntoViewMock).toHaveBeenCalledWith({ behavior: 'smooth', block: 'start' });
    });

    it('updates zoom percentage when scale mode changes', async () => {
        expect(wrapper.emitted()['zoom-percentage-updated']?.length).toBe(1);
        expect(wrapper.emitted()['zoom-percentage-updated']![0][0]).toStrictEqual('10%');

        await wrapper.setProps({
            scaleConfig: {
                scaleMode: 'act',
                scaleValue: 0,
                resetScale: true,
            },
        }); // set scale to fit actual size
        await nextTick();

        expect(wrapper.emitted()['zoom-percentage-updated']?.length).toBe(2);
        expect(wrapper.emitted()['zoom-percentage-updated']![1][0]).toStrictEqual('100%');
    });

    it('it has slots with highlight data for each pdf page', async () => {
        const pdfPages = wrapper.findAllComponents(DocumentViewerPdfPage);
        expect(pdfPages.length).toBe(5);

        const pages = [1, 2, 3, 4, 5];
        pages.forEach((page) => {
            const slotContent = wrapper.find(`.highlight-slot-${page}`);
            expect(slotContent.exists()).toBe(true);
            expect(slotContent.text()).toContain(`Page Width: 0, Page Height: 0, Page Number: ${page}`);
        });
    });

    it('when pageWrapWidth changes for a page, highlight data is updated', async () => {
        const pdfPages = wrapper.findAllComponents(DocumentViewerPdfPage);
        expect(pdfPages.length).toBe(5);

        const slotContent = wrapper.find(`.highlight-slot-3`);
        expect(slotContent.exists()).toBe(true);
        expect(slotContent.text()).toContain(`Page Width: 0, Page Height: 0, Page Number: 3`);

        pdfPages.at(2).vm.pageWrapWidth = 180;
        await nextTick();

        expect(slotContent.text()).toContain('Page Width: 180, Page Height: 0, Page Number: 3');
    });

    it('when pageWrapHeight changes for a page, highlight data is updated', async () => {
        const pdfPages = wrapper.findAllComponents(DocumentViewerPdfPage);
        expect(pdfPages.length).toBe(5);

        const slotContent = wrapper.find(`.highlight-slot-3`);
        expect(slotContent.exists()).toBe(true);
        expect(slotContent.text()).toContain(`Page Width: 0, Page Height: 0, Page Number: 3`);

        pdfPages.at(2).vm.pageWrapHeight = 900;
        await nextTick();

        expect(slotContent.text()).toContain('Page Width: 0, Page Height: 900, Page Number: 3');
    });

    it('when scaleConfig mode is not defined, scale mode is set to fit to screen', async () => {
        expect(wrapper.props('scaleConfig')).toBeUndefined();
        expect(wrapper.vm.internalScaleConfig.scaleMode).toBe('scr');
    });

    it('when scaleConfig mode is defined, scale mode is set to scale mode of the config', async () => {
        const wrapperWithScaleConfig = mountComponent({
            props: {
                pdfSource: new ArrayBuffer(8),
                docViewerWrapWidth: 1200,
                scaleConfig: {
                    scaleMode: 'act',
                    scaleValue: 0,
                    resetScale: true,
                },
            },
        });
        await flushPromises();

        const vm = wrapperWithScaleConfig.vm as ComponentPublicInstance<{
            internalScaleConfig: { scaleMode: string; scaleValue: number; resetScale: boolean };
        }>;

        expect(vm.internalScaleConfig.scaleMode).toBe('act');
    });

    it('when in preview mode, no emits for scale mode and zoom value changes', async () => {
        const wrapperInPreviewMode = mountComponent({
            props: {
                pdfSource: new ArrayBuffer(8),
                docViewerWrapWidth: 1200,
                pagePreviewMode: true,
            },
        });
        await flushPromises();

        expect(wrapperInPreviewMode.emitted('update-scale')).toBeUndefined();
        expect(wrapperInPreviewMode.emitted('zoom-percentage-updated')).toBeUndefined();
    });
});

function mountComponent({ props = {}, slots = {} } = {}) {
    return mount(DocumentViewerPdfDocument as any, {
        props,
        slots,
        global: {
            plugins: [wtgUi],
        },
    });
}
