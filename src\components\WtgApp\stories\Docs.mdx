import statusAvailable from '../../../storybook/assets/statusAvailable.svg';
import statusPlanned from '../../../storybook/assets/statusPlanned.svg';
import statusDeprecated from '../../../storybook/assets/statusDeprecated.svg';
import info from '../../../storybook/assets/info.png';

import { Meta, Title, Description, Story, Canvas, Controls, ArgTypes } from '@storybook/blocks';
import * as WtgApp from './WtgApp.stories.ts';

<Meta of={WtgApp} />

<div className="component-header">
    <h1>App</h1>
</div>

<table style={{ width: '100%' }} className="component-status">
    <thead>
        <tr>
            <th>Design</th>
            <th>Develop</th>
            <th>Platform Builder</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td></td>
            <td>
                <img className="status-chip" src={statusAvailable}></img>
            </td>
            <td>
                <a href="../?path=/docs/getting-started-engineering-platform-builder-components--overview">
                    <img className="status-chip" src={info}></img>
                </a>
            </td>
        </tr>
    </tbody>
</table>

## Overview

<p className="component-description">
    <Description />
</p>

## Application layout

Supply takes advantage of the underlying [Vuetify layout system](https://vuetifyjs.com/en/features/application-layout/) features for it's application layout. This allows us to easily create complex website designs.

## Utility layout components

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Component</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>[App Bar](/docs/utilities-app-bar--docs)</td>
            <td>A container that is used navigation, branding, search, and actions</td>
        </tr>
        <tr>
            <td>[Container](/docs/utilities-container--docs)</td>
            <td>
                The container component is a layout wrapper that provides responsive and flexible grid-based spacing. It
                ensures proper alignment and padding for content, supporting fluid and fixed-width layouts. It works in
                conjunction with the layout grid system to create responsive designs
            </td>
        </tr>
        <tr>
            <td>[Main](/docs/utilities-main--docs)</td>
            <td>
                Main is used to contain the main content of an application. It is a structural component part of the
                layout system that allows you to easily create complex website designs.
            </td>
        </tr>
        <tr>
            <td>[Navigation Drawer](/docs/utilities-navigation-drawer--docs)</td>
            <td>A persistent or temporary container that holds site navigation links</td>
        </tr>
        <tr>
            <td>[Footer](/docs/utilities-footer--docs)</td>
            <td>A generic component used to replace the default html footer</td>
        </tr>
        <tr>
            <td>[Bottom Sheet](/docs/utilities-bottom-sheet--docs)</td>
            <td>A modified dialog that slides from the bottom of the screen, similar to bottom navigation</td>
        </tr>
    </tbody>
</table>

## Supply Layout Components

## Tailored Core Layout Components

Our design system includes tailored versions of the core layout components that further decorate our applications to suit the specific purposes of the Supply design system. These customized components ensure that our applications maintain a consistent look and feel while meeting the unique requirements of our design guidelines.

By leveraging these tailored components, we can provide a cohesive user experience across all Supply applications, ensuring that they align with our design principles and standards.

<table className="component-status" style={{ width: '100%' }}>
    <thead>
        <tr>
            <th>Component</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>[Action Bar](/docs/components-action-bar--docs)</td>
            <td>
                The Action Bar component is a key element of the core framework. The Action Bar, divided into 'Main
                Actions' and 'Destructive Actions' by a visual divider, enables users to perform tasks affecting the
                entire page or entity
            </td>
        </tr>
        <tr>
            <td>[Entity Actions](/docs/components-entity-actions--docs)</td>
            <td>
                EntityActions are a collection of entity specific actions you can perform based on where you are in the
                product and what is available on the current page.
            </td>
        </tr>
        <tr>
            <td>[Masthead](/docs/components-masthead--docs)</td>
            <td>
                The Masthead component, a key element of the core framework, appears at the top of every portal page.
                Customizable to meet the specific requirements of different portals, its primary role is to orient
                users, provide easy navigation, and offer consistent actions across entities
            </td>
        </tr>
        <tr>
            <td>[Navigation](/docs/components-navigation--docs)</td>
            <td>
                The Navigation component, a key element of the core framework, is represented as a vertical left-side
                menu in CargoWise portals. It contains links that help users navigate portals and understand where they
                are
            </td>
        </tr>
        <tr>
            <td>[Notifications](/docs/components-notifications--docs)</td>
            <td>
                The Notifications Component functions as a specialized drawer, designed to house all notifications. It
                alerts users to different types of information, warnings and errors.
            </td>
        </tr>
    </tbody>
</table>

## Desktop

<div class="docs-page">
    <Canvas of={WtgApp.Default} />
</div>

## Mobile

<div class="docs-page">
    <Canvas of={WtgApp.Mobile} />
</div>

<footer>
    💙 Have ideas, comments, or suggestions to improve this page? [Let us
    know!](https://teams.microsoft.com/l/channel/19%3aad1b3a73d4b34bba995b02feb1057a21%40thread.tacv2/support-documentation?groupId=79a0c65e-4192-4dd6-960b-93eabeb2b1be&tenantId=8b493985-e1b4-4b95-ade6-98acafdbdb01)
</footer>
