import WtgHyperlink from '@components/WtgHyperlink';
import WtgLabel from '@components/WtgLabel';
import WtgAboutDialog from '@components/framework/WtgAboutDialog';
import { WtgFramework } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, mount } from '@vue/test-utils';
import { h, nextTick, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi, { AppearanceOption } from '../../../../../../../WtgUi';
import AboutDialog from '../AboutDialog.vue';

enableAutoUnmount(afterEach);
let wtgUi = new WtgUi();

jest.mock('@composables/object-url', () => {
    return {
        useObjectUrl: (image: string, type: string) => {
            return {
                url: `${image}-${type}`,
                dispose: () => {
                    /* Do nothing */
                },
            };
        },
    };
});

describe('about-dialog', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);

        application = reactive(new WtgFramework());
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('it renders', () => {
        const wrapper = mountComponent();
        expect(wrapper.exists()).toBe(true);
    });

    test('it renders a WtgAboutDialog', () => {
        const wrapper = mountComponent();
        expect(wrapper.findComponent(WtgAboutDialog).exists()).toBe(true);
    });
    test('sets correct props on WtgAboutDialog', () => {
        const wrapper = mountComponent();
        const modal = wrapper.findComponent(WtgAboutDialog);

        expect(modal.props('modelValue')).toBe(false);
        expect(modal.props('dialogTitle')).toBe(application.about.dialogTitle);
        expect(modal.props('title')).toBe(application.about.portalName);
    });

    test('opens the dialog', async () => {
        const wrapper = mountComponent();
        const modal = wrapper.findComponent(WtgAboutDialog);
        expect(modal.props('modelValue')).toBe(false);

        application.dialogs.about.open();
        await nextTick();

        expect(modal.props('modelValue')).toBe(true);
    });

    test('when dialog is closed', async () => {
        application.dialogs.about.visible = true;
        const wrapper = mountComponent();
        const modal = wrapper.findComponent(WtgAboutDialog);
        expect(modal.props('modelValue')).toBe(true);

        modal.vm.$emit('update:modelValue', false);

        expect(application.dialogs.about.visible).toBe(false);
    });

    test.each([
        [{ appearance: 'dark', image: 'light', type: 'typeLight' }],
        [{ appearance: 'light', image: 'dark', type: 'typeDark' }],
        [{ appearance: 'muted', image: 'dark', type: 'typeDark' }],
    ])('it displays the title object-url for %s', ({ appearance, image, type }) => {
        wtgUi = createWtgUi(appearance as AppearanceOption);
        application.title = 'Portal Name';
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();
        const form = wrapper.findComponent({ name: 'WtgForm' });
        const imageAttributes = form.find('img').attributes();
        expect(imageAttributes.src).toEqual(`${image}-${type}`);
        expect(imageAttributes.alt).toEqual('Portal Name');
    });

    test('it displays the dialog static labels defined in the application object', async () => {
        application.about.frameworkVersionLabel = 'Framework';
        application.about.resourcesLabel = 'Resource Locations';
        application.about.serviceURLLabel = 'Service';
        application.about.portalURLLabel = 'Portal';
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();

        const labels = wrapper.findAllComponents(WtgLabel);
        expect(labels.at(2)!.text()).toEqual('Framework');
        expect(labels.at(6)!.text()).toEqual('Resource Locations');
        expect(labels.at(7)!.text()).toEqual('Service');
        expect(labels.at(8)!.text()).toEqual('Portal');
    });

    test('it displays the framework label & version', () => {
        application.title = 'Portal Title';
        application.about.frameworkVersion = 'frameworkVersion';
        application.about.frameworkVersionLabel = 'Framework';
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();

        const labels = wrapper.findAllComponents(WtgLabel);
        expect(labels.at(2)!.text()).toEqual('Framework');
        expect(labels.at(3)!.text()).toEqual('frameworkVersion');
    });

    test('it displays the correct version label if the application-version is known', () => {
        application.title = 'Portal Title';
        application.about.applicationVersionLabel = 'Application';
        application.about.applicationVersion = 'applicationVersion';
        application.about.versionsLabel = 'Plural';
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();

        const labels = wrapper.findAllComponents(WtgLabel);
        expect(labels.at(1)!.text()).toEqual('Plural');
        expect(labels.at(4)!.text()).toEqual('Application');
        expect(labels.at(5)!.text()).toEqual('applicationVersion');
    });

    test('it displays the correct version label if the application-version is NOT known', () => {
        application.title = 'Portal Title';
        application.about.applicationVersionLabel = 'Application';
        application.about.applicationVersion = '';
        application.about.versionLabel = 'Single';
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();

        const labels = wrapper.findAllComponents(WtgLabel);
        expect(labels.at(1)!.text()).toEqual('Single');
        expect(labels.at(3)!.text()).not.toEqual('Application');
    });

    test('it displays the service URL', () => {
        application.title = 'Portal Title';
        application.about.serviceURL = 'serviceURL';
        application.about.serviceURLLabel = 'Service';
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();

        const labels = wrapper.findAllComponents(WtgLabel);
        const links = wrapper.findAllComponents(WtgHyperlink);
        expect(labels.at(7)!.text()).toEqual('Service');
        expect(links.at(0)!.text()).toEqual('serviceURL');
    });

    test('it displays the portal URL', () => {
        application.title = 'Portal Title';
        application.about.portalURL = 'portalURL';
        application.about.portalURLLabel = 'Portal';
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();

        const labels = wrapper.findAllComponents(WtgLabel);
        const links = wrapper.findAllComponents(WtgHyperlink);
        expect(labels.at(8)!.text()).toEqual('Portal');
        expect(links.at(1)!.text()).toEqual('portalURL');
    });

    test('it displays the current UTC time', () => {
        application.title = 'Portal Title';
        application.about.UTCLabel = 'UTC Time';
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();

        const labels = wrapper.findAllComponents({ name: 'WtgLabel' });
        expect(labels.at(-2)!.text()).toContain('UTC Time');
    });

    test('it displays the copyright text', () => {
        application.dialogs.about.visible = true;

        const wrapper = mountComponent();

        const labels = wrapper.findAllComponents({ name: 'WtgLabel' });
        expect(labels.at(-1)!.text()).toContain(`© ${new Date().getFullYear()} WiseTech Global`);
    });

    function mountComponent({ propsData = {}, listeners = {}, slots = { default: h(AboutDialog) } } = {}) {
        const wrapper = mount(VApp, {
            listeners,
            propsData,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        return wrapper.findComponent(AboutDialog);
    }

    function createWtgUi(appearance: AppearanceOption): WtgUi {
        const ui = new WtgUi({
            appearance,
            theme: {
                logoLightImage: 'light',
                logoLightImageFileType: 'typeLight',
                logoDarkImage: 'dark',
                logoDarkImageFileType: 'typeDark',
            },
        });

        ui.breakpoint.smAndUp = true;
        return ui;
    }
});
