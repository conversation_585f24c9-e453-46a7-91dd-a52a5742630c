import WtgCol from '@components/WtgCol';
import WtgLayoutGrid from '@components/WtgLayoutGrid';
import WtgRow from '@components/WtgRow';
import { inputArgTypes } from '@composables/input';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/vue3';
import WtgDurationField from '..';

type Story = StoryObj<typeof WtgDurationField>;
const meta: Meta<typeof WtgDurationField> = {
    title: 'Components/Duration Field',
    component: WtgDurationField,
    parameters: {
        docs: {
            description: {
                component:
                    'Duration Field is specifically designed to allow users to enter values in a HH:MM format for recording a length of time.',
            },
        },
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/g6kTPiwXZrzyIZb4i41RYl/%5BCargoWise%5D-SUPPLY---Components?type=design&node-id=383-43226',
        },
        layout: 'centered',
    },
    argTypes: {
        ...inputArgTypes,
        displayFormat: {
            options: ['HHH:MM', 'DD:HH:MM'],
            control: {
                type: 'select',
            },
        },
    },
    render: (args) => ({
        components: { WtgDurationField },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `<WtgDurationField
                        v-bind="args"
                        @update:model-value="updateModel"
                        @change="changeAction"
                        @input="inputAction"
                        @focus="focusAction"
                        @blur="blurAction">
                    </WtgDurationField>`,
    }),
    decorators: [
        () => ({
            template: `
                <div style="max-width:300px">
                    <story/>
                </div>`,
        }),
    ],
};

export default meta;

export const Default: Story = {
    args: {
        label: 'Label',
        modelValue: '0:00',
    },
};

export const Sentiments: Story = {
    args: {
        modelValue: '0:00',
    },
    render: (args) => ({
        components: { WtgDurationField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
        <WtgRow>
        <WtgRow style="max-width: fit-content; gap: 8px;" class="d-flex flex-column col-md-3">
            <WtgLayoutGrid>
                <WtgDurationField v-bind="args" 
                    label="Default"
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDurationField>
                <WtgDurationField v-bind="args" 
                    label="Success" 
                    sentiment="success" 
                    messages="Sample success message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDurationField>
                <WtgDurationField v-bind="args" 
                    label="Warning" 
                    sentiment="warning" 
                    messages="Sample warning message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDurationField>
                <WtgDurationField v-bind="args" 
                    label="Error" 
                    sentiment="critical" 
                    messages="Sample error message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDurationField>
                <WtgDurationField v-bind="args" 
                    label="Info" 
                    sentiment="info" 
                    messages="Sample info message"             
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDurationField>
            </WtgLayoutGrid>
        </WtgRow>
    </WtgRow>`,
    }),
};

export const ReadOnly: Story = {
    args: {
        modelValue: '0:00',
        label: 'Read only',
        readonly: true,
    },
    render: (args) => ({
        components: { WtgDurationField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
                <WtgDurationField v-bind="args" 
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDurationField>`,
    }),
};

export const Disabled: Story = {
    args: {
        modelValue: '0:00',
        label: 'Disabled',
        disabled: true,
    },
    render: (args) => ({
        components: { WtgDurationField, WtgCol, WtgLayoutGrid, WtgRow },
        setup: () => ({ args }),
        methods: {
            updateModel: action('update:model'),
            inputAction: action('input'),
            changeAction: action('change'),
            focusAction: action('focus'),
            blurAction: action('blur'),
        },
        template: `
                <WtgDurationField v-bind="args" 
                    @update:model-value="updateModel"
                    @input="inputAction" 
                    @change="changeAction"
                    @focus="focusAction"
                    @blur="blurAction">
                ></WtgDurationField>`,
    }),
};
