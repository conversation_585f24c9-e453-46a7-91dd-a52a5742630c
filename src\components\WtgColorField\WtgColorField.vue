<template>
    <WtgInput v-bind="baseInputProps" promptable :input="input">
        <input
            :id="computedId"
            ref="input"
            autocomplete="off"
            :aria-label="ariaLabel"
            :aria-labelledby="ariaLabelledby"
            :disabled="disabled || displayOnly"
            type="text"
            :placeholder="placeholder"
            :readonly="readonly"
            :value="internalValue"
            @blur="onBlur"
            @focus="onFocus"
            @input="onInput"
        />
        <template #prompter>
            <WtgMenu
                v-model="menuRef"
                :close-on-content-click="false"
                location="bottom end"
                transition="slide-y-transition"
                offset="10px"
                nudge-bottom="4px"
                scroll-strategy="none"
                style="padding: var(--s-padding-xl)"
            >
                <template #activator="{ props: activatorProps }">
                    <WtgIcon
                        role="button"
                        tabindex="-1"
                        :aria-label="formatCaption('colorField.selectColor')"
                        :aria-hidden="false"
                        v-bind="activatorProps"
                        class="wtg-input--interactive-element"
                        :disabled="disabled || displayOnly || readonly"
                        :style="computedColorStyle"
                        icon="s-icon-status"
                    >
                    </WtgIcon>
                </template>
                <template #default>
                    <WtgColorPicker :model-value="computedColor" @update:model-value="onSetColor($event)" />
                </template>
            </WtgMenu>
        </template>
    </WtgInput>
</template>

<script setup lang="ts">
import { WtgColorPicker } from '@components/WtgColorPicker';
import { WtgIcon } from '@components/WtgIcon';
import { WtgInput } from '@components/WtgInput';
import { WtgMenu } from '@components/WtgMenu';
import { useFocus } from '@composables/focus';
import { basePropsFromProps, makeInputProps } from '@composables/input';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import { useLocale } from '@composables/locale';
import { useTheme } from '@composables/theme';
import { makeValidationProps } from '@composables/validation';
import { computed, getCurrentInstance, ref, watchEffect } from 'vue';

//
// Properties
//
const props = defineProps({
    /**
     * The current value of the color field, used for two-way binding.
     * Should be a string representing a valid color (e.g., '#FFFFFF' or 'rgba(255, 255, 255, 1)').
     */
    modelValue: {
        type: String,
        default: undefined,
    },

    ...makeInputProps(),
    ...makeLayoutGridColumnProps(),
    ...makeValidationProps(),

    /**
     * The value of the color field.
     * @deprecated Use `modelValue` instead.
     */
    value: {
        type: String,
        default: undefined,
    },
});

//
// Emits
//
const emit = defineEmits<{
    blur: [e: FocusEvent];
    focus: [e: FocusEvent];
    input: [value: string];
    'model-compat:input': [value: string];
    'update:modelValue': [value: string];
}>();

//
// State
//
const menuRef = ref(false);
const input = ref<HTMLElement>();
const internalValue = ref('');

//
// Composables
//
const instance = getCurrentInstance();
const { isFocused, focus, blur } = useFocus(props);
const { rgbToHex, isValidHexaColor } = useTheme();
const { formatCaption } = useLocale();

useLayoutGridColumn(props);

//
// Computed
//
const computedId = computed(() => props.id || props.inputId || `input-${instance!.uid}`);

const baseInputProps = computed(() => {
    return {
        ...basePropsFromProps(props),
        filled: internalValue.value !== '',
        id: computedId.value,
        focused: isFocused.value,
        hideMessages: menuRef.value,
    };
});

const computedColor = computed(() => {
    let color = internalValue.value;
    if (color.length > 0 && color[0] !== '#') {
        const el = document.createElement('div');
        el.style.background = color;
        document.body.appendChild(el);
        const computedStyle = window.getComputedStyle(el);
        color = rgbToHex(computedStyle.backgroundColor);
        document.body.removeChild(el);
    }
    if (color.length === 7 && color[0] === '#') {
        color += 'FF';
    }
    return isValidHexaColor(color) ? color : undefined;
});

const computedColorStyle = computed(() => {
    let styles: Record<string, string> = {};
    if (computedColor.value) {
        styles = {
            color: computedColor.value,
        };
    }
    return styles;
});

//
// Watchers
//
watchEffect(() => {
    internalValue.value = props.modelValue ?? props.value ?? '';
});

//
// Event Handlers
//
function onSetColor(newValue: string | Record<string, unknown>) {
    if (typeof newValue === 'string') {
        updateValue(newValue, true);
    }
}

function onInput(e: Event) {
    const newValue = (e.target as HTMLInputElement).value;
    updateValue(newValue, true);
}

function onFocus(e: FocusEvent) {
    emit('focus', e);
    if (!isFocused.value) {
        focus();
    }
}

function onBlur(e: FocusEvent) {
    emit('blur', e);
    blur();
}

//
// Helpers
//
function updateValue(newValue: string, notify: boolean) {
    if (internalValue.value !== newValue) {
        internalValue.value = newValue;
        if (notify) {
            emit('input', internalValue.value);
            emit('model-compat:input', internalValue.value);
            emit('update:modelValue', internalValue.value);
        }
    }
}
</script>

<style lang="scss">
.wtg-color-field__color {
    border-radius: var(--s-radius-xs);
    font-size: 14px;
    margin-inline-start: 3px;
    margin-inline-end: 3px;
}
</style>
