import WtgChart from '@components/WtgChart/WtgChart.vue';
import { makeLayoutGridColumnProps, useLayoutGridColumn } from '@composables/layoutGridColumn';
import {
    Chart,
    ChartData,
    ChartOptions,
    LineElement,
    PointElement,
    RadarController,
    RadialLinearScale,
} from 'chart.js';
import { PropType, VNode, defineComponent, h } from 'vue';

Chart.register(PointElement, RadarController, LineElement, RadialLinearScale);

export default defineComponent({
    name: 'WtgRadarChart',
    props: {
        data: {
            type: Object as PropType<ChartData>,
            default: (): ChartData => {
                return {
                    datasets: [],
                };
            },
        },
        options: {
            type: Object as PropType<ChartOptions>,
            default: (): ChartOptions => {
                return {};
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        ...makeLayoutGridColumnProps(),
    },
    setup(props) {
        useLayoutGridColumn(props);
    },
    render(): VNode {
        return h(WtgChart, {
            type: 'radar',
            data: this.data,
            options: this.options,
            loading: this.loading,
        });
    },
});
