import { Meta, StoryObj } from '@storybook/vue3';
import { WtgPolarAreaChart } from '../..';

type Story = StoryObj<typeof WtgPolarAreaChart>;
const meta: Meta<typeof WtgPolarAreaChart> = {
    title: 'Data viz/Polar Area Chart',
    component: WtgPolarAreaChart,
    parameters: {
        docs: {
            description: {
                component:
                    'Polar area charts are similar to pie charts, but each segment has the same angle - the radius of the segment differs depending on the value. This type of chart is often useful when we want to show a comparison data similar to a pie chart, but also show a scale of values for context.',
            },
        },
    },
    render: (args) => ({
        components: { WtgPolarAreaChart },
        setup: () => ({ args }),
        template: `<wtg-polar-area-chart v-bind="args"/>`,
    }),
};

export default meta;

export const Default: Story = {
    args: {
        data: {
            labels: ['Red', 'Orange', 'Yellow', 'Green', 'Blue'],
            datasets: [
                {
                    label: 'Dataset 1',
                    data: [11, 9, 7, 12, 8],
                    backgroundColor: ['#F4433680', '#FF980080', '#FFEB3B80', '#4CAF5080', '#03A9F480'],
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
            },
        },
        loading: false,
    },
};
