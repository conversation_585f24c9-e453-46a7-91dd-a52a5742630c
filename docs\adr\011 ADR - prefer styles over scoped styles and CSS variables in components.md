# Prefer styles over scoped styles and CSS variables in components

## Status
**Status**: Proposed  
> Options: `Proposed`, `Accepted`, `Rejected`, `Deprecated`, `Superseded`

## Context

In building the Supply design system, a decision needs to be made on how component styling is exposed and managed—specifically, whether to use <PERSON>ue’s `scoped` styles or to expose design system values (e.g. colors, spacing, sizes) as CSS variables.

The `scoped` attribute provides automatic style encapsulation, while CSS variables allow for dynamic runtime theming and consumer-level overrides. However, both approaches introduce complexity, increase specificity, and can make it harder for consumers to apply consistent and controlled modifications.

Instead, this ADR proposes that we prefer using standard styles written in a predictable, BEM-based class structure. Consumers should apply custom styling through predefined tokens, modifiers, and extension patterns—not by relying on scoped styles or variable overrides.

## Decision

- Do **not** use `scoped` styles in design system components.
- Prefer traditional styles using predictable, BEM-based classnames.
- Avoid exposing or depending on global CSS variables for design tokens unless necessary for runtime theming.
- Teams should use supported modifiers, component APIs, or slots to customize appearance.
- As a last resort, teams may apply targeted overrides using class selectors, with the understanding that:
  - These overrides are **not** officially supported.
  - They may break in future updates.
  - They should only be used after exploring design system enhancements or composition patterns.

## Consequences

### Benefits:
- Simplifies the implementation and mental model of component styles.
- Encourages use of officially supported APIs and modifiers.
- Avoids CSS variable proliferation and the complexity of maintaining a runtime theming strategy.
- Reduces specificity issues introduced by `scoped` styles.
- Enables better debugging and clarity in generated CSS.

### Trade-offs / Risks:
- Without scoped styles, internal styles may unintentionally clash with external styles if naming conventions aren't followed.
- Some teams may need more support or documentation to understand how to modify components safely.
- Not supporting CSS variables may limit dynamic theming options in future, unless explicitly added at the token level.

---

### Notes

This ADR follows the structure from [Documenting Architecture Decisions by Michael Nygard](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions). ADRs are stored in `docs/adr/` in this repository.

Use a sequential naming format: `001 ADR - title.md`, `001 ADR - title.md`, etc.
