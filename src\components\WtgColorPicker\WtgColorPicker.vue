<template>
    <VColorPicker
        v-bind="props"
        v-model="model"
        v-floating-vue-tooltip="tooltip"
        class="wtg-color-picker"
        @update:mode="onModeChange"
    />
</template>

<script setup lang="ts">
import { makeTooltipProps } from '@composables/tooltip';
import { PropType } from 'vue';
import { VColorPicker } from 'vuetify/components/VColorPicker';
import type { ColorMode } from './types';

//
// Properties
//
const props = defineProps({
    /**
     * The border size or visibility of the color picker.
     * Can be a string, number, or boolean.
     */
    border: {
        type: [String, Number, Boolean] as PropType<string | number | boolean>,
        default: false,
    },

    /**
     * The height of the canvas area in the color picker.
     * Can be a string (e.g., '150px') or a number (interpreted as pixels).
     */
    canvasHeight: {
        type: [String, Number] as PropType<string | number>,
        default: 150,
    },

    /**
     * The currently selected color in the color picker.
     * Should be a string representing the color (e.g., '#FFFFFF').
     */
    color: {
        type: String,
        default: undefined,
    },

    /**
     * If true, the color picker will be disabled and not interactive.
     */
    disabled: {
        type: Boolean,
        default: false,
    },

    /**
     * The size of the dot used in the color picker.
     * Can be a string (e.g., '10px') or a number (interpreted as pixels).
     */
    dotSize: {
        type: [String, Number] as PropType<string | number>,
        default: 10,
    },

    /**
     * The elevation level of the color picker.
     * Can be a string or number representing the elevation.
     */
    elevation: {
        type: [String, Number] as PropType<string | number>,
        default: undefined,
    },

    /**
     * If true, the canvas area of the color picker will be hidden.
     */
    hideCanvas: {
        type: Boolean,
        default: false,
    },

    /**
     * If true, the input fields for color values will be hidden.
     */
    hideInputs: {
        type: Boolean,
        default: false,
    },

    /**
     * If true, the sliders for adjusting color values will be hidden.
     */
    hideSliders: {
        type: Boolean,
        default: false,
    },

    /**
     * The current color mode of the color picker.
     * Options include 'hexa', 'rgba', 'hsla', etc.
     */
    mode: {
        type: String as PropType<ColorMode>,
        default: 'hexa',
    },

    /**
     * The available color modes for the color picker.
     * Example: ['hexa', 'rgba', 'hsla'].
     */
    modes: {
        type: Array as PropType<ColorMode[]>,
        default: () => ['hexa'],
    },

    /**
     * The CSS position property for the color picker.
     * Options include 'static', 'relative', 'fixed', 'absolute', or 'sticky'.
     */
    position: {
        type: String as PropType<'static' | 'relative' | 'fixed' | 'absolute' | 'sticky'>,
        default: undefined,
    },

    /**
     * If true, the swatches section will be displayed in the color picker.
     */
    showSwatches: {
        type: Boolean,
        default: false,
    },

    /**
     * The swatches to display in the color picker.
     * Can be an array of colors or color objects.
     */
    swatches: {
        type: Array as PropType<
            (
                | string
                | number
                | { readonly h: number; readonly s: number; readonly v: number; readonly a?: number }
                | { readonly r: number; readonly g: number; readonly b: number; readonly a?: number }
                | { readonly h: number; readonly s: number; readonly l: number; readonly a?: number }
            )[][]
        >,
        default: undefined,
    },

    /**
     * The maximum height of the swatches section.
     * Can be a string (e.g., '150px') or a number (interpreted as pixels).
     */
    switchMaxHeight: {
        type: [String, Number] as PropType<string | number>,
        default: 150,
    },

    /**
     * The HTML tag to use for the color picker container.
     * Example: 'div', 'span'.
     */
    tag: {
        type: String,
        default: 'div',
    },

    /**
     * If true, the color picker will use a tile layout.
     */
    tile: {
        type: Boolean,
        default: false,
    },

    /**
     * The width of the color picker.
     * Can be a string (e.g., '300px') or a number (interpreted as pixels).
     */
    width: {
        type: [String, Number] as PropType<string | number>,
        default: 300,
    },

    ...makeTooltipProps(),
});

const model = defineModel<string | Record<string, unknown>>({ default: undefined });

//
// Emits
//
const emit = defineEmits<{
    'update:mode': [value: ColorMode];
}>();

//
// Event Handlers
//
function onModeChange(value: ColorMode) {
    emit('update:mode', value);
}
</script>

<style lang="scss">
.wtg-color-picker {
    .v-color-picker-edit__input input {
        border: 1px solid var(--s-neutral-border-weak-default);
        background: var(--s-neutral-bg-default);
        color: var(--s-neutral-txt-default);
    }
}
</style>
