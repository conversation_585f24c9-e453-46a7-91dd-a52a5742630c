<template>
    <div>
        <WtgDivider />
        <WtgList
            class="pa-l"
            :style="isTabletOrMobile ? 'margin-top: var(--s-padding-s); margin-bottom: var(--s-padding-s)' : ''"
            role="menu"
        >
            <WtgNavigationRecentMenu
                :items="items"
                :recents-caption="application.captions.recent"
                :favorites-caption="application.captions.favorites"
                :collapsed="railActive"
                :caption-close="application.captions.close"
                :on-click="clickFavoritesHandler"
                :on-dialog-open="onRecentDialogOpen"
                @item-click="onMenuItemClick"
            />
        </WtgList>
        <WtgDivider />
        <WtgList class="pb-8" role="menu">
            <ConfigureMenu @item-click="onMenuItemClick" />
            <HelpMenu @item-click="onMenuItemClick" />
            <UserMenu @item-click="onMenuItemClick" />
        </WtgList>
    </div>
</template>

<script setup lang="ts">
import { WtgFrameworkRecentItem } from '@components/framework/types';
import WtgNavigationRecentMenu from '@components/framework/WtgNavigationRecentMenu';
import { Items } from '@components/framework/WtgNavigationRecentMenu/types';
import WtgDivider from '@components/WtgDivider';
import { WtgList } from '@components/WtgList';
import { useApplication } from '@composables/application';
import { useFramework } from '@composables/framework';
import { computed } from 'vue';
import ConfigureMenu from './configure';
import HelpMenu from './help';
import UserMenu from './user';

const { isTabletOrMobile } = useFramework();
const application = useApplication();

const emit = defineEmits<{
    'item-click': [item?: WtgFrameworkRecentItem];
}>();

function onMenuItemClick(item?: WtgFrameworkRecentItem): void {
    emit('item-click', item);
}

const railActive = computed((): boolean => {
    return isTabletOrMobile.value ? false : application.navDrawer.isRailActive;
});

const items = computed(
    (): Items => ({
        favorites: application.favorites,
        recents: application.recentItems,
    })
);

function onRecentDialogOpen(): void {
    emit('item-click');
    application.dialogs.recentItems.open();
}

function clickFavoritesHandler(): void {
    application.clickFavoritesHandler?.();
}
</script>
