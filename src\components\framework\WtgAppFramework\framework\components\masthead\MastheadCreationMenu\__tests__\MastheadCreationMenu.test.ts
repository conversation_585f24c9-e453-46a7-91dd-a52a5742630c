import { WtgFramework, WtgFrameworkMenuItemType } from '@components/framework/types';
import { setApplication } from '@composables/application';
import { enableAutoUnmount, flushPromises, mount } from '@vue/test-utils';
import { h, reactive } from 'vue';
import { VApp } from 'vuetify/components/VApp';
import WtgUi from '../../../../../../../../WtgUi';
import MastheadCreationMenu from '../MastheadCreationMenu.vue';

enableAutoUnmount(afterEach);

const wtgUi = new WtgUi();

const singleActionCreationMenu = [
    {
        id: '1',
        action: WtgFrameworkMenuItemType.Link,
        caption: 'ListItem Caption',
        onClick: jest.fn(),
    },
];

const multipleActionCreationMenu = [
    {
        id: '1',
        action: WtgFrameworkMenuItemType.Link,
        caption: 'ListItem Caption',
        onClick: jest.fn(),
    },
    {
        id: '2',
        action: WtgFrameworkMenuItemType.Link,
        caption: 'ListItem Caption 2',
        onClick: jest.fn(),
    },
];

describe('MastheadCreationMenu', () => {
    let el: HTMLElement;
    let application: WtgFramework;

    beforeEach(() => {
        el = document.createElement('div');
        el.setAttribute('data-app', 'true');
        document.body.appendChild(el);
        application = reactive(new WtgFramework());
        setApplication(application);
    });

    afterEach(() => {
        document.body.removeChild(el);
    });

    test('its name is MastheadCreationMenu', async () => {
        const wrapper = await mountComponentAsync();
        expect(wrapper.vm.$options.__name).toBe('MastheadCreationMenu');
    });

    describe('on a mobile device', () => {
        beforeEach(() => {
            wtgUi.breakpoint.lgAndUp = false;
        });

        test('it does not render a menu', async () => {
            const wrapper = await mountComponentAsync();
            const menu = wrapper.findComponent({ name: 'WtgDropdownButton' });
            expect(menu.exists()).toBe(false);
        });
    });

    describe('on a desktop device', () => {
        beforeEach(() => {
            wtgUi.breakpoint.mdAndDown = false;
        });

        test('it does render a menu', async () => {
            application.entityCreationMenu = multipleActionCreationMenu;
            const wrapper = await mountComponentAsync();
            const menu = wrapper.findComponent({ name: 'WtgDropdownButton' });
            expect(menu.exists()).toBe(true);
        });

        test('it renders a button with the caption', async () => {
            application.entityCreationMenu = multipleActionCreationMenu;
            const wrapper = await mountComponentAsync();
            const button = wrapper.findComponent({ name: 'WtgDropdownButton' });
            expect(button.exists()).toBe(true);
            expect(button.text()).toBe('Add new');
        });

        test('it does not renders a button when there are no application items', async () => {
            const wrapper = await mountComponentAsync();
            const button = wrapper.findComponent({ name: 'WtgDropdownButton' });
            expect(button.exists()).toBe(false);
        });

        test('it calls the singleItem click event from the button click when only 1 create item is available', async () => {
            application.entityCreationMenu = singleActionCreationMenu;
            const wrapper = await mountComponentAsync();
            const button = wrapper.findComponent({ name: 'WtgButton' });
            expect(singleActionCreationMenu[0].onClick).not.toHaveBeenCalled();
            await button.trigger('click');

            expect(singleActionCreationMenu[0].onClick).toHaveBeenCalled();
        });

        test('it displays a menu when more than 1 create item is available', async () => {
            application.entityCreationMenu = multipleActionCreationMenu;
            const wrapper = await mountComponentAsync();
            const button = wrapper.findComponent({ name: 'WtgIconButton' });
            await button.trigger('click');

            const items = wrapper.findAllComponents({ name: 'WtgListItem' });
            expect(items.length).toBe(2);
        });
    });

    async function mountComponentAsync({ props = {}, slots = { default: h(MastheadCreationMenu) } } = {}) {
        const wrapper = mount(VApp, {
            props,
            slots,
            global: {
                plugins: [wtgUi],
            },
        });
        await flushPromises();
        return wrapper.findComponent(MastheadCreationMenu);
    }
});
